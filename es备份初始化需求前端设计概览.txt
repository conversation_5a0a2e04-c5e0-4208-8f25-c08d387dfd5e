1、平台提供一个es备份查询列表，支持按条件查询，支持分页显示。 对应接口   /spider/es_mgt/backups/list/ 列表操作栏  存在三个按钮： 详情，验证，初始化 操作按钮   页面 对应图1 

2、验证按钮调用  已存在接口 /spider/es_mgt/backups/status/ 掉用后，再次掉用  /spider/es_mgt/backups/list/  刷新图1中 列表数据的备份状态

3、点击【详情】，跳出备份初始化详情，弹框的【详情】 页面备份详情信息  对应接口： /spider/es_mgt/backups/details/list/ 分页查询 对应图2



4、点击【初始化】，弹出初始化的模态框，选择业务，分支，环境  

    业务下拉列表 数据来源于： 已存在接口 spider/biz_mgt/get_biz_name_list/
	
	环境下拉列表来源于 ：已存在接口 spider/es_mgt/es_mgt_api/env-modules/
	
	分支下拉列表信息来源于：业务选择后 带入参数查询 ： spider/biz_mgt/get_test_iter_list/?biz_code=TRADE-BATCH


点击【确定】后，调用   

已存在接口 spider/db_mgt/db_iter_mgt_pipeline/  业务参数格式为 {
    "job_name": "test_es_init",
    "bis_pipeline_id": "MIDDLEWARE-SCHEDULE_dev",
    "suite_code": "it29",   
    "biz_br_name": "dev"
}   触发test_es_init流水线。    其中   "job_name": "test_es_init", 为固定参数，  

   "bis_pipeline_id": "MIDDLEWARE-SCHEDULE_dev",
    "suite_code": "it29",   
    "biz_br_name": "dev"   为页面下拉框数据选择   页面对应图三	
	
	
