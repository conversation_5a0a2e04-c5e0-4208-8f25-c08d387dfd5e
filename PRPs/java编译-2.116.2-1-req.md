* 现状与背景

  1. 开发人员在平台上对java项目在拉分支的时候，平台会根据规则改变pom中的依赖包的version变量名。
  2. 在某种情况下，会改成改依赖的父pom的artifact_id为变量，另外一种情况，改成已pom本身的artifact_id为变量名。
* 需求：

  1. 全局保留一种依赖包版本的properties的管理模式，全部已依赖包的**artifactId作为基准名，比如artifactId = otc-commons-utils，对应的version的properties就改成**com.howbuy.otc-commons-utils.version，不管master里面是什么形式。
* 需求优先级：

  1. 高
* 验收条件：

  1. master中是用父pom名管理的依赖version，不管依赖包有没有一起拉分支，都改成用已依赖包的artifactId作为基准的version变量名。
  2. master中是用子pom名管理的依赖version，不管依赖包有没有一起拉分支，都改成用已依赖包的artifactId作为基准的version变量名。
* 边界条件：

  1. 改功能只在java项目拉分支或者编译时，对pom进行修改时触发。其他场景不涉及。
