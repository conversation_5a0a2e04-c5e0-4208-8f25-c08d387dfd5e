name: "基础PRP模板 v2 - 富上下文与验证循环"
description: |

## 目的

为AI智能体优化的模板，用于实现具有足够上下文和自我验证能力的功能，通过迭代改进实现可工作的代码。

## 核心原则

1. **上下文为王**: 包含所有必要的文档、示例和注意事项
2. **验证循环**: 提供AI可以运行和修复的可执行测试/检查
3. **信息密集**: 使用代码库中的关键字和模式
4. **渐进式成功**: 从简单开始，验证，然后增强
5. **全局规则**: 确保遵循CLAUDE.md中的所有规则

---

## 目标

[需要构建什么 - 对最终状态和期望具体说明]

## 为什么

- [业务价值和用户影响]
- [与现有功能的集成]
- [解决了什么问题以及为谁解决]

## 什么

[用户可见的行为和技术要求]

### 成功标准

- [ ] [具体的可衡量结果]

## 所需的全部上下文

### 文档与参考资料（列出实现功能所需的所有上下文）

```yaml
# 必须阅读 - 将这些包含在您的上下文窗口中
- url: [官方API文档URL]
  why: [您需要的具体章节/方法]
  
- file: [path/to/example.py]
  why: [要遵循的模式，要避免的陷阱]
  
- doc: [库文档URL] 
  section: [关于常见陷阱的具体章节]
  critical: [防止常见错误的关键见解]

- docfile: [PRPs/ai_docs/file.md]
  why: [用户粘贴到项目中的文档]

# 内部知识查询 - 使用Neo4j MCP查询相关内部知识
- neo4j_query: "查询项目相关的技术实现模式和最佳实践"
  why: "获取现有的成功实现经验和避免重复的设计错误"
  
- neo4j_query: "查找类似功能的历史实现方案和技术选型"
  why: "参考已验证的技术方案和架构模式"
```

### 当前代码库结构（在项目根目录运行 `tree`）以获取代码库概览

```bash

```

### 期望的代码库结构，包含要添加的文件及文件职责

```bash

```

### 我们代码库的已知陷阱和库特性

```python
# 关键: [库名称] 需要 [特定设置]
# 示例: FastAPI需要异步函数用于端点
# 示例: 这个ORM不支持超过1000条记录的批量插入
# 示例: 我们使用pydantic v2并且  
```

## 实现蓝图

### 数据模型和结构

创建核心数据模型，确保类型安全和一致性。

```python
示例: 
 - orm模型
 - pydantic模型
 - pydantic模式
 - pydantic验证器

```

### 完成PRP需要完成的任务列表，按应完成的顺序排列

```yaml
任务1:
修改 src/existing_module.py:
  - 查找模式: "class OldImplementation"
  - 在包含"def __init__"的行后注入
  - 保留现有方法签名

创建 src/new_feature.py:
  - 镜像模式来源: src/similar_feature.py
  - 修改类名和核心逻辑
  - 保持错误处理模式相同

...(...)

任务N:
...

```

### 根据需要为每个任务添加伪代码

```python

# 任务1
# 包含关键细节的伪代码，不要写完整代码
async def new_feature(param: str) -> Result:
    # 模式: 总是先验证输入（参见src/validators.py）
    validated = validate_input(param)  # 抛出ValidationError
  
    # 陷阱: 这个库需要连接池
    async with get_connection() as conn:  # 参见src/db/pool.py
        # 模式: 使用现有的重试装饰器
        @retry(attempts=3, backoff=exponential)
        async def _inner():
            # 关键: API在>10请求/秒时返回429
            await rate_limiter.acquire()
            return await external_api.call(validated)
    
        result = await _inner()
  
    # 模式: 标准化响应格式
    return format_response(result)  # 参见src/utils/responses.py
```

### 集成点

```yaml
数据库:
  - 迁移: "向users表添加'feature_enabled'列"
  - 索引: "CREATE INDEX idx_feature_lookup ON users(feature_id)"
  
配置:
  - 添加到: config/settings.py
  - 模式: "FEATURE_TIMEOUT = int(os.getenv('FEATURE_TIMEOUT', '30'))"
  
路由:
  - 添加到: src/api/routes.py  
  - 模式: "router.include_router(feature_router, prefix='/feature')"
```

## 内部知识查询集成

### 🔍 Neo4j MCP 技术知识查询

在执行功能实现PRP时，AI智能体可以直接查询公司内部知识图谱：

```yaml
内部技术知识查询场景:
  - 相似功能实现: "查询现有的类似功能实现方案和代码模式"
  - 技术选型参考: "获取项目中使用的技术栈和库的最佳实践"
  - 架构模式查询: "查找现有的架构模式和设计原则"
  - 集成点参考: "查询与现有系统的集成方式和接口约定"
  - 错误处理模式: "获取项目中统一的错误处理和异常管理模式"

查询示例:
  - "查询howbuy-dfile项目中的SPI实现模式"
  - "获取文件服务相关的配置管理最佳实践"
  - "查找存储后端实现的通用错误处理模式"
  - "分析模块间依赖关系和集成点设计"
```

> ⚠️ **重要提示**: 此Neo4j MCP连接包含公司内部技术知识，AI在实现功能时可以直接查询和引用这些知识。

## 最终验证检查清单

- [ ] 错误情况得到优雅处理
- [ ] 日志信息丰富但不冗长
- [ ] 如需要则更新文档

---

## 要避免的反模式

- ❌ 当现有模式有效时不要创建新模式
- ❌ 不要因为"应该可以工作"而跳过验证
- ❌ 不要忽略失败的测试 - 修复它们
- ❌ 不要在异步上下文中使用同步函数
- ❌ 不要硬编码应该配置的值
- ❌ 不要捕获所有异常 - 要具体
