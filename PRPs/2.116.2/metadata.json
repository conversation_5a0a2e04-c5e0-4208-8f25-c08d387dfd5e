{"version": "2.116.2", "created_at": "2025-09-19T15:28:00Z", "generated_files": [{"path": "prps/java-pom-version-unifier.md", "type": "prp", "description": "Java POM版本变量名统一管理系统"}, {"path": "requirements/INITIAL.md", "type": "requirements", "description": "初始需求文档备份"}], "source_files": ["INITIAL_EXAMPLE.md", "java编译-2.116.2-1-req.md", "java编译-2.116.2-1-HLD.md", "pom解析的详细设计.md"], "template_used": "PRPs/templates/prp_base.md", "quality_score": 9, "confidence_level": "high", "context_included": ["业务需求文档", "高阶设计文档", "详细设计参考", "现有代码库分析", "实现模式和架构", "测试和验证策略"]}