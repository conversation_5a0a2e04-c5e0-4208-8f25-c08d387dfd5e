name: "Java POM版本变量名统一化 - 基于现有方法修改"
description: |

## 目的
为AI智能体优化的模板，用于修改现有的`__generate_real_variable_name`方法，实现依赖包版本变量名的统一管理，通过迭代改进实现可工作的代码。

## 核心原则
1. **上下文为王**: 包含所有必要的文档、示例和注意事项
2. **验证循环**: 提供AI可以运行和修复的可执行测试/检查
3. **信息密集**: 使用代码库中的关键字和模式
4. **渐进式成功**: 从简单开始，验证，然后增强
5. **全局规则**: 确保遵循CLAUDE.md中的所有规则

---

## 目标
修改现有的`__generate_real_variable_name`方法，实现全局统一的依赖包版本properties管理模式，全部以依赖包的artifactId作为基准名。

## 为什么
- **业务价值**: 统一版本变量命名规范，提高维护效率和一致性
- **用户影响**: 开发人员在拉分支时不再需要处理多种不同的版本变量命名格式
- **集成需求**: 与现有的Jenkins CI/CD流水线系统集成，在编译时自动触发

## 什么
修改`/Users/<USER>/data/be-scripts/be-scripts/common/files/xml/pom.py`文件中的`__generate_real_variable_name`方法，使其始终使用依赖包自身的artifactId作为版本变量名，格式为`${com.howbuy.<artifactId>.version}`。

### 成功标准
- [ ] 所有依赖包的版本变量名都按照`${com.howbuy.<artifactId>.version}`格式统一
- [ ] 不管master分支中原本使用父POM名还是子POM名，都转换为标准格式
- [ ] 不管依赖包是否一起拉分支，都应用统一的变量名规则

## 所需的全部上下文

### 文档与参考资料（列出实现功能所需的所有上下文）
```yaml
# 必须阅读 - 将这些包含在您的上下文窗口中
- file: /Users/<USER>/data/be-scripts/PRPs/java编译-2.116.2-1-req.md
  why: 包含核心业务需求和验收标准
  
- file: /Users/<USER>/data/be-scripts/PRPs/java编译-2.116.2-1-HLD.md
  why: 系统架构设计和技术选型，包含更新后的处理流程
  
- file: /Users/<USER>/data/be-scripts/PRPs/pom解析的详细设计.md
  why: 现有实现模式和技术细节，包含完整的代码示例

- file: /Users/<USER>/data/be-scripts/be-scripts/common/files/xml/pom.py
  why: 需要修改的目标文件，包含现有的__generate_real_variable_name方法
  critical: 第394-426行的__generate_real_variable_name方法是修改重点
```

### 当前代码库结构（在项目根目录运行`tree`）以获取代码库概览
```bash
# 核心文件结构
be-scripts/
├── common/files/xml/pom.py         # 需要修改的目标文件
├── utils/compile/mvn/pom_sorter.py  # POM解析器入口
└── job/jenkins/jenkins_xml.py      # Jenkins集成入口
```

### 期望的代码库结构，包含要添加的文件及文件职责
```bash
# 修改后的文件结构（无新增文件）
be-scripts/
├── common/files/xml/pom.py         # 修改__generate_real_variable_name方法
├── utils/compile/mvn/pom_sorter.py  # 保持不变
└── job/jenkins/jenkins_xml.py      # 保持不变
```

## 实现蓝图

### 数据模型和结构

无需创建新的数据模型，使用现有的数据结构和参数格式。

### 完成PRP需要完成的任务列表，按应完成的顺序排列

```yaml
任务1:
修改 __generate_real_variable_name方法:
  - 位置: be-scripts/common/files/xml/pom.py:394-426行
  - 保持方法签名不变: def __generate_real_variable_name(self, ref_node, artifact_id, artifact_kinship)
  - 移除复杂的父子关系判断逻辑
  - 直接使用artifact_id生成标准变量名
  - 保持返回值格式一致

任务2:
简化变量名生成逻辑:
  - 移除independent_flag相关逻辑
  - 移除artifact_kinship的复杂判断
  - 统一使用com.howbuy.{artifact_id}.version格式
  - 保持与__change_ref_and_get_variable_name_of_version_node的调用关系

任务3:
验证修改后的方法行为:
  - 确保所有情况都返回标准格式的变量名
  - 测试与现有代码的兼容性
  - 验证XML节点修改的正确性
```

### 根据需要为每个任务添加伪代码
```python
# 任务1: 修改__generate_real_variable_name方法
def __generate_real_variable_name(self, ref_node, artifact_id, artifact_kinship):
    """
    生成标准化的版本变量名
    新规则：统一使用com.howbuy.{artifact_id}.version格式
    
    @param ref_node: XML节点引用
    @param artifact_id: 依赖包的artifactId
    @param artifact_kinship: 依赖关系字典（保持兼容性但不再使用）
    @return: 标准化的变量名字符串
    """
    
    # 直接使用artifact_id生成标准变量名
    # 移除复杂的父子关系判断逻辑
    variable_name = f"com.howbuy.{artifact_id}.version"
    
    # 调用现有的方法修改XML节点并返回结果
    return self.__change_ref_and_get_variable_name_of_version_node(ref_node, artifact_id)

# 任务2: 简化后的完整实现
def __generate_real_variable_name(self, ref_node, artifact_id, artifact_kinship):
    """
    生成标准化的版本变量名
    统一规则：全部以依赖包的artifactId作为基准名
    
    @param ref_node: XML节点引用
    @param artifact_id: 依赖包的artifactId
    @param artifact_kinship: 依赖关系字典（保持兼容性但不再使用）
    @return: 标准化的变量名字符串
    """
    
    # 直接使用标准的变量名格式，移除所有复杂的判断逻辑
    standard_variable_name = f"com.howbuy.{artifact_id}.version"
    
    # 调用现有的节点修改方法，保持兼容性
    return self.__change_ref_and_get_variable_name_of_version_node(ref_node, artifact_id)

# 任务3: 验证伪代码
# 验证点1: 确保方法签名保持不变
# 验证点2: 确保返回值格式与现有代码兼容
# 验证点3: 确保XML节点修改逻辑正确
```

### 集成点
```yaml
现有方法调用:
  - 调用位置: pom_sorter.py中的POM解析流程
  - 调用方式: pom_modifier.__generate_real_variable_name(ref_node, artifact_id, artifact_kinship)
  - 返回值: 变量名字符串，用于后续的properties标签更新
  
参数兼容性:
  - ref_node: 保持不变，用于XML节点操作
  - artifact_id: 保持不变，作为变量名的基础
  - artifact_kinship: 保持参数兼容，但内部逻辑不再使用
  
返回值兼容性:
  - 格式: 保持与现有__change_ref_and_get_variable_name_of_version_node方法返回值一致
  - 用途: 用于后续的properties标签映射和版本管理
```

## 验证循环

### 级别1: 语法和样式
```bash
# 首先运行这些 - 在继续之前修复任何错误
pylint --rcfile=pylint.conf be-scripts/common/files/xml/pom.py --disable=C0103,C0114,C0115,C0116

# 预期: 没有语法错误和严重的代码风格问题
```

### 级别2: 单元测试，使用现有测试模式
```python
# 创建测试脚本来验证修改后的方法行为
def test_variable_name_generation():
    """测试版本变量名生成是否符合统一标准"""
    
    # 模拟输入参数
    ref_node = create_mock_ref_node()
    artifact_id = "otc-commons-utils"
    artifact_kinship = {"parent": ["child"]}  # 不再使用但保持兼容
    
    # 创建PomModifier实例
    pom_modifier = PomModifier(workspace="/tmp", version="test-RELEASE")
    
    # 调用修改后的方法
    result = pom_modifier.__generate_real_variable_name(ref_node, artifact_id, artifact_kinship)
    
    # 验证结果
    expected_variable_name = "com.howbuy.otc-commons-utils.version"
    assert expected_variable_name in result, f"Expected {expected_variable_name}, got {result}"
    
    print("✓ 版本变量名生成测试通过")

def test_compatibility_with_existing_code():
    """测试与现有代码的兼容性"""
    
    # 确保方法签名没有改变
    import inspect
    signature = inspect.signature(PomModifier.__generate_real_variable_name)
    expected_params = ['self', 'ref_node', 'artifact_id', 'artifact_kinship']
    actual_params = list(signature.parameters.keys())
    
    assert actual_params == expected_params, f"方法签名改变: {actual_params} != {expected_params}"
    
    print("✓ 兼容性测试通过")

# 运行测试
python37 -c "
import sys
sys.path.append('/Users/<USER>/data/be-scripts')
from be_scripts.common.files.xml.pom import PomModifier

# 测试变量名生成逻辑
modifier = PomModifier('/tmp', 'test')
print('方法签名兼容性检查通过')
print('✓ 基本功能验证完成')
"
```

### 级别3: 集成测试
```bash
# 使用现有的Jenkins流水线进行集成测试
# 或者创建模拟测试环境

# 创建测试POM文件
cat > test_pom.xml << 'EOF'
<project xmlns="http://maven.apache.org/POM/4.0.0">
    <groupId>com.howbuy.test</groupId>
    <artifactId>test-app</artifactId>
    <version>1.0.0</version>
    
    <dependencies>
        <dependency>
            <groupId>com.howbuy.otc.common</groupId>
            <artifactId>otc-commons-utils</artifactId>
            <version>${old-variable-format}</version>
        </dependency>
    </dependencies>
</project>
EOF

# 运行修改后的POM处理逻辑
python37 -c "
import sys
sys.path.append('/Users/<USER>/data/be-scripts')
from be_scripts.common.files.xml.pom import PomModifier
import xml.etree.ElementTree as ET

# 解析测试POM
tree = ET.parse('test_pom.xml')
root = tree.getroot()

# 创建PomModifier实例
modifier = PomModifier('/tmp', 'test-RELEASE')

# 模拟调用修改后的方法
print('✓ 集成测试环境准备完成')
"

# 预期: 版本变量名被正确修改为${com.howbuy.otc-commons-utils.version}
```

## 最终验证检查清单
- [ ] 方法签名保持不变，确保向后兼容
- [ ] 所有版本变量名都按照`${com.howbuy.<artifactId>.version}`格式生成
- [ ] 移除了复杂的父子关系判断逻辑
- [ ] 代码检查无严重错误: `pylint --rcfile=pylint.conf be-scripts/common/files/xml/pom.py`
- [ ] 基本功能测试通过
- [ ] 与现有代码集成正常
- [ ] 日志信息符合预期

---

## 要避免的反模式
- ❌ 不要修改方法签名，保持参数兼容性
- ❌ 不要创建新的类或方法，只在现有方法上修改
- ❌ 不要忽略参数验证和错误处理
- ❌ 不要破坏与现有代码的集成
- ❌ 不要添加otc-commons-server特殊逻辑或其他项目的特殊处理

## 质量评分
**评分: 9/10**

**评分理由**:
- ✅ 包含所有必要的上下文和现有实现分析
- ✅ 提供了具体的修改步骤和验证方法
- ✅ 保持了与现有代码的兼容性
- ✅ 验证关卡可执行且有意义
- ✅ 避免了复杂的重构，专注于单一方法修改
- ✅ 明确了测试策略和验收标准
- ⚠️ 需要在实际环境中验证修改后的行为

**一次性实现成功信心**: 高 - 基于现有代码的精确修改，有明确的验证步骤