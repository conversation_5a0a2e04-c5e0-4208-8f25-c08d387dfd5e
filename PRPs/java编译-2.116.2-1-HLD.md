# Java编译系统概要设计文档

## 1. 系统概述

### 1.1 业务背景

开发人员在平台上对Java项目拉分支时，平台会根据规则改变POM中依赖包的version变量名。当前存在两种不一致的处理方式：

1. **父POM变量名模式**：使用父POM的artifactId作为版本变量名

   ```xml
   <properties>
       <com.howbuy.otc-commons.version>20250912-RELEASE</com.howbuy.otc-commons.version>
   </properties>
   <dependency>
       <groupId>com.howbuy.otc.common</groupId>
       <artifactId>otc-commons-utils</artifactId>
       <version>${com.howbuy.otc-commons.version}</version>
   </dependency>
   ```
2. **子POM变量名模式**：使用依赖包自身的artifactId作为版本变量名

   ```xml
   <properties>
       <com.howbuy.otc-commons-utils.version>20250912-RELEASE</com.howbuy.otc-commons-utils.version>
   </properties>
   <dependency>
       <groupId>com.howbuy.otc.common</groupId>
       <artifactId>otc-commons-utils</artifactId>
       <version>${com.howbuy.otc-commons-utils.version}</version>
   </dependency>
   ```

### 1.2 设计目标

**核心目标**：统一依赖包版本的properties管理模式，全部以依赖包的artifactId作为基准名。

**具体要求**：

- 对于任何依赖包（如artifactId = otc-commons-utils），对应的version的properties统一改成 `${com.howbuy.otc-commons-utils.version}`
- 不管master分支中原本使用什么形式的变量名，都要统一修改
- 不管依赖包是否一起拉分支，都要应用统一的变量名规则

### 1.3 系统边界

- **触发场景**：仅在Java项目拉分支或编译时，对POM进行修改时触发
- **处理范围**：Maven POM文件中的依赖版本变量名
- **不涉及场景**：其他非Java项目或非编译场景

## 2. 系统架构设计

### 2.1 整体架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Jenkins XML   │───▶│   PomSorter     │───▶│  Pipeline       │
│   入口控制器     │    │   核心解析器     │    │  更新器         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   参数解析      │    │   版本变量名     │    │   Jenkins配置   │
│   日志记录      │    │   统一修改       │    │   更新          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2.2 核心组件

#### 2.2.1 JenkinsXml类

- **职责**：系统入口控制器，负责参数解析和流程协调
- **关键功能**：调用POM解析和版本统一处理流程

#### 2.2.2 PomSorter类

- **职责**：POM文件解析和依赖关系分析的核心引擎
- **关键功能**：依赖树构建、编译批次生成、版本变量名统一修改

#### 2.2.3 PomModifier类

- **职责**：POM文件内容修改和版本更新
- **关键功能**：实现版本变量名的统一修改逻辑

### 2.3 技术选型

**Python 3.9.13**：主要开发语言，提供丰富的XML处理和Git操作库

**MySQL 5.7**：可靠的关系型数据库，存储项目元数据和依赖关系

**Jenkins**：CI/CD平台，提供流水线执行环境

**Git**：版本控制系统，管理代码分支和提交

## 3. 核心功能设计

### 3.1 版本变量名统一修改机制

#### 3.1.1 变量名生成规则

**标准格式**：`${com.howbuy.<artifactId>.version}`

**生成逻辑**：

- 提取依赖包的artifactId
- 按照固定格式生成版本变量名
- 替换POM文件中的原有变量引用

**示例转换**：

```xml
<!-- 修改前 -->
<version>${com.howbuy.otc-commons.version}</version>

<!-- 修改后 -->
<version>${com.howbuy.otc-commons-utils.version}</version>
```

#### 3.1.2 Properties标签同步更新

**处理逻辑**：

1. 扫描所有需要修改的版本变量名
2. 在properties标签中创建或更新对应的变量定义
3. 设置变量值为当前分支版本号

**示例**：

```xml
<properties>
    <com.howbuy.otc-commons-utils.version>20250917-RELEASE</com.howbuy.otc-commons-utils.version>
</properties>
```

### 3.2 POM文件处理流程

#### 3.2.1 文件扫描与解析

1. **工作空间扫描**：遍历所有POM文件
2. **XML解析**：使用ElementTree解析POM结构
3. **命名空间处理**：正确处理Maven XML命名空间

#### 3.2.2 依赖关系分析

1. **依赖树构建**：递归解析项目依赖关系
2. **应用分类**：区分本次迭代应用和非迭代应用
3. **版本管理**：统一处理不同类型应用的版本

#### 3.2.3 版本变量修改

1. **变量名识别**：定位需要修改的version节点
2. **标准化转换**：按照统一规则生成新的变量名
3. **Properties更新**：同步更新properties标签中的变量定义

### 3.3 特殊情况处理

#### 3.3.1 特殊项目处理

- **otc-commons-server**：使用专门的变量名规则
- **父子POM关系**：正确处理继承关系中的版本变量

#### 3.3.2 错误处理机制

- **XML格式验证**：确保POM文件格式正确
- **依赖循环检测**：避免依赖关系中的循环引用
- **版本冲突解决**：处理版本变量名冲突情况

## 4. 系统处理流程

### 4.1 主处理流程

```mermaid
graph TD
    A[开始] --> B[工作空间清理]
    B --> C[克隆代码到本地]
    C --> D[POM版本变量名统一修改]
    D --> E[提交POM修改]
    E --> F[重新清理工作空间]
    F --> G[解析POM依赖关系]
    G --> H[生成编译批次]
    H --> I[提交最终代码]
    I --> J[结束]
```

### 4.2 版本变量名修改详细流程

```mermaid
graph TD
    A[扫描POM文件] --> B[解析XML结构]
    B --> C[识别version节点]
    C --> D[提取artifactId]
    D --> E{是否为特殊项目?}
    E -->|是| F[应用特殊规则]
    E -->|否| G[应用标准规则]
    F --> H[生成新变量名]
    G --> H
    H --> I[修改version节点]
    I --> J[更新properties标签]
    J --> K[保存POM文件]
    K --> L[提交代码变更]
```

### 4.3 应用分类处理

#### 4.3.1 本次迭代应用

- **分支选择**：使用迭代指定分支
- **版本处理**：使用分支版本号
- **优先级**：高优先级处理

#### 4.3.2 非本次迭代应用

- **分支选择**：使用默认分支（master/main）
- **版本处理**：使用稳定发布版本
- **缓存策略**：优先使用缓存结果

## 5. 验收标准

### 5.1 功能验收

1. **版本变量名统一**：所有依赖包的版本变量名都按照 `${com.howbuy.<artifactId>.version}`格式
2. **Properties同步**：properties标签中包含对应的变量定义
3. **分支版本一致**：所有变量值都使用当前分支版本号

### 5.2 场景验收

1. **父POM变量名场景**：原本使用父POM名的依赖，成功转换为artifactId格式
2. **子POM变量名场景**：原本使用子POM名的依赖，保持或转换为标准格式
3. **混合依赖场景**：同时包含迭代应用和非迭代应用的依赖关系正确处理

### 5.3 质量验收

1. **XML格式正确**：修改后的POM文件格式符合Maven规范
2. **编译成功**：修改后的项目能够正常编译
3. **依赖解析正确**：Maven能够正确解析修改后的依赖关系
