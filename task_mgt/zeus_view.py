import datetime

from rest_framework import viewsets, serializers
from rest_framework.response import Response
from rest_framework import status

from iter_mgt.archive_step_enum import ArchiveStepEnum
from public.ssh_cnt import SSHConnectionManager
from spider.settings import ApiR<PERSON><PERSON>, logger, GITLAP_INFO
from task_mgt.http_task import HttpTask
from task_mgt import models
from iter_mgt.models import Branches, IterMgtArchiveLog
from task_mgt import zeus_ser
from env_mgt import env_info_ser
from task_mgt.zeus_ser import (get_app_master_address, get_app_giturl_members,
                               get_app_suite_spec_region_group, get_app_prod_publish_version_by_node)
from lib_repo_mgt.lib_repo_mgt_ser import get_node_version, get_app_prod_ver_by_prod_group
from user.views import SpiderAnonRateThrottle


class CreateConfigBranchApi(viewsets.ViewSet):
    interface_name = "c_config_br"

    def get_zeus_app_info(self, repos_path_list, app_list):
        multi_repos_list = []
        # 先找到一个仓库下有多个应用的仓库
        for row in zeus_ser.get_app_info_from_git_code_path(repos_path_list):
            if row[0] in app_list:
                multi_repos_list.append(row[1])

        zeus_app_info = {}
        for row in zeus_ser.get_app_info_from_git_code_path(repos_path_list):
            # 一个仓库下有多个应用，本次没有申请则跳过
            if row[1] in multi_repos_list and row[0] not in app_list:
                continue

            # 如果存在namespace，证明不能通过应用名当做唯一标识
            if row[2]:
                nacos_namespace = row[2]
            else:
                nacos_namespace = row[0]
            # 不重复拉取zeus分支
            if nacos_namespace in zeus_app_info:
                continue
            zeus_app_info[nacos_namespace] = {"repos_path": row[1]}
        return zeus_app_info

    def create(self, request):
        logger.info("创建配置分支参数{}".format(request.data))
        ser = zeus_ser.CreateConfigSerializer(data=request.data)
        if not ser.is_valid():
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(ser.errors))
        result_list = []
        repos_path_list = []
        app_list = []
        for row in ser.validated_data["repos_str"]:
            repos_path_list.append(row["repos_path"])
            if "module_name" in row:
                app_list.append(row["module_name"])
        with HttpTask() as http_task:
            zeus_app_info = self.get_zeus_app_info(repos_path_list, app_list)
            logger.info(zeus_app_info)
            for app_name in zeus_app_info:
                logger.info(app_name)
                r_status, result = http_task.call_interface(self.interface_name,
                                                            {"app_name_temp": app_name,
                                                             "app_branch": GITLAP_INFO["HTTP_URL"] + "/" +
                                                                           zeus_app_info[app_name][
                                                                               "repos_path"] + ".git",
                                                             "iteration_number": ser.validated_data["branch_name"],
                                                             "desc": ser.validated_data["desc"],
                                                             "environments": "dev"})
                if r_status == models.InterfaceResults.FAILURE:
                    return Response(data=ApiResult.failed_dict(msg="宙斯接口调用失败{}".format(result)),
                                    status=status.HTTP_400_BAD_REQUEST)
                result_list.append(result)
        return Response(data=ApiResult.success_dict(msg="宙斯接口调用结束{}".format(result_list)),
                        status=status.HTTP_200_OK)


class FileConfigBranchApi(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer
    interface_name = "archive_config"

    def put(self, request):
        logger.info("归档配置分支参数{}".format(request.data))
        ser = zeus_ser.FileConfigSerializer(data=request.data)
        if not ser.is_valid():
            return Response(status=status.HTTP_200_OK, data=ApiResult.failed_dict(ser.errors))
        result_list = []
        # 获取接入宙斯的应用
        request_params = []
        r_status = None
        with HttpTask() as http_task:
            # 优化移动端归档时没有调用宙斯配置归档的问题。zt@2024-11-21
            # for row in zeus_ser.get_app_info(ser.validated_data["iteration_id"]):
            for row in zeus_ser.get_app_list_with_iter_id_for_zeus(ser.validated_data["iteration_id"]):
                logger.info(row)
                """
                if row[2] == 1:
                    部署调用宙斯的归档接口，不再判断该应用是否已经标识为zeus_type=1 20210617 by fwm
                """
                params = {"iteration_number": row[0], "app_name": row[1]}
                r_status, result = http_task.call_interface(self.interface_name, params)
                if r_status == models.InterfaceResults.FAILURE:
                    return Response(data=ApiResult.failed_dict(msg="宙斯接口调用失败{}".format(result)),
                                    status=status.HTTP_400_BAD_REQUEST)
                result_list.append(result)
                request_params.append(params)
        IterMgtArchiveLog.objects.create(iteration_id=ser.validated_data["iteration_id"],
                                         step_order=ArchiveStepEnum.ARCHIVE_CONFIG.value[0],
                                         step_name=ArchiveStepEnum.ARCHIVE_CONFIG.value[1],
                                         step_desc=ArchiveStepEnum.ARCHIVE_CONFIG.value[2],
                                         step_status=r_status, request_params={"request_params": request_params},
                                         response_result={"msg": result_list}, opt_user=str(request.user),
                                         opt_time=datetime.datetime.now())
        return Response(data=ApiResult.success_dict(msg="宙斯接口调用结束{}".format(result_list)),
                        status=status.HTTP_200_OK)


class PublishConfigBranchApi(viewsets.ViewSet):
    interface_name = "publish_config"

    def create(self, request):
        logger.info("发布配置分支参数{}".format(request.data))
        pipeline_id = request.data.get('iteration_id')
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()

        app_name = request.data.get('app_name')
        env = request.data.get('env')
        # 获取接入宙斯的应用
        join_zeus_app = zeus_ser.get_join_zeus_app([app_name], pipeline_id)
        if len(join_zeus_app) == 0:
            logger.info("{}没有接入宙斯".format(app_name))
            return Response(data=ApiResult.success_dict(msg="没有接入宙斯"))
        logger.info("接入宙斯的应用为{}".format(join_zeus_app))

        with HttpTask() as http_task:
            if env == 'prod':
                env = ['prod', 'zb']
                suite_info = env_info_ser.get_suite_name(join_zeus_app, env)
            else:
                suite_info = env_info_ser.get_suite_name(join_zeus_app, [env])
            if suite_info:
                for row in suite_info:
                    logger.info(row)
                    r_status, result = http_task.call_interface(self.interface_name,
                                                                {"iteration_number": br_info.br_name,
                                                                 "app_name": row[1],
                                                                 "environment": row[0],
                                                                 })
                    if r_status == models.InterfaceResults.FAILURE:
                        return Response(data=ApiResult.failed_dict(msg="宙斯接口调用失败{}".format(result)))
                    if result["code"] == "error":
                        return Response(data=ApiResult.failed_dict(msg=result["message"]))
                    logger.info("调用结果{}".format(result))
            else:
                return Response(data=ApiResult.failed_dict(msg="{}应用没有绑定{}环境套".format(join_zeus_app, env)))
        return Response(data=ApiResult.success_dict(msg="宙斯接口调用结束{}".format(result)))


class SyncConfigApi(viewsets.ViewSet):
    interface_name = "sync_config"

    def create(self, request):
        logger.info(request.data)
        pipeline_id = request.data.get('iteration_id')
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()

        app_name_list = request.data.get('app_name')
        logger.info(app_name_list)
        # 获取接入宙斯的应用
        join_zeus_app = zeus_ser.get_join_zeus_app(app_name_list, pipeline_id)
        if len(join_zeus_app) == 0:
            logger.info("{}没有接入宙斯".format(app_name_list))
            return Response(data=ApiResult.success_dict(msg="没有接入宙斯"))
        logger.info("接入宙斯的应用为{}".format(join_zeus_app))
        env = request.data.get('env')
        if env == 'prod':
            env = ['prod', 'zb']
        result_list = []
        with HttpTask() as http_task:
            suite_info = env_info_ser.get_suite_name(join_zeus_app, env)
            if suite_info:
                for row in suite_info:
                    logger.info(row)
                    r_status, result = http_task.call_interface(self.interface_name,
                                                                {"iteration_number": br_info.br_name,
                                                                 "app_name": row[1],
                                                                 "environments": row[0],
                                                                 })

                    if r_status == models.InterfaceResults.FAILURE:
                        return Response(data=ApiResult.failed_dict(msg=result))

                    if result["code"] == "error":
                        return Response(data=ApiResult.failed_dict(msg=result["message"]))
                    logger.info("调用结果{}".format(result))
                    result_list.append(result["message"])
            else:
                return Response(data=ApiResult.failed_dict(msg="{}应用没有绑定{}环境套".format(join_zeus_app, env)))
        return Response(data=ApiResult.success_dict(msg=join_zeus_app))


class CheckConfigConsistentApi(viewsets.ViewSet):
    interface_name = "check_config_consistent"

    def list(self, request):
        logger.info("发布配置分支参数{}".format(request.GET))
        pipeline_id = request.GET.get('iteration_id')
        logger.info("删除iter_mgt_archive_log中的迭代{}相关数据".format(pipeline_id))
        IterMgtArchiveLog.objects.filter(iteration_id=pipeline_id).delete()
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()
        if "app_name[]" in request.GET:
            app_name = request.GET.get('app_name[]')
            join_zeus_app = zeus_ser.get_join_zeus_app(app_name, pipeline_id)
        elif "app_name" in request.GET:
            app_name = request.GET.get('app_name')
            join_zeus_app = zeus_ser.get_join_zeus_app([app_name], pipeline_id)
        else:
            # 针对非上线应用的迭代归档，比如common 0819 by fwm
            app_name = ''
            join_zeus_app = []
        env = request.GET.get('env')
        # 获取接入宙斯的应用

        if len(join_zeus_app) == 0:
            logger.info("{}没有接入宙斯".format(app_name))
            return Response(data=ApiResult.success_dict(msg="没有接入宙斯"))
        logger.info("接入宙斯的应用为{}".format(join_zeus_app))
        r_status = None
        request_params = []
        with HttpTask() as http_task:
            if env == 'prod':
                env = ['prod', 'zb']
                suite_info = env_info_ser.get_suite_name(join_zeus_app, env)
            else:
                suite_info = env_info_ser.get_suite_name(join_zeus_app, [env])
            if suite_info:
                for row in suite_info:
                    logger.info(row)
                    params = {"iteration_number": br_info.br_name, "app_name": row[1], "tenant_id": row[0]}
                    request_params.append(params)
                    r_status, result = http_task.call_interface(self.interface_name, params)
                    if r_status == models.InterfaceResults.FAILURE:
                        return Response(data=ApiResult.failed_dict(msg=result))
                    if result["code"] == "error" or result["block"] == "true":
                        logger.error("调用结果：{}".format(result["message"]))
                        return Response(data=ApiResult.failed_dict(msg="应用{}环境{}的调用结果{}".format(row[1], row[0], result["message"])))
                    logger.info("调用结果：{}".format(result["message"]))

            else:
                return Response(data=ApiResult.failed_dict(msg="{}应用没有绑定{}环境套".format(join_zeus_app, env)))
            IterMgtArchiveLog.objects.create(iteration_id=pipeline_id,
                                             step_order=ArchiveStepEnum.CHECK_CONFIG.value[0],
                                             step_name=ArchiveStepEnum.CHECK_CONFIG.value[1],
                                             step_desc=ArchiveStepEnum.CHECK_CONFIG.value[2],
                                             step_status=r_status, request_params={"request_params": request_params},
                                             response_result={"msg": result}, opt_user=str(request.user),
                                             opt_time=datetime.datetime.now())

            return Response(data=ApiResult.success_dict(msg=result))


class CheckConfigSync(viewsets.ViewSet):
    interface_name = "check_config_sync"

    def list(self, request):
        logger.info("检查同步配置分支参数{}".format(request.GET))
        pipeline_id = request.GET.get('iteration_id')
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()
        app_name_list = request.GET.getlist('app_name[]')
        # 获取接入宙斯的应用
        join_zeus_app = zeus_ser.get_join_zeus_app(app_name_list, pipeline_id)
        if len(join_zeus_app) == 0:
            logger.info("{}没有接入宙斯".format(app_name_list))
            return Response(data=ApiResult.success_dict(msg="没有接入宙斯"))
        logger.info("接入宙斯的应用为{}".format(join_zeus_app))
        env = request.GET.get('env')

        with HttpTask() as http_task:
            if env == 'prod':
                env = ['prod', 'zb']
                suite_info = env_info_ser.get_suite_name(join_zeus_app, env)
            else:
                suite_info = env_info_ser.get_suite_name(join_zeus_app, [env])
            if suite_info:
                for row in suite_info:
                    logger.info(row)
                    r_status, result = http_task.call_interface(self.interface_name,
                                                                {"iteration_number": br_info.br_name,
                                                                 "app_name": row[1],
                                                                 "tenant_id": row[0],
                                                                 })
                    if r_status == models.InterfaceResults.FAILURE:
                        return Response(data=ApiResult.failed_dict(msg=result["message"]))
                    if result["code"] == "error" or result["block"] == "true":
                        logger.error("调用结果{}".format(result["message"]))
                        return Response(data=ApiResult.failed_dict(msg=result["message"]))

            else:
                return Response(data=ApiResult.failed_dict(msg="{}应用没有绑定{}环境套".format(join_zeus_app, env)))
            return Response(data=ApiResult.success_dict(msg=result["message"]))


class CheckConfigMerge(viewsets.ViewSet):
    interface_name = "check_config_merge"

    def list(self, request):
        logger.info("检查配置回合{}".format(request.GET))
        pipeline_id = request.GET.get('iteration_id')
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()
        app_name_list = request.GET.getlist('app_name[]')
        # 获取接入宙斯的应用
        join_zeus_app = zeus_ser.get_join_zeus_app(app_name_list, pipeline_id)
        if len(join_zeus_app) == 0:
            logger.info("{}没有接入宙斯".format(app_name_list))
            return Response(data=ApiResult.success_dict(msg="没有接入宙斯"))
        logger.info("接入宙斯的应用为{}".format(join_zeus_app))

        with HttpTask() as http_task:
            for app_name in join_zeus_app:
                logger.info(app_name)
                r_status, result = http_task.call_interface(self.interface_name,
                                                            {"iteration_number": br_info.br_name,
                                                             "app_name": app_name
                                                             })
                if r_status == models.InterfaceResults.FAILURE:
                    return Response(data=ApiResult.failed_dict(msg=result["message"]))
                if result["code"] == "error" or result["block"] == "true":
                    logger.error("调用结果{}".format(result["message"]))
                    return Response(data=ApiResult.failed_dict(msg=result["message"]))
            return Response(data=ApiResult.success_dict(msg=result["message"]))


# 获取产线最新版本号
class GetAppProdVersion(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        version = ''
        logger.info("查询产线部署版本参数{}".format(request.GET))

        app_name = request.GET.get('app_name')
        # 获取产线版本的应用
        suite_code = get_app_suite_spec_region_group(app_name, 'prod')
        version = get_node_version(app_name, suite_code)

        return Response(data=ApiResult.success_dict(data=version, msg="success"))


class GetAppProdGroupVersion(viewsets.ViewSet):
    """获取prod组的最后发布分支号（不包括灾备）zt@2024-09-02"""
    authentication_classes = []

    def list(self, request):
        version = ''
        app_name = request.GET.get('app_name')
        # 获取产线版本的应用
        ver_cursor = get_app_prod_ver_by_prod_group(app_name)
        ver_dict_list = GetAppProdGroupVersion.dict_fetchall(ver_cursor)
        if ver_dict_list:
            ver_first = ver_dict_list[0]
            if ver_first:
                version = ver_first.get("br_name")

        return Response(data=ApiResult.success_dict(data=version, msg="success"))

    @staticmethod
    def dict_fetchall(cursor):
        return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


# 获取主干代码地址
class GetAppMasterAddress(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        address = ''
        logger.info("查询应用地址{}".format(request.GET))

        app_name = request.GET.get('app_name')
        # 查询应用主干地址
        app_master = get_app_master_address(app_name)
        logger.info(app_master)
        if app_master:
            for row in app_master:
                logger.info(row)
                address = row["vcs_addr"]
        return Response(data=ApiResult.success_dict(data=address, msg="success"))


# 获取部署平台的应用、git地址和用户的关系
class GetAppGiturlMembers(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        logger.info("查询应用地址{}".format(request.GET))

        # 查询应用、git地址和用户的关系
        app_git_members = get_app_giturl_members()
        logger.info(app_git_members)
        return_data = []
        if app_git_members:
            for row in app_git_members:
                data = {'app_name': row["app_name"],
                        'app_git_path': row["app_git_path"],
                        'members': row["members"].split(",")}
                return_data.append(data)
                logger.info(row["app_name"])
                logger.info(row["app_git_path"])
                logger.info(row["members"].split(","))
        return Response(data=ApiResult.success_dict(data=return_data, msg="success"))


class SyncAppBranchToNacosApi(viewsets.ViewSet):

    def list(self, request):
        sh_cmd = "nohup python3.x ~/be-scripts/be-scripts/job/jenkins/nacos_util/sync_app_info_to_nacos.py " \
                 "howbuyscm-app-30 1.0.0 prod 30 &> ~/logs/spider/sync_branch_to_nacos/sync_nacos.log & "
        with SSHConnectionManager() as ssh:
            tdin, stdout, stderr = ssh.SSH.exec_command(sh_cmd)
            logger.info(stdout.read())
            return_data = stdout.read()
        return Response(data=ApiResult.success_dict(data=return_data, msg="success"))


class InterfaceTimeoutTest(viewsets.ViewSet):
    authentication_classes = []
    throttle_classes = [SpiderAnonRateThrottle, ]

    def list(self, request):
        logger.info(111111111111111)
        import time
        # time.sleep(80)
        return Response(data=ApiResult.success_dict(msg="success"))


class GetAppPublishVersionByNode(viewsets.ViewSet):
    authentication_classes = []
    throttle_classes = [SpiderAnonRateThrottle, ]

    def list(self, request):
        env = request.GET.get('env')
        return_data = get_app_prod_publish_version_by_node(env)
        return Response(data=ApiResult.success_dict(data=return_data, msg="success"))
