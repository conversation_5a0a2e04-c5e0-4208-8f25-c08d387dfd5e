select
            bind.id as bind_id,
            bind.module_name,
            bind.suite_id,
            bind.node_id,
            bind.deploy_path,
            bind.deploy_type,
            suite.suite_code,
            node.node_ip,
            node.tomcat_password
        from env_mgt_node_bind bind
        inner join env_mgt_suite suite on suite.id = bind.suite_id
        left join env_mgt_node node on node.id = bind.node_id and node.node_status = 0
        where bind.module_name = 'devops-view'
          and bind.enable_bind = 1
          and suite.suite_code = 'test'
          and suite.suite_is_active = 1
        group by bind.module_name, bind.suite_id, node.node_ip