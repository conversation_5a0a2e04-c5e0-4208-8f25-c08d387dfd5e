-- =================================
-- ==== 小程序接入（zt@2024-11-01）====
-- =================================

-- ---------------------------------------------
-- h5-external-link切换成小程序_for_华楠。zt@2025-09-02
-- ---------------------------------------------
select * from app_mgt_app_info where app_name in('h5-external-link');
select * from app_mgt_app_module where module_name in('h5-external-link');
select * from app_mgt_app_build where module_name in('h5-external-link');
select * from app_mgt_h5_build where module_name in('h5-external-link');
select * from app_mgt_h5_apply where app_name in('h5-external-link');
select * from publish_deploy_info where module_name in('h5-external-link');

select * from iter_mgt_iter_info where pipeline_id in ('h5_online-deploy');
select * from iter_mgt_iter_app_info where pipeline_id in ('h5_online-deploy');

insert into app_mgt_app_build(id ,app_id, module_name, module_code, module_version, package_type, package_name, package_full, build_jdk_version, create_user, create_time, update_user, update_time, stamp,
                              need_mock, mock_build_cmd, build_cmd, install_cmd)
values(3311, 1006, 'otc-dep-static-h5', NULL, NULL, 'remote', NULL, 1, NULL, 'huaitian.zhang', '2025-07-07 10:11:12', 'huaitian.zhang', '2025-07-07 10:11:12', 0,
       NULL, NULL, 'yarn build', 'yarn install');
-- 1、更新包类型：
update app_mgt_app_build set package_type = 'mini-program' where id = 2138 and app_id = 823 and module_name = 'h5-external-link' and package_type = 'static';
update app_mgt_app_build set build_cmd = 'yarn build:weapp', install_cmd = 'yarn install' where id = 2138 and app_id = 823 and module_name = 'h5-external-link' and package_type = 'mini-program';
-- 2、节点绑定：
select * from env_mgt_node_bind where module_name = 'h5-external-link';
-- 3、在途模板更新：
-- http://jkp-s1.howbuy.pa/jenkins/blue/organizations/jenkins/h5_online-deploy_h5-external-link/detail/h5_online-deploy_h5-external-link/7/
-- 4、迭代应用表：
update iter_mgt_iter_app_info set pom_path = 'mini-program' where id = 133497 and pipeline_id = 'h5_online-deploy' and appName = 'h5-external-link' and pom_path = 'static';
update iter_mgt_iter_app_info set pom_path = '', package_type = 'mini-program' where id = 133497 and pipeline_id = 'h5_online-deploy' and appName = 'h5-external-link';
-- 5、切换节点绑定的环境：
select bind.id as bind_id, bind.module_name, bind.node_bind_desc,
       suite.id as suite_id, suite.suite_code,
       node.id as node_id, node.node_ip
from env_mgt_node_bind bind
inner join env_mgt_suite suite on suite.id = bind.suite_id
inner join env_mgt_node node on node.id = bind.node_id
where bind.module_name = 'h5-external-link'
;
select * from env_mgt_suite where suite_code in('bs-prod', 'pd-prod'); -- 78 --> 121
-- upd:
update env_mgt_node_bind set suite_id = 121,
                             node_bind_desc = '从remote切换为小程序_for_华楠。zt@2025-09-08',
                             update_user = 'huaitian.zhang', update_time = '2025-09-08 10:11:12'
                         where id = 21277 and module_name = 'h5-external-link' and suite_id = 78 and node_id = 2699;

select iter_i.pipeline_id, iter_i.br_status,
       iter_a.appName, iter_a.sys_status
from iter_mgt_iter_info iter_i
inner join iter_mgt_iter_app_info iter_a on iter_a.pipeline_id = iter_i.pipeline_id
inner join app_mgt_app_build app_b on iter_a.appName = app_b.module_name
where app_b.package_type = 'mini-program'
order by iter_i.id desc;

SELECT m.module_name,
       p.platform_name,
       p.platform_code,
       b.package_type,
       i.app_name, i.git_url, i.git_path,
       ti.team_alias
FROM h5_dist_platform p
INNER JOIN h5_dist_bind bind ON bind.platform_code = p.platform_code
INNER JOIN app_mgt_app_module m ON m.module_name = bind.module_name
INNER JOIN app_mgt_app_build b ON b.module_name = m.module_name AND b.app_id = m.app_id
INNER JOIN app_mgt_app_info i ON i.id = m.app_id
LEFT JOIN team_mgt_app_bind t ON t.app_id = i.id
LEFT JOIN tool_mgt_team_info ti ON ti.id = t.team_id
WHERE 1=1 and m.module_name = 'h5-external-link'
ORDER BY bind.platform_bind_order, bind.module_bind_order