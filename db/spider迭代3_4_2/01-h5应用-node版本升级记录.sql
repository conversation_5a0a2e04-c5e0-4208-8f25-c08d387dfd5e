-- 已升级版本：
/*
howbuy-qa-management  16.14.0->18.20.8 鲍超（已完成）
athena-web  16.14.0->18.20.8 詹杨（已完成）
vue-newpig  16.14.0->20.19.4 涂冰冰（已验证）
nv-vendor   16.14.0->20.19.4 涂冰冰（已验证）
nf-newpig   16.14.0->20.19.4 涂冰冰（已验证）
nf-fund     16.14.0->20.19.4 涂冰冰（已验证）
app-bop         16.14.0->18.20.8 李荘莊（已完成）
uniapp-overseas     16.14.0 -->20.19.4 郭翔宇（已完成）
dtms-overseas-trade 16.14.0 -->20.19.4 郭翔宇（已完成）
h5-abroad-ssr   16.14.0->18.20.8 曹华楠（已完成）
hk-fin-ui       16.14.0->20.19.4 韦晓威（已完成）
otc-mock-static 16.14.0->18.20.8 孙丹（已上线）
 */
-- 孙丹 --> otc-static_20250916_insight-trade
select * from app_mgt_h5_build where module_name in ('insight-trade', 'insight-base', 'insight-manage');
select module_name, node_version from app_mgt_h5_build where module_name like 'partner-%';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 83 AND module_name = 'insight-trade' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 80 AND module_name = 'insight-base' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 84 AND module_name = 'insight-manage' AND node_version = '16.14.0';


-- 饶君 --> h5_feature-v250801_partner_iteration_18_partner-admin
select * from app_mgt_h5_build where module_name in ('partner-admin');
select module_name, node_version from app_mgt_h5_build where module_name like 'partner-%';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 47 AND module_name = 'partner-admin' AND node_version = '16.14.0';

-- 鲍超
select * from app_mgt_h5_build where module_name in ('howbuy-qa-management');
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 52 AND module_name = 'howbuy-qa-management' AND node_version = '16.14.0';

-- 詹杨 --> w-4.28.1
select * from app_mgt_h5_build where module_name in ('athena-web');
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 148 AND module_name = 'athena-web' AND node_version = '16.14.0';


-- 李荘莊 --> otc-static_7.14.2
select * from app_mgt_h5_build where module_name in ('app-bop');
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 119 AND module_name = 'app-bop' AND node_version = '16.14.0';

-- 涂冰冰 --> h5_node-version-upgrade
select * from app_mgt_h5_build where module_name in ('vue-newpig', 'nv-vendor', 'nf-newpig', 'nf-fund');

UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 16 AND module_name = 'nf-fund' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 17 AND module_name = 'nf-fund' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 22 AND module_name = 'nf-newpig' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 23 AND module_name = 'nv-vendor' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 112 AND module_name = 'vue-newpig' AND node_version = '16.14.0';
-- 测试完还原：
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 16 AND module_name = 'nf-fund' AND node_version = '20.19.4';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 17 AND module_name = 'nf-fund' AND node_version = '20.19.4';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 22 AND module_name = 'nf-newpig' AND node_version = '20.19.4';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 23 AND module_name = 'nv-vendor' AND node_version = '20.19.4';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 112 AND module_name = 'vue-newpig' AND node_version = '20.19.4';



-- 郭翔宇 --> bugfix_20250826
select * from app_mgt_h5_build where module_name in ('uniapp-overseas', 'dtms-overseas-trade');
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 92 AND module_name = 'uniapp-overseas' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 124 AND module_name = 'dtms-overseas-trade' AND node_version = '16.14.0';


-- 曹华楠 --> CRM-h5_patest20250815-ssr
select * from app_mgt_h5_build where module_name = 'h5-abroad-ssr';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 128 AND module_name = 'h5-abroad-ssr' AND node_version = '16.14.0';

-- 韦晓威 --> hk-fin_2.0.1
select * from app_mgt_h5_build where module_name = 'hk-fin-ui';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 144 AND module_name = 'hk-fin-ui' AND node_version = '16.14.0';
UPDATE app_mgt_h5_build SET node_version = '20.19.4' WHERE id = 144 AND module_name = 'hk-fin-ui' AND node_version = '18.20.8';

-- 孙丹：
select DISTINCT node_version from app_mgt_h5_build; -- 18.20.8
select * from app_mgt_h5_build where module_name = 'otc-mock-static';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 146 AND module_name = 'otc-mock-static' AND node_version = '16.14.0';
select * from app_mgt_h5_build where module_name = 'fpc-manage-web';
UPDATE app_mgt_h5_build SET node_version = '18.20.8' WHERE id = 24 AND module_name = 'fpc-manage-web' AND node_version = '14.15.5';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 24 AND module_name = 'fpc-manage-web' AND node_version = '18.20.8';

-- 确定使用的「14.15.5」
select * from app_mgt_h5_build where module_name in ('nf-partner-tools', 'nf-partner-news', 'nf-partner-fund');
select * from iter_mgt_iter_app_info where pipeline_id = 'h5_h5_teration_250601_sAr_feadback';

UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 78 AND module_name = 'nf-partner-tools' AND node_version = '14.15.5';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 79 AND module_name = 'nf-partner-fund' AND node_version = '14.15.5';
UPDATE app_mgt_h5_build SET node_version = '16.14.0' WHERE id = 93 AND module_name = 'nf-partner-news' AND node_version = '14.15.5';