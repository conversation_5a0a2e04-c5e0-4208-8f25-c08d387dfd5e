# ES备份管理模块 - API接口文档

## 概述

本文档提供ES备份管理模块所有API接口的详细说明，包括接口地址、请求参数、响应格式等。

## 基础配置

- **基础URL:** `http://localhost:8000/api/es-mgt`
- **Content-Type:** `application/json`
- **认证方式:** Bearer <PERSON>（如果需要）

## 接口示例

### 1. 创建ES备份

**接口地址:** `POST /api/es-mgt/backups`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| suite_code | string | 是 | 环境编码 |
| source_es_module_name | string | 是 | ES模块名称 |
| creator | string | 是 | 创建人 |
| biz_code | string | 否 | 业务编码 |
| branch | string | 否 | 分支名称 |

#### 请求报文
```json
{
    "suite_code": "it29",
    "source_es_module_name": "Elasticsearch7",
    "creator": "user.name",
    "biz_code": "my-project",
    "branch": "feature-branch-01"
}
```

#### 成功响应报文 (202 Accepted)
```json
{
    "code": 200,
    "message": "ES backup process initiated successfully.",
    "data": {
        "es_dump_name": "http://es-host:9200_it29-2025091006"
    }
}
```

#### 错误响应报文 (400 Bad Request)
```json
{
    "code": 400,
    "message": "参数验证失败",
    "data": {
        "errors": {
            "suite_code": ["此字段为必填项"]
        }
    }
}
```

### 2. 查询环境的备份列表

**接口地址:** `GET /api/es-mgt/backups`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| suite_code | string | 是 | 环境编码 |

#### 请求URL
```
GET /api/es-mgt/backups?suite_code=it29
```

#### 成功响应报文 (200 OK)
```json
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "suite_code": "it29",
        "backups": [
            "http://es-host:9200_it29-20231027103000",
            "http://es-host:9200_it29-20231026150000"
        ]
    }
}
```

### 3. 验证/更新备份状态

**接口地址:** `POST /api/es-mgt/backups/status`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| es_dump_name | string | 是 | ES备份名称 |

#### 请求报文
```json
{
    "es_dump_name": "http://es-host:9200_it29-20231027103000"
}
```

#### 成功响应报文 (200 OK)
```json
{
    "code": 200,
    "message": "状态检查成功",
    "data": {
        "es_dump_name": "http://es-host:9200_it29-20231027103000",
        "current_status": "SUCCESS"
    }
}
```

### 4. 绑定业务与备份

**接口地址:** `POST /api/es-mgt/bindings`

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| biz_code | string | 是 | 业务编码 |
| branch | string | 是 | 分支名称 |
| es_dump_name | string | 是 | ES备份名称 |
| operator | string | 是 | 操作人 |

#### 请求报文
```json
{
    "biz_code": "my-project",
    "branch": "feature-branch-01",
    "es_dump_name": "http://es-host:9200_it29-20231027103000",
    "operator": "user.name"
}
```

#### 成功响应报文 (201 Created)
```json
{
    "code": 200,
    "message": "Binding created successfully.",
    "data": null
}
```

### 5. 获取环境与模块关联关系列表

**接口地址:** `GET /api/es-mgt/env-modules`

#### 请求参数

无需请求参数

#### 请求URL
```
GET /api/es-mgt/env-modules
```

#### 成功响应报文 (200 OK)
```json
{
    "code": 200,
    "message": "查询成功",
    "data": [
        {
            "node_docker": "es-cluster-01",
            "module_name": "Elasticsearch"
        },
        {
            "node_docker": "es7-cluster-01",
            "module_name": "Elasticsearch7"
        },
        {
            "node_docker": "es-cluster-02",
            "module_name": "Elasticsearch"
        }
    ]
}
```

## 错误码说明

| HTTP状态码 | 说明 |
|------------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 202 | 请求已接受，正在处理 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 通用错误响应格式

```json
{
    "code": 400,
    "message": "错误描述",
    "data": {
        "errors": {
            "field_name": ["具体错误信息"]
        }
    }
}
```

## 总结

本文档提供了ES备份管理模块的完整API接口说明，包括：

1. **接口地址和请求方法**
2. **请求参数说明**
3. **响应报文格式**
4. **错误码说明**

所有接口都遵循RESTful设计规范，返回标准的JSON格式数据。