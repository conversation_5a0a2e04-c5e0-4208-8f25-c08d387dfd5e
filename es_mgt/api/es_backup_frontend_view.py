"""
ES备份前端接口API视图层
实现es_backup_api_frontend.md中定义的四个接口
"""
import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response

from spider.settings import NewApiResult
from es_mgt.service.es_backup_frontend_service import ESBackupFrontendService

logger = logging.getLogger(__name__)


class ESBackupFrontendView(viewsets.ViewSet):
    """ES备份前端接口API视图"""
    
    @action(methods=['get'], detail=False, url_path='dump-info/list')
    def get_dump_info_list(self, request):
        """
        ES备份列表分页查询
        
        GET /spider/es_mgt/frontend/dump-info/list/
        
        Query Parameters:
            es_dump_name (str, optional): ES备份名称，支持模糊查询
            remark (str, optional): 备注，支持模糊查询
            source_es_module_name (str, optional): 备份源ES模块名
            page (int, optional): 页码，默认1
            page_size (int, optional): 每页大小，默认20
        """
        try:
            # 获取查询参数
            es_dump_name = request.query_params.get('es_dump_name')
            remark = request.query_params.get('remark')
            source_es_module_name = request.query_params.get('source_es_module_name')
            
            # 获取分页参数
            try:
                page = int(request.query_params.get('page', 1))
                page_size = int(request.query_params.get('page_size', 20))
            except ValueError:
                return Response(
                    data=NewApiResult.error_dict(message="分页参数格式错误"),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 调用服务层获取数据
            result = ESBackupFrontendService.get_dump_info_list(
                es_dump_name=es_dump_name,
                remark=remark,
                source_es_module_name=source_es_module_name,
                page=page,
                page_size=page_size
            )
            
            return Response(
                data=NewApiResult.success_dict(
                    data=result,
                    message="查询成功"
                )
            )
            
        except Exception as e:
            logger.error(f"查询ES备份信息列表失败: {str(e)}")
            return Response(
                data=NewApiResult.error_dict(message="系统内部错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(methods=['get'], detail=False, url_path='backup-details/list')
    def get_backup_details_list(self, request):
        """
        备份详情列表分页查询
        
        GET /spider/es_mgt/frontend/backup-details/list/
        
        Query Parameters:
            es_dump_name (str, required): ES备份名称
            page (int, optional): 页码，默认1
            page_size (int, optional): 每页大小，默认20
        """
        try:
            # 获取必需参数
            es_dump_name = request.query_params.get('es_dump_name')
            if not es_dump_name:
                return Response(
                    data=NewApiResult.error_dict(message="es_dump_name参数不能为空"),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 获取分页参数
            try:
                page = int(request.query_params.get('page', 1))
                page_size = int(request.query_params.get('page_size', 20))
            except ValueError:
                return Response(
                    data=NewApiResult.error_dict(message="分页参数格式错误"),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 调用服务层获取数据
            result = ESBackupFrontendService.get_backup_details_list(
                es_dump_name=es_dump_name,
                page=page,
                page_size=page_size
            )
            
            return Response(
                data=NewApiResult.success_dict(
                    data=result,
                    message="查询成功"
                )
            )
            
        except ValueError as e:
            return Response(
                data=NewApiResult.error_dict(message=str(e)),
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"查询备份详情列表失败: {str(e)}")
            return Response(
                data=NewApiResult.error_dict(message="系统内部错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(methods=['post'], detail=False, url_path='backups/status')
    def update_backup_status(self, request):
        """
        验证/更新ES备份状态
        
        POST /spider/es_mgt/frontend/backups/status/
        
        Request Body:
        {
            "es_dump_name": "prod_backup_user_20240730",
            "status": "VERIFIED"
        }
        """
        try:
            # 获取请求参数
            es_dump_name = request.data.get('es_dump_name')
            new_status = request.data.get('status')
            
            if not es_dump_name:
                return Response(
                    data=NewApiResult.error_dict(message="es_dump_name参数不能为空"),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            if not new_status:
                return Response(
                    data=NewApiResult.error_dict(message="status参数不能为空"),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 调用服务层更新状态
            result = ESBackupFrontendService.update_backup_status(
                es_dump_name=es_dump_name,
                new_status=new_status
            )
            
            return Response(
                data=NewApiResult.success_dict(
                    data=result,
                    message="状态更新成功"
                )
            )
            
        except ValueError as e:
            return Response(
                data=NewApiResult.error_dict(message=str(e)),
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"更新备份状态失败: {str(e)}")
            return Response(
                data=NewApiResult.error_dict(message="系统内部错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(methods=['get'], detail=False, url_path='business-info/list')
    def get_business_info_list(self, request):
        """
        关联业务信息查询
        
        GET /spider/es_mgt/frontend/business-info/list/
        
        Query Parameters:
            es_dump_name (str, required): ES备份名称
        """
        try:
            # 获取查询参数
            es_dump_name = request.query_params.get('es_dump_name')
            if not es_dump_name:
                return Response(
                    data=NewApiResult.error_dict(message="es_dump_name参数不能为空"),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 调用服务层获取数据
            result = ESBackupFrontendService.get_business_info_list(
                es_dump_name=es_dump_name
            )
            
            if not result:
                return Response(
                    data=NewApiResult.error_dict(message="未找到与该备份关联的业务信息"),
                    status=status.HTTP_404_NOT_FOUND
                )
            
            return Response(
                data=NewApiResult.success_dict(
                    data=result,
                    message="查询成功"
                )
            )
            
        except ValueError as e:
            return Response(
                data=NewApiResult.error_dict(message=str(e)),
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"查询业务信息列表失败: {str(e)}")
            return Response(
                data=NewApiResult.error_dict(message="系统内部错误"),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )