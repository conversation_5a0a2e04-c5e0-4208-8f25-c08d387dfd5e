import logging
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction

from spider.settings import NewApiResult
from es_mgt.service.es_backup_service import ESBackupService

logger = logging.getLogger(__name__)


class ESBackupView(viewsets.ViewSet):
    """ES备份管理API视图"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.backup_service = ESBackupService()
    
    @action(methods=['post'], detail=False, url_path='create')
    def create_backup(self, request):
        """
        创建ES备份
        
        POST /api/es-mgt/backups/create
        
        Request Body:
        {
            "suite_code": "it29",
            "source_es_module_name": "Elasticsearch7",
            "indices": "test",
            "creator": "user.name",
            "remark": "备份说明",
            "biz_code": "optional_biz_code",  // 可选
            "biz_test_iter_br": "optional_branch_name"   // 可选
        }
        """
        try:
            # 参数验证
            suite_code = request.data.get('suite_code')
            source_es_module_name = request.data.get('source_es_module_name')
            indices = request.data.get('indices', '*,-.*')
            creator = request.data.get('creator')
            remark = request.data.get('remark')
            biz_code = request.data.get('biz_code')
            biz_test_iter_br = request.data.get('branch')
            
            # 必填参数检查
            if not all([suite_code, source_es_module_name, creator, remark]):
                return Response(
                    data=NewApiResult.failed_dict(
                        message='参数错误: suite_code, source_es_module_name, creator, remark 不能为空'
                    ),
                    status=status.HTTP_200_OK
                )
            
            # 如果提供了业务信息，两个参数都必须提供
            if (biz_code and not biz_test_iter_br) or (biz_test_iter_br and not biz_code):
                return Response(
                    data=NewApiResult.failed_dict(
                        message='参数错误: biz_code 和 biz_test_iter_br 必须同时提供或同时为空'
                    ),
                    status=status.HTTP_200_OK
                )
            
            # 如果提供了业务信息，检查唯一键冲突：biz_test_iter_id + source_es_module_name
            if biz_code and biz_test_iter_br:
                biz_test_iter_id = f"{biz_code}_{biz_test_iter_br}"
                from es_mgt.model.models import EsMgtDumpBizBind
                if EsMgtDumpBizBind.objects.filter(
                    biz_test_iter_id=biz_test_iter_id,
                    source_es_module_name=source_es_module_name
                ).exists():
                    return Response(
                        data=NewApiResult.failed_dict(
                            message=f'唯一键冲突: 测试迭代ID "{biz_test_iter_id}" 与模块 "{source_es_module_name}" 的组合已存在'
                        ),
                        status=status.HTTP_200_OK
                    )
            
            # 调用服务层创建备份
            success, message, es_dump_name = self.backup_service.create_backup(
                suite_code=suite_code,
                source_es_module_name=source_es_module_name,
                indices=indices,
                creator=creator,
                remark=remark,
                biz_code=biz_code,
                biz_test_iter_br=biz_test_iter_br
            )
            
            if success:
                return Response(
                    data=NewApiResult.success_dict(
                        message=message,
                        data={'es_dump_name': es_dump_name}
                    ),
                    status=status.HTTP_202_ACCEPTED
                )
            else:
                return Response(
                    data=NewApiResult.failed_dict(message=message),
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
        except Exception as e:
            logger.error(f"创建ES备份失败: {str(e)}")
            return Response(
                data=NewApiResult.failed_dict(message=f'创建备份时发生错误: {str(e)}'),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(methods=['get'], detail=False, url_path='list')
    def get_backups(self, request):
        """
        查询环境的备份列表
        
        GET /api/es-mgt/backups/list?suite_code=it29
        """
        try:
            suite_code = request.GET.get('suite_code')
            
            if not suite_code:
                return Response(
                    data=NewApiResult.failed_dict(message='参数错误: suite_code 不能为空'),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 调用服务层查询备份列表
            backups = self.backup_service.get_backups_by_suite_code(suite_code)
            
            result_data = {
                'suite_code': suite_code,
                'backups': backups
            }
            
            return Response(
                data=NewApiResult.success_dict(
                    message='查询成功',
                    data=result_data
                ),
                status=status.HTTP_200_OK
            )
            
        except Exception as e:
            logger.error(f"查询备份列表失败: {str(e)}")
            return Response(
                data=NewApiResult.failed_dict(message=f'查询备份列表时发生错误: {str(e)}'),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(methods=['post'], detail=False, url_path='status')
    def update_backup_status(self, request):
        """
        验证/更新备份状态
        
        POST /api/es-mgt/backups/status
        
        Request Body:
        {
            "es_dump_name": "http://es-host:9200_it29-20231027103000"
        }
        """
        try:
            es_dump_name = request.data.get('es_dump_name')
            
            if not es_dump_name:
                return Response(
                    data=NewApiResult.failed_dict(message='参数错误: es_dump_name 不能为空'),
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            # 调用服务层更新状态
            success, message, current_status = self.backup_service.update_backup_status(es_dump_name)
            
            result_data = {
                'es_dump_name': es_dump_name,
                'current_status': current_status
            }
            
            if success:
                return Response(
                    data=NewApiResult.success_dict(
                        message=message,
                        data=result_data
                    ),
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    data=NewApiResult.failed_dict(
                        message=message,
                        data=result_data
                    ),
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
                
        except Exception as e:
            logger.error(f"更新备份状态失败: {str(e)}")
            return Response(
                data=NewApiResult.failed_dict(message=f'更新备份状态时发生错误: {str(e)}'),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(methods=['post'], detail=False, url_path='bind')
    def create_biz_binding(self, request):
        """
        绑定业务与备份
        
        POST /api/es-mgt/backups/bind
        
        Request Body:
        {
            "biz_code": "my-project",
            "biz_test_iter_br": "feature-branch-01",
            "es_dump_name": "http://es-host:9200_it29-20231027103000",
            "operator": "user.name",
            "source_es_module_name": "Elasticsearch7"  // 可选，如果不提供则从备份记录中获取
        }
        """
        try:
            # 参数验证
            biz_code = request.data.get('biz_code')
            biz_test_iter_br = request.data.get('branch')
            es_dump_name = request.data.get('es_dump_name')
            operator = request.data.get('operator')
            source_es_module_name = request.data.get('source_es_module_name')
            
            # 必填参数检查
            if not all([biz_code, biz_test_iter_br, es_dump_name, operator]):
                return Response(
                    data=NewApiResult.failed_dict(
                        message='参数错误: biz_code, biz_test_iter_br, es_dump_name, operator 不能为空'
                    ),
                    status=status.HTTP_200_OK
                )
            
            # 如果没有提供source_es_module_name，从备份记录中获取
            if not source_es_module_name:
                from es_mgt.model.models import EsMgtDumpInfo
                try:
                    dump_info = EsMgtDumpInfo.objects.get(es_dump_name=es_dump_name)
                    source_es_module_name = dump_info.source_es_module_name
                except EsMgtDumpInfo.DoesNotExist:
                    return Response(
                        data=NewApiResult.failed_dict(message='指定的备份不存在'),
                        status=status.HTTP_200_OK
                    )
            
            # 检查唯一键冲突：biz_test_iter_id + source_es_module_name
            biz_test_iter_id = f"{biz_code}_{biz_test_iter_br}"
            from es_mgt.model.models import EsMgtDumpBizBind
            if EsMgtDumpBizBind.objects.filter(
                biz_test_iter_id=biz_test_iter_id,
                source_es_module_name=source_es_module_name
            ).exists():
                return Response(
                    data=NewApiResult.failed_dict(
                        message=f'唯一键冲突: 测试迭代ID "{biz_test_iter_id}" 与模块 "{source_es_module_name}" 的组合已存在'
                    ),
                    status=status.HTTP_200_OK
                )
            
            # 调用服务层创建绑定关系
            success, message = self.backup_service.create_biz_binding(
                biz_code=biz_code,
                biz_test_iter_br=biz_test_iter_br,
                es_dump_name=es_dump_name,
                operator=operator,
                source_es_module_name=source_es_module_name
            )
            
            if success:
                return Response(
                    data=NewApiResult.success_dict(message=message),
                    status=status.HTTP_201_CREATED
                )
            else:
                return Response(
                    data=NewApiResult.failed_dict(message=message),
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"创建业务绑定失败: {str(e)}")
            return Response(
                data=NewApiResult.failed_dict(message=f'创建绑定时发生错误: {str(e)}'),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(methods=['get'], detail=False, url_path='suite-codes')
    def get_suite_codes(self, request):
        """
        获取去重的 suite_code 列表
        
        GET /api/es-mgt/backups/suite-codes
        
        Response:
        {
            "code": 200,
            "message": "获取成功",
            "data": ["it29", "prod", "test"]
        }
        """
        try:
            # 调用服务层获取去重的 suite_code 列表
            success, message, suite_codes = self.backup_service.get_distinct_suite_codes()
            
            if success:
                return Response(
                    data=NewApiResult.success_dict(data=suite_codes, message=message),
                    status=status.HTTP_200_OK
                )
            else:
                return Response(
                    data=NewApiResult.failed_dict(message=message),
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except Exception as e:
            logger.error(f"获取 suite_code 列表失败: {str(e)}")
            return Response(
                data=NewApiResult.failed_dict(message=f'获取 suite_code 列表时发生错误: {str(e)}'),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )