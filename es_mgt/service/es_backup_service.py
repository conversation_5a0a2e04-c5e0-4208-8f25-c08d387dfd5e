import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from django.db import transaction, connection
from django.conf import settings

from es_mgt.model.models import EsMgtDumpInfo, EsMgtDumpBizBind

logger = logging.getLogger(__name__)


class ESBackupService:
    """ES备份服务类"""
    
    def __init__(self):
        self.repository_name = 'my_s3_backup'
        self.s3_config = {
            "type": "s3",
            "settings": {
                "bucket": "es-backup-bucket",
                "region": "us-east-1",
                "base_path": "snapshots"
            }
        }
    
    def get_es_host_by_suite_code(self, suite_code: str, source_es_module_name: str) -> Optional[str]:
        """
        根据suite_code和source_es_module_name获取ES节点地址
        
        Args:
            suite_code: 环境唯一编码
            source_es_module_name: 备份源ES模块名
            
        Returns:
            ES主机地址，格式为 http://host:port
        """
        sql = """
        SELECT em.node_docker_service_hosts 
        FROM env_mgt_node_bind t 
        JOIN env_mgt_node_bind_dynamic em ON t.id = em.bind_id 
        WHERE t.module_name IN ('Elasticsearch', 'Elasticsearch7') 
          AND t.node_docker = %s 
          AND t.module_name = %s
        """
        
        try:
            with connection.cursor() as cursor:
                cursor.execute(sql, [suite_code, source_es_module_name])
                result = cursor.fetchone()
                if result:
                    host = result[0]
                    # 确保返回完整的HTTP URL
                    #if not host.startswith('http'):
                    #    host = f'http://{host}'
                    return host
                return None
        except Exception as e:
            logger.error(f"查询ES主机地址失败: {str(e)}")
            return None
    
    def verify_or_create_repository(self, es_host: str) -> Tuple[bool, str]:
        """
        验证或创建ES快照仓库
        
        Args:
            es_host: ES主机地址
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            # 验证仓库是否存在
            verify_url = f"http://{es_host}/_snapshot/{self.repository_name}/_verify"
            response = requests.post(verify_url, timeout=30)
            
            if response.status_code == 200:
                return True, "仓库验证成功"
            elif response.status_code == 404:
                # 仓库不存在，创建仓库
                create_url = f"{es_host}/_snapshot/{self.repository_name}"
                create_response = requests.put(
                    create_url, 
                    json=self.s3_config,
                    headers={'Content-Type': 'application/json'},
                    timeout=30
                )
                
                if create_response.status_code in [200, 201]:
                    # 再次验证
                    verify_response = requests.post(verify_url, timeout=30)
                    if verify_response.status_code == 200:
                        return True, "仓库创建并验证成功"
                    else:
                        return False, f"仓库创建成功但验证失败: {verify_response.text}"
                else:
                    return False, f"创建仓库失败: {create_response.text}"
            else:
                return False, f"验证仓库失败: {response.text}"
                
        except requests.RequestException as e:
            return False, f"网络请求失败: {str(e)}"
        except Exception as e:
            return False, f"验证或创建仓库时发生错误: {str(e)}"
    
    def create_snapshot(self, es_host: str, snapshot_name: str, indices: str = "*") -> Tuple[bool, str]:
        """
        创建ES快照
        
        Args:
            es_host: ES主机地址
            snapshot_name: 快照名称
            indices: 要备份的索引，默认为所有索引
            
        Returns:
            (是否成功, 错误信息)
        """
        try:
            snapshot_url = f"http://{es_host}/_snapshot/{self.repository_name}/{snapshot_name}?wait_for_completion=false"
            snapshot_config = {
                "indices": indices,
                "ignore_unavailable": True,
                "include_global_state": False
            }
            
            response = requests.put(
                snapshot_url,
                json=snapshot_config,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code in [200, 202]:
                return True, "快照创建请求已提交"
            else:
                return False, f"创建快照失败: {response.text}"
                
        except requests.RequestException as e:
            return False, f"网络请求失败: {str(e)}"
        except Exception as e:
            return False, f"创建快照时发生错误: {str(e)}"
    
    def get_snapshot_status(self, es_host: str, snapshot_name: str) -> Tuple[bool, str, Dict]:
        """
        获取快照状态
        
        Args:
            es_host: ES主机地址
            snapshot_name: 快照名称
            
        Returns:
            (是否成功, 错误信息, 状态数据)
        """
        try:
            status_url = f"http://{es_host}/_snapshot/{self.repository_name}/{snapshot_name}/_status"
            response = requests.get(status_url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                return True, "获取状态成功", data
            else:
                return False, f"获取快照状态失败: {response.text}", {}
                
        except requests.RequestException as e:
            return False, f"网络请求失败: {str(e)}", {}
        except Exception as e:
            return False, f"获取快照状态时发生错误: {str(e)}", {}
    
    def count_user_indices_from_snapshot(self, snapshot_data: Dict) -> int:
        """
        从快照状态数据中统计用户索引数量
        排除包含history和以.开头的索引
        
        Args:
            snapshot_data: 快照状态数据
            
        Returns:
            用户索引数量
        """
        try:
            if 'snapshots' not in snapshot_data or len(snapshot_data['snapshots']) == 0:
                return 0
            
            snapshot_info = snapshot_data['snapshots'][0]
            indices = snapshot_info.get('indices', {})
            
            user_indices_count = 0
            for index_name in indices.keys():
                # 排除以.开头的系统索引和包含history的索引
                if not index_name.startswith('.') and 'history' not in index_name.lower():
                    user_indices_count += 1
            
            logger.info(f"快照中用户索引数量: {user_indices_count}，索引列表: {[idx for idx in indices.keys() if not idx.startswith('.') and 'history' not in idx.lower()]}")
            return user_indices_count
            
        except Exception as e:
            logger.error(f"统计快照中用户索引数量失败: {str(e)}")
            return 0
    
    def get_user_indices_count(self, es_host: str) -> Tuple[bool, int, str]:
        """
        获取ES中用户创建的索引数量（排除系统索引）
        
        Args:
            es_host: ES主机地址
            
        Returns:
            (是否成功, 索引数量, 错误信息)
        """
        try:
            # 调用ES的_cat/indices接口获取所有索引信息
            url = f"http://{es_host}/_cat/indices/*?format=json"
            response = requests.get(url, timeout=30)
            
            #if response.status_code != 200:
            #    return False, 0, f"获取索引信息失败，HTTP状态码: {response.status_code}"
            
            indices_data = response.json()
            
            # 过滤掉系统索引（以.开头的索引）
            user_indices = []
            for index_info in indices_data:
                index_name = index_info.get('index', '')
                if index_name and not index_name.startswith('.'):
                    user_indices.append(index_name)
            
            user_indices_count = len(user_indices)
            logger.info(f"ES主机 {es_host} 用户索引数量: {user_indices_count}，索引列表: {user_indices}")
            
            return True, user_indices_count, ""
            
        except requests.exceptions.RequestException as e:
            error_msg = f"请求ES接口失败: {str(e)}"
            logger.error(error_msg)
            return False, 0, error_msg
        except json.JSONDecodeError as e:
            error_msg = f"解析ES响应JSON失败: {str(e)}"
            logger.error(error_msg)
            return False, 0, error_msg
        except Exception as e:
            error_msg = f"获取用户索引数量时发生未知错误: {str(e)}"
            logger.error(error_msg)
            return False, 0, error_msg
    
    def create_backup(self, suite_code: str, source_es_module_name: str, 
                     indices: str, creator: str, remark: str, biz_code: Optional[str] = None, 
                     biz_test_iter_br: Optional[str] = None) -> Tuple[bool, str, Optional[str]]:
        """
        创建ES备份
        
        Args:
            suite_code: 环境唯一编码
            source_es_module_name: 备份源ES模块名
            indices: 要备份的索引
            creator: 创建人
            remark: 备注说明
            biz_code: 业务编码（可选）
            biz_test_iter_br: 业务测试迭代分支（可选）
            
        Returns:
            (是否成功, 消息, es_dump_name)
        """
        try:
            # 1. 获取ES节点地址
            es_host = self.get_es_host_by_suite_code(suite_code, source_es_module_name)
            if not es_host:
                return False, f"未找到环境 {suite_code} 和模块 {source_es_module_name} 对应的ES节点", None
            
            # 2. 验证/创建快照仓库
            repo_success, repo_msg = self.verify_or_create_repository(es_host)
            if not repo_success:
                return False, f"仓库验证失败: {repo_msg}", None
            
            # 3. 生成快照名称
            timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
            snapshot_name = f"{suite_code}_{timestamp}"
            # 截取 es_host 点前半部分作为前缀
            es_host_prefix = es_host.split('.')[0] if '.' in es_host else es_host
            es_dump_name = f"{es_host_prefix}_backup_{snapshot_name}"
            
            # 4. 创建快照
            snapshot_success, snapshot_msg = self.create_snapshot(es_host, snapshot_name, indices)
            if not snapshot_success:
                return False, f"创建快照失败: {snapshot_msg}", None
            
            # 5. 获取用户索引数量
            indices_success, user_indices_count, indices_error = self.get_user_indices_count(es_host)
            if not indices_success:
                logger.warning(f"获取索引数量失败: {indices_error}，将继续创建备份记录")
                user_indices_count = None
            
            # 6. 数据库操作（事务）
            with transaction.atomic():
                # 创建备份记录
                dump_info = EsMgtDumpInfo.objects.create(
                    suite_code=suite_code,
                    es_dump_name=es_dump_name,
                    source_es_module_name=source_es_module_name,
                    status='RUNNING',
                    repository_name=self.repository_name,
                    creator=creator,
                    remark=remark,
                    index_count=user_indices_count
                )
                
                # 如果提供了业务信息，创建绑定关系
                if biz_code and biz_test_iter_br:
                    biz_test_iter_id = f"{biz_code}_{biz_test_iter_br}"
                    EsMgtDumpBizBind.objects.create(
                        biz_code=biz_code,
                        biz_test_iter_br=biz_test_iter_br,
                        biz_test_iter_id=biz_test_iter_id,
                        source_es_module_name=source_es_module_name,
                        es_dump_name=es_dump_name,
                        operator=creator
                    )
            
            return True, "ES备份流程启动成功", es_dump_name
            
        except Exception as e:
            logger.error(f"创建ES备份失败: {str(e)}")
            return False, f"创建备份时发生错误: {str(e)}", None
    
    def get_backups_by_suite_code(self, suite_code: str) -> List[str]:
        """
        根据环境编码查询备份列表
        
        Args:
            suite_code: 环境唯一编码
            
        Returns:
            备份名称列表
        """
        try:
            backups = EsMgtDumpInfo.objects.filter(
                suite_code=suite_code
            ).values_list('es_dump_name', flat=True).order_by('-create_time')
            
            return list(backups)
            
        except Exception as e:
            logger.error(f"查询备份列表失败: {str(e)}")
            return []
    
    def update_backup_status(self, es_dump_name: str) -> Tuple[bool, str, str]:
        """
        验证并更新备份状态
        
        Args:
            es_dump_name: 备份名称
            
        Returns:
            (是否成功, 消息, 当前状态)
        """
        try:
            # 解析es_dump_name获取快照名称
            # 新格式: {es_host_prefix}_backup_{snapshot_name}
            if '_backup_' not in es_dump_name:
                return False, "备份名称格式错误", "UNKNOWN"
            
            parts = es_dump_name.split('_backup_')
            if len(parts) != 2:
                return False, "备份名称格式错误", "UNKNOWN"
            
            es_host_prefix, snapshot_name = parts
            
            # 获取数据库中的备份记录
            try:
                dump_info = EsMgtDumpInfo.objects.get(es_dump_name=es_dump_name)
            except EsMgtDumpInfo.DoesNotExist:
                return False, "备份记录不存在", "UNKNOWN"
            
            # 从快照名称中提取suite_code来获取完整的ES主机地址
            # snapshot_name格式: {suite_code}-{timestamp}
            if '_' not in snapshot_name:
                return False, "快照名称格式错误", "UNKNOWN"
            
            es_host = self.get_es_host_by_suite_code(dump_info.suite_code, dump_info.source_es_module_name)
            if not es_host:
                return False, f"无法获取环境 {dump_info.suite_code} 的ES主机地址", "UNKNOWN"
            
            # 获取快照状态
            status_success, status_msg, status_data = self.get_snapshot_status(es_host, snapshot_name)
            if not status_success:
                return False, f"获取快照状态失败: {status_msg}", "UNKNOWN"
            
            # 解析状态
            current_status = "UNKNOWN"
            if 'snapshots' in status_data and len(status_data['snapshots']) > 0:
                snapshot_info = status_data['snapshots'][0]
                state = snapshot_info.get('state', 'UNKNOWN')
                
                if state == 'SUCCESS':
                    # 当ES快照状态为SUCCESS时，需要进一步验证索引数量
                    snapshot_indices_count = self.count_user_indices_from_snapshot(status_data)
                    db_indices_count = dump_info.index_count
                    
                    if db_indices_count is not None:
                        if snapshot_indices_count == db_indices_count:
                            current_status = 'SUCCESS'
                            logger.info(f"快照 {snapshot_name} 索引数量匹配: {snapshot_indices_count}")
                        elif snapshot_indices_count < db_indices_count:
                            current_status = 'RUNNING'
                            logger.warning(f"快照 {snapshot_name} 索引数量不足: {snapshot_indices_count} < {db_indices_count}")
                        else:
                            current_status = 'FAILED'
                            logger.error(f"快照 {snapshot_name} 索引数量异常: {snapshot_indices_count} > {db_indices_count}")
                    else:
                        # 如果数据库中没有记录索引数量，直接使用ES的状态
                        current_status = 'SUCCESS'
                        logger.warning(f"快照 {snapshot_name} 数据库中无索引数量记录，直接使用ES状态")
                elif state == 'IN_PROGRESS':
                    current_status = 'RUNNING'
                elif state == 'FAILED':
                    current_status = 'FAILED'
                else:
                    current_status = state
            
            # 更新数据库状态
            if dump_info.status != current_status:
                dump_info.status = current_status
                dump_info.save()
                logger.info(f"备份 {es_dump_name} 状态更新: {dump_info.status} -> {current_status}")
            
            return True, "状态更新成功", current_status
            
        except Exception as e:
            logger.error(f"更新备份状态失败: {str(e)}")
            return False, f"更新状态时发生错误: {str(e)}", "UNKNOWN"
    
    def create_biz_binding(self, biz_code: str, biz_test_iter_br: str, 
                          es_dump_name: str, operator: str, source_es_module_name: str) -> Tuple[bool, str]:
        """
        创建业务与备份的绑定关系
        
        Args:
            biz_code: 业务编码
            biz_test_iter_br: 业务测试迭代分支
            es_dump_name: 备份名称
            operator: 操作人
            source_es_module_name: 备份源ES模块名
            
        Returns:
            (是否成功, 消息)
        """
        try:
            # 检查备份是否存在
            if not EsMgtDumpInfo.objects.filter(es_dump_name=es_dump_name).exists():
                return False, "指定的备份不存在"
            
            # 检查绑定关系是否已存在
            if EsMgtDumpBizBind.objects.filter(
                biz_code=biz_code, 
                biz_test_iter_br=biz_test_iter_br, 
                es_dump_name=es_dump_name
            ).exists():
                return False, "绑定关系已存在"
            
            # 创建绑定关系
            biz_test_iter_id = f"{biz_code}_{biz_test_iter_br}"
            EsMgtDumpBizBind.objects.create(
                biz_code=biz_code,
                biz_test_iter_br=biz_test_iter_br,
                biz_test_iter_id=biz_test_iter_id,
                source_es_module_name=source_es_module_name,
                es_dump_name=es_dump_name,
                operator=operator
            )
            
            return True, "绑定关系创建成功"
            
        except Exception as e:
            logger.error(f"创建绑定时发生错误: {str(e)}")
            return False, f"创建绑定时发生错误: {str(e)}"
    
    def get_distinct_suite_codes(self) -> Tuple[bool, str, List[str]]:
        """
        获取 es_mgt_dump_info 表中去重的 suite_code 列表
        
        Returns:
            (是否成功, 消息, suite_code列表)
        """
        try:
            # 使用 Django ORM 获取去重的 suite_code
            suite_codes = EsMgtDumpInfo.objects.values_list('suite_code', flat=True).distinct().order_by('suite_code')
            
            # 转换为列表并过滤空值
            suite_code_list = [code for code in suite_codes if code]
            
            logger.info(f"成功获取到 {len(suite_code_list)} 个去重的 suite_code")
            return True, "获取成功", suite_code_list
            
        except Exception as e:
            logger.error(f"获取去重 suite_code 失败: {str(e)}")
            return False, f"获取去重 suite_code 时发生错误: {str(e)}", []