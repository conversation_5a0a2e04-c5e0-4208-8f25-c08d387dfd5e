"""
ES备份前端接口Service层
实现es_backup_api_frontend.md中定义的四个接口的业务逻辑处理
"""
from typing import Dict, List, Any, Optional
from es_mgt.dao.es_backup_frontend_dao import ESBackupFrontendDAO
import logging

logger = logging.getLogger(__name__)


class ESBackupFrontendService:
    """ES备份前端接口服务类"""
    
    @staticmethod
    def get_dump_info_list(es_dump_name: str = None, remark: str = None, 
                          source_es_module_name: str = None, page: int = 1, 
                          page_size: int = 20) -> Dict[str, Any]:
        """
        获取ES备份信息列表（分页）
        
        Args:
            es_dump_name: ES备份名称，支持模糊查询
            remark: 备注，支持模糊查询
            source_es_module_name: 备份源ES模块名
            page: 页码，默认1
            page_size: 每页大小，默认20
            
        Returns:
            Dict: 包含分页信息和结果列表的字典
        """
        try:
            # 参数验证
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 20
            
            # 调用DAO层查询数据
            results, total = ESBackupFrontendDAO.get_dump_info_list(
                es_dump_name=es_dump_name,
                remark=remark,
                source_es_module_name=source_es_module_name,
                page=page,
                page_size=page_size
            )
            
            return {
                "total": total,
                "page": page,
                "page_size": page_size,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"获取ES备份信息列表失败: {str(e)}")
            raise
    
    @staticmethod
    def get_backup_details_list(es_dump_name: str, page: int = 1, 
                               page_size: int = 20) -> Dict[str, Any]:
        """
        获取备份详情列表（分页）
        
        Args:
            es_dump_name: ES备份名称
            page: 页码，默认1
            page_size: 每页大小，默认20
            
        Returns:
            Dict: 包含分页信息和结果列表的字典
        """
        try:
            # 参数验证
            if not es_dump_name:
                raise ValueError("es_dump_name参数不能为空")
            
            if page < 1:
                page = 1
            if page_size < 1 or page_size > 100:
                page_size = 20
            
            # 调用DAO层查询数据
            results, total = ESBackupFrontendDAO.get_backup_details_list(
                es_dump_name=es_dump_name,
                page=page,
                page_size=page_size
            )
            
            return {
                "total": total,
                "page": page,
                "page_size": page_size,
                "results": results
            }
            
        except Exception as e:
            logger.error(f"获取备份详情列表失败: {str(e)}")
            raise
    
    @staticmethod
    def update_backup_status(es_dump_name: str, new_status: str) -> Dict[str, Any]:
        """
        更新ES备份状态
        
        Args:
            es_dump_name: ES备份名称
            new_status: 新状态
            
        Returns:
            Dict: 更新结果
        """
        try:
            # 参数验证
            if not es_dump_name:
                raise ValueError("es_dump_name参数不能为空")
            
            if not new_status:
                raise ValueError("status参数不能为空")
            
            # 验证状态值
            valid_statuses = ['PENDING', 'RUNNING', 'SUCCESS', 'FAILED', 'VERIFIED']
            if new_status not in valid_statuses:
                raise ValueError(f"无效的状态值，支持的状态: {', '.join(valid_statuses)}")
            
            # 调用DAO层更新状态
            success = ESBackupFrontendDAO.update_backup_status(
                es_dump_name=es_dump_name,
                new_status=new_status
            )
            
            if not success:
                raise ValueError("ES备份不存在或更新失败")
            
            return {
                "es_dump_name": es_dump_name,
                "new_status": new_status
            }
            
        except Exception as e:
            logger.error(f"更新备份状态失败: {str(e)}")
            raise
    
    @staticmethod
    def get_business_info_list(es_dump_name: str) -> List[Dict[str, Any]]:
        """
        获取关联业务信息列表
        
        Args:
            es_dump_name: ES备份名称
            
        Returns:
            List[Dict]: 业务信息列表
        """
        try:
            # 参数验证
            if not es_dump_name:
                raise ValueError("es_dump_name参数不能为空")
            
            # 调用DAO层查询数据
            results = ESBackupFrontendDAO.get_business_info_list(
                es_dump_name=es_dump_name
            )
            
            return results
            
        except Exception as e:
            logger.error(f"获取业务信息列表失败: {str(e)}")
            raise