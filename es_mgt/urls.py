from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from es_mgt.api.es_mgt_view import EsMgtView
from es_mgt.api.es_backup_view import ESBackupView
from es_mgt.api.es_backup_frontend_view import ESBackupFrontendView

router = DefaultRouter()

# ES管理相关API
router.register(r'es_mgt_api', EsMgtView, basename="es_mgt_api")

# ES备份管理API
router.register(r'backups', ESBackupView, basename="es_backups")

# ES备份前端接口API
router.register(r'frontend', ESBackupFrontendView, basename="es_backup_frontend")

urlpatterns = [
    path("", include(router.urls))
]