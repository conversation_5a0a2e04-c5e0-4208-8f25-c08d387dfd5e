"""
ES备份前端接口DAO层
实现es_backup_api_frontend.md中定义的四个接口的数据库查询方法
"""
from django.db import connection
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class ESBackupFrontendDAO:
    """ES备份前端接口数据访问对象"""
    
    @staticmethod
    def get_dump_info_list(es_dump_name: str = None, remark: str = None, 
                          source_es_module_name: str = None, page: int = 1, 
                          page_size: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """
        分页查询ES备份信息列表
        
        Args:
            es_dump_name: ES备份名称，支持模糊查询
            remark: 备注，支持模糊查询
            source_es_module_name: 备份源ES模块名
            page: 页码
            page_size: 每页大小
            
        Returns:
            Tuple[List[Dict], int]: (查询结果列表, 总记录数)
        """
        try:
            # 构建WHERE条件
            where_conditions = []
            params = []
            
            if es_dump_name:
                where_conditions.append("es_dump_name LIKE %s")
                params.append(f"%{es_dump_name}%")
            
            if remark:
                where_conditions.append("remark LIKE %s")
                params.append(f"%{remark}%")
            
            if source_es_module_name:
                where_conditions.append("source_es_module_name = %s")
                params.append(source_es_module_name)
            
            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)
            
            # 查询总数
            count_sql = f"""
            SELECT COUNT(*) as total
            FROM es_mgt_dump_info
            {where_clause}
            """
            
            # 查询数据
            offset = (page - 1) * page_size
            data_sql = f"""
            SELECT id, suite_code, es_dump_name, source_es_module_name, status,index_count,
                   repository_name, creator, remark, create_time, update_time
            FROM es_mgt_dump_info
            {where_clause}
            ORDER BY create_time DESC
            LIMIT %s OFFSET %s
            """
            
            with connection.cursor() as cursor:
                # 获取总数
                cursor.execute(count_sql, params)
                total = cursor.fetchone()[0]
                
                # 获取数据
                data_params = params + [page_size, offset]
                cursor.execute(data_sql, data_params)
                
                column_names = [
                    "id", "suiteCode", "esDumpName", "sourceEsModuleName", "status","index_count",
                    "repositoryName", "creator", "remark", "createTime", "updateTime"
                ]
                
                results = []
                for record in cursor.fetchall():
                    row_dict = dict(zip(column_names, record))
                    # 格式化时间字段
                    if row_dict.get('createTime'):
                        row_dict['createTime'] = row_dict['createTime'].strftime('%Y-%m-%d %H:%M:%S')
                    if row_dict.get('updateTime'):
                        row_dict['updateTime'] = row_dict['updateTime'].strftime('%Y-%m-%d %H:%M:%S')
                    results.append(row_dict)
                
                return results, total
                
        except Exception as e:
            logger.error(f"查询ES备份信息列表失败: {str(e)}")
            raise
    
    @staticmethod
    def get_backup_details_list(es_dump_name: str, page: int = 1, 
                               page_size: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """
        根据es_dump_name查询Jenkins任务详情列表
        
        Args:
            es_dump_name: ES备份名称
            page: 页码
            page_size: 每页大小
            
        Returns:
            Tuple[List[Dict], int]: (查询结果列表, 总记录数)
        """
        try:
            # 查询总数
            count_sql = """
            SELECT COUNT(*) as total
             from  jenkins_mgt_test_es_init_job
            where  id in (select  max(id)  from  jenkins_mgt_test_es_init_job
            where    es_dump_name = %s group by  biz_iter_id)
            """
            
            # 查询数据
            offset = (page - 1) * page_size
            data_sql = """
            select jmtei.id,jmtei.biz_code,jmtei.biz_br_name,jmtei.suite_code,jmtei.create_time,CASE 
                    WHEN biz_info.biz_scenario_name IS NOT NULL 
                    THEN CONCAT(biz_type.biz_type_department, '-', biz_type.biz_type_transaction, '-', 
                               biz_info.biz_name, '-', biz_info.biz_scenario_name)
                    ELSE biz_info.biz_name
                END AS bizInfoName , concat(jmtei.job_url,'/',jmtei.id,'/pipeline') pipelineUrl
             from   jenkins_mgt_test_es_init_job  jmtei
             LEFT JOIN biz_base_info biz_info ON jmtei.biz_code = biz_info.biz_code
             LEFT JOIN biz_base_type biz_type ON biz_info.biz_type = biz_type.id
             where  jmtei.id in (select  max(id)  from  jenkins_mgt_test_es_init_job 
             where    es_dump_name = %s group by  biz_iter_id)
            ORDER BY jmtei.create_time DESC
            LIMIT %s OFFSET %s
            """
            
            with connection.cursor() as cursor:
                # 获取总数
                cursor.execute(count_sql, [es_dump_name])
                total = cursor.fetchone()[0]
                
                # 获取数据
                cursor.execute(data_sql, [es_dump_name, page_size, offset])
                
                column_names = [
                    "id","biz_code", "biz_br_name", "suite_code", "create_time", "bizInfoName","pipelineUrl"
                ]
                
                results = []
                for record in cursor.fetchall():
                    row_dict = dict(zip(column_names, record))
                    # 格式化时间字段
                    if row_dict.get('createTime'):
                        row_dict['createTime'] = row_dict['createTime'].strftime('%Y-%m-%d %H:%M:%S')
                    if row_dict.get('updateTime'):
                        row_dict['updateTime'] = row_dict['updateTime'].strftime('%Y-%m-%d %H:%M:%S')
                    results.append(row_dict)
                
                return results, total
                
        except Exception as e:
            logger.error(f"查询备份详情列表失败: {str(e)}")
            raise
    
    @staticmethod
    def update_backup_status(es_dump_name: str, new_status: str) -> bool:
        """
        更新ES备份状态
        
        Args:
            es_dump_name: ES备份名称
            new_status: 新状态
            
        Returns:
            bool: 是否更新成功
        """
        try:
            sql = """
            UPDATE es_mgt_dump_info 
            SET status = %s, update_time = NOW()
            WHERE es_dump_name = %s
            """
            
            with connection.cursor() as cursor:
                cursor.execute(sql, [new_status, es_dump_name])
                return cursor.rowcount > 0
                
        except Exception as e:
            logger.error(f"更新备份状态失败: {str(e)}")
            raise
    
    @staticmethod
    def get_business_info_list(es_dump_name: str) -> List[Dict[str, Any]]:
        """
        根据es_dump_name查询关联的业务信息
        
        Args:
            es_dump_name: ES备份名称
            
        Returns:
            List[Dict]: 业务信息列表
        """
        try:
            sql = """
            SELECT DISTINCT 
                bind.biz_test_iter_br as branchCode,
                bind.biz_code as bizCode,
                CASE 
                    WHEN biz_info.biz_scenario_name IS NOT NULL 
                    THEN CONCAT(biz_type.biz_type_department, '-', biz_type.biz_type_transaction, '-', 
                               biz_info.biz_name, '-', biz_info.biz_scenario_name)
                    ELSE biz_info.biz_name
                END AS bizInfoName
            FROM es_mgt_dump_biz_bind bind
            LEFT JOIN biz_base_info biz_info ON bind.biz_code = biz_info.biz_code
            LEFT JOIN biz_base_type biz_type ON biz_info.biz_type = biz_type.id
            WHERE bind.es_dump_name = %s
            AND biz_info.biz_is_active = 1
            ORDER BY bind.create_time DESC
            """
            
            with connection.cursor() as cursor:
                cursor.execute(sql, [es_dump_name])
                
                column_names = ["branchCode", "bizCode", "bizInfoName"]
                
                results = []
                for record in cursor.fetchall():
                    row_dict = dict(zip(column_names, record))
                    results.append(row_dict)
                
                return results
                
        except Exception as e:
            logger.error(f"查询业务信息列表失败: {str(e)}")
            raise