
CREATE TABLE `jenkins_mgt_test_es_init_job` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `es_dump_name` varchar(100) NOT NULL COMMENT '备份名称（业务唯一键）',
  `job_type` varchar(100) DEFAULT NULL COMMENT '任务类型',
  `biz_iter_id` varchar(100) DEFAULT NULL,
  `biz_code` varchar(100) DEFAULT NULL COMMENT '业务code',
  `biz_br_name` varchar(20) DEFAULT NULL COMMENT '目标分支',
  `job_name` varchar(20) DEFAULT NULL COMMENT 'job名称',
  `job_build_id` varchar(20) DEFAULT NULL,
  `suite_code` varchar(50) DEFAULT NULL COMMENT '环境',
  `status` varchar(20) DEFAULT NULL COMMENT '状态',
  `job_param` json DEFAULT NULL COMMENT '任务参数',
  `job_url` varchar(255) DEFAULT NULL COMMENT '组合任务url',
  `create_time` timestamp NULL DEFAULT NULL,
  `update_time` timestamp NULL DEFAULT NULL,
  `create_user` varchar(50) DEFAULT NULL,
  `update_user` varchar(50) DEFAULT NULL,
  `stamp` bigint(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_0` (`biz_iter_id`) USING BTREE,
  KEY `idx_1` (`suite_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;


CREATE TABLE `es_mgt_dump_info` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `suite_code` varchar(50) NOT NULL COMMENT '环境唯一编码',
  `es_dump_name` varchar(100) NOT NULL COMMENT '备份名称（业务唯一键）',
  `source_es_module_name` varchar(100) NOT NULL COMMENT '备份源ES模块名',
  `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '状态（RUNNING, SUCCESS, FAILED）',
  `repository_name` varchar(100) NOT NULL COMMENT '使用的ES快照仓库名',
  `file_path` varchar(255) DEFAULT NULL COMMENT '备份文件路径',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（Bytes）',
  `index_count` int(11) DEFAULT NULL COMMENT '索引数量',
  `document_count` bigint(20) DEFAULT NULL COMMENT '文档总数',
  `creator` varchar(50) NOT NULL COMMENT '创建人',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `error_log` text COMMENT '错误日志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_es_dump_name` (`es_dump_name`) COMMENT 'ES备份名称唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='ES备份元数据表';





CREATE TABLE `es_mgt_dump_biz_bind` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `biz_test_iter_id` varchar(100) NOT NULL COMMENT '测试迭代ID-非biz_test_iter主键ID',
  `source_es_module_name` varchar(100) NOT NULL COMMENT '备份源ES模块名',
  `biz_code` varchar(100) NOT NULL COMMENT '业务编码（业务唯一键）',
  `biz_test_iter_br` varchar(100) NOT NULL COMMENT '分支',
  `es_dump_name` varchar(100) NOT NULL COMMENT '关联的备份名称',
  `operator` varchar(50) NOT NULL COMMENT '操作人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_biz_test_iter_id_source_es_module` (`biz_test_iter_id`,`source_es_module_name`) COMMENT '测试迭代下的module类型'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='业务ES备份关系表';



1、 生成一个列表分页查询接口 接口名包含List字符用来描述 ，查询  es_mgt_dump_info  入参为：es_dump_name，remark，source_es_module_name ，作为筛选条件

2、 生成一个备份详情列表分页查询接口  接口名包含List字符用来描述  查询jenkins_mgt_test_es_init_job  倒序分页展示，入参只有 es_dump_name


3、复用口地址 : POST /spider/es_mgt/backups/status/

HTTP方法 : POST

接口功能 : 验证/更新ES备份状态

4、  创建一个新的接口
    根据SQL ：select
    es_mgt_dump_biz_bind.biz_test_iter_br branch_code,
    biz_base_info.biz_code,
    CASE
        WHEN biz_base_info.biz_scenario_name IS NOT NULL AND biz_base_info.biz_scenario_name != ''
        THEN CONCAT(biz_base_type.biz_type_department, biz_base_type.biz_type_transaction, '-', biz_base_info.biz_name, '-', biz_base_info.biz_scenario_name)
        ELSE biz_base_info.biz_name
    END AS biz_info_name
FROM biz_base_info
INNER JOIN biz_base_type ON (biz_base_info.biz_type = biz_base_type.id)
join  es_mgt_dump_biz_bind  on  biz_base_info.biz_code = es_mgt_dump_biz_bind.biz_code
WHERE biz_base_info.biz_is_active = 1
AND es_mgt_dump_biz_bind.es_dump_name = 'it103-20250922095957';

es_dump_name  作为入参，返回一个  branch_code,biz_code,biz_info_name  为一组的List集合

 
 
 
