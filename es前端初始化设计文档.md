# ES备份初始化前端设计文档

本文档根据《es备份初始化需求前端设计概览.txt》编写，旨在描述ES备份管理的前端页面设计和交互逻辑。

## 1. ES备份列表页面

对应图1，此页面提供ES备份数据的查询和管理功能。

### 1.1. 页面功能

-   **查询功能**：支持根据文件名、备份说明、备份所属ES等条件进行筛选查询。
-   **分页显示**：查询结果以分页列表的形式展示。
-   **操作功能**：对列表中的每一项备份，提供详情、验证和初始化操作。

### 1.2. 接口定义

-   **获取备份列表**：
    -   **URL**： `/spider/es_mgt/backups/list/`
    -   **Method**： `GET`
    -   **说明**：用于获取ES备份列表，支持分页和条件查询。

-   **验证备份状态**：
    -   **URL**： `/spider/es_mgt/backups/status/`
    -   **Method**： `POST` (或根据后端定义)
    -   **说明**：点击“验证”按钮后调用此接口，成功后再次调用列表接口刷新当前页数据。

### 1.3. 页面元素

-   **查询表单**：
    -   文件名：输入框
    -   备份说明：输入框
    -   备份所属ES：下拉选择框
    -   查询按钮
-   **数据列表**：
    -   **列**：es备份文件名, 备份说明, 索引数, 备份状态, 备份所属ES, 创建人, 操作
    -   **操作栏**：
        -   `详情`：按钮，点击后弹出备份详情模态框。
        -   `验证`：按钮，点击后调用验证接口。
        -   `初始化`：按钮，点击后弹出初始化模态框。

## 2. 备份详情弹窗

对应图2，此弹窗显示特定备份的初始化历史详情。

### 2.1. 页面功能

-   **触发方式**：在ES备份列表页面点击“详情”按钮。
-   **内容显示**：以列表形式展示该备份在不同业务和环境下的初始化记录。

### 2.2. 接口定义

-   **获取备份详情**：
    -   **URL**： `/spider/es_mgt/backups/details/list/`
    -   **Method**： `GET`
    -   **说明**：分页查询指定备份的初始化详情。

### 2.3. 页面元素

-   **标题**：`{es_backup_name} 备份详情`
-   **数据列表**：
    -   **列**：业务, 分支, 最近一次初始化环境, 最近一次初始化时间, 操作
    -   **操作栏**：
        -   `详情`：按钮，可用于查看更详细的单次初始化信息（如果需要）。

## 3. 初始化弹窗

对应图3，此弹窗用于执行ES备份的初始化操作。

### 3.1. 页面功能

-   **触发方式**：在ES备份列表页面点击“初始化”按钮。
-   **表单交互**：提供下拉框供用户选择业务、分支和环境。

### 3.2. 接口定义

-   **获取业务列表**：
    -   **URL**： `spider/biz_mgt/get_biz_name_list/`
    -   **Method**： `GET`
    -   **说明**：用于填充“业务”下拉框。

-   **获取环境列表**：
    -   **URL**： `spider/es_mgt/es_mgt_api/env-modules/`
    -   **Method**： `GET`
    -   **说明**：用于填充“环境”下拉框。

-   **获取分支列表**：
    -   **URL**： `spider/biz_mgt/get_test_iter_list/`
    -   **Method**： `GET`
    -   **Query Params**： `biz_code={selected_biz_code}`
    -   **说明**：在选择“业务”后，根据业务代码获取并填充“分支”下拉框。

-   **执行初始化**：
    -   **URL**： `spider/db_mgt/db_iter_mgt_pipeline/`
    -   **Method**： `POST`
    -   **Body**：
        ```json
        {
            "job_name": "test_es_init",
            "bis_pipeline_id": "{selected_value}",
            "suite_code": "{selected_env}",
            "biz_br_name": "{selected_branch}"
        }
        ```
    -   **说明**：点击“确定”按钮后，收集表单数据并调用此接口，触发后端流水线。

### 3.3. 页面元素

-   **标题**：`{es_backup_name} 初始化`
-   **表单**：
    -   业务：下拉选择框
    -   分支：下拉选择框
    -   环境：下拉选择框
-   **操作按钮**：
    -   `取消`：关闭弹窗。
    -   `确定`：提交表单，执行初始化。