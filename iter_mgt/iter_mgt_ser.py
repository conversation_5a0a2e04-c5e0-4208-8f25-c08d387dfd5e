import datetime

from django.db.models import Q
from rest_framework import serializers
from django.db import connection

from iter_mgt.models import Branches
from spider.settings import logger


class IterApplySerializer(serializers.Serializer):
    branch_name = serializers.CharField(error_messages={'required': '分支名不能为空'}, required=True)
    gitlab_group = serializers.CharField(error_messages={'required': 'gitlab组不能为空'}, required=True)
    is_update_out_dep = serializers.IntegerField(error_messages={'required': '是否升级依赖不能为空'}, required=True)
    branch_type = serializers.CharField(error_messages={'required': '分支类型不能为空'}, required=True)
    deadline = serializers.CharField(error_messages={'required': '截止日期不能为空'}, required=True)
    repos_str = serializers.ListField(error_messages={'required': 'git仓库不能为空'}, required=True)
    desc = serializers.CharField(error_messages={'required': '描述不能为空'}, required=True)


class IterMgtSerializer(serializers.Serializer):
    iteration_id = serializers.CharField(error_messages={'required': '迭代号不能为空'}, required=True)
    repos_str = serializers.ListField(error_messages={'required': 'git仓库不能为空'}, required=True)


class IterModSerializer(serializers.Serializer):
    iteration_id = serializers.CharField(error_messages={'required': '迭代号不能为空'}, required=True)
    deadline = serializers.DateTimeField(error_messages={'required': '截止日期不能为空'}, required=True)
    desc = serializers.CharField(error_messages={'required': '描述不能为空'}, required=True)


class IterConfigMapSerializer(serializers.Serializer):
    env = serializers.CharField(error_messages={'required': '环境不能为空'}, required=True)
    apps = serializers.CharField(error_messages={'required': '应用列表不能为空'}, required=True)


def get_breach_info_for_apply(app_name_list):
    cursor = connection.cursor()
    cursor.execute(''' (SELECT
                        tab1.id,
                        tab1.pipeline_id,
                        tab1.appName,
                        tab2.br_name,
                        tab1.sys_status,
                        tab2.br_end_date 
                        FROM
                            `iter_mgt_iter_app_info` AS tab1
                            LEFT JOIN iter_mgt_iter_info AS tab2 ON tab1.pipeline_id = tab2.pipeline_id 
                        WHERE
                            tab1.appName IN ("{app_name_list}") 
                            AND tab1.sys_status != '已归档' 
                            ) UNION
                            (
                        SELECT 
                            t.id AS id, 
                            t.pipeline_id AS pipeline_id, 
                            t.appName, i.br_name AS br_name, 
                            t.sys_status AS sys_status, 
                            i.br_end_date AS br_end_date 
                        FROM iter_mgt_iter_app_info t 
                        INNER JOIN iter_mgt_iter_info i ON t.pipeline_id = i.pipeline_id
                        WHERE t.appName in ("{app_name_list}") AND i.br_status = 'close'
                        ORDER BY i.br_end_date DESC
                        LIMIT 1 ) '''.format(app_name_list='","'.join(app_name_list)))
    return cursor.fetchall()


def get_tapd_id(app_name):
    cursor = connection.cursor()
    cursor.execute('''
           SELECT
                tapd_id 
            FROM
                tool_mgt_team_info a4 
            WHERE
                a4.id IN ( SELECT team_id FROM team_mgt_app_bind a3 WHERE a3.app_id IN ( SELECT id FROM app_mgt_app_info a2 WHERE a2.git_path = '{}' ) ) '''.format(
        '/' + app_name))
    return cursor.fetchall()


def get_iter_info(user, project_type, br_status="open"):
    cursor = connection.cursor()
    """增加排序 20210701 by fwm"""
    sql = '''
        SELECT DISTINCT ii.pipeline_id,ii.br_style,ii.project_group,ii.br_start_date,ii.duedate,ii.description,ii.tapd_id,ii.br_name 
        FROM iter_mgt_iter_info ii 
        INNER JOIN iter_mgt_iter_app_info ai ON ii.pipeline_id = ai.pipeline_id
        INNER JOIN app_mgt_app_build ab on ai.appName = ab.module_name 
        WHERE ii.br_status='{}'
        AND ii.project_group IN (
            SELECT git_group_name FROM user_gitlab_members
            WHERE username="{}" AND permission>20)
            
    '''.format(br_status, user)
    if 'server' == project_type:
        sql += ''' AND ab.package_type in ('jar','tar','war','py') '''
    if 'h5' == project_type:
        sql += ''' AND ab.package_type in ('remote','static','dist', 'ios', 'ios-com', 'android', 'android-com', 
        'param-remote','mini-program','ssr-remote', 'android-global', 'ios-global') '''
    if 'py' == project_type:
        sql += ''' AND ab.package_type in ('py') '''
    sql += ' ORDER BY ii.project_group, ii.br_start_date DESC '
    logger.info(sql)
    cursor.execute(sql)
    return cursor.fetchall()


def get_mobile_iter_info(user, br_status="open"):
    cursor = connection.cursor()

    if br_status == 'close':
        date_condition = 'i.br_end_date'
        count_condition = 'v.count_of_group < 30 AND'
    else:
        date_condition = 'i.br_start_date'
        count_condition = ''

    sql = '''
        SELECT DISTINCT v.pipeline_id, v.project_group, v.br_start_date, v.count_of_group,v.br_style, v.duedate, v.description, v.tapd_id, v.br_name,v.br_status
                    FROM(
                    SELECT  t.pipeline_id, t.project_group, t.br_start_date,t.br_style, t.duedate, t.description, t.tapd_id, t.br_name,t.br_status,

                    @ROW_NUMBER:=CASE
                    WHEN @proj_group = t.project_group THEN
                        @ROW_NUMBER + 1
                    ELSE
                        1
                    END AS count_of_group,
                    @proj_group:=t.project_group AS proj_group
        FROM(
            SELECT DISTINCT i.pipeline_id, i.project_group, i.br_start_date,i.br_style, i.duedate, i.description, i.tapd_id, i.br_name,i.br_status
            FROM iter_mgt_iter_info i
            INNER JOIN iter_mgt_iter_app_info ai ON ai.pipeline_id = i.pipeline_id AND i.br_status = '{}'
            ORDER BY i.project_group, {} DESC
            )t,(SELECT @ROW_NUMBER:=0)T1, (SELECT @proj_group:='')T2
        )v 
        LEFT JOIN user_gitlab_members tt ON v.project_group = tt.git_group_name
        WHERE {} tt.username='{}' AND tt.permission>20
        '''.format(br_status, date_condition, count_condition, user)
    logger.info(sql)
    cursor.execute(sql)
    return cursor.fetchall()


def get_iter_search_info(user, pipeline_id, project_group, br_style, page_num, page_size, page_total,
                         description_content, branch_is_new):
    cursor = connection.cursor()
    sql = '''
                SELECT DISTINCT i.pipeline_id, i.br_style, i.project_group, i.br_start_date, 
                    i.duedate, i.description, i.tapd_id, i.br_name, 
                    CASE
                        WHEN GROUP_CONCAT(DISTINCT b.package_type) REGEXP '(^|,)(py)(,|$)' THEN 'py' 
                        WHEN GROUP_CONCAT(DISTINCT b.package_type) REGEXP '(^|,)(jar|pom|war|tar)(,|$)' THEN 'server'
                        WHEN GROUP_CONCAT(DISTINCT b.package_type) REGEXP '(^|,)(ios|ios-com|android|android-com|harmony|ios-global|android-global)(,|$)' THEN 'app'
                        ELSE 'h5'
                    END AS package_type,
                       wa.wl_pass AS dev_self_test_flag, ai.proposer
                    FROM iter_mgt_iter_info i
                    LEFT JOIN iter_mgt_iter_app_info ai ON i.pipeline_id = ai.pipeline_id
                    LEFT JOIN app_mgt_app_build b ON ai.appName = b.module_name
                    LEFT JOIN iter_whitelist_app wa ON i.pipeline_id = wa.wl_name
                    WHERE br_status="open" AND project_group IN (
                    SELECT git_group_name FROM user_gitlab_members
                    WHERE username="{}" AND permission>20)  '''.format(user)
    if pipeline_id:
        sql += ' AND i.pipeline_id LIKE "%{}%"'.format(pipeline_id)
    if project_group:
        sql += ' AND i.project_group="{}"'.format(project_group)
    if br_style:
        sql += ' AND i.br_style="{}"'.format(br_style)
    if description_content:
        sql += ' AND i.description LIKE "%{}%"'.format(description_content)
    if branch_is_new:
        sql += ' AND i.is_new={}'.format(int(branch_is_new))

    if not page_num:
        page_num = 1
    if not page_size:
        page_size = 10
    if not page_total:
        page_total = 0
    logger.info(sql)
    group_by_sql = " GROUP BY i.pipeline_id "
    order_by_sql = " ORDER BY i.br_start_date DESC "
    try:
        page_total = cursor.execute(sql + group_by_sql + order_by_sql)
        page = {
            'num': page_num,
            'size': page_size,
            'total': page_total,
        }
        start_idx = page_size * (page_num - 1)

        limit_sql = " limit {},{}".format(start_idx, page_size)
        logger.info(limit_sql)
        logger.info(sql + group_by_sql + order_by_sql + limit_sql)

        cursor.execute(sql + group_by_sql + order_by_sql + limit_sql)
    except Exception as e:
        logger.error(str(e))

    return page, cursor.fetchall()


def get_git_url():
    cursor = connection.cursor()
    cursor.execute('''
               SELECT DISTINCT git_url FROM tool_mgt_git_url ''')
    return cursor.fetchall()


def get_repos_info(iteration_id):
    cursor = connection.cursor()
    # 返回jdk版本从iter_mgt_iter_app_info表中取信息 20211220 by fwm
    cursor.execute('''
                  SELECT CONCAT(s.git_url,s.git_path) AS git_path, t.proposer, 
                  CONCAT( h.last_name,h.first_name) AS cname, 
                  t.appName, t.sys_status, z.package_type,  t.jdkVersion, f.need_online, f.need_ops, f.is_component
                  FROM iter_mgt_iter_app_info t
                  LEFT JOIN app_mgt_app_module f ON f.module_name=t.appName 
                  LEFT JOIN app_mgt_app_build z ON f.module_name=z.module_name
                  LEFT JOIN app_mgt_app_info s ON f.app_id =s.id 
                  LEFT JOIN auth_user h ON t.proposer=h.username WHERE t.pipeline_id="{}" 
                  AND CONCAT(s.git_url,s.git_path) IS NOT NULL'''.format(iteration_id))
    return cursor.fetchall()


def get_iter_repos_info(iteration_id):
    cursor = connection.cursor()
    cursor.execute('''
                  SELECT CONCAT(s.git_url,s.git_path) AS git_path,  
 t.appName, f.need_online
                  FROM iter_mgt_iter_app_info t
                  LEFT JOIN app_mgt_app_module f ON f.module_name=t.appName               
                  LEFT JOIN app_mgt_app_info s ON f.app_id =s.id 
                 WHERE t.pipeline_id="{}" 
                 AND CONCAT(s.git_url,s.git_path) IS NOT NULL'''.format(iteration_id))
    return cursor.fetchall()


def get_iter_other_repos_info(iteration_id):
    """获取迭代下其他仓库信息"""
    cursor = connection.cursor()
    cursor.execute('''
                 SELECT  DISTINCT f.module_name,CONCAT(s.git_url,s.git_path) AS git_path,f.need_online, f.is_component
                  FROM app_mgt_app_module f
                  LEFT JOIN app_mgt_app_info s ON f.app_id =s.id 
                 WHERE f.need_check = 1 and s.git_url in (
                        SELECT DISTINCT s.git_url AS git_path
                        FROM app_mgt_app_module f
                          LEFT JOIN app_mgt_app_info s ON f.app_id =s.id 
                          LEFT JOIN iter_mgt_iter_app_info it ON it.appName = f.module_name
                         WHERE it.pipeline_id="{}"
                         )
                  AND CONCAT(s.git_url,s.git_path) IS NOT NULL AND f.module_name NOT IN 
                  (SELECT appName FROM iter_mgt_iter_app_info WHERE pipeline_id="{}")'''
                   .format(iteration_id, iteration_id))
    return cursor.fetchall()


def truncate_git_members():
    cursor = connection.cursor()
    # 截断表数据
    cursor.execute('TRUNCATE user_gitlab_members')


def get_pipeline_app():
    cursor = connection.cursor()
    cursor.execute("SELECT DISTINCT git_url  FROM app_mgt_app_info WHERE platform_type = 1")
    return [row[0] for row in cursor.fetchall()]


def get_product_info(br_status, page_data, app_name=None, br_name=None):
    cursor = connection.cursor()

    sql = """
    SELECT 
        m.pipeline_id, 
        m.br_name, 
        m.br_end_date, 
        m.br_status,
        i.appName, 
        i.git_repo_version,
        IF(v.max_br_end_date IS NOT NULL, m.br_name, NULL) AS online_br_name,ab.package_type packageType
    FROM iter_mgt_iter_info m
    INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id AND i.git_repo_version IS NOT NULL
    left join app_mgt_app_build ab on i.appName = ab.module_name
    LEFT JOIN(
        SELECT 
        i.appName, MAX(m.br_end_date) AS max_br_end_date
        FROM iter_mgt_iter_info m
        INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id AND i.git_repo_version IS NOT NULL
        WHERE m.br_status = 'close'
        GROUP BY i.appName
    )v ON v.appName = i.appName AND v.max_br_end_date = m.br_end_date
    WHERE m.br_status = '{}'
    
    """.format(br_status)

    if app_name:
        sql += " AND i.appName = '{}'".format(app_name)
    if br_name:
        sql += " AND m.br_name = '{}'".format(br_name)
    order_sql = ' ORDER BY br_end_date DESC'
    page_total = cursor.execute(sql + order_sql)
    total_data_list = [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]
    print(page_total)
    page_num = page_data['pageNum']
    page_size = page_data['pageSize']
    if not page_num:
        page_num = 1
    if not page_size:
        page_size = 10
    # if not page_total:
    #     page_total = 0
    limit_up = page_size * (page_num - 1)
    page_limit = ' limit {},{}'.format(limit_up, page_size)
    sql = sql + order_sql + page_limit
    cursor.execute(sql)
    logger.info(sql)
    data_list = [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]
    return data_list, page_total, total_data_list


def get_user_repos_info(username):
    """
    获取有权限
    :param username:
    :return:
    """
    cursor = connection.cursor()
    sql = """
            SELECT DISTINCT CONCAT(t.git_url,git_path) AS git_path,f.module_name,f.need_online, f.is_component 
            FROM app_mgt_app_info t
            LEFT JOIN  app_mgt_app_module f ON t.id=f.app_id 
            LEFT JOIN user_gitlab_members m ON m.git_group_name=t.git_url 
            WHERE t.platform_type=1 AND m.username="{}" AND m.permission>29 AND f.need_check = 1
          """.format(username)
    logger.info(sql)
    cursor.execute(sql)
    return cursor.fetchall()


def get_app_latest_archive_info(pipeline_id):
    """
    获取迭代所在gitlab组下所有上线应用的最近一次上线完成（归档）信息
    """
    cursor = connection.cursor()
    sql = """select m.pipeline_id,m.br_name,i.appName,m.br_end_date,ab.package_type packageType from iter_mgt_iter_info m 
			LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
			left join app_mgt_app_module am on i.appName = am.module_name
			left join app_mgt_app_build ab on i.appName = ab.module_name
			left join (		
			SELECT
                i.appName, MAX(m.br_end_date) AS max_br_end_date
                FROM iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                WHERE m.br_status = 'close' 
                and m.project_group in (select m.project_group from iter_mgt_iter_info m where m.pipeline_id = '{}')
                GROUP BY i.appName ) v on v.appName = i.appName and  v.max_br_end_date = m.br_end_date				
			where  m.br_end_date IS NOT NULL AND    
		v.max_br_end_date IS NOT NULL and am.need_online = 1 ORDER BY m.br_end_date desc""".format(pipeline_id)
    logger.info(sql)
    cursor.execute(sql)
    return cursor.fetchall()


def get_app_online_info(pipeline_id):
    """
    获取迭代所在gitlab组下所有正在上线的应用信息
    """
    cursor = connection.cursor()
    sql = """select i.pipeline_id, m.br_name, i.appName from iter_mgt_iter_info m
        LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
        where i.sys_status = '上线中' and m.project_group in 
        (select m.project_group from iter_mgt_iter_info m where m.pipeline_id = '{}') """.format(pipeline_id)
    logger.info(sql)
    cursor.execute(sql)
    return cursor.fetchall()


def get_need_publish_app_info(guard_name, pipeline_id):
    """
    未接入健康检查
    获取迭代下需要上线的应用列表
    :param guard_name:
    :param pipeline_id:
    :return:
    """
    cursor = connection.cursor()
    sql = """
            SELECT DISTINCT m.module_name 
            FROM iter_mgt_iter_info ii
            LEFT JOIN iter_mgt_iter_app_info i on i.pipeline_id = ii.pipeline_id
            LEFT JOIN app_mgt_app_module m on m.module_name = i.appName
            LEFT JOIN qc_mgt_guard_switch_info a on a.guard_name = '{guard_name}'
            LEFT JOIN iter_whitelist_group b on a.id = b.wl_switch_id and ii.project_group = b.wl_group_name
            LEFT JOIN env_mgt_node_bind e on e.module_name = m.module_name
            WHERE ii.pipeline_id = '{pipeline_id}'
            AND m.need_online = 1
            AND m.need_ops = 1
            AND (a.guard_switch = 0 or b.wl_group_pass = 1 or e.is_cold_standby_node = 1 or m.jenkins_batch_publish = 0 or m.jenkins_batch_publish IS NULL)
             """.format(guard_name=guard_name, pipeline_id=pipeline_id)
    logger.info(sql)
    cursor.execute(sql)
    app_list = []
    for row in cursor.fetchall():
        app_list.append(row[0])
    return app_list


def find_app_branch_info(app_name):
    sql = ''' 
            SELECT
                info.br_name br_name_value,
                case info.br_status
                    when 'close'
                    then concat(info.br_name,'(已归档：',info.br_end_date,')')
                    else info.br_name
                end
                br_name_label                  
            FROM
                iter_mgt_iter_info info,
                iter_mgt_iter_app_info app 
            WHERE
                info.pipeline_id = app.pipeline_id
                and app.appName = '{app_name}'
            order by info.br_end_date desc
         '''.format(app_name=app_name)
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    br_info = []
    for row in cursor.fetchall():
        br_name_value = row[0]
        br_name_label = row[1]
        br_info.append({
            'br_name_value': br_name_value,
            'br_name_label': br_name_label,
        })
    return br_info


def get_package_type_in_iter(iter_id):
    sql = """SELECT b.package_type FROM iter_mgt_iter_app_info i LEFT JOIN app_mgt_app_build b 
            ON i.appName = b.module_name  
             WHERE i.pipeline_id = "{}"
    """.format(iter_id)
    logger.info(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    pkg_type_list = []
    for row in cursor.fetchall():
        if row[0] in pkg_type_list:
            continue
        pkg_type_list.append(row[0])
    return pkg_type_list


def get_h5_group():
    sql = """SELECT DISTINCT git_url 
             FROM app_mgt_app_info 
             WHERE id in  
             ( SELECT DISTINCT app_id 
               from app_mgt_app_build 
               WHERE package_type not in ('jar', 'war', 'tar', 'pom'))"""
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchall()

def get_py_group():
    sql = """SELECT DISTINCT git_url 
             FROM app_mgt_app_info 
             WHERE id in  
             ( SELECT DISTINCT app_id 
               from app_mgt_app_build 
               WHERE package_type in ('py'))"""
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


# def get_nf_app_sys_statu(app_name, iteration_id):
#     sql = "select n.id,n.nf_br_name,i.pipeline_id,a.sys_status FROM h5_app_bind_nf_app n " \
#           "left join iter_mgt_iter_info i on i.br_name = n.nf_br_name " \
#           "left join iter_mgt_iter_app_info a on a.pipeline_id = i.pipeline_id " \
#           "where n.app_name = '{}' and n.iteration_id = '{}' and n.stage = '上线' and a.appName = 'nf-{}' ORDER BY n.id desc".format(
#         app_name, iteration_id, app_name)
#     cursor = connection.cursor()
#     cursor.execute(sql)
#     result = cursor.fetchall()[0]
#     sys_status = result[3]
#     nf_iteration_id = result[2]
#     return nf_iteration_id, sys_status


class IterTestEndDateSerializer(serializers.Serializer):
    iter_id = serializers.CharField(error_messages={'required': '迭代名不能为空'}, required=True)

    # test_end_date = serializers.DateField(error_messages={'required': '截止日期不能为空'}, format="%Y/%m/%d", required=True)
    # test_end_date = serializers.DateField(error_messages={'required': '截止日期不能为空'},required=True)

    def update_test_end_date(self, iter_id, test_end_date, user):
        sql = 'UPDATE iter_mgt_iter_info SET test_end_date = "{}" ,update_test_end_date_user="{}"' \
              ' WHERE pipeline_id = "{}" '.format(test_end_date, user, iter_id)
        logger.info(sql)
        cursor = connection.cursor()
        cursor.execute(sql)
        return True


def get_branch_version_by_module_name(module_name, branch_name=None):
    sql = """
                select i.br_name, i.br_status, i.create_date from iter_mgt_iter_app_info t 
                INNER JOIN iter_mgt_iter_info i on t.pipeline_id = i.pipeline_id
                where t.appName = '{}'
          """.format(module_name)
    if branch_name:
        sql += """ AND i.br_name = '{}'""".format(branch_name)

    cursor = connection.cursor()
    cursor.execute(sql)

    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


def get_iter(module_name, br_name):
    """
    获取迭代号
    :param module_name:
    :param br_name:
    :return:
    """
    sql = """
            select i.pipeline_id from iter_mgt_iter_app_info t 
			INNER JOIN iter_mgt_iter_info i on t.pipeline_id = i.pipeline_id
			where t.appName = '{}' and i.br_name = '{}'
        """.format(module_name, br_name)

    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        return row[0]
    return None


def get_agent_version(module_name):
    sql = """select m.br_name from iter_mgt_iter_info m
            left join iter_mgt_iter_app_info i on i.pipeline_id = m.pipeline_id
            where br_end_date = 
            (
            select MAX(m1.br_end_date) from iter_mgt_iter_info m1
            INNER JOIN iter_mgt_iter_app_info i1 ON i1.pipeline_id = m1.pipeline_id
            WHERE m1.br_status = 'close' and i1.appName = '{appName}'
            ) 
            and i.appName = '{appName}';
        """.format(appName=module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    data = []
    for row in cursor.fetchall():
        achieved_version = row[0]
        data.append({"message": "已归档版本", "version": achieved_version})

    sql = """
        select t.lib_repo_branch from product_mgt_product_info t where t.id = 
        (select max(id) from product_mgt_product_info t where t.module_name = '{module_name}');
        """.format(module_name=module_name)
    cursor.execute(sql)
    for row in cursor.fetchall():
        if achieved_version != row[0]:
            data.append({"message": "最后一次构建版本", "version": row[0]})

    return data


def get_last_several_archive_version(app_name, several=4):
    open_branch_list = []
    sql = "SELECT i.appName,m.br_name,m.br_status ,m.br_end_date FROM iter_mgt_iter_info m " \
          "LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id " \
          "LEFT JOIN(SELECT i.appName FROM iter_mgt_iter_info m INNER JOIN iter_mgt_iter_app_info i " \
          "ON i.pipeline_id = m.pipeline_id WHERE m.br_status = 'close' and i.appName = '{}')v " \
          "ON v.appName = i.appName  WHERE m.br_end_date IS NOT NULL AND m.br_status = 'close' and " \
          "i.appName = '{}'  GROUP BY i.appName ,i.pipeline_id ORDER BY m.br_end_date DESC LIMIT {}". \
        format(app_name, app_name, several)
    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        open_branch_list.append({'br_name': row[1], 'br_status': row[2]})
    return open_branch_list


def get_open_branch_version_by_module_name(module_name):
    open_branch_list = []
    sql = """
            select t.appName, i.br_name, i.br_status, i.create_date from iter_mgt_iter_app_info t 
            INNER JOIN iter_mgt_iter_info i on 
            t.pipeline_id = i.pipeline_id where t.appName = '{}' AND i.br_status = 'open'
        """.format(module_name)

    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        open_branch_list.append({'br_name': row[1], 'br_status': row[2]})
    return open_branch_list


def get_diff_package_type(project_list):
    package_type_list = []
    if len(project_list) == 1:
        sql = """
            SELECT b.package_type FROM `app_mgt_app_module` m 
        INNER JOIN `app_mgt_app_build` b ON m.app_id = b.app_id
        WHERE m.module_name IN ("{}")
            """.format(project_list[0])
    else:
        sql = """
        SELECT b.package_type FROM `app_mgt_app_module` m 
    INNER JOIN `app_mgt_app_build` b ON m.app_id = b.app_id
    WHERE m.module_name IN {}
        """.format(tuple(project_list))
    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        package_type_list.append(row[0])
    return package_type_list


def get_iter_diff_package_type(branch_name, project_group):
    package_type_list = []
    sql = '''
    SELECT b.package_type FROM `app_mgt_app_module` a 
    LEFT JOIN `app_mgt_app_build` b ON b.module_name = a.module_name
    LEFT JOIN  `iter_mgt_iter_app_info` ai ON ai.appName = a.module_name
    LEFT JOIN `iter_mgt_iter_info` i ON ai.pipeline_id = i.pipeline_id 
    WHERE i.br_name = '{}' AND project_group = '{}'
    '''.format(branch_name, project_group)
    cursor = connection.cursor()
    cursor.execute(sql)
    for row in cursor.fetchall():
        package_type_list.append(row[0])
    return package_type_list


def get_test_data_dev_iter_info():
    iter_info = []
    sql = 'SELECT d.bis_pipeline_id,a.module_name,a.archive_branch_name,d.creator FROM `db_mgt_bis_iter_info` d ' \
          'INNER JOIN `db_mgt_bis_iter_app_info` a ON d.bis_pipeline_id = a.bis_pipeline_id'
    cursor = connection.cursor()
    cursor.execute(sql)
    for item in cursor.fetchall():
        iter_info.append({'bis_pipeline_id': item[0], 'module_name': item[1], 'archive_branch_name': item[2],
                          'creator': item[3]})
    return iter_info


def get_iteration_id_list(app_list):
    sql = '''
        SELECT DISTINCT a.app_module_name,a.br_name_fifteen, c.git_url
        FROM app_br_cache a 
        LEFT JOIN app_mgt_app_module b ON a.app_module_name = b.module_name
        LEFT JOIN app_mgt_app_info c on b.app_id = c.id 
        WHERE a.app_module_name IN ('{app_str}')
        AND a.time_batch = ( SELECT MAX( time_batch ) FROM app_br_cache )
    '''.format(app_str="','".join(app_list))
    cursor = connection.cursor()
    logger.info(sql)
    cursor.execute(sql)
    result = cursor.fetchall()
    br_name_list = []
    if result:
        for item in result:
            if item[1] and item[2]:
                br_list = item[1].split(",")
                for br_name in br_list:
                    br_name_list.append(item[2] + "_" + br_name)
    return list(set(br_name_list))


def get_app_list_by_iteration_id(app_list, iteration_id):
    br_name = iteration_id.split('_')[1]
    
    if app_list is None:
        # 获取所有应用的情况（用于测试左移功能）
        sql = '''
        SELECT DISTINCT b.appName
        FROM iter_mgt_iter_info a 
        LEFT JOIN iter_mgt_iter_app_info b ON a.pipeline_id = b.pipeline_id
        LEFT JOIN app_br_cache c ON b.appName = c.app_module_name
        WHERE a.pipeline_id = '{iteration_id}' 
        AND a.br_status = 'open' 
        AND FIND_IN_SET('{br_name}', c.br_name_fifteen) > 0
        AND b.appName IS NOT NULL
        '''.format(iteration_id=iteration_id, br_name=br_name)
        cursor = connection.cursor()
        logger.info(sql)
        cursor.execute(sql)
        result = cursor.fetchall()
        app_list_result = []
        if result:
            for item in result:
                app_list_result.append({
                    'app_name': item[0],
                    'app_desc': '暂无描述'
                })
        return app_list_result
    else:
        # 原有逻辑：根据指定应用列表过滤
        sql = '''
        SELECT DISTINCT b.appName 
        FROM iter_mgt_iter_info a 
        LEFT JOIN iter_mgt_iter_app_info b ON a.pipeline_id = b.pipeline_id
        LEFT JOIN app_br_cache c ON b.appName = c.app_module_name
        WHERE a.pipeline_id = '{iteration_id}' 
        AND a.br_status = 'open' 
        AND b.appName IN ('{app_str}')
        AND FIND_IN_SET('{br_name}', c.br_name_fifteen) > 0
        '''.format(iteration_id=iteration_id, br_name=br_name, app_str="','".join(app_list))
        cursor = connection.cursor()
        logger.info(sql)
        cursor.execute(sql)
        result = cursor.fetchall()
        app_list_result = []
        if result:
            for item in result:
                app_list_result.append(item[0])
        return app_list_result


def get_test_data_dev_iter_info(iter_id):
    iter_info = []
    sql = 'SELECT d.bis_pipeline_id,a.module_name,a.archive_branch_name,d.creator FROM `db_mgt_bis_iter_info` d ' \
          'INNER JOIN `db_mgt_bis_iter_app_info` a ON d.bis_pipeline_id = a.bis_pipeline_id' \
          ' where d.bis_pipeline_id = "{}"'.format(iter_id)
    cursor = connection.cursor()
    cursor.execute(sql)
    for item in cursor.fetchall():
        iter_info.append({'bis_pipeline_id': item[0], 'module_name': item[1], 'archive_branch_name': item[2],
                          'creator': item[3]})
    return iter_info


def get_app_build_package_type(app_name):
    sql = '''
    SELECT package_type FROM `app_mgt_app_build` WHERE module_name = '{}'
    '''.format(app_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0]


def get_app_list_for_h5_iter(br_name, git_path):
    sql = '''
            SELECT distinct ai.appName FROM iter_mgt_iter_info i
            INNER JOIN iter_mgt_iter_app_info ai ON i.pipeline_id = ai.pipeline_id
            INNER JOIN app_mgt_app_module m ON ai.appName = m.module_name
            INNER JOIN app_mgt_app_info amai ON amai.id = m.app_id
            WHERE i.project_group = 'h5' AND i.br_name = '{}'
          '''.format(br_name)
    if git_path:
        sql += ' AND amai.git_path = "/{}"'.format(git_path)
    cursor = connection.cursor()
    cursor.execute(sql)

    app_list = []
    for item in cursor.fetchall():
        app_list.append(item[0])

    return app_list


def get_app_db_type(app_name):
    # 加domain，需要修改 已完成优化 20231114
    # 因重构，二次优化 20240606
    sql = '''
            SELECT DISTINCT s.db_srv_type 
                FROM db_mgt_app_bind b
            INNER JOIN db_mgt_logic_info li ON b.db_domain_id = li.db_domain_id
            INNER JOIN db_mgt_suite_bind sb ON li.id = sb.db_logic_id
            INNER JOIN db_mgt_info i ON sb.db_info_id = i.id
            INNER JOIN db_mgt_srv s ON i.db_srv_id = s.id
            WHERE b.app_module_name = '{}'
          '''.format(app_name)

    cursor = connection.cursor()
    cursor.execute(sql)

    for item in cursor.fetchall():
        return item[0]
    return None


def get_team_owner_list(pipeline_id):
    sql = '''
            SELECT DISTINCT p.team_owner FROM iter_mgt_iter_info i
            INNER JOIN iter_mgt_iter_app_info ai ON i.pipeline_id = ai.pipeline_id
            INNER JOIN app_mgt_app_module m ON ai.appName = m.module_name
            INNER JOIN team_mgt_app_bind t ON m.app_id = t.app_id
            INNER JOIN tool_mgt_team_info ti ON t.team_id = ti.id
            INNER JOIN tool_mgt_team_info p ON p.id = ti.parent_id
            WHERE i.pipeline_id = '{}';
          '''.format(pipeline_id)

    cursor = connection.cursor()
    cursor.execute(sql)

    team_owner_list = []
    for item in cursor.fetchall():
        team_owner_list.append(item[0])

    return team_owner_list


def get_package_type_by_pipeline_id(pipeline_id):
    sql = '''
            SELECT DISTINCT ab.package_type 
            FROM iter_mgt_iter_info ii 
            INNER JOIN iter_mgt_iter_app_info ai ON ii.pipeline_id = ai.pipeline_id
            INNER JOIN app_mgt_app_build ab on ai.appName = ab.module_name 
            WHERE ii.br_status='open'
            AND ii.pipeline_id = '{}';
          '''.format(pipeline_id)

    cursor = connection.cursor()
    cursor.execute(sql)

    package_type_list = []
    for item in cursor.fetchall():
        package_type_list.append(item[0])

    return package_type_list


def get_iter_limit_count(gitlab_group, iter_limit_type):
    sql = '''
            SELECT COUNT(1) AS iter_limit_count FROM iter_mgt_iter_info t
            WHERE t.project_group = '{}' AND t.is_new = 1 AND t.br_status = 'open' 
            AND t.br_style IN ('{}')
          '''.format(gitlab_group, "', '".join(iter_limit_type.split(',')))

    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    return result[0]


def get_wl_group_id(pipeline_id, guard_name):
    sql = '''
            SELECT wg.id FROM iter_whitelist_group wg
            LEFT JOIN iter_mgt_iter_info i ON i.project_group = wg.wl_group_name
            LEFT JOIN qc_mgt_guard_switch_info q ON q.id = wg.wl_switch_id
            WHERE i.pipeline_id = '{}' AND q.guard_name = '{}'
          '''.format(pipeline_id, guard_name)

    cursor = connection.cursor()
    cursor.execute(sql)
    result = cursor.fetchone()
    if result:
        return result[0]
    else:
        return None


def get_archive_branch_list_by_module_name(module_name, limit_num):
    search_sql = '''
        select archive_a.appName,
               archive_i.pipeline_id,
               archive_i.br_name,
               archive_i.br_start_date,
               archive_i.br_end_date,
               archive_a.proposer
        from iter_mgt_iter_info archive_i
            inner join iter_mgt_iter_app_info archive_a on archive_a.pipeline_id = archive_i.pipeline_id
        where 1=1
            and archive_i.br_status = 'close'
            and archive_a.sys_status = '已归档'
            and archive_a.appName = '{}'
            order by archive_i.br_end_date desc
        limit {}
        ;
        '''.format(module_name, limit_num)
    cursor = connection.cursor()
    cursor.execute(search_sql)
    return cursor


def get_biz_flow_info_by_iteration_id(iteration_id):
    sql = '''
             SELECT GROUP_CONCAT(n.module_name) AS app_name, n.biz_code, n.biz_flow_name, i.br_name FROM biz_test_app_need n 
             INNER JOIN iter_mgt_iter_app_info ai ON n.module_name = ai.appName
             INNER JOIN iter_mgt_iter_info i ON ai.pipeline_id = i.pipeline_id
             WHERE ai.pipeline_id = '{}' AND n.need_auto_test = 1
             GROUP BY n.biz_code, n.biz_flow_name, i.br_name
          '''.format(iteration_id)

    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor.fetchall()


def get_last_build_time_by_app_and_branch(app_branch_tuple):
    if len(app_branch_tuple) == 1:
        app_branch_tuple = '(("' + app_branch_tuple[0][0] + '","' + app_branch_tuple[0][1] + '"))'
    sql = '''
            SELECT MAX(create_time), module_name, lib_repo_branch 
            FROM product_mgt_product_info 
            WHERE (module_name, lib_repo_branch) IN {}
            GROUP BY module_name, lib_repo_branch
          '''.format(app_branch_tuple)

    cursor = connection.cursor()
    cursor.execute(sql)

    result = []
    for item in cursor.fetchall():
        result.append({'build_time': item[0], 'app_name': item[1], 'app_deploy_branch': item[2]})

    return result
