import datetime
import json
import re

from rest_framework import viewsets, serializers
from rest_framework.response import Response

from app_mgt import app_mgt_ser
from app_mgt.models import TemplateInfo, AppBuild
from rest_framework.decorators import action
from iter_mgt.iter_mgt_ser import get_product_info
from iter_mgt.models import Branches, BranchIncludeSys, PublishApplicationSQL, PublishApplicationCC, PublishOrder, \
    PublishApplicationConfirmation, Artifactinfo, \
    IterativeAppComponentRelations
from iter_mgt.plan_ser import BranchesSerializer, ApplySerializer, get_all_app_by_gitpath
from public.generate_content import generate_apply_email_content
from public.generate_content import h5_generate_apply_email_content
from public.md5_cmd import get_random_str
from public.send_email import SendMail
from public.ssh_cnt import SSHConnectionManager
from publish.models import PackageDownload
from publish.publish_ser import get_prod_deploy_result
from spider.settings import ConsPythonIns, ConsRequestKeys, <PERSON><PERSON><PERSON><PERSON>ult, logger
from task_mgt.external_service import ExternalService
from task_mgt.models import H5D<PERSON><PERSON><PERSON>esult
from task_mgt.test_publish_task import TestPublishThread
from tool_mgt.models import TeamInfo
from user.models import ActionRecord


def send_mail(team, duedate, branch_name, applicant, pipeline_id, sql_address, description, notice, app_name_list,
              mail_to,
              confirm_url, schedules="", file_ccms_config="", suite_name="", warn_info=""):
    """发送邮件通知"""
    content = generate_apply_email_content(
        team=team, duedate=duedate, branch_name=branch_name, applicant=applicant,
        description=description, sql_address=sql_address, schedule=schedules,
        configure=file_ccms_config, notice=notice, app_name_list=app_name_list, confirm_url=confirm_url,
        suite_name=suite_name, warn_info=warn_info
    )
    subject = '{}上线申请确认'.format(pipeline_id)

    mail_builder = SendMail()
    mail_builder.set_subject(subject)
    mail_builder.set_content(content)
    mail_builder.set_to(mail_to)
    mail_builder.send()


def h5_send_mail(team, duedate, branch_name, applicant, pipeline_id, sql_address, description, notice, app_name_list,
                 mail_to,
                 confirm_url, schedules="", file_ccms_config="", suite_name="", fund_end_ver='', piggy_end_ver='',
                 appversion='', fundh5ZipVersion='', piggyh5ZipVersion='', h5_resource_branch='', prod_content=''):
    """发送邮件通知"""
    content = h5_generate_apply_email_content(
        team=team, duedate=duedate, branch_name=branch_name, applicant=applicant,
        description=description, sql_address=sql_address, schedule=schedules,
        configure=file_ccms_config, notice=notice, app_name_list=app_name_list, confirm_url=confirm_url,
        suite_name=suite_name, fund_end_ver=fund_end_ver, piggy_end_ver=piggy_end_ver, appversion=appversion,
        fundh5ZipVersion=fundh5ZipVersion, piggyh5ZipVersion=piggyh5ZipVersion, h5_resource_branch=h5_resource_branch,
        prod_content=prod_content
    )
    subject = '{}上线申请确认'.format(pipeline_id)

    mail_builder = SendMail()
    mail_builder.set_subject(subject)
    mail_builder.set_content(content)
    mail_builder.set_to(mail_to)
    mail_builder.send()


def send_quick_sql_mail(team, duedate, branch_name, applicant, pipeline_id, sql_address,
                        description, notice, app_name_list, mail_to, cc_list, confirm_url,
                        schedules="", configure="", suite_name="", warn_info=""):
    """发送邮件通知"""
    content = generate_apply_email_content(
        team=team, duedate=duedate, branch_name=branch_name, applicant=applicant,
        description=description, sql_address=sql_address, schedule=schedules,
        configure=configure, notice=notice, app_name_list=app_name_list, confirm_url=confirm_url,
        suite_name=suite_name, warn_info=warn_info
    )
    subject = '{}快速SQL上线申请'.format(pipeline_id)

    try:
        mail_builder = SendMail()
        mail_builder.set_subject(subject)
        mail_builder.set_content(content)
        mail_builder.set_to(mail_to)
        mail_builder.set_cc(cc_list)
        mail_builder.send()
    except Exception as e:
        logger.error("send_quick_sql_mail error:{}".format(str(e)))
        raise Exception("send_quick_sql_mail error:{}".format(str(e)))


class ApplyCheckViewSet(viewsets.ModelViewSet):
    queryset = Branches.objects
    serializer_class = serializers.Serializer

    def create(self, request, *args, **kwargs):
        serializer = ApplySerializer(data=request.data)
        if not serializer.is_valid():
            return Response(data=ApiResult.failed_dict('参数不正确'))
        pipeline_id = request.data.get('pipeline_id')
        in_app_name_list = request.data.get('app_name_list', 'empty')
        env = request.data.get('env')
        logger.info(pipeline_id)
        logger.info(in_app_name_list)
        logger.info(env)

        if isinstance(self.request.user, str):
            operator = self.request.user
        else:
            operator = self.request.user.username
        business_name = 'publish_apply_check'
        app_list = []
        for app in BranchIncludeSys.objects.filter(pipeline_id=pipeline_id):
            obj = Artifactinfo.objects.filter(appName=app.appName, need_online=1)
            if obj:
                app_list.append(app.appName)

        if in_app_name_list:
            print('>>>>「apply_check app_name_list」入参{},覆盖查询结果{}'.format(in_app_name_list, app_list))
            if in_app_name_list == 'empty':
                app_list = []
            else:
                if isinstance(in_app_name_list, list):
                    app_list = in_app_name_list
                else:
                    app_list = str(in_app_name_list).split(",")
        else:
            app_list = []
            print('>>>>「apply_check app_name_list」未传入')

        params = {
            'name': pipeline_id + '_' + env + '_' + 'check',
            'business_name': business_name,
            'iteration_id': pipeline_id,
            'app_name_list': app_list,
            'env': env
        }
        rt_code, sid = ExternalService(business_name, operator, params).call_service()
        if rt_code == 0:
            return Response(data=ApiResult.success_dict(data={'sid': sid}, msg="开始执行"))
        else:
            return Response(data=ApiResult.failed_dict(msg="执行异常"))


class ApplyViewSet(viewsets.ModelViewSet):
    queryset = Branches.objects
    serializer_class = BranchesSerializer

    @staticmethod
    def is_exist(pipeline_id):
        br_info = Branches.objects.filter(pipeline_id=pipeline_id, br_status='open')
        if not br_info:
            return False
        return True

    def get_apply_iteration_info(self, pipeline_id):
        br_name = pipeline_id.split('_')[1]
        app_name_list = []
        apps = BranchIncludeSys.get_online_apps(pipeline_id)
        for app in apps:
            app_name_list.append(app.appName)

        pipeline_info = Branches.objects.get(pipeline_id=pipeline_id)
        notice = pipeline_info.releaseNotic if pipeline_info.releaseNotic else ''
        description = pipeline_info.description if pipeline_info.description else ''
        file_ccms_config = pipeline_info.file_ccms_config if pipeline_info.file_ccms_config else ''
        schedule = pipeline_info.schedule if pipeline_info.schedule else ''
        duedate = pipeline_info.duedate if pipeline_info.duedate else ''
        sql_content = '无'
        try:
            sql_obj = PublishApplicationSQL.objects.get(business_id=pipeline_id)
            sql_content = sql_obj.sql_content
        except PublishApplicationSQL.DoesNotExist:
            pass
        receivers = list(PublishApplicationCC.objects.filter(businessID=pipeline_id).values_list('email', flat=True))
        member = ';'.join(receivers).replace('@howbuy.com', '')

        operator = ""
        if self.request.user:
            if isinstance(self.request.user, str):
                operator = self.request.user
            else:
                operator = self.request.user.username

        return br_name, app_name_list, sql_content, notice, member, operator, description, file_ccms_config, schedule, duedate

    @staticmethod
    def check_iter_apply_already_confirm_today(pipeline_id):
        today_date = datetime.datetime.today().date()
        objs = PublishApplicationConfirmation.objects.filter(iteration_id=pipeline_id, affirm_time__gt=today_date,
                                                             status="success")
        if objs:
            return True
        return False

    @staticmethod
    def is_apply_running(check_cmd):
        cmd = "pgrep -f '{}'".format(check_cmd)
        with SSHConnectionManager() as ssh:
            stat, rst, err = ssh.exec_ssh(cmd)
        return stat

    def create(self, request, *args, **kwargs):
        serializer = ApplySerializer(data=request.data)
        if not serializer.is_valid():
            return Response(data=ApiResult.failed_dict('参数不正确'))
        pipeline_id = request.data.get('pipeline_id')
        in_app_name_list = request.data.get('app_name_list', 'empty')
        send_email = request.data.get('send_email')
        env = request.data.get('env')
        business_name = 'publish_apply'
        try:
            br_name, app_name, sql_content, notice, member, operator, description, file_ccms_config, schedule, duedate = self.get_apply_iteration_info(
                pipeline_id)
            if in_app_name_list:
                print('>>>>apply「app_name_list」入参{},覆盖查询结果{}'.format(in_app_name_list, app_name))
                if in_app_name_list == 'empty':
                    app_name = []
                else:
                    app_name = str(in_app_name_list).split(",")
            else:
                app_name = []
                print('>>>>apply「app_name_list」未传入')

        except Exception as err:
            return Response(data=ApiResult.failed_dict('获取分支信息失败: {}'.format(str(err))))
        params = {
            'name': pipeline_id + '_' + env + '_' + 'apply',
            'business_name': business_name,
            'iteration_id': pipeline_id,
            'jenkins_job_name': pipeline_id.split('_')[0] + '_发布申请',
            'branch_name': br_name,
            'app_name_list': app_name,
            'env': env,
            'applicant': operator,
            'sql_address': sql_content,
            'notice': notice,
            'description': description,
            'duedate': duedate,
            'file_ccms_config': file_ccms_config,
            'schedule': schedule,
            'send_email': send_email,
            'mail_to': member,
        }
        rt_code, sid = ExternalService(business_name, operator, params).call_service()
        if rt_code == 0:
            return Response(data=ApiResult.success_dict(data={'sid': sid}, msg="开始执行"))
        else:
            return Response(data=ApiResult.failed_dict(msg="执行异常"))

    @action(methods=['post'], detail=False, url_path='cancel_apply')
    def cancel_prod_apply(self, request):
        """
        查询是否可取消产线申请
        :params: pipeline_id  迭代id
        """
        pipeline_id = request.data.get('pipeline_id')
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username
        if not user:
            return Response(data=ApiResult.failed_dict('用户未登录'))
        deploy_result = get_prod_deploy_result(pipeline_id)
        if deploy_result:
            team_leaders = list(TeamInfo.objects.values_list('team_owner', flat=True).distinct())
            if user not in team_leaders:
                return Response(data=ApiResult.failed_dict('当前迭代存在应用发布过产线，只有负责人才能取消发布'))
        return Response(data=ApiResult.success_dict('若取消上线，后续上线版本不会自带本版本的代码，请评估确认'))

    def list(self, request, *args, **kwargs):
        """
        查询发布确认状态
        :params: pipeline_id  迭代id
        """
        pipeline_id = request.GET.get('pipeline_id', '')
        if self.check_iter_apply_already_confirm_today(pipeline_id):
            return Response(data=ApiResult.failed_dict(data=[], msg="该迭代今日已确认"))
        else:
            return Response(data=ApiResult.success_dict(''))

    def delete(self, request, *args, **kwargs):
        pipeline_id = request.data.get(ConsRequestKeys.pipeline_id)
        if not self.is_exist(pipeline_id):
            return Response(data=ApiResult.failed_dict('迭代{}不存在'.format(pipeline_id)))
        app_list = BranchIncludeSys.objects.filter(pipeline_id=pipeline_id, git_repo_version__isnull=False).values_list(
            'appName', flat=True)
        BranchIncludeSys.objects.filter(pipeline_id=pipeline_id).update(sys_status="测试中")

        app_info = AppBuild.objects.filter(
            module_name__in=BranchIncludeSys.objects.filter(pipeline_id=pipeline_id).values_list('appName')).values(
            'module_name', 'package_type')
        logger.info(app_info)

        for app in app_info:
            if app['package_type'] in ('ios'):
                com_app_info = IterativeAppComponentRelations.objects.filter(pipeline_id=pipeline_id,
                                                                             app_name=app['module_name']). \
                    values("component_app_name", "dep_pipeline_id")
                for com_app in com_app_info:
                    BranchIncludeSys.objects.filter(pipeline_id=com_app['dep_pipeline_id'],
                                                    appName=com_app['component_app_name']).update(sys_status="测试中")
        for app in app_list:
            obj = PublishOrder.objects.filter(pipeline_id=pipeline_id, env='prod', appName=app)
            if obj:
                tmp = obj.last()
                tmp.status = '取消'
                tmp.save()
        today_date = datetime.datetime.today().date()
        PublishApplicationConfirmation.objects.filter(iteration_id=pipeline_id, affirm_time__gt=today_date,
                                                      status="success").update(
            status="cancel")
        return Response(data=ApiResult.success_dict('取消产线申请成功'))


class ApplyNoticeViewSet(viewsets.ModelViewSet):
    queryset = Branches.objects
    serializer_class = BranchesSerializer

    @staticmethod
    def is_exist(pipeline_id):
        br_info = Branches.objects.filter(pipeline_id=pipeline_id, br_status='open')
        if not br_info:
            return False
        return True

    @staticmethod
    def check_iter_apply_already_confirm_today(pipeline_id):
        today_date = datetime.datetime.today().date()
        objs = PublishApplicationConfirmation.objects.filter(iteration_id=pipeline_id, affirm_time__gt=today_date,
                                                             status="success")
        if objs:
            return True
        return False

    def list(self, request, *args, **kwargs):
        logger.info(request.GET)
        env_name = request.GET.get('env_name', '')
        pipeline_id = request.GET.get('pipeline_id', '')

        cmd = 'cd /home/<USER>/logs/spider && ls -t {}_{}* | head -n1 '.format(pipeline_id, env_name)
        logger.info(cmd)
        with SSHConnectionManager() as ssh:
            stdin, stdout, err = ssh.exec_ssh_raw(cmd)
            file_list = stdout.readlines()
            logger.info(file_list)
        if file_list:
            file_name = file_list[0].strip()
            logger.info(file_name)
        else:
            logger.info("未找到日志文件")
            return Response(data=ApiResult.failed_dict(msg="未找到日志文件"))

        try:
            cmd = 'tail -n 50 /home/<USER>/logs/spider/{}'.format(file_name)
            with SSHConnectionManager() as ssh:
                stdin, stdout, err = ssh.exec_ssh_raw(cmd)
                code = stdout.channel.recv_exit_status()
                data = stdout.readlines()
                # logger.info(data)
        except Exception as err:
            logger.error(str(err))
            return Response(data=ApiResult.failed_dict(data=[], msg=""))

        if not code and len(data) > 0:
            if "调用结束" in data[-1]:
                return Response(data=ApiResult.success_dict(data=data, msg="end"))
            else:
                return Response(data=ApiResult.success_dict(data=data, msg=""))
        else:
            return Response(data=ApiResult.failed_dict(data=data, msg=""))

    def create(self, request, *args, **kwargs):
        """
        发送二次确认邮件
        :params: pipeline_id  迭代id
        :params: receiver 确认人
        :params: domain 当前域
        :params: suite_name 环境
        """
        pipeline_id = request.data.get(ConsRequestKeys.pipeline_id)
        receiver = request.data.get('receiver', '')
        domain = request.data.get('domain', '')
        if domain.endswith("/"):
            domain = domain[:-1]
        suite_name = request.data.get('suite_name', '')
        warn_info = request.data.get('warn_info', '')

        if not self.is_exist(pipeline_id):
            return Response(data=ApiResult.failed_dict('迭代{}不存在'.format(pipeline_id)))

        if not receiver:
            return Response(data=ApiResult.failed_dict('确认人未填写'))

        if not domain:
            return Response(data=ApiResult.failed_dict('域名未收到'))

        # 获取迭代信息
        team = pipeline_id.split('_')[0]
        branch_name = pipeline_id.split('_')[1]
        if isinstance(request.user, str):
            applicant = request.user
        else:
            applicant = request.user.username
        sql_address = ''
        try:
            sql_obj = PublishApplicationSQL.objects.get(business_id=pipeline_id)
            sql_address = sql_obj.sql_content
        except PublishApplicationSQL.DoesNotExist:
            pass

        pipeline_info = Branches.objects.get(pipeline_id=pipeline_id)
        notice = pipeline_info.releaseNotic if pipeline_info.releaseNotic else ''
        description = pipeline_info.description if pipeline_info.description else ''
        duedate = pipeline_info.duedate if pipeline_info.duedate else ''
        schedules = pipeline_info.schedule if pipeline_info.schedule else ''
        file_ccms_config = pipeline_info.file_ccms_config if pipeline_info.file_ccms_config else ''

        app_name = []
        apps = BranchIncludeSys.get_online_apps(pipeline_id)
        for app in apps:
            app_name.append(app.appName)
        app_name_list = ','.join(app_name)
        module_name_list = []
        apps_name_list = []
        for app in BranchIncludeSys.get_online_apps(pipeline_id):
            apps_name_list.append(app.appName)
            if app.git_path:
                git_path = app.git_path[app.git_path.index('/'):]
                for row in get_all_app_by_gitpath(git_path):
                    module_name_list.append(row[0])
        warn_info_list = list(set(module_name_list).difference(set(app_name_list)))
        if warn_info_list:
            warn_info = '<span style="color: red">(注意：代码仓库下有拉取后删除应用,若有代码改动会归档到主干)</span>'
        if self.check_iter_apply_already_confirm_today(pipeline_id):
            return Response(data=ApiResult.success_dict(msg="该迭代今日已确认"))

        md5_str = get_random_str()
        md5_email = PublishApplicationConfirmation.objects.create(
            md5_id=md5_str,
            iteration_id=pipeline_id,
            create_time=datetime.datetime.now(),
            create_user=applicant,
            receiver=receiver,
            suite_name=suite_name
        )
        md5_email.save()
        for mail_to in receiver.split(','):
            confirm_url = '<a href="{}/git_components/pipeline_page?md5Str={}">确认产线发布申请</a>'.format(
                domain, md5_str)
            send_mail(team, duedate, branch_name, applicant, pipeline_id, sql_address, description, notice,
                      app_name_list, mail_to, confirm_url, schedules=schedules, file_ccms_config=file_ccms_config,
                      suite_name=suite_name, warn_info=warn_info)

        return Response(data=ApiResult.success_dict('产线发布确认邮件已发送'))

    def put(self, request, *args, **kwargs):
        logger.info(request.data)
        md5_str = request.data.get('md5_str', '')
        if isinstance(request.user, str):
            confirm_user = request.user
        else:
            confirm_user = request.user.username
        try:
            obj = PublishApplicationConfirmation.objects.get(md5_id=md5_str)
            if confirm_user not in obj.receiver:
                return Response(data=ApiResult.failed_dict(msg='没有权限'))
            if obj.status == 'success':
                return Response(data=ApiResult.failed_dict(msg='发布请求已被{}确认'.format(obj.assertor)))
            if self.check_iter_apply_already_confirm_today(obj.iteration_id):
                return Response(data=ApiResult.failed_dict(data=[], msg="该迭代今日已确认"))

            PublishApplicationConfirmation.objects.filter(md5_id=md5_str).update(
                assertor=confirm_user, affirm_time=datetime.datetime.now(), status="success")
            return Response(data=ApiResult.success_dict('发布确认成功'))
        except Exception as err:
            logger.error(str(err))
            return Response(data=ApiResult.failed_dict(msg=str(err)))


class H5ApplyNoticeViewSet(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer

    @staticmethod
    def is_exist(pipeline_id):
        br_info = Branches.objects.filter(pipeline_id=pipeline_id, br_status='open')
        if not br_info:
            return False
        return True

    @staticmethod
    def check_iter_apply_already_confirm_today(pipeline_id, suite_name):
        today_date = datetime.datetime.today().date()
        objs = PublishApplicationConfirmation.objects.filter(iteration_id=pipeline_id, affirm_time__gt=today_date,
                                                             status="success",
                                                             suite_name=suite_name)
        if objs:
            return True
        return False

    def create(self, request, *args, **kwargs):
        """
        h5发送二次确认邮件
        :params: pipeline_id  迭代id
        :params: receiver 确认人
        :params: domain 当前域
        :params: suite_name 环境
        """
        pipeline_id = request.data.get(ConsRequestKeys.pipeline_id)
        receiver = request.data.get('receiver', '')
        domain = request.data.get('domain', '')
        if domain.endswith("/"):
            domain = domain[:-1]
        suite_name = request.data.get('suite_name', '')
        app_name_list = request.data.get('app_name_list', '')
        fund_end_ver = request.data.get('fund_end_ver', '')
        piggy_end_ver = request.data.get('piggy_end_ver', '')
        appversion = request.data.get('Version', '')
        branch = request.data.get('branch', '')
        fundh5ZipVersion = request.data.get('fundh5ZipVersion', '')
        piggyh5ZipVersion = request.data.get('piggyh5ZipVersion', '')
        branch_type = request.data.get('branch_type', '')
        prod_content = request.data.get('prod_content', '')

        # 因为tag也会申请，所以允许关闭的分支申请 by帅 20210531
        # if not self.is_exist(pipeline_id):
        #     return Response(data=ApiResult.failed_dict('迭代{}不存在'.format(pipeline_id)))
        if fundh5ZipVersion and fundh5ZipVersion != 'undefined':
            h5ZipVersion = fundh5ZipVersion  # 传到二次确认table里面
            if branch_type != 'prod':
                Downloadinfo = PackageDownload.objects.filter(
                    file_name=fundh5ZipVersion + '.zip').last()  # get当query有多个元素的时候会报错
                h5_resource_branch = Downloadinfo.mid.br_name
                h5_app_name = Downloadinfo.mid.app_name
            else:
                Downloadinfo = PackageDownload.objects.get(file_name=fundh5ZipVersion + '.zip')
                h5_resource_branch = Downloadinfo.mid.br_name
                h5_app_name = Downloadinfo.mid.app_name
        elif piggyh5ZipVersion and piggyh5ZipVersion != 'undefined':
            h5ZipVersion = piggyh5ZipVersion
            Downloadinfo = PackageDownload.objects.filter(file_name=piggyh5ZipVersion + '.zip').last()
            h5_resource_branch = Downloadinfo.mid.br_name
            h5_app_name = Downloadinfo.mid.app_name
            if piggyh5ZipVersion == 'undefined':  # 避免把undefined传到邮件里面
                piggyh5ZipVersion = ''
        else:
            h5ZipVersion = ''
            h5_resource_branch = ''
            fundh5ZipVersion = ''
            piggyh5ZipVersion = ''

        if fundh5ZipVersion == 'undefined':  # 避免把undefined传到邮件里面
            fundh5ZipVersion = ''
        elif piggyh5ZipVersion == 'undefined':
            piggyh5ZipVersion = ''

        if h5_resource_branch:
            Branchesobj = Branches.objects.get(br_name=h5_resource_branch)
            h5_pipeline_id = Branchesobj.pipeline_id
            BranchIncludeSysobj = BranchIncludeSys.objects.get(pipeline_id=h5_pipeline_id, appName=h5_app_name)
            Sysstatus = BranchIncludeSysobj.sys_status
            if Sysstatus == '已归档':
                branch_type = 'tag'
            else:
                branch_type = 'branch'
            print(Sysstatus)

        if fund_end_ver or piggy_end_ver:  # 传给二次确认弹框的
            apph5version = fund_end_ver + ',' + piggy_end_ver
        else:
            apph5version = appversion

        if not receiver:
            return Response(data=ApiResult.failed_dict('确认人未填写'))

        if not domain:
            return Response(data=ApiResult.failed_dict('域名未收到'))

        # 获取迭代信息
        team = pipeline_id.split('_')[0]
        branch_name = branch  # 产线申请邮件里面的分支一直是组名而不是分支民
        # branch_name = pipeline_id.split('_')[1]
        applicant = request.user

        sql_address = ''
        try:
            sql_obj = PublishApplicationSQL.objects.get(business_id=pipeline_id)
            sql_address = sql_obj.sql_content
        except PublishApplicationSQL.DoesNotExist:
            pass

        pipeline_info = Branches.objects.get(pipeline_id=pipeline_id)
        notice = pipeline_info.releaseNotic if pipeline_info.releaseNotic else ''
        description = pipeline_info.description if pipeline_info.description else ''
        duedate = pipeline_info.duedate.replace('T', ' ').replace('Z', '').split('.')[
            0] if pipeline_info.duedate else ''
        schedules = pipeline_info.schedule if pipeline_info.schedule else ''
        file_ccms_config = pipeline_info.file_ccms_config if pipeline_info.file_ccms_config else ''

        # app_name = []
        # apps = BranchIncludeSys.get_online_apps(pipeline_id)
        # for app in apps:
        #     app_name.append(app.appName)
        # app_name_list = ','.join(app_name)

        if self.check_iter_apply_already_confirm_today(pipeline_id, suite_name):
            return Response(data=ApiResult.success_dict(msg="该迭代今日已确认"))

        md5_str = get_random_str()
        md5_email = PublishApplicationConfirmation.objects.create(
            md5_id=md5_str,
            iteration_id=pipeline_id,
            create_time=datetime.datetime.now(),
            receiver=receiver,
            suite_name=suite_name
        )
        md5_email.save()
        for mail_to in receiver.split(','):
            comfirm_param = 'md5Str={}&suite_name={}&comfirm_status=true&branch_name={}&apph5version={}' \
                            '&h5_resource_branch={}&h5ZipVersion={}&branch_type={}'.format(
                md5_str, suite_name, pipeline_id, apph5version, h5_resource_branch, h5ZipVersion, branch_type)
            confirm_url = '<a href="{}/git_components/mobile_pipeline?{}">确认产线发布申请</a>'.format(
                domain, comfirm_param)
            h5_send_mail(team, duedate, branch_name, applicant, pipeline_id, sql_address, description, notice,
                         app_name_list, mail_to, confirm_url, schedules=schedules, file_ccms_config=file_ccms_config,
                         suite_name=suite_name, fund_end_ver=fund_end_ver, piggy_end_ver=piggy_end_ver,
                         appversion=appversion, fundh5ZipVersion=fundh5ZipVersion, piggyh5ZipVersion=piggyh5ZipVersion,
                         h5_resource_branch=h5_resource_branch, prod_content=prod_content)

        return Response(data=ApiResult.success_dict('产线发布确认邮件已发送'))

    def put(self, request, *args, **kwargs):
        logger.info(request.data)
        md5_str = request.data.get('md5_str', '')
        if isinstance(request.user, str):
            confirm_user = request.user
        else:
            confirm_user = request.user.username
        try:
            obj = PublishApplicationConfirmation.objects.get(md5_id=md5_str)
            if confirm_user not in obj.receiver:
                return Response(data=ApiResult.failed_dict(msg='没有权限'))
            if obj.status == 'success':
                return Response(data=ApiResult.failed_dict(msg='发布请求已被{}确认'.format(obj.assertor)))
            if self.check_iter_apply_already_confirm_today(obj.iteration_id, obj.suite_name):
                return Response(data=ApiResult.failed_dict(data=[], msg="该迭代今日已确认"))

            PublishApplicationConfirmation.objects.filter(md5_id=md5_str).update(
                assertor=confirm_user, affirm_time=datetime.datetime.now(), status="success")
            return Response(data=ApiResult.success_dict('发布确认成功'))
        except Exception as err:
            logger.error(str(err))
            return Response(data=ApiResult.failed_dict(msg=str(err)))


class ProductReposApi(viewsets.ViewSet):
    """查询制品库信息"""

    def list(self, request, *args, **kwargs):
        status = request.GET.get('status')
        page_data = json.loads(request.GET.get('page_data'))
        app_name_input = request.GET.get('app_name')
        br_name = request.GET.get('br_name')
        # print(page_data)
        # logger.info(status)
        product_info_list = []
        app_name_list = []
        br_name_list = []
        page_list = {}
        data_list, page_total, total_data_list = get_product_info(status, page_data, app_name_input, br_name)
        page_list['page_total'] = page_total
        close_data_list = list(filter(lambda x: x['br_status'] == 'close', data_list))
        close_data_list = sorted(close_data_list, key=lambda x: x['br_end_date'], reverse=True)
        open_data_list = list(filter(lambda x: x['br_status'] == 'open', data_list))
        open_data_list = sorted(open_data_list, key=lambda x: x['pipeline_id'], reverse=True)
        print(close_data_list)
        print(open_data_list)
        data_list = close_data_list + open_data_list
        for row in data_list:
            pipeline_id = row['pipeline_id']
            br_name = row['br_name']
            br_end_date = row['br_end_date']
            app_name = row['appName']
            git_repo_version = row['git_repo_version']
            online_br_name = row['online_br_name']
            package_type = row['packageType']
            product_info_list.append({"app_name": app_name,
                                      "br_name": br_name,
                                      "online_br_name": online_br_name,
                                      "repos_version": git_repo_version,
                                      "iteration_id": pipeline_id,
                                      "br_end_date": br_end_date,
                                      "packageType": package_type})

        if not app_name_input:
            br_name_list = []
        else:
            for row in total_data_list:
                br_name_list.append(row['br_name'])

        return Response(data=ApiResult.success_dict(
            msg="查询制品库信息成功",
            data={
                "data_list": product_info_list,
                "app_name_list": app_name_list,
                "br_name_list": br_name_list,
                "page_list": page_list,
            }
        ))


class ProductPublishConfirmApi(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer

    def list(self, request, *args, **kwargs):
        md5_str = request.GET.get('md5_str')
        md5_info = PublishApplicationConfirmation.objects.filter(md5_id=md5_str).first()
        pipeline_id = json.loads(md5_info.iteration_id.replace("'", '"'))[0]
        br_info = Branches.objects.filter(pipeline_id=pipeline_id).first()
        if md5_info.status == "success":
            return Response(data=ApiResult.failed_dict(msg="该上线申请已经被{}确认".format(md5_info.assertor)))
        return Response(data=ApiResult.success_dict(msg="查询迭代id成功",
                                                    data={"iteration_id_list": md5_info.iteration_id,
                                                          "br_status": br_info.br_status}))

    def put(self, request, *args, **kwargs):
        logger.info(request.data)
        try:
            PublishApplicationConfirmation.objects.filter(md5_id=request.data["md5_str"]).update(
                assertor=str(request.user), affirm_time=datetime.datetime.now(), status=request.data["status"])
        except Exception as e:
            logger.error(str(e))
            return Response(data=ApiResult.failed_dict(msg=str(e)))
        return Response(data=ApiResult.success_dict('更新操作人'))

    def create(self, request, *args, **kwargs):
        """
        发送二次确认邮件
        :params: pipeline_id  迭代id
        :params: receiver 确认人
        :params: domain 当前域
        """
        receiver = request.data.get('assert_receiver', '')
        domain = request.data.get('domain', '')
        if domain.endswith("/"):
            domain = domain[:-1]
        env = request.data.get('env', '')
        if not receiver:
            return Response(data=ApiResult.failed_dict('确认人未填写'))
        if not domain:
            return Response(data=ApiResult.failed_dict('域名未收到'))
        feature = request.data.get('feature', '')
        date = request.data.get('date', '')
        time = request.data.get('time', '')
        if re.match('\d{4}-\d{2}-\d{2}T', date):
            date_diff = datetime.datetime.strptime(date, '%Y-%m-%dT%H:%M:%S.%fZ') + datetime.timedelta(
                hours=8)
        elif re.match('\d{4}-\d{2}-\d{2}', date):
            date_diff = datetime.datetime.strptime(date, '%Y-%m-%d')
        else:
            date_diff = datetime.datetime.now()
        duedate = date_diff.strftime('%Y-%m-%d') + " " + time
        sql_content = request.data.get('sql_content', '')
        notice = request.data.get('notice', '')
        iteration_id_list = []
        repos_info = request.data["repos_info"]
        request.data.pop("repos_info")
        schedules = request.data["schedule"]
        file_ccms_config = request.data["schedule"]
        applicant = request.user
        app_name_list = []
        team_list = []
        branch_name_list = []
        for row in repos_info:
            app_name_list.append(row["app_name"])
            if row["iteration_id"] in iteration_id_list:
                continue
            iteration_id_list.append(row["iteration_id"])
            team_list.append(row["iteration_id"].split('_')[0])
            branch_name_list.append(row["br_name"])

        md5_str = get_random_str()
        md5_email = PublishApplicationConfirmation.objects.create(
            md5_id=md5_str,
            iteration_id=iteration_id_list,
            create_time=datetime.datetime.now(),
            receiver=receiver,
            suite_name=env
        )
        md5_email.save()
        for mail_to in receiver.split(','):
            confirm_url = '<a href="{}/git_components/app_apply?md5Str={}">确认并执行产线发布申请</a>'.format(
                domain, md5_str)
            send_mail(",".join(team_list), duedate, ",".join(branch_name_list), applicant, ",".join(iteration_id_list),
                      sql_content, feature, notice, app_name_list, mail_to, confirm_url, schedules=schedules,
                      file_ccms_config=file_ccms_config)

        return Response(data=ApiResult.success_dict('产线申请确认邮件已发送'))


class ApplyCheckEmailViewSet(viewsets.ViewSet):
    """查询当天是否已发送确认人邮件"""

    def list(self, request, *args, **kwargs):
        pipeline_id = request.GET.get('pipeline_id', '')
        today_date = datetime.datetime.today().date()
        objs = PublishApplicationConfirmation.objects.filter(iteration_id=pipeline_id, create_time__gt=today_date)
        logger.info(objs)
        if objs:
            return Response(data=ApiResult.success_dict(data=True))
        return Response(data=ApiResult.success_dict(data=False))


class TestPublishApi(viewsets.ViewSet):
    """
    测试环境发布 zt@2020-08-13
    """

    def list(self, request):
        sid = request.GET.get('sid')
        if sid:
            test_publish_thread = TestPublishThread(1, "TestPublishThread-{}".format(sid), 10, sid)
            test_publish_thread.start()
            # test_publish_thread.join()

        else:
            print(">>>>「测试环境初始化」sid为空")

        print('>>>>「测试环境初始化」多线程启动『{}』'.format(sid))

        return Response(data=ApiResult.success_dict(
            msg="「测试环境初始化」执行成功",
            data={"sid": sid}
        ))


class TestPublishAll(viewsets.ViewSet):
    authentication_classes = []
    """
    测试环境「全量」一键初始化 zt@2020-08-15
    """

    def create(self, request, *args, **kwargs):
        new_build_number = TestPublishThread.test_publish_all()

        return Response(data=ApiResult.success_dict(
            msg="测试环境「全量」一键初始化，执行成功",
            data={"new_build_number": new_build_number}
        ))


class TestPublishList(viewsets.ViewSet):
    authentication_classes = []
    """
    根据测试模板获取所有的测试应用列表 zt@2020-08-17
    """

    def list(self, request):
        test_template = request.GET.get('test_template')
        if not test_template:
            test_template = 'default'

        default_suite_name, pipeline_app_dict, tms_app_dict, tp_app_dict, middleware_app_dict = \
            TestPublishThread.test_publish_list(test_template)

        return Response(data=ApiResult.success_dict(
            msg="获取「测试应用列表」列表成功",
            data={
                "default_suite_name": default_suite_name,
                "pipeline_app_dict": pipeline_app_dict,
                "tms_app_dict": tms_app_dict,
                "tp_app_dict": tp_app_dict,
                "middleware_app_dict": middleware_app_dict,
            }
        ))

    def create(self, request, *args, **kwargs):
        pass

    def update(self, request, *args, **kwargs):
        pass

    def destroy(self, request, *args, **kwargs):
        pass


class TestPublishDict(viewsets.ViewSet):
    authentication_classes = []
    """
    根据测试模板获取所有的测试应用字典 zt@2020-08-17
    """

    def list(self, request):
        test_template = request.GET.get('test_template')
        if not test_template:
            test_template = 'default'

        template_info_obj = TemplateInfo.objects.get(template_name=test_template)
        default_suite_name = template_info_obj.default_suite

        # 获取需要初始化的应用（包含是否流水线、docker还是虚拟机、后面还会增加中间件）
        page, dict_list = app_mgt_ser.get_test_publish_app(
            test_template=test_template,
            page_num=1,
            page_size=1000,
            page_total=0,
        )

        pipeline_list = []
        tms_list = []
        tp_list = []
        middleware_list = []
        for obj in dict_list:
            module_name = obj['module_name']
            platform_type = obj['platform_type']
            support_docker = obj['support_docker']
            is_middleware = obj['is_middleware']
            default_deploy = obj['default_deploy']
            # 应用是否默认选中
            app_dict = {
                module_name: default_deploy
            }
            # 中间件
            if is_middleware:
                middleware_list.append(app_dict)
            else:
                # 流水线
                if platform_type:
                    pipeline_list.append(app_dict)
                else:
                    if support_docker:
                        tms_list.append(app_dict)
                    else:
                        tp_list.append(app_dict)

        print(">>>>default_suite_name = {}".format(default_suite_name))
        print(">>>>pipeline_list = {}".format(pipeline_list))
        print(">>>>tms_list = {}".format(tms_list))
        print(">>>>tp_list = {}".format(tp_list))
        print(">>>>middleware_list = {}".format(middleware_list))

        return Response(data=ApiResult.success_dict(
            msg="获取「测试应用字典」成功",
            data={
                "default_suite_name": default_suite_name,
                "pipeline_list": pipeline_list,
                "tms_list": tms_list,
                "tp_list": tp_list,
                "middleware_list": middleware_list,
            }
        ))

    def create(self, request, *args, **kwargs):
        pass

    def update(self, request, *args, **kwargs):
        pass

    def destroy(self, request, *args, **kwargs):
        pass


class TestPublishSuite(viewsets.ViewSet):
    """
    根据传入环境套执行测试环境发布 zt@2020-08-18
    """

    def list(self, request):
        suite_name = request.GET.get('suite_name')

        jenkins_ret_msg = TestPublishThread.test_publish_by_suite(suite_name)

        return Response(data=ApiResult.success_dict(
            msg="根据传入环境套执行测试环境发布，启动成功",
            data={
                "jenkins_ret_msg": jenkins_ret_msg,
            }
        ))

    def create(self, request, *args, **kwargs):
        suite_name = request.GET.get('suite_name')

        jenkins_ret_msg = TestPublishThread.test_publish_by_suite(suite_name)

        return Response(data=ApiResult.success_dict(
            msg="根据传入环境套执行测试环境发布，启动成功",
            data={
                "jenkins_ret_msg": jenkins_ret_msg,
            }
        ))

    def update(self, request, *args, **kwargs):
        pass

    def destroy(self, request, *args, **kwargs):
        pass


class RecordProdApply(viewsets.ViewSet):
    def create(self, request):
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username
        action_item = request.data.get('action_item')
        request_data = request.data.get('request_data')
        ActionRecord.objects.create(username=user, operate_time=datetime.datetime.now(),
                                    action_item=action_item,
                                    action_value=request_data)
        return Response(data=ApiResult.success_dict(
            msg="创建行为记录",
            data=''
        ))
