import datetime
import json
import os
import json
import publish
import spider.settings

from django.db.models import Q
from rest_framework import viewsets, serializers
from rest_framework.response import Response
from rest_framework import status

from app_mgt.models import AppBuild
from biz_mgt.models import BizTestFlowExecHistory
from env_mgt.models import Suite, Region
from check_mgt.models import GuardSwitchInfo
from spider.settings import SCRIPT_MAP, logger, ApiResult, ITERATION_LIMIT, INTERFACE_URL, NewApiResult
from public import call_script
from public.models import ScriptMain
from iter_mgt import models
from iter_mgt import iter_mgt_ser
from iter_mgt.models import Branches, DbMgtArcheryCheckTestEnv, Iter<PERSON><PERSON>elistApp, IterW<PERSON>elistGroup
from rest_framework.views import APIView
from task_mgt.http_task import HttpTask
from iter_mgt.iter_mgt_ser import (get_diff_package_type, get_test_data_dev_iter_info, \
                                   get_app_list_for_h5_iter, get_app_db_type, get_team_owner_list, get_iter_limit_count,
                                   get_wl_group_id,
                                   get_iter_diff_package_type,
                                   get_archive_branch_list_by_module_name, get_package_type_by_pipeline_id,
                                   get_iteration_id_list, get_app_list_by_iteration_id,
                                   get_biz_flow_info_by_iteration_id, get_last_build_time_by_app_and_branch)


class IterApplyApi(viewsets.ViewSet):
    server_package_type = ['jar', 'tar', 'war']
    h5_package_type = ['remote', 'static', 'dist', 'ios', 'ios-com', 'android', 'param-remote', 'mini-program',
                       'ssr-remote', 'android-com']
    py_package_type = ['py']
    """分支申请"""

    def list(self, request, *args, **kwargs):
        logger.info(request.data)
        app_name_list = request.GET.get('app_name_list')
        if app_name_list:
            app_name_list = app_name_list.split(',')
        apply = []
        for row in iter_mgt_ser.get_breach_info_for_apply(app_name_list):
            strftime = row[5]
            if strftime:
                strftime = datetime.datetime.strptime(row[5], "%Y年%m月%d日 %H时%M分")
            branch_info = {
                'id': row[0],
                'pipeline_id': row[1],
                'appName': row[2],
                'br_name': row[3],
                'sys_status': row[4],
                'br_end_date': strftime
            }
            apply.append(branch_info)
        return Response(data=spider.settings.ApiResult.success_dict(msg="查询成功", data=apply),
                        status=status.HTTP_200_OK)

    def create(self, request, *args, **kwargs):
        logger.info("分支申请参数{}".format(request.data))
        if isinstance(request.data, str):
            data = json.loads(request.data)
        else:
            data = request.data
        ser = iter_mgt_ser.IterApplySerializer(data=data)
        if ser.is_valid():
            ser.validated_data["task"] = 'iter_apply'
            ser.validated_data["proposer"] = str(request.user)
            ser.validated_data["tapd_id"] = data["tapd_id"]
        else:
            logger.error(ser.errors)
            return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.failed_dict(ser.errors))
        logger.info("ser.validated_data={}".format(ser.validated_data))
        gitlab_group = ser.validated_data.get('gitlab_group')
        branch_name = ser.validated_data.get('branch_name')
        repos_str = ser.validated_data.get('repos_str')
        if branch_name.startswith(gitlab_group):
            pipeline_id = branch_name
        else:
            pipeline_id = gitlab_group + "_" + branch_name

        package_type_list = get_package_type_by_pipeline_id(pipeline_id)
        server_set = set(self.server_package_type)
        package_set = set(package_type_list)
        # 计算交集
        intersection = server_set.intersection(package_set)
        app_list = []
        for repo in repos_str:
            module_name = repo.get('"module_name"')
            if module_name:
                app_list.append(module_name)
        app_build_list = AppBuild.objects.filter(module_name__in=app_list).values(
            'module_name', 'package_type')
        # 检查Python包类型
        py_set = set(self.py_package_type)
        py_intersection = py_set.intersection(package_set)
        
        for app_build in app_build_list:
            if intersection:
                if app_build["package_type"] not in self.server_package_type:
                    return Response(spider.settings.ApiResult.failed_dict(
                        "模块{}的包类型{}，不是服务端应用,不可以在同一个迭代".format(app_build["module_name"],
                                                                                    app_build["package_type"])))
            elif py_intersection:
                if app_build["package_type"] not in self.py_package_type:
                    return Response(spider.settings.ApiResult.failed_dict(
                        "模块{}的包类型{}，不是Python应用,不可以在同一个迭代".format(app_build["module_name"],
                                                                                    app_build["package_type"])))
            else:
                if app_build["package_type"] in self.h5_package_type:
                    return Response(spider.settings.ApiResult.failed_dict(
                        "模块{}的包类型{}，不是移动端应用,不可以在同一个迭代".format(app_build["module_name"],
                                                                                    app_build["package_type"])))
        check_result, iter_limit = self.__check_iter_pipeline_total(ser.validated_data)

        if check_result:
            s_call = call_script.ScriptCaller()
            logger.info(ser.validated_data["repos_str"])
            cmd_status, sid, msg = s_call.exc_script(ser.validated_data)
            if cmd_status:
                return Response(data=spider.settings.ApiResult.success_dict(msg="订单创建成功", data={"sid": sid}),
                                status=status.HTTP_200_OK)
            else:
                return Response(spider.settings.ApiResult.failed_dict(msg), status=status.HTTP_200_OK)
        else:
            return Response(
                spider.settings.ApiResult.failed_dict("新开分支数超过平台限制：{}，不允许再开release".format(iter_limit)),
                status=status.HTTP_200_OK)

    def update(self, request, *args, **kwargs):
        pass

    def destroy(self, request, *args, **kwargs):
        pass

    def __check_iter_pipeline_total(self, iter_data):
        branch_name = iter_data.get("branch_name")
        gitlab_group = iter_data.get("gitlab_group")
        branch_type = iter_data.get("branch_type")
        # 总开关
        guard_switch_obj = GuardSwitchInfo.objects.filter(guard_name='check_iter_number_limit')
        if guard_switch_obj and guard_switch_obj[0].guard_switch == 1:
            # 分支不存在，表示新增，否则是追加，不需要判断
            if Branches.objects.filter(br_name=branch_name, project_group=gitlab_group, br_status='open').count() == 0:
                # 获取单独设置的迭代限制数，如果没有设置，则使用平台默认的
                obj = IterWhitelistGroup.objects.filter(wl_group_name=gitlab_group,
                                                        wl_switch_id=guard_switch_obj[0].id).values("wl_group_value")
                default_limit = int(ITERATION_LIMIT.get("iteration_limit"))
                if obj and obj[0].get("wl_group_value") > default_limit:
                    iter_num_limit = obj[0].get("wl_group_value")
                else:
                    iter_num_limit = default_limit
                # 从配置中获取需要统计的分支的类型，默认是release,bugfix
                iter_limit_type = ITERATION_LIMIT.get("iteration_limit_type")
                if iter_limit_type:
                    iter_num = get_iter_limit_count(gitlab_group, iter_limit_type)
                else:
                    # 如果没有配置，则默认全类型统计,含有release,bugfix,feature
                    iter_num = Branches.objects.filter(project_group=gitlab_group, is_new=1, br_status='open').count()

                if iter_num >= iter_num_limit:
                    # 当统计总数大于等于限制数时，需要判断当前分支类型是否是release，如果是，则不允许新开分支，否则允许
                    if branch_type == 'release':
                        return False, iter_num_limit
                    else:
                        return True, iter_num_limit
                else:
                    return True, iter_num_limit
        return True, 0


class GetIterInfo(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer

    def __get_iter_info(self):
        pass

    def list(self, request, *args, **kwargs):
        pipeline_id = request.GET.get('pipeline_id')
        group_name = request.GET.get('group_name')
        pipeline_id = "{}_{}".format(group_name, pipeline_id)
        obj = Branches.objects.filter(pipeline_id=pipeline_id).last()

        if obj:
            if obj.br_status == 'open':
                return Response(status=status.HTTP_200_OK,
                                data=spider.settings.ApiResult.success_dict(msg="该迭代已存在",
                                                                            data={"id": obj.id}))
            else:
                return Response(status=status.HTTP_200_OK,
                                data=spider.settings.ApiResult.success_dict(msg="该迭代已关闭",
                                                                            data={"id": obj.id}))
        else:
            return Response(status=status.HTTP_200_OK,
                            data=spider.settings.ApiResult.success_dict(msg="该迭代首次申请",
                                                                        data={"pipeline_id": pipeline_id}), )


class IterMgtApi(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer
    """分支管理"""

    def create(self, request, *args, **kwargs):
        logger.info("分支追加参数{}".format(request.data))
        ser = iter_mgt_ser.IterMgtSerializer(data=request.data)
        if ser.is_valid():
            ser.validated_data["task"] = 'add_repos'
            ser.validated_data["proposer"] = str(request.user)
        else:
            return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.failed_dict(ser.errors))
        s_call = call_script.ScriptCaller()
        cmd_status, sid, msg = s_call.exc_script(ser.validated_data)
        if cmd_status:
            return Response(data=spider.settings.ApiResult.success_dict(msg="订单创建成功", data={"sid": sid}),
                            status=status.HTTP_201_CREATED)
        else:
            return Response(spider.settings.ApiResult.failed_dict(msg), status=status.HTTP_400_BAD_REQUEST)

    def put(self, request, *args, **kwargs):
        logger.info("修改迭代信息参数{}".format(request.data))
        ser = iter_mgt_ser.IterModSerializer(data=request.data)
        if ser.is_valid():
            try:
                models.Branches.objects.filter(pipeline_id=ser.validated_data["iteration_id"]). \
                    update(duedate=ser.validated_data["deadline"], description=ser.validated_data["desc"])

                return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.success_dict(msg="更新成功"))
            except Exception as e:
                return Response(spider.settings.ApiResult.failed_dict(str(e)), status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.failed_dict(ser.errors))

    def delete(self, request, *args, **kwargs):
        logger.info("分支删除参数{}".format(request.data))
        ser = iter_mgt_ser.IterMgtSerializer(data=request.data)
        logger.info(request.user)
        if ser.is_valid():
            ser.validated_data["task"] = 'del_repos'
            ser.validated_data["proposer"] = str(request.user)
            logger.info(ser.validated_data)
        else:
            return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.failed_dict(ser.errors))
        s_call = call_script.ScriptCaller()
        cmd_status, sid, msg = s_call.exc_script(ser.validated_data)
        if cmd_status:
            return Response(data=spider.settings.ApiResult.success_dict(msg="订单创建成功", data={"sid": sid}),
                            status=status.HTTP_201_CREATED)
        else:
            return Response(spider.settings.ApiResult.failed_dict(msg), status=status.HTTP_400_BAD_REQUEST)


class DeleteIterApi(viewsets.ModelViewSet):
    serializer_class = serializers.Serializer
    """分支管理"""

    def create(self, request, *args, **kwargs):
        logger.info("分支删除参数{}".format(request.data))
        if isinstance(request.data, str):
            data = json.loads(request.data)
        else:
            data = request.data
        ser = iter_mgt_ser.IterMgtSerializer(data=data)
        logger.info(request.user)
        if ser.is_valid():
            ser.validated_data["task"] = 'del_repos'
            ser.validated_data["proposer"] = str(request.user)
            logger.info(ser.validated_data)
        else:
            return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.failed_dict(ser.errors))
        s_call = call_script.ScriptCaller()
        cmd_status, sid, msg = s_call.exc_script(ser.validated_data)
        if cmd_status:
            return Response(data=spider.settings.ApiResult.success_dict(msg="订单创建成功", data={"sid": sid}),
                            status=status.HTTP_201_CREATED)
        else:
            return Response(spider.settings.ApiResult.failed_dict(msg), status=status.HTTP_400_BAD_REQUEST)


class IterApplyStatusApi(viewsets.ViewSet):
    """分支申请状态"""

    def list(self, request, *args, **kwargs):
        logger.info(request.data)
        sid = request.GET.get('sid').strip()
        main_log = ScriptMain.objects.get(sid=sid)
        # 判断是否在运行中
        if main_log.status in ("nonexec", "running"):
            return Response(data=spider.settings.ApiResult.success_dict(msg="running"), status=status.HTTP_200_OK)
        # 返回最后一次日志
        detail_log = ''
        for row in main_log.scriptminor_set.filter():
            detail_log = row.log
        if main_log.status == "success":
            return Response(data=spider.settings.ApiResult.success_dict(msg=detail_log), status=status.HTTP_200_OK)
        else:
            # for row in main_log.scriptminor_set.filter(status="failure"):
            return Response(spider.settings.ApiResult.failed_dict(detail_log), status=status.HTTP_400_BAD_REQUEST)


class IterApplyTapdIdApi(viewsets.ViewSet):
    """分支申请TAPD_ID"""

    def list(self, request, *args, **kwargs):
        logger.info(request.data)
        app_name = request.GET.get('app_name').strip()
        tapd_id = ''
        for row in iter_mgt_ser.get_tapd_id(app_name):
            tapd_id = row[0]
        return Response(data=spider.settings.ApiResult.success_dict(msg=tapd_id), status=status.HTTP_200_OK)


class ReposTreeApi(viewsets.ViewSet):
    """获取git仓库信息"""

    # serializer_class = BranchesSerializer

    def create(self, request, *args, **kwargs):
        return Response(status=status.HTTP_200_OK, data={})

    @staticmethod
    def produce_repos_data(username):
        """

        :param username:
        :return: 仓库路径 应用list字典
        # """
        repos_path_dict = {}

        for row in iter_mgt_ser.get_user_repos_info(username):
            if row[2] == 1 or row[3] == 1:
                if row[0] in repos_path_dict:
                    repos_path_dict[row[0]].append(row[1])
                else:
                    repos_path_dict[row[0]] = [row[1]]

            if row[0] in repos_path_dict:
                continue
            else:
                repos_path_dict[row[0]] = []
        return repos_path_dict

    @staticmethod
    def convert_repos_tree(repos_path_dict):
        """
        将数据转换为树形格式
        :param repos_path_dict:
        :return:
        """
        group_dict = {}
        for repos_path in repos_path_dict:
            # 一个仓库下面存在一个或多个应用时候 需要列出来
            if len(repos_path_dict[repos_path]) >= 1:
                module_tree_list = []
                for module_name in repos_path_dict[repos_path]:
                    module_tree_list.append({"title": module_name,
                                             "repos_path": repos_path,
                                             "module_name": module_name,
                                             "expand": True,
                                             "level": 1})
                repos_node = {"title": repos_path.split("/")[1],
                              "repos_path": repos_path,
                              "children": module_tree_list,
                              "expand": True,
                              "level": 2
                              }
            else:
                repos_node = {"title": repos_path.split("/")[1], "repos_path": repos_path, "level": 1}

            if repos_path.split("/")[0] in group_dict:
                group_dict[repos_path.split("/")[0]].append(repos_node)
            else:
                group_dict[repos_path.split("/")[0]] = [repos_node]
        tree_list = []
        for group_name in group_dict:
            tree_list.append({"title": group_name, "group_name": group_name, "children": group_dict[group_name],
                              "level": 0, "expand": False})
        logger.info(tree_list)
        return tree_list

    def list(self, request):

        repos_path_dict = self.produce_repos_data(request.user)
        tree_list = self.convert_repos_tree(repos_path_dict)
        tree_list.sort(key=lambda k: k['title'].lower())
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="查询仓库成功", data={"repos_tree": tree_list}), status=status.HTTP_201_CREATED)

    def update(self, request):
        return Response({'method': 'put'})


class IterListApi(viewsets.ViewSet):

    def list(self, request):
        project_type = request.GET.get('project_type')
        iterative_list = []
        group_list = []
        for row in iter_mgt_ser.get_iter_info(request.user, project_type):
            if {"label": row[2], "value": row[2]} not in group_list:
                group_list.append({"label": row[2], "value": row[2]})
            iterative_list.append({"pipeline_id": row[0], "br_style": row[1], "project_group": row[2],
                                   "br_start_date": row[3], "duedate": row[4], "description": row[5],
                                   "tapd_id": row[6],
                                   "branch_name": row[7]})
        # logger.info("迭代信息{}".format(iterative_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代信息查询成功", data={"iterative_list": iterative_list, "group_list": group_list}),
            status=status.HTTP_200_OK)


class HistoryIterListApi(viewsets.ViewSet):
    """
    历史迭代信息
    """

    def list(self, request):
        iterative_list = []
        group_list = []
        for row in iter_mgt_ser.get_mobile_iter_info(request.user, br_status="close"):
            if {"label": row[1], "value": row[1]} not in group_list:
                group_list.append({"label": row[1], "value": row[1]})
            iterative_list.append({"pipeline_id": row[0], "br_style": row[4], "project_group": row[1],
                                   "br_start_date": row[2], "duedate": row[5], "description": row[6],
                                   "tapd_id": row[7],
                                   "branch_name": row[8],
                                   "tag_name": "tag_" + row[8]})
        # logger.info("迭代信息{}".format(iterative_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代信息查询成功", data={"iterative_list": iterative_list, "group_list": group_list}),
            status=status.HTTP_200_OK)


class IterListAllApi(viewsets.ViewSet):

    def list(self, request):
        iterative_list = []
        days = request.GET.get('days')
        start_time = datetime.datetime.now() - datetime.timedelta(days=int(days))
        for item in Branches.objects.filter(
                Q(create_date__gt=start_time) & Q(br_style__in=['bugfix', 'release'])).values_list('pipeline_id',
                                                                                                   flat=True):
            iterative_list.append({"pipeline_id": item})
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代信息查询成功", data={"iterative_list": iterative_list}),
            status=status.HTTP_200_OK)


class IterListSearchApi(viewsets.ViewSet):

    def list(self, request):
        iterative_list = []
        group_list = []
        git_urls = []
        page_num = request.GET.get('pageNum')
        page_size = request.GET.get('pageSize')
        page_total = request.GET.get('pageTotal')
        if page_num:
            page_num = int(page_num)
        else:
            page_num = 1

        if page_size:
            page_size = int(page_size)
        else:
            page_size = 10

        if page_total:
            page_total = int(page_total)
        else:
            page_total = 0
        pipeline_id = request.GET.get('pipeline_id_search')
        project_group_search = request.GET.get('project_group_search')
        br_style = request.GET.get('br_style_search')
        description_content = request.GET.get('description_content_search')
        branch_is_new = request.GET.get('branch_is_new')
        git_groups = iter_mgt_ser.get_git_url()
        for git_group in git_groups:
            git_urls.append({'git_urls_name': git_group[0]})
        print(git_urls)
        page, rows = iter_mgt_ser.get_iter_search_info(request.user, pipeline_id, project_group_search, br_style,
                                                       page_num, page_size,
                                                       page_total, description_content, branch_is_new)
        for row in rows:
            if {"label": row[2], "value": row[2]} not in group_list:
                group_list.append({"label": row[2], "value": row[2]})
            iterative_list.append({"pipeline_id": row[0], "br_style": row[1], "project_group": row[2],
                                   "br_start_date": row[3], "duedate": row[4], "description": row[5],
                                   "tapd_id": row[6],
                                   "branch_name": row[7], "pipeline_type": row[8],
                                   "self_test_flag": True if row[9] else False,
                                   "proposer": row[10]})
        # logger.info("迭代信息{}".format(iterative_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代信息查询成功",
            data={"iterative_list": iterative_list, "group_list": group_list, "git_urls": git_urls, "page": page, }),
            status=status.HTTP_200_OK)


class GitReposInfoApi(viewsets.ViewSet):
    """
    获取迭代仓库信息
    """

    def list(self, request):
        iteration_id = request.GET.get('iterationID')
        git_repo_list = []
        appname_list = []
        git_repo_dict = {}
        git_repo_app_dict = {}
        repos_set = iter_mgt_ser.get_repos_info(iteration_id)
        for row in repos_set:
            git_path = row[0]
            proposer = row[1]
            cname = row[2]
            app_name = row[3]
            sys_status = row[4]
            package_type = row[5]
            build_jdk_version = row[6]
            need_online = row[7]
            need_ops = row[8]
            is_component = row[9]
            if package_type:
                tmp_app_info = {"appName": app_name, "sys_status": sys_status, "appType": package_type,
                                "need_ops": need_ops}
            else:
                tmp_app_info = {"appName": app_name, "sys_status": sys_status, "appType": 'jar', "need_ops": need_ops}
            if build_jdk_version:
                tmp_app_info['jdkVersion'] = build_jdk_version
            else:
                set = models.Artifactinfo.objects.filter(appName=app_name)
                for i in set:
                    jdk_result = i.jdkVersion
                tmp_app_info['jdkVersion'] = jdk_result
            appname_list.append(tmp_app_info)
            if need_online == 1 or is_component == 1:
                if git_path in git_repo_app_dict:
                    git_repo_app_dict[git_path].append(
                        {"gitRepo": git_path, "proposer": proposer, "cname": cname, "app_name": app_name})
                else:
                    git_repo_app_dict[git_path] = [
                        {"gitRepo": git_path, "proposer": proposer, "cname": cname, "app_name": app_name}]

            if git_path in git_repo_dict:
                continue
            else:
                git_repo_dict[git_path] = {"gitRepo": git_path, "proposer": proposer, "cname": cname, "app_name": "无"}
        logger.info(git_repo_app_dict)
        for repos_path in git_repo_dict:
            if repos_path in git_repo_app_dict:
                git_repo_list.extend(git_repo_app_dict[repos_path])
            else:
                git_repo_list.append(git_repo_dict[repos_path])
        logger.info("仓库信息{}".format(git_repo_list))
        logger.info("应用信息{}".format(appname_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代信息查询成功", data={"git_repo_list": git_repo_list, "appname_list": appname_list}),
            status=status.HTTP_200_OK)


class GroupReposInfoApi(ReposTreeApi):
    """gitgroup下的仓库信息"""

    @staticmethod
    def produce_repos_data(iteration_id):
        """
        :param iteration_id:
        :return: 仓库路径 应用list字典
        # """
        repos_path_dict = {}

        for row in iter_mgt_ser.get_iter_repos_info(iteration_id):
            if row[2] == 1:
                if row[0] in repos_path_dict:
                    repos_path_dict[row[0]].append(row[1])
                else:
                    repos_path_dict[row[0]] = [row[1]]

            if row[0] in repos_path_dict:
                continue
            else:
                repos_path_dict[row[0]] = []
        return repos_path_dict

    def list(self, request):
        iteration_id = request.GET.get('iterationID')
        repos_path_dict = self.produce_repos_data(iteration_id)
        tree_list = self.convert_repos_tree(repos_path_dict)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="查询仓库成功", data={"repos_list": tree_list}), status=status.HTTP_201_CREATED)


class IterGroupOtherReposInfoApi(ReposTreeApi):
    """迭代所属组的 其他仓库信息"""

    @staticmethod
    def produce_repos_data(iteration_id):
        """
        :param iteration_id:
        :return: 仓库路径 应用list字典
        # """
        repos_path_dict = {}
        for row in iter_mgt_ser.get_iter_other_repos_info(iteration_id):
            if row[2] == 1 or row[3] == 1:
                if row[1] in repos_path_dict:
                    repos_path_dict[row[1]].append(row[0])
                else:
                    repos_path_dict[row[1]] = [row[0]]

            if row[1] in repos_path_dict:
                continue
            else:
                repos_path_dict[row[1]] = []
        return repos_path_dict

    def list(self, request):
        iteration_id = request.GET.get('iterationID')
        logger.info(iteration_id)
        repos_path_dict = self.produce_repos_data(iteration_id)
        logger.info(repos_path_dict)
        tree_list = self.convert_repos_tree(repos_path_dict)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="查询仓库成功", data={"repos_list": tree_list}), status=status.HTTP_201_CREATED)


class GitMember(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        # set = GitMembers.objects.filter(username="hefeng.qiu", permission__gt=29)
        # for i in set:
        #     logger.info(i.username)
        # mem_info = self.get_queryset()
        # # logger.info(mem_info)
        # ser = self.get_serializer(instance=mem_info, many=True)
        # logger.info(ser.data)
        post_data = {"app_name_temp": "mring-web", "app_branch": "ddd", "iteration_number": "9.9.8",
                     "desc": "dfafdfsfsfsf", "environments": "dev"}
        with HttpTask() as http_task:
            http_task.call_interface("c_config_br", post_data)
            # http_task.send_request("get", "http://192.168.122.52:8080/zeus-service/synchronous/newBranchSynchronous", post_data)
            # http_task.send_request("get", "http://192.168.122.52:8080/zeus-service/synchronous/newBranchSynchronous",
            #                        post_data)
            # http_task.send_request("get", "http://192.168.122.52:8080/zeus-service/synchronous/newBranchSynchronous",
            #                        post_data)
            # print("dddd")
        print("dddd")
        return Response(data=spider.settings.ApiResult.success_dict(msg="迭代信息查询成功"),
                        status=status.HTTP_200_OK)


class Modifyjdk(APIView):
    def get(self, request):
        app_name = request.GET.get('app_name')
        iterative_name = request.GET.get('iterative_name')
        try:
            app_info_obj = models.BranchIncludeSys.objects.get(appName=app_name, pipeline_id=iterative_name)
            jdkversion = app_info_obj.jdkVersion
        except Exception as e:
            logger.info(str(e))
            jdkversion = ''
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="", data={"jdkversion": jdkversion}),
            status=status.HTTP_200_OK)

    def put(self, request):
        app_name = request.data.get('app_name')
        iterative_id = request.data.get('iterative_id')
        jdk_version = request.data.get('jdk_version')
        models.BranchIncludeSys.objects.filter(appName=app_name, pipeline_id=iterative_id).update(
            jdkVersion=jdk_version)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="编译JDK版本更新成功，只在本迭代生效。", data={"jdkversion": jdk_version}),
            status=status.HTTP_200_OK)


class IterGroupAppLatestArchiveInfoApi(viewsets.ViewSet):
    """迭代所属组的下应用的最近一次归档信息"""

    def list(self, request):
        app_latest_archive_info_list = []

        pipeline_id = request.GET.get('pipeline_id')

        rows = iter_mgt_ser.get_app_latest_archive_info(pipeline_id)
        for row in rows:
            app_latest_archive_info_list.append({"pipeline_id": row[0], "br_name": row[1], "appName": row[2],
                                                 "br_end_date": row[3], "packageType": row[4]})
        logger.info("应用信息{}".format(app_latest_archive_info_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代归档信息查询成功",
            data={"app_latest_archive_info_list": app_latest_archive_info_list}),
            status=status.HTTP_200_OK)


class IterGroupAppOnlineInfoApi(viewsets.ViewSet):
    """迭代所属组的下应用的上线中的信息"""

    def list(self, request):
        app_online_info_list = []

        pipeline_id = request.GET.get('pipeline_id')

        rows = iter_mgt_ser.get_app_online_info(pipeline_id)
        for row in rows:
            app_online_info_list.append({"pipeline_id": row[0], "br_name": row[1], "appName": row[2]})
        logger.info("上线中的应用信息{}".format(app_online_info_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代正在上线中的信息查询成功",
            data={"app_online_info_list": app_online_info_list}),
            status=status.HTTP_200_OK)


class AppBranchInfoApi(viewsets.ViewSet):
    """应用的分支信息"""

    def list(self, request):
        logger.info("查询的应用名{}".format(request.GET))
        app_name = request.GET.get('app_name')

        logger.info("查询的应用名{}".format(app_name))
        br_info = iter_mgt_ser.find_app_branch_info(app_name)
        logger.info("分支信息列表{}".format(br_info))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代正在上线中的信息查询成功",
            data={"br_info": br_info}),
            status=status.HTTP_200_OK)


class PackageTypeInIterApi(viewsets.ViewSet):
    """获取迭代中存在的应用类型"""

    def list(self, request):
        logger.info("查询的应用名{}".format(request.GET))
        iter_id = request.GET.get('iter_id')
        package_type_list = iter_mgt_ser.get_package_type_in_iter(iter_id)
        logger.info("迭代中存在的应用类型{}".format(package_type_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="获取迭代中存在的应用类型",
            data={"package_type_list": package_type_list}),
            status=status.HTTP_200_OK)


class GetH5Group(viewsets.ViewSet):
    """
    获取移动端分组
    """

    def list(self, request):
        h5_group_list = []
        h5_group = iter_mgt_ser.get_h5_group()
        for item in h5_group:
            for i in item:
                if i:
                    h5_group_list.append(i)
        print(h5_group_list)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="获取移动端分组",
            data={"h5_group_list": h5_group_list}),
            status=status.HTTP_200_OK)
class GetPyGroup(viewsets.ViewSet):
    """
    获取Python分组
    """

    def list(self, request):
        py_group_list = []
        py_group = iter_mgt_ser.get_py_group()
        for item in py_group:
            for i in item:
                if i:
                    py_group_list.append(i)
        print(py_group_list)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="获取移动端分组",
            data={"py_group_list": py_group_list}),
            status=status.HTTP_200_OK)


class IterPublishAppInfoApi(viewsets.ViewSet):

    def list(self, request):
        logger.info("获取迭代中需要发布的应用信息{}".format(request.GET))
        iter_id = request.GET.get('iter_id')
        guard_name = "git_group_publish"
        app_list = iter_mgt_ser.get_need_publish_app_info(guard_name, iter_id)
        logger.info("获取迭代中需要发布的应用信息{}".format(app_list))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="获取迭代中需要发布的应用信息",
            data={"app_list": app_list}),
            status=status.HTTP_200_OK)


class IterTestEndDateApi(viewsets.ViewSet):

    def list(self, request):
        logger.info("获取测试结束时间{}".format(request.GET))
        iter_id = request.GET.get('iter_id')
        test_end_date = Branches.objects.filter(pipeline_id=iter_id).last().test_end_date
        if test_end_date is None:
            test_end_date = ""
        logger.info("测试结束时间{}".format(test_end_date))
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="获取测试结束时间",
            data={"test_end_date": test_end_date}),
            status=status.HTTP_200_OK)

    def create(self, request):
        logger.info("修改测试结束时间{}".format(request.data))
        if isinstance(request.user, str):
            user = request.user
        else:
            user = request.user.username

        ser = iter_mgt_ser.IterTestEndDateSerializer(data=request.data)
        if ser.is_valid():
            update_test_end_date = datetime.datetime.strptime(request.data.get("test_end_date"), "%Y/%m/%d")
            # logger.info(ser.validated_data["test_end_date"])
            logger.info(ser.validated_data["iter_id"])
            test_end_date = Branches.objects.filter(pipeline_id=ser.validated_data["iter_id"]).last().test_end_date
            # update_test_end_date = ser.validated_data["test_end_date"] + datetime.timedelta(hours=8)
            # update_test_end_date = ser.validated_data["test_end_date"]
            logger.info(test_end_date)
            logger.info(update_test_end_date)
            if test_end_date == update_test_end_date:
                return Response(data=spider.settings.ApiResult.success_dict(
                    msg="时间没有变化",
                    data={"test_end_date": test_end_date}),
                    status=status.HTTP_200_OK)
            else:
                ser.update_test_end_date(ser.validated_data["iter_id"], update_test_end_date, user)
                return Response(data=spider.settings.ApiResult.success_dict(
                    msg="修改时间成功",
                    data={"test_end_date": test_end_date}),
                    status=status.HTTP_200_OK)
        else:
            logger.info(ser.errors)
            return Response(status=status.HTTP_200_OK, data=spider.settings.ApiResult.failed_dict(ser.errors))


class BranchVersionApi(viewsets.ViewSet):
    """
    提供给mock系统查询应用的接口
    """
    authentication_classes = []

    def list(self, request):
        module_name = request.GET.get("module_name")
        branch_name = request.GET.get("branch_name")
        return_data = iter_mgt_ser.get_branch_version_by_module_name(module_name, branch_name)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="success",
            data=return_data),
            status=status.HTTP_200_OK
        )


class GetAgentVersion(viewsets.ViewSet):
    """
    查询Agent版本
    """
    authentication_classes = []

    def list(self, request):
        module_name = request.GET.get("module_name")
        return_data = iter_mgt_ser.get_agent_version(module_name)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="success",
            data=return_data),
            status=status.HTTP_200_OK
        )


class GetAppBranchArchiveInfo(viewsets.ViewSet):
    """
        查询在途分支和最近归档分支信息
        """
    authentication_classes = []

    def list(self, request):
        module_name = request.GET.get("module_name")
        open_branch_dict = iter_mgt_ser.get_open_branch_version_by_module_name(module_name)
        archive_branch_dict = iter_mgt_ser.get_last_several_archive_version(module_name)
        open_branch_dict.extend(archive_branch_dict)
        return Response(data=spider.settings.ApiResult.success_dict(
            msg="success",
            data=open_branch_dict),
            status=status.HTTP_200_OK
        )


class GetAppArchiveVersion(viewsets.ViewSet):
    """
        获取应用的归档版本
        """
    authentication_classes = []

    def list(self, request):
        app_name = request.GET.get("app_name")
        sql = """
        SELECT
            m.id,
            m.pipeline_id,
            i.appName ,
            v.max_br_end_date AS max_br_end_date
        FROM
            iter_mgt_iter_info m
            LEFT JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
            LEFT JOIN (
            SELECT
                i.appName,
                MAX( m.br_end_date ) AS max_br_end_date 
            FROM
                iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id 
            WHERE
                m.br_status = 'close' 
            GROUP BY
                i.appName 
            ) v ON v.appName = i.appName 
            AND v.max_br_end_date = m.br_end_date 
        WHERE
            m.br_end_date IS NOT NULL 
            AND m.br_status = 'close' 
            AND v.max_br_end_date IS NOT NULL 
            AND i.appName = '{}';
        """.format(app_name)
        # sql = """ SELECT
        #         m.id,
        #         m.pipeline_id,
        #         MAX( m.br_end_date ) AS max_br_end_date FROM iter_mgt_iter_info m
        #         INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id WHERE
        #         m.br_status = 'close' AND i.appName= "{}" GROUP BY i.appName
        # """.format(app_name)
        iteration_id = ""
        for row in models.Branches.objects.raw(sql):
            iteration_id = row.pipeline_id
        return Response(data=spider.settings.ApiResult.success_dict(msg="success", data={"iteration_id": iteration_id}))


class CheckServiceMobile(viewsets.ViewSet):
    """
    校验统一仓库是否同时存在服务端和移动端
    """

    def list(self, request):
        module_list = []
        mobile_list = ["remote", "static", "dist", "ios", "android", "param-remote", "mini-program"]
        service_list = ["jar", "war"]
        print(request.GET["project_list"])
        project_dict = json.loads(request.GET["project_list"])
        print(type(json.loads(request.GET["project_list"])))
        project_list = project_dict["project_list"]
        print(type(project_list))
        if len(project_list) == 1:
            module_list.append(project_list[0].get("module_name"))
            if not project_list[0].get("module_name"):
                return Response(data=spider.settings.ApiResult.success_dict(msg="success",
                                                                            data={"diff_package_list": ""}))
        else:
            for item in project_list:
                module_list.append(item.get("module_name"))
        module_list = list(filter(None, module_list))
        if module_list:
            package_type_list = get_diff_package_type(module_list)
        else:
            package_type_list = []
        if set(package_type_list).intersection(mobile_list) and set(package_type_list).intersection(service_list):
            mobile_set = set(package_type_list).intersection(mobile_list)
            service_set = set(package_type_list).intersection(service_list)
            print(mobile_set)
            print(service_set)
            diff_package_list = mobile_set | service_set
            return Response(data=spider.settings.ApiResult.failed_dict(msg="failed",
                                                                       data={"diff_package_list": diff_package_list}))
        return Response(data=spider.settings.ApiResult.success_dict(msg="success",
                                                                    data={"diff_package_list": ""}))


# DbMgtBisIterInfo已不再使用，不需要创建业务分支
# class TestDataDevMgtBranch(viewsets.ViewSet):
# def list(self, request):
#     test_data_dev_iter_info = []
#     info_objects = DbMgtBisIterInfo.objects.filter(br_status='open')
#     for item in info_objects:
#         # print(item.value)
#         test_data_dev_iter_info.append({'bis_pipeline_id': item.bis_pipeline_id})
#     # test_data_dev_iter_info = get_test_data_dev_iter_info()
#     return Response(data=spider.settings.ApiResult.success_dict(msg="success",
#                                                                 data=test_data_dev_iter_info))

# def create(self, request):
#     repo_strs = []
#     data = request.data
#     biz_test_iter_br = data.get('biz_test_iter_br').strip()
#     biz_code = data.get('biz_code')
#     biz_base_db_set = data.get('biz_base_db_set')
#     biz_test_iter_id = biz_code + '_' + biz_test_iter_br
#     repo_strs.append('test-dml/{}'.format(biz_base_db_set))
#     data['repo_strs'] = repo_strs
#     data['biz_test_iter_id'] = biz_test_iter_id
#     data['task'] = "test_data_dev_iter_apply"
#     data["proposer"] = str(request.user)
#     s_call = call_script.ScriptCaller()
#     cmd_status, sid, msg = s_call.test_sql_exc_script(data, SCRIPT_MAP['test_data_dev_iter_mgt'])
#     if cmd_status:
#         return Response(data=ApiResult.success_dict(data=sid, msg="创建分支成功"))
#     else:
#         return Response(data=ApiResult.failed_dict(msg="创建分支失败"))


class TestDataDevMgtBranchDetail(viewsets.ViewSet):
    def list(self, request):
        bis_pipeline_id = request.GET.get('bis_pipeline_id')
        test_data_dev_iter_detail_info = get_test_data_dev_iter_info(bis_pipeline_id)
        return Response(data=spider.settings.ApiResult.success_dict(msg="success",
                                                                    data=test_data_dev_iter_detail_info))


# class IterBisLabApi(viewsets.ViewSet):
#
#     def list(self, request):
#         bis_pipeline_id = request.GET.get('bis_pipeline_id')
#         bis_lab_list = []
#         for item in DbMgtBisLabIterBind.objects.filter(bis_pipeline_id=bis_pipeline_id).values("bis_lab_code"):
#             bis_lab_list.append(item.get("bis_lab_code"))
#         if bis_lab_list:
#             bis_lab_str = ', '.join(bis_lab_list)
#         else:
#             bis_lab_str = ''
#         return Response(data=spider.settings.ApiResult.success_dict(msg="查询迭代数据标签信息成功",
#                                                                     data=bis_lab_str))

class IterationIdListByAppApi(viewsets.ViewSet):
    '''
    查询应用的在途分支列表
    '''

    def create(self, request):
        app_name_list = request.data.get('app_name_list')
        logger.info("app_name_list:{}".format(app_name_list))
        br_name_list = get_iteration_id_list(app_name_list)
        return Response(data=spider.settings.NewApiResult.success_dict(message="查询成功", data=br_name_list))


class AppListByIterationIdApi(viewsets.ViewSet):
    '''
    根据分支和应用查询当前分支下的应用列表
    '''

    def create(self, request):
        app_name_list = request.data.get('app_name_list')
        iteration_id = request.data.get('iteration_id')
        logger.info("app_name_list:{}".format(app_name_list))
        app_list = get_app_list_by_iteration_id(app_name_list, iteration_id)
        return Response(data=spider.settings.NewApiResult.success_dict(message="查询成功", data=app_list))
    
    def list(self, request):
        '''
        GET方法：根据迭代ID获取应用列表，用于测试左移功能
        '''
        iteration_id = request.GET.get('iteration_id')
        if not iteration_id:
            return Response(data=spider.settings.ApiResult.failed_dict(msg="iteration_id参数不能为空"))
        
        try:
            # 获取当前迭代下的所有应用列表
            app_list = get_app_list_by_iteration_id(None, iteration_id)
            return Response(data=spider.settings.ApiResult.success_dict(msg="获取应用列表成功", data=app_list))
        except Exception as e:
            logger.error(f"获取应用列表失败: {e}")
            return Response(data=spider.settings.ApiResult.failed_dict(msg="获取应用列表失败"))


class H5IterAppApi(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        br_name = request.GET.get("br_name")
        git_path = request.GET.get("git_path")

        app_list = get_app_list_for_h5_iter(br_name, git_path)

        return Response(data=spider.settings.ApiResult.success_dict(msg="查询h5迭代应用成功",
                                                                    data=app_list))


class GetAppSqlCheckSuiteCode(viewsets.ViewSet):

    def list(self, request):
        app_name = request.GET.get("app_name")

        data = DbMgtArcheryCheckTestEnv.objects.filter(module_name=app_name).values("check_suite_code").first()
        if data:
            return Response(data=spider.settings.ApiResult.success_dict(msg="查询成功", data=data))
        else:
            return Response(data=spider.settings.ApiResult.failed_dict(msg="查询失败"))

    def create(self, request):
        app_name = request.data.get("app_name")
        check_suite_code = request.data.get("check_suite_code")
        user = str(request.user)
        cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        try:
            DbMgtArcheryCheckTestEnv.objects.update_or_create(module_name=app_name,
                                                              defaults={"check_suite_code": check_suite_code,
                                                                        "create_user": user,
                                                                        "create_time": cur_time})
        except Exception as e:
            logger.error(str(e))
            return Response(data=spider.settings.ApiResult.failed_dict(msg="保存失败"))

        return Response(data=spider.settings.ApiResult.success_dict(msg="保存成功"))


class GetTestSuiteCode(viewsets.ViewSet):

    def list(self, reuqest):
        suite_list = Suite.objects.filter(
            Q(region_id__in=Region.objects.filter(type_short_name='test').values('id')),
            suite_is_active=True
        ).values_list('suite_code', flat=True)

        return Response(data=spider.settings.ApiResult.success_dict(msg="查询成功",
                                                                    data=suite_list))


class CheckAppSqlCheckSuite(viewsets.ViewSet):

    def list(self, reuqest):
        app_name = reuqest.GET.get("app_name")
        db_srv_type = get_app_db_type(app_name)
        obj = DbMgtArcheryCheckTestEnv.objects.filter(module_name=app_name).first()
        check_flag = True
        if db_srv_type == "mysql" and not obj:
            check_flag = False
        if not GuardSwitchInfo.objects.filter(guard_name='check_sql').values('guard_switch')[0].get(
                "guard_switch"):
            check_flag = True
        return Response(data=spider.settings.ApiResult.success_dict(msg="查询成功",
                                                                    data=check_flag))


class SelfTestFlagApi(viewsets.ViewSet):

    def create(self, request):
        pipeline_id = request.data.get("pipeline_id")
        self_test_flag = request.data.get("self_test_flag")
        br_style = request.data.get("br_style")
        user = str(request.user)
        curr_time = datetime.datetime.now()
        guard_name = 'check_iter_quality_report'
        wl_type = '2'

        if br_style != "release":
            return Response(data=spider.settings.ApiResult.failed_dict(msg="非release分支无需设置",
                                                                       data=""))
        team_owner_list = get_team_owner_list(pipeline_id)

        if user not in team_owner_list:
            return Response(
                data=spider.settings.ApiResult.failed_dict(msg="你无权设置，请联系团队长{}".format(team_owner_list),
                                                           data=""))

        wl_group_id = get_wl_group_id(pipeline_id, guard_name)

        if not wl_group_id:
            return Response(
                data=spider.settings.ApiResult.failed_dict(msg="研发自测设置功能，缺少白名单组信息，请联系PA！",
                                                           data=""))

        IterWhitelistApp.objects.update_or_create(wl_group_id=wl_group_id,
                                                  wl_type=wl_type,
                                                  wl_name=pipeline_id,
                                                  defaults={"wl_pass": self_test_flag,
                                                            "wl_opt_user": user,
                                                            "wl_opt_time": curr_time,
                                                            "create_user": user,
                                                            "create_time": curr_time})

        return Response(data=spider.settings.ApiResult.success_dict(
            msg="迭代{}设置研发自测为：{}".format(pipeline_id, self_test_flag),
            data=""))


class VerifyPackageType(viewsets.ViewSet):
    def list(self, request):
        module_name_list = []
        mobile_list = ["remote", "static", "dist", "ios", "android", "param-remote", "mini-program"]
        service_list = ["jar", "war"]
        branch_info = json.loads(request.GET.get("branch_info"))
        logger.info(branch_info)
        branch_name = branch_info.get('branch_name')
        gitlab_group = branch_info.get('gitlab_group')
        repos_str = branch_info.get('repos_str')
        iter_package_type_list = get_iter_diff_package_type(branch_name, gitlab_group)
        iter_mobile_type = set(iter_package_type_list).intersection(mobile_list)
        iter_service_type = set(iter_package_type_list).intersection(service_list)
        for repo_str in repos_str:
            if repo_str.get('module_name'):
                module_name_list.append(repo_str.get('module_name'))
        if module_name_list:
            package_type_list = get_diff_package_type(module_name_list)
            package_mobile_type = set(package_type_list).intersection(mobile_list)
            package_service_type = set(package_type_list).intersection(service_list)

        if iter_mobile_type and iter_service_type:
            return Response(data=spider.settings.ApiResult.failed_dict(msg="failed",
                                                                       data=True))
        else:
            if iter_mobile_type and package_service_type:
                return Response(data=spider.settings.ApiResult.failed_dict(msg="failed",
                                                                           data=True))
            elif iter_service_type and package_mobile_type:
                return Response(data=spider.settings.ApiResult.failed_dict(msg="failed",
                                                                           data=True))
            else:
                return Response(data=spider.settings.ApiResult.success_dict(msg="查询成功",
                                                                            data=True))


class ArchiveBranchList(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        module_name = request.GET.get("module_name")
        limit_num = request.GET.get("limit_num")

        if not module_name:
            return Response(data=ApiResult.failed_dict("参数错误：module_name is empty"))

        if not limit_num:
            limit_num = 10
        else:
            try:
                limit_num = int(limit_num)
            except ValueError:
                return Response(data=ApiResult.failed_dict("参数错误：limit_num is not int"))
            if limit_num <= 0:
                return Response(data=ApiResult.failed_dict("参数错误：limit_num 必须大于0"))

        app_cursor = get_archive_branch_list_by_module_name(module_name, limit_num)
        app_dict_list = ArchiveBranchList.dict_fetchall(app_cursor)

        return Response(data=ApiResult.success_dict(msg="success", data=app_dict_list))

    @staticmethod
    def dict_fetchall(cursor):
        return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class IterAutoTestReportApi(viewsets.ViewSet):

    def list(self, request):
        iteration_id = request.GET.get("iteration_id")
        biz_flow_info = get_biz_flow_info_by_iteration_id(iteration_id)

        # 1. 参数预处理
        params_data = [{
            "biz_code": item[1],
            "biz_flow_name": item[2],
            "branch_name": item[3],
            "app_name_list": item[0].split(",")
        } for item in biz_flow_info]

        # 2. API请求（带异常处理）
        try:
            with HttpTask() as http_task:
                params = {"params_data": params_data}
                request_url = INTERFACE_URL["mantis"] + "/mantis/test_report/get_test_flow_result_by_app_and_branch/"
                business_name = "get_test_flow_result_by_app_and_branch"
                status, result = http_task.send_request("post", request_url, params, business_name, None)
                final_result_list = json.loads(result).get("data", [])
        except Exception as e:
            logger.exception(f"API请求异常: {str(e)}")
            return Response(data=NewApiResult.failed_dict(message="mantis接口请求失败"))

        # 3. 批量获取构建信息
        batch_numbers = [r.get("run_batch_number") for r in final_result_list]
        biz_types = BizTestFlowExecHistory.objects.filter(
            exec_detail_param__batch_no__in=batch_numbers
        ).values("exec_detail_param__batch_no", "biz_type")
        biz_type_map = {item["exec_detail_param__batch_no"]: item["biz_type"] for item in biz_types}

        # 4. 处理结果数据
        for final_result in final_result_list:
            # 处理构建时间
            app_branch_list = [
                (app["app_name"], app["app_deploy_branch"])
                for app in final_result.get("app_deploy_info", [])
            ]
            build_info_list = get_last_build_time_by_app_and_branch(tuple(app_branch_list))

            for app in final_result.get("app_deploy_info", []):
                app["build_time"] = next(
                    (b["build_time"] for b in build_info_list
                     if b["app_name"] == app["app_name"] and
                     b["app_deploy_branch"] == app["app_deploy_branch"]),
                    ""
                )
                if app["build_time"] and final_result.get("run_time"):
                    try:
                        # Match the actual format with the 'T' separator
                        run_time = datetime.datetime.strptime(
                            final_result.get("run_time"),
                            "%Y-%m-%dT%H:%M:%S"
                        )
                        final_result["run_time"] = run_time
                        if app["build_time"] > run_time:
                            app["is_out_of_run_time"] = True
                        else:
                            app["is_out_of_run_time"] = False
                    except (ValueError, TypeError):
                        # Handle cases where parsing fails
                        app["is_out_of_run_time"] = False
                else:
                    app["is_out_of_run_time"] = False

            # 确保 testset_status 字段返回给前端
            if "testset_status" not in final_result:
                final_result["testset_status"] = ""

            # 处理业务类型
            if biz_type := biz_type_map.get(final_result.get("run_batch_number")):
                parts = biz_type.split("_")
                final_result.update({
                    "biz_name": parts[2],
                    "biz_iter_br": parts[1]
                })
        # 前端联调数据
        # final_result_list = [
        #     {
        #         "biz_code": "MIDDLEWARE-SCHEDULE",
        #         "biz_flow_name": "调度自动化编排",
        #         "run_batch_number": "2025051915374662754737337",
        #         "avg_pass_rate": 0.0,
        #         "run_time": "2025-05-19T15:41:11",
        #         "app_deploy_info": [
        #             {
        #                 "app_name": "howbuy-middleware-remote",
        #                 "app_deploy_branch": "patest20250519",
        #                 "build_time": "2025-05-19T13:51:19"
        #             },
        #             {
        #                 "app_name": "AAA",
        #                 "app_deploy_branch": "patest20250519",
        #                 "build_time": "2025-05-19T12:51:19"
        #             }
        #         ],
        #         "biz_name": "平台产品中间件-调度-调度",
        #         "biz_iter_br": "dev"
        #     },
        #     {
        #         "biz_code": "MIDDLEWARE-SCHEDULE",
        #         "biz_flow_name": "调度自动化编排",
        #         "run_batch_number": "2025051915374662754737337",
        #         "avg_pass_rate": 0.0,
        #         "run_time": "2025-05-19T15:41:11",
        #         "app_deploy_info": [
        #             {
        #                 "app_name": "howbuy-middleware-remote",
        #                 "app_deploy_branch": "patest20250519",
        #                 "build_time": "2025-05-19T13:51:19"
        #             },
        #             {
        #                 "app_name": "AAA",
        #                 "app_deploy_branch": "patest20250519",
        #                 "build_time": "2025-05-19T12:51:19"
        #             }
        #         ],
        #         "biz_name": "平台产品中间件-调度-调度",
        #         "biz_iter_br": "dev"
        #     }
        # ]
        return Response(data=NewApiResult.success_dict(data=final_result_list, message="查询成功"))


class CodeFetcherApi(viewsets.ViewSet):
    """
    代码获取API - 调用mantis的code_fetcher接口
    """

    def create(self, request):
        """
        批量根据项目ID和文件路径查询文件内容
        请求体格式: {
            "dataList": [
                {
                    "project_id": "1451",
                    "file_path": "src/main/java/com/howbuy/dfile/pm/prepare/impl/actions/makedir/nfs/MakeNfsDirAction.java"
                },
                ...
            ]
        }
        """
        # 获取请求体数据
        try:
            if isinstance(request.data, str):
                request_body = json.loads(request.data)
            else:
                request_body = request.data
        except (json.JSONDecodeError, TypeError) as e:
            return Response(data=NewApiResult.failed_dict(message="请求体格式错误，请提供有效的JSON对象"))
        
        # 验证请求体是否为对象且包含dataList字段
        if not isinstance(request_body, dict):
            return Response(data=NewApiResult.failed_dict(message="请求体必须是对象格式"))
        
        if "dataList" not in request_body:
            return Response(data=NewApiResult.failed_dict(message="请求体必须包含dataList字段"))
        
        file_requests = request_body["dataList"]
        
        # 验证dataList是否为数组
        if not isinstance(file_requests, list):
            return Response(data=NewApiResult.failed_dict(message="dataList必须是数组格式"))
        
        if not file_requests:
            return Response(data=NewApiResult.failed_dict(message="dataList不能为空数组"))
        
        # 验证每个请求项的格式
        for i, file_request in enumerate(file_requests):
            if not isinstance(file_request, dict):
                return Response(data=NewApiResult.failed_dict(message=f"第{i+1}个请求项格式错误，必须是对象"))
            
            project_id = file_request.get("project_id")
            file_path = file_request.get("file_path")
            
            if not project_id:
                return Response(data=NewApiResult.failed_dict(message=f"第{i+1}个请求项缺少project_id参数"))
            
            if not file_path:
                return Response(data=NewApiResult.failed_dict(message=f"第{i+1}个请求项缺少file_path参数"))
        
        # 批量处理文件请求
        result_list = []
        failed_requests = []
        
        for i, file_request in enumerate(file_requests):
            project_id = file_request.get("project_id")
            file_path = file_request.get("file_path")
            
            try:
                with HttpTask() as http_task:
                    # 使用POST请求调用mantis接口，数据必须是对象格式
                    post_data = {
                        "dataList": [{
                            "project_id": project_id,
                            "file_path": file_path
                        }]
                    }
                    request_url = INTERFACE_URL["mantis"] + "/mantis/code_fetcher/query_files_by_project_and_path/"
                    business_name = "query_files_by_project_and_path"
                    
                    # 设置JSON请求头
                    headers = {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                    
                    # 将数据序列化为JSON字符串
                    json_data = json.dumps(post_data)
                    
                    status, result = http_task.send_request("post", request_url, json_data, business_name, None, headers)
                    
                    # 解析mantis返回的数据格式
                    mantis_response = json.loads(result)
                    mantis_data = mantis_response.get("data", [])
                    
                    # 调试日志：记录mantis返回的原始数据
                    logger.info(f"Mantis返回数据 (project_id: {project_id}, file_path: {file_path}): {mantis_response}")
                    
                    # 处理mantis返回的文件内容
                    file_content = None
                    if mantis_data and isinstance(mantis_data, list):
                        # 查找匹配的文件
                        for file_item in mantis_data:
                            item_project_id = str(file_item.get("project_id"))
                            item_path_name = file_item.get("path_name")
                            
                            logger.info(f"比较文件: mantis项目ID={item_project_id}, 请求项目ID={project_id}, mantis路径={item_path_name}, 请求路径={file_path}")
                            
                            if (item_project_id == str(project_id) and item_path_name == file_path):
                                file_content = file_item.get("file_content")
                                logger.info(f"找到匹配文件，内容长度: {len(file_content) if file_content else 0}")
                                break
                    
                    if file_content is None:
                        logger.warning(f"未找到匹配的文件内容 (project_id: {project_id}, file_path: {file_path})")
                    
                    # 添加请求信息到结果中（简化格式）
                    result_item = {
                        "project_id": project_id,
                        "file_path": file_path,
                        "file_content": file_content
                    }
                    result_list.append(result_item)
                    
            except Exception as e:
                logger.exception(f"第{i+1}个文件请求异常 (project_id: {project_id}, file_path: {file_path}): {str(e)}")
                
                # 记录失败的请求（简化格式）
                failed_item = {
                    "project_id": project_id,
                    "file_path": file_path,
                    "file_content": None
                }
                result_list.append(failed_item)
                failed_requests.append(failed_item)
        
        # 构造简化的响应格式
        response_data = {
            "total_count": len(file_requests),
            "results": result_list
        }
        
        # 确定消息
        if failed_requests:
            message = f"批量查询完成，成功{len(file_requests) - len(failed_requests)}个，失败{len(failed_requests)}个"
        else:
            message = "批量查询全部成功"
        
        return Response(data=NewApiResult.success_dict(data=response_data, message=message))
