import datetime
from collections import Counter

from django.db import transaction
from rest_framework import viewsets
from rest_framework.response import Response

from biz_mgt.biz_mgt_dao import get_open_dev_pipeline_id, check_suite_code_is_using, \
    get_biz_last_init_biz_iter_id, update_execute_status
from biz_mgt.models import BizTestIterApp
from db_mgt.db_iter_mgt_pipeline_ser import get_app_info_dump_is_null
from db_mgt.models import JenkinsMgtTestDataDevJob
from iter_mgt.models import Branches
from jenkins_mgt.jenkins_job_mgt import JenkinsJobMgt
from jenkins_mgt.models import JenkinsInfo
from pipeline.pipeline_view import PipelineStatus
from spider.settings import ApiResult
from test_env_mgt.env_apply_ser import enable_jenkins


class DbIterMgtPipeline(viewsets.ViewSet):
    queryset = Branches.objects

    def list(self, request):
        bis_pipeline_id = request.GET.get("bis_pipeline_id")
        suite_code = request.GET.get("suite_code")
        if not bis_pipeline_id or not suite_code:
            return Response(data=ApiResult.failed_dict(
                msg="参数不能为空"))
        bis_pipeline_split = bis_pipeline_id.split("_")
        biz_code = bis_pipeline_split[0]
        check_result = get_biz_last_init_biz_iter_id(biz_code, suite_code)
        if not check_result:
            return Response(data=ApiResult.failed_dict(
                msg="当前业务：{}在环境:{}未初始化或初始化失败".format(biz_code, suite_code)))
        else:
            return Response(data=ApiResult.success_dict(msg="查询成功"))

    def create(self, request):
        jobs = request.data.get('job_name', '')
        biz_iter_id = request.data.get('bis_pipeline_id')
        biz_code = biz_iter_id.split("_")[0]
        # 源分支名
        biz_branch_name = biz_iter_id.split("_")[1]
        suite_code = request.data.get('suite_code')
        # 目标分支名
        biz_br_name = request.data.get('biz_br_name')
        target_biz_iter_id = "{}_{}".format(biz_code, biz_br_name)
        # biz_base_db_br = request.data.get('biz_base_db_br')
        # biz_base_db_code = request.data.get('biz_base_db_code')

        app_list = request.data.get('app_list')
        self._update_biz_test_iter_app(target_biz_iter_id, app_list, str(request.user))
        result = get_open_dev_pipeline_id(target_biz_iter_id)
        group_counter = Counter([d['project_group'] for d in result])
        # 检查是否有分组超过1个
        groups_with_multiple = [group for group, count in group_counter.items() if count > 1]
        if groups_with_multiple:
            group_names = '、'.join(groups_with_multiple)
            return Response(data=ApiResult.failed_dict(msg=f"{group_names}分组的迭代有多个，请重新选择"))
        parameters = {"biz_code": biz_code, "biz_branch_name": biz_branch_name, "suite_code": suite_code}
        job_type = check_suite_code_is_using(suite_code)
        if job_type:
            return Response(data=ApiResult.failed_dict(msg="当前环境正在使用中:{},请稍后重试".format(job_type)))
        job_param = parameters
        # 排除没有dump的应用（包含第一次做数据开发的老应用和新接数据开发的新应用） 20250321 by fwm

        app_list = self._exclude_app_have_no_dump(app_list, biz_iter_id)

        job_param['app_list'] = app_list
        jenkins_infos = JenkinsInfo.objects.filter(jenkins_identity='master').all()
        obj = JenkinsMgtTestDataDevJob.objects.create(biz_iter_id=biz_iter_id,
                                                      biz_code=biz_code,
                                                      status='running',
                                                      job_name=jobs,
                                                      job_param=job_param,
                                                      job_type="test_data_init",
                                                      suite_code=suite_code,
                                                      job_build_id=0,
                                                      job_url=jenkins_infos[
                                                                   0].jenkins_url + "blue/organizations/jenkins/test_data_init/detail/test_suite_init",
                                                      create_time=datetime.datetime.now(),
                                                      create_user=str(request.user))
        parameters["business_id"] = obj.id

        # jenkins_job_mgt = JenkinsJobMgt()
        enable_status, msg, url = enable_jenkins(jobs, parameters, operator=str(request.user))
        # rst, msg = jenkins_job_mgt.call_jenkins_job(jobs, parameters)
        if enable_status:
            return Response(data=ApiResult.success_dict(data='', msg=''))
        else:
            update_execute_status(PipelineStatus.failure, obj.id)
            return Response(data=ApiResult.failed_dict(msg=msg))

    def _update_biz_test_iter_app(self, bis_pipeline_id, app_list, opt_user):
        with transaction.atomic():
            BizTestIterApp.objects.filter(biz_test_iter_id=bis_pipeline_id).delete()
            for item in app_list:
                BizTestIterApp.objects.update_or_create(biz_test_iter_id=bis_pipeline_id,
                                                        app_module_name=item.get("module_name"),
                                                        defaults={"archive_br_name": item.get("branch_name"),
                                                                  "create_user": opt_user,
                                                                  "create_time": datetime.datetime.now()})

    def _exclude_app_have_no_dump(self, app_dict_list, biz_iter_id):
        # 使用列表推导式提取模块名称列表
        module_name_list = [item["module_name"] for item in app_dict_list]

        # 获取无dump的模块名称并转换为集合提高查询效率
        no_dump_module_set = set(get_app_info_dump_is_null(module_name_list, biz_iter_id))

        # 使用列表推导式过滤原列表，时间复杂度降为O(n)
        return [item for item in app_dict_list if item["module_name"] not in no_dump_module_set]

# class DbBisLabel(viewsets.ViewSet):
#
#     def list(self, request):
#         bis_code = request.GET.get("bis_code")
#
#         lab_list = DbMgtBisLabBind.objects.filter(bis_code=bis_code, bis_lab_bind_is_active=1).values("bis_lab_code")
#
#         return Response(data=ApiResult.success_dict(data=lab_list, msg='业务标签查询成功'))


# class DbBisLabelByPipelineId(viewsets.ViewSet):
#
#     def list(self, request):
#         bis_pipeline_id = request.GET.get("bis_pipeline_id")
#         bis_code = bis_pipeline_id.split("_")[0]
#         bis_lab_list = DbMgtBisLabBind.objects.filter(bis_code=bis_code, bis_lab_bind_is_active=1).values(
#             "bis_lab_code")
#         bis_iter_lab_list = DbMgtBisLabIterBind.objects.filter(bis_pipeline_id=bis_pipeline_id).values("bis_lab_code")
#         bis_lab_data_list = []
#         for item in bis_lab_list:
#             if item in bis_iter_lab_list:
#                 check = True
#             else:
#                 check = False
#             item.update({"_checked": check})
#             bis_lab_data_list.append(item)
#
#         return Response(data=ApiResult.success_dict(data=bis_lab_data_list, msg='业务标签查询成功'))
#
#     def create(self, request):
#         bis_pipeline_id = request.data.get("bis_pipeline_id")
#         new_bis_lab_list = request.data.get("bis_lab_list")
#         old_bis_lab_list = []
#         opt_user = str(request.user)
#         cur_time = datetime.datetime.now()
#         for item in DbMgtBisLabIterBind.objects.filter(bis_pipeline_id=bis_pipeline_id).values("bis_lab_code"):
#             old_bis_lab_list.append(item.get("bis_lab_code"))
#
#         to_delete_lab_list = list(set(old_bis_lab_list) - set(new_bis_lab_list))
#         to_add_lab_list = list(set(new_bis_lab_list) - set(old_bis_lab_list))
#
#         for lab_code in to_delete_lab_list:
#             DbMgtBisLabIterBind.objects.filter(bis_pipeline_id=bis_pipeline_id, bis_lab_code=lab_code).delete()
#
#         for lab_code in to_add_lab_list:
#             new_data = {"create_user": opt_user, "create_time": cur_time, "update_user": opt_user,
#                         "update_time": cur_time, "stamp": 0,
#                         "bis_pipeline_id": bis_pipeline_id, "bis_lab_code": lab_code,
#                         "bis_lab_iter_bind_desc": "迭代【{}】绑定【{}】业务".format(bis_pipeline_id, lab_code)}
#             DbMgtBisLabIterBind.objects.create(**new_data)
#
#         return Response(data=ApiResult.success_dict(data='', msg='业务标签查询成功'))
