from django.db import models
from public.models import SpiderBaseModels


# class DbMgtBisIterInfo(models.Model):
#     bis_pipeline_id = models.CharField(verbose_name='测试数据开发迭代', max_length=64)
#     bis_code = models.CharField(verbose_name='业务名', max_length=64)
#     bis_branch_name = models.CharField(verbose_name='分支名', max_length=64)
#     br_status = models.CharField(verbose_name='分支状态', max_length=64)
#     br_start_time = models.DateTimeField(verbose_name='分支开始时间', null=True, blank=True)
#     br_end_time = models.DateTimeField(verbose_name='分支结束时间', null=True, blank=True)
#     creator = models.CharField(verbose_name='创建人', max_length=64)
#
#     class Meta:
#         db_table = 'db_mgt_bis_iter_info'
#         verbose_name = '测试开发数据迭代表'


# class TestMgtExecPlan(models.Model):
#     bis_pipeline_id = models.CharField(verbose_name='测试数据开发迭代', max_length=64)
#     test_id = models.IntegerField(verbose_name='测试集')
#     app_branch_info = models.JSONField(verbose_name='应用分支信息')
#     setting_time = models.DateTimeField(verbose_name='时间设置', max_length=64)
#     init_ccms = models.IntegerField(verbose_name='是否初始化ccms')
#     clean_cache = models.IntegerField(verbose_name='是否清理缓存')
#     create_user = models.CharField(verbose_name='创建人', max_length=64)
#     update_user = models.CharField(verbose_name='更新人', max_length=64)
#     create_time = models.DateTimeField(verbose_name='创建时间', max_length=64)
#     update_time = models.DateTimeField(verbose_name='更新时间', max_length=64)
#
#     class Meta:
#         db_table = 'test_mgt_exec_plan'
#         verbose_name = '测试计划管理表'


class IterMgtSqlIterInfo(models.Model):
    app_name = models.CharField(verbose_name="应用名", max_length=100)
    br_name = models.CharField(verbose_name="分支名称", max_length=200)
    br_style = models.CharField(verbose_name="分支类型", max_length=20)
    pipeline_id = models.CharField(verbose_name="原迭代ID", max_length=300)
    br_start_time = models.DateTimeField(verbose_name="分支开始时间")
    br_end_time = models.DateTimeField(verbose_name="分支结束时间")
    br_status = models.CharField(verbose_name="迭代状态", max_length=10)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='更新时间')
    stamp = models.IntegerField(verbose_name="版本")

    class Meta:
        db_table = 'iter_mgt_sql_iter_info'
        verbose_name = '快速SQL迭代表'


class IterMgtSqlIterSqlInfo(models.Model):
    app_name = models.CharField(verbose_name="应用名", max_length=100)
    br_name = models.CharField(verbose_name="分支名称", max_length=200)
    sql_type = models.CharField(verbose_name="SQL类型", max_length=10)
    sql_order = models.IntegerField(verbose_name="SQL执行顺序")
    sql_content = models.CharField(verbose_name="sql内容", max_length=10000)
    db_name = models.CharField(verbose_name="数据库名称", max_length=50)
    sql_status = models.IntegerField(verbose_name="SQL归档状态")
    sql_ver_name = models.CharField(verbose_name="sql制品名称", max_length=255)
    sql_ver_db = models.CharField(verbose_name="sql制品对应库", max_length=100)
    sql_ver_group = models.CharField(verbose_name="sql制品对应组", max_length=100)
    create_user = models.CharField(verbose_name='创建人', max_length=50)
    update_user = models.CharField(verbose_name='更新人', max_length=50)
    create_time = models.DateTimeField(verbose_name='创建时间')
    update_time = models.DateTimeField(verbose_name='更新时间')
    stamp = models.IntegerField(verbose_name="版本")

    class Meta:
        db_table = 'iter_mgt_sql_iter_sql_info'
        verbose_name = '快速SQL迭代sql详情表'


# class DbMgtBisLabBind(models.Model):
#     bis_code = models.CharField(verbose_name="业务类型", max_length=100)
#     bis_lab_code = models.CharField(verbose_name="业务标签编码", max_length=100)
#     bis_lab_bind_is_active = models.IntegerField(verbose_name="业务标签绑定是否可用")
#     bis_lab_bind_desc = models.CharField(verbose_name="业务标签绑定说明", max_length=255)
#     create_user = models.CharField(verbose_name='创建人', max_length=50)
#     update_user = models.CharField(verbose_name='更新人', max_length=50)
#     create_time = models.DateTimeField(verbose_name='创建时间')
#     update_time = models.DateTimeField(verbose_name='更新时间')
#     stamp = models.IntegerField(verbose_name="版本")
#
#     class Meta:
#         db_table = 'db_mgt_bis_lab_bind'
#         verbose_name = '业务标签绑定表'


# class DbMgtBisLabIterBind(models.Model):
#     bis_pipeline_id = models.CharField(verbose_name="业务数据生产迭代id", max_length=100)
#     bis_lab_code = models.CharField(verbose_name="业务标签编码", max_length=100)
#     bis_lab_iter_bind_desc = models.CharField(verbose_name="业务标签绑定说明", max_length=255)
#     create_user = models.CharField(verbose_name='创建人', max_length=50)
#     update_user = models.CharField(verbose_name='更新人', max_length=50)
#     create_time = models.DateTimeField(verbose_name='创建时间')
#     update_time = models.DateTimeField(verbose_name='更新时间')
#     stamp = models.IntegerField(verbose_name="版本")
#
#     class Meta:
#         db_table = 'db_mgt_bis_lab_iter_bind'
#         verbose_name = '业务迭代与标签绑定表'


class JenkinsMgtTestDataDevJob(models.Model):
    id = models.BigAutoField(primary_key=True)
    job_type = models.CharField(max_length=100, blank=True, null=True)
    biz_iter_id = models.CharField(max_length=20, blank=True, null=True)
    biz_code = models.CharField(max_length=20, blank=True, null=True)
    suite_code = models.CharField(max_length=50)
    job_name = models.CharField(max_length=200, blank=True, null=True)
    job_build_id = models.CharField(max_length=20, blank=True, null=True)
    job_param = models.JSONField(blank=True, null=True)
    job_url = models.CharField(max_length=255, blank=True, null=True)
    status = models.CharField(max_length=20)
    create_time = models.DateTimeField(blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    create_user = models.CharField(max_length=50, blank=True, null=True)
    update_user = models.CharField(max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'jenkins_mgt_test_data_dev_job'


class JenkinsMgtTestEsInitJob(models.Model):
    """
    Jenkins ES初始化任务模型
    对应数据库表：jenkins_mgt_test_es_init_job
    """
    id = models.BigAutoField(primary_key=True)
    job_type = models.CharField(max_length=100, blank=True, null=True, verbose_name='任务类型')
    biz_iter_id = models.CharField(max_length=100, blank=True, null=True, verbose_name='业务迭代ID')
    biz_code = models.CharField(max_length=100, blank=True, null=True, verbose_name='业务code')
    biz_br_name = models.CharField(max_length=20, blank=True, null=True, verbose_name='目标分支')
    job_name = models.CharField(max_length=20, blank=True, null=True, verbose_name='job名称')
    job_build_id = models.CharField(max_length=20, blank=True, null=True, verbose_name='构建ID')
    suite_code = models.CharField(max_length=50, blank=True, null=True, verbose_name='环境')
    status = models.CharField(max_length=20, blank=True, null=True, verbose_name='状态')
    job_param = models.JSONField(blank=True, null=True, verbose_name='任务参数')
    job_url = models.CharField(max_length=255, blank=True, null=True, verbose_name='组合任务url')
    es_dump_name = models.CharField(max_length=100, blank=True, null=True, verbose_name='ES dump名称')
    create_time = models.DateTimeField(blank=True, null=True, verbose_name='创建时间')
    update_time = models.DateTimeField(blank=True, null=True, verbose_name='更新时间')
    create_user = models.CharField(max_length=50, blank=True, null=True, verbose_name='创建人')
    update_user = models.CharField(max_length=50, blank=True, null=True, verbose_name='更新人')
    stamp = models.BigIntegerField(blank=True, null=True, verbose_name='时间戳')

    class Meta:
        managed = False
        db_table = 'jenkins_mgt_test_es_init_job'
        verbose_name = 'Jenkins ES初始化任务'
        verbose_name_plural = 'Jenkins ES初始化任务'


class JenkinsMgtTestDumpRestoreLog(models.Model):
    id = models.BigAutoField(primary_key=True)
    s_id = models.BigIntegerField(blank=True, null=True)
    job_build_id = models.IntegerField(blank=True, null=True)
    suite_code = models.CharField(max_length=50)
    db_name = models.CharField(max_length=50)
    status = models.CharField(max_length=20)
    create_user = models.CharField(max_length=50, blank=True, null=True)
    create_time = models.DateTimeField(auto_now=True)
    update_user = models.CharField(max_length=50, blank=True, null=True)
    update_time = models.DateTimeField(auto_now=True)

    class Meta:
        managed = False
        db_table = 'jenkins_mgt_test_dump_restore_log'


class DbMgtArcheryInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    archery_name = models.CharField(max_length=100, blank=True, null=True)
    archery_type = models.CharField(max_length=10, blank=True, null=True)
    archery_srv_hosts = models.CharField(max_length=100, blank=True, null=True)
    archery_srv_port = models.IntegerField(blank=True, null=True)
    is_active = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'db_mgt_archery_info'
        unique_together = (('archery_name', 'archery_type'),)


class DbMgtDbRestoreHis(models.Model):
    id = models.BigAutoField(primary_key=True)
    suite_code = models.CharField(max_length=50, blank=True, null=True)
    db_srv_hosts = models.CharField(max_length=100, blank=True, null=True)
    db_srv_port = models.IntegerField(blank=True, null=True)
    db_name = models.CharField(max_length=50, blank=True, null=True)
    db_info_id = models.BigIntegerField(blank=True, null=True)
    restore_datetime = models.DateTimeField(blank=True, null=True)
    opt_pipeline_id = models.CharField(max_length=100, blank=True, null=True)
    cdc_position = models.CharField(max_length=100, blank=True, null=True)
    binlog_name = models.CharField(max_length=100, blank=True, null=True)
    log_pos = models.CharField(max_length=100, blank=True, null=True)
    is_active = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'db_mgt_db_restore_his'


class DbMgtCdcRecordInfo(models.Model):
    id = models.BigAutoField(primary_key=True)
    bis_pipeline_id = models.CharField(max_length=255, blank=True, null=True)
    restore_his_id = models.CharField(max_length=255, blank=True, null=True)
    cdc_batch_no = models.CharField(max_length=100, blank=True, null=True)
    suite_db_name = models.CharField(max_length=100, blank=True, null=True)
    cdc_pipeline_url = models.CharField(max_length=1000, blank=True, null=True)
    start_cdc_position = models.CharField(max_length=20, blank=True, null=True)
    end_cdc_position = models.CharField(max_length=20, blank=True, null=True)
    binlog_name = models.CharField(max_length=100, blank=True, null=True)
    create_user = models.CharField(max_length=50, blank=True, null=True)
    create_time = models.DateTimeField(blank=True, null=True)
    update_user = models.CharField(max_length=50, blank=True, null=True)
    update_time = models.DateTimeField(blank=True, null=True)
    stamp = models.IntegerField(blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'db_mgt_cdc_record_info'
