#
from django.urls import path, include

from db_mgt import db_iter_mgt_pipeline_view, db_commit_diff_sql_pipeline_view, db_mgt_view, db_test_init_multi_push, \
    db_quick_sql_view, db_es_init_pipeline_view
from rest_framework.routers import DefaultRouter

router = DefaultRouter()

router.register(r'db_iter_mgt_pipeline', db_iter_mgt_pipeline_view.DbIterMgtPipeline, basename="db_iter_mgt_pipeline")
router.register(r'db_es_init_pipeline', db_es_init_pipeline_view.DbEsInitPipeline, basename="db_es_init_pipeline")
router.register(r'db_commit_diff_sql_pipeline', db_commit_diff_sql_pipeline_view.DbCommitDiffSqlPipeline, basename="db_commit_diff_sql_pipeline")
router.register(r'test_data_dev_mgt_branch_verify', db_mgt_view.TestDataDevMgtBranchVerify,
                basename="test_data_dev_mgt_branch_verify")
router.register(r'app_db_info', db_mgt_view.AppDbInfoView, basename="app_db_info")
router.register(r'db_test_init_multi_push', db_test_init_multi_push.DbTestInitMultiPush,
                basename="db_test_init_multi_push")

router.register(r'quick_sql_api', db_quick_sql_view.QuickSqlApi, basename="quick_sql_api")
router.register(r'app_sql_suite_info', db_quick_sql_view.AppSqlSuiteInfo, basename="app_sql_suite_info")
router.register(r'quick_sql_iter_api', db_quick_sql_view.QuickSqlIterApi, basename="quick_sql_iter_api")
router.register(r'get_app_branch_and_db_info', db_quick_sql_view.AppBranchAndDBInfo, basename="get_app_branch_and_db_info")
router.register(r'quick_sql_iter_archive', db_quick_sql_view.QuickSqlIterArchiveApi, basename="quick_sql_iter_archive")

router.register(r'get_sql_br_name', db_quick_sql_view.AppSqlBranchInfo, basename="get_sql_br_name")
router.register(r'get_sql_detail_info', db_quick_sql_view.AppSqlDetailInfo, basename="get_sql_detail_info")



urlpatterns = [
    path("", include(router.urls))
]
