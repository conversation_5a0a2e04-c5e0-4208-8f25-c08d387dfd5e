"""
ES初始化流水线视图
基于test_data_init的实现模式，为test_es_init提供API接口
"""
import datetime

from rest_framework import viewsets
from rest_framework.response import Response

from biz_mgt.biz_mgt_dao import check_es_suite_code_is_using, update_es_execute_status
from db_mgt.models import JenkinsMgtTestEsInitJob
from iter_mgt.models import Branches
from jenkins_mgt.models import JenkinsInfo
from pipeline.pipeline_view import PipelineStatus
from spider.settings import NewApiResult
from test_env_mgt.env_apply_ser import enable_jenkins


class DbEsInitPipeline(viewsets.ViewSet):
    """
    ES初始化流水线视图集
    提供ES初始化任务的创建和查询功能
    """
    queryset = Branches.objects

    def list(self, request):
        """
        查询ES初始化状态
        
        Args:
            request: HTTP请求对象
                - bis_pipeline_id: 业务流水线ID
                - suite_code: 环境代码
                
        Returns:
            Response: API响应结果
        """
        bis_pipeline_id = request.GET.get("bis_pipeline_id")
        suite_code = request.GET.get("suite_code")
        
        if not bis_pipeline_id or not suite_code:
            return Response(data=NewApiResult.failed_dict(
                message="参数不能为空"))
                
        bis_pipeline_split = bis_pipeline_id.split("_")
        biz_code = bis_pipeline_split[0]
        
        # 检查业务是否已初始化

    def create(self, request):
        """
        创建ES初始化任务
        
        Args:
            request: HTTP请求对象
                - job_name: 任务名称
                - bis_pipeline_id: 业务流水线ID
                - suite_code: 环境代码
                - biz_br_name: 目标分支名
                - es_dump_name: ES dump名称（必传）
                
        Returns:
            Response: API响应结果
        """
        jobs = request.data.get('job_name', '')
        biz_iter_id = request.data.get('bis_pipeline_id')
        suite_code = request.data.get('suite_code')
        biz_br_name = request.data.get('biz_br_name')
        es_dump_name = request.data.get('es_dump_name')
        
        # 参数验证
        if not all([jobs, biz_iter_id, suite_code, biz_br_name, es_dump_name]):
            return Response(data=NewApiResult.failed_dict(
                message="参数不能为空：job_name, bis_pipeline_id, suite_code, biz_br_name, es_dump_name"))
        
        if jobs != "test_es_init":
            return Response(data=NewApiResult.failed_dict(
                message="job_name必须为test_es_init"))
        
        biz_code = biz_iter_id.split("_")[0]
        # 源分支名
        biz_branch_name = biz_iter_id.split("_")[1]
            
        # 准备Jenkins任务参数
        parameters = {
            "biz_code": biz_code, 
            "biz_branch_name": biz_branch_name, 
            "suite_code": suite_code,
            "es_dump_name": es_dump_name
        }
        
        # 检查环境是否正在使用
        job_type = check_es_suite_code_is_using(suite_code)
        if job_type:
            return Response(data=NewApiResult.failed_dict(
                message="当前环境正在使用中:{},请稍后重试".format(job_type)))
                
        job_param = parameters
        
        # 获取Jenkins信息
        jenkins_infos = JenkinsInfo.objects.filter(jenkins_identity='master').all()
        
        # 创建ES初始化任务记录
        obj = JenkinsMgtTestEsInitJob.objects.create(
            biz_iter_id=biz_iter_id,
            biz_code=biz_code,
            biz_br_name=biz_br_name,
            status='running',
            job_name=jobs,
            job_param=job_param,
            job_type="test_es_init",
            suite_code=suite_code,
            es_dump_name=es_dump_name,
            job_build_id=0,
            job_url=jenkins_infos[0].jenkins_url + "blue/organizations/jenkins/test_es_init/detail/test_es_init",
            create_time=datetime.datetime.now(),
            create_user=str(request.user)
        )
        
        parameters["business_id"] = obj.id

        # 触发Jenkins任务
        enable_status, msg, url = enable_jenkins(jobs, parameters, operator=str(request.user))
        
        if enable_status:
            return Response(data=NewApiResult.success_dict(data='', message='ES初始化任务启动成功'))
        else:
            # 更新任务状态为失败
            update_es_execute_status(PipelineStatus.failure, obj.id)
            return Response(data=NewApiResult.failed_dict(message=msg))