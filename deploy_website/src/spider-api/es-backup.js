import spider_axios from '@/libs/spider_api.request'

// 创建ES备份
export const createESBackup = (data) => {
    return spider_axios.request({
        url: 'spider/es_mgt/backups/create/',
        method: 'post',
        data
    })
}

// 查询环境的备份列表
export const getBackupList = (params) => {
    return spider_axios.request({
        url: '/spider/es_mgt/backups/list',
        method: 'get',
        params
    })
}

// 验证/更新备份状态
export const validateBackupStatus = (data) => {
    return spider_axios.request({
        url: '/spider/es_mgt/backups/status/',
        method: 'post',
        data
    })
}

// 绑定业务与备份
export const bindBusinessBackup = (data) => {
    return spider_axios.request({
        url: '/spider/es_mgt/backups/bind/',
        method: 'post',
        data
    })
}

// 获取环境与模块关联关系列表（用于create-backup页面）
export const getEnvModules = () => {
    return spider_axios.request({
        url: '/spider/es_mgt/es_mgt_api/env-modules',
        method: 'get'
    })
}

// 获取套件代码列表（用于bind-backup页面）
export const getSuiteCodes = (params) => {
    return spider_axios.request({
        url: '/spider/es_mgt/backups/suite-codes',
        method: 'get',
        params
    })
}
