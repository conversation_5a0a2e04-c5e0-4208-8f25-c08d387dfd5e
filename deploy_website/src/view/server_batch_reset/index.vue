<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-06-27 09:55:26
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-09-29 17:28:35
 * @FilePath: /website_web/deploy_website/src/view/server_batch_reset/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Card v-if="showPage" class="service-publish-card">
        <Form ref="formInline" :model="formInline" inline :label-width="80">
            <FormItem label="申请人" prop="user">
                <Input clearable v-model="formInline.create_user" placeholder="请输入申请人"> </Input>
            </FormItem>
            <FormItem label="批次号" prop="password">
                <Input clearable v-model="formInline.batch_no" placeholder="请输入批次号"> </Input>
            </FormItem>
            <FormItem label="状态" prop="user">
                <Select clearable style="width: 160px" v-model="formInline.status" placeholder="请选择状态">
                    <Option :value="item.value" v-for="item in status_list" :key="item.value">{{ item.label }}</Option>
                </Select>
            </FormItem>
            <FormItem>
                <Button style="margin-right: 10px" type="primary" @click="handleSearch">查询</Button>
                <Button type="primary" @click="add">新增</Button>
            </FormItem>
        </Form>
        <Table border ref="selection" :columns="columns" :data="tableData"></Table>
        <Page
            style="margin-top: 10px"
            :total="total"
            :page-size="page_size"
            :current="page_num"
            show-total
            @on-change="changePage"
        ></Page>
    </Card>
</template>

<script>
import { get_all_team_lead_info, page_reboot_flow, get_reboot_flow_jenkins, reboot_flow_action } from './api'
export default {
    data() {
        return {
            formInline: {
                status: null,
                batch_no: '',
                create_user: ''
            },
            columns: [
                {
                    title: '申请人',
                    key: 'create_user'
                },
                {
                    title: '重启描述',
                    key: 'create_user'
                },
                {
                    title: '批次号',
                    key: 'batch_no'
                },
                {
                    title: '创建时间',
                    key: 'create_time'
                },
                {
                    title: '执行时间',
                    key: 'execute_time'
                },
                {
                    title: '状态',
                    key: 'status',
                    render: (h, params) => {
                        const statusItem = this.status_list.find(item => item.value === params.row.status.toString())
                        const statusText = statusItem ? statusItem.label : '未知状态'
                        const statusColor = this.getStatusColor(params.row.status)

                        return h(
                            'Tooltip',
                            {
                                props: {
                                    content: params.row.audit_result,
                                    placement: 'top'
                                }
                            },
                            [
                                h(
                                    'Tag',
                                    {
                                        props: {
                                            color: statusColor
                                        }
                                    },
                                    statusText
                                )
                            ]
                        )
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 300,
                    align: 'center',
                    render: (h, params) => {
                        const status = params.row.status.toString()
                        const buttons = []

                        // 根据状态显示不同的按钮
                        // 0初始化：可编辑、发起审核、跳转jerkins
                        // 2审核通过：可编辑、启动Jenkins、跳转jerkins
                        // 4执行中：跳转jerkins
                        // 3审核不通过：可编辑、跳转jerkins
                        // 1审核中：可编辑、跳转jerkins
                        // 5执行成功：可编辑、跳转jerkins
                        // 6执行失败：可编辑、跳转jerkins、重试

                        // 编辑按钮 - 除了执行中(4)状态外都可以编辑
                        if (!['4'].includes(status)) {
                            buttons.push(
                                h(
                                    'Button',
                                    {
                                        props: {
                                            type: 'primary',
                                            size: 'small'
                                        },
                                        style: {
                                            marginRight: '8px'
                                        },
                                        on: {
                                            click: () => {
                                                this.handleEdit(params.index, params.row)
                                            }
                                        }
                                    },
                                    '编辑'
                                )
                            )
                        }

                        // 发起审核按钮 - 只有初始化(0)状态可以发起审核
                        if (status === '0') {
                            buttons.push(
                                h(
                                    'Button',
                                    {
                                        props: {
                                            type: 'success',
                                            size: 'small',
                                            loading: this.curIndex === params.index ? this.comLoadding : false
                                        },
                                        style: {
                                            marginRight: '8px'
                                        },
                                        on: {
                                            click: () => {
                                                this.handleCommon(params.index, params.row, 'audit')
                                            }
                                        }
                                    },
                                    '发起审核'
                                )
                            )
                        }

                        // 启动Jenkins按钮 - 只有审核通过(2)状态可以启动Jenkins
                        if (status === '2') {
                            buttons.push(
                                h(
                                    'Poptip',
                                    {
                                        props: {
                                            confirm: true,
                                            title: '确定要启动Jenkins任务吗？',
                                            placement: 'top',
                                            transfer: true
                                        },
                                        style: {
                                            marginRight: '8px'
                                        },
                                        on: {
                                            'on-ok': () => {
                                                this.handleCommon(params.index, params.row, 'start')
                                            }
                                        }
                                    },
                                    [
                                        h(
                                            'Button',
                                            {
                                                props: {
                                                    type: 'info',
                                                    size: 'small',
                                                    loading: this.curIndex === params.index ? this.comLoadding : false
                                                }
                                            },
                                            '启动Jenkins'
                                        )
                                    ]
                                )
                            )
                        }

                        if (status === '6') {
                            // buttons.push(
                            //     h(
                            //         'Button',
                            //         {
                            //             props: {
                            //                 type: 'error',
                            //                 size: 'small',
                            //                 loading: this.curIndex === params.index ? this.comLoadding : false
                            //             },
                            //             on: {
                            //                 click: () => {
                            //                     this.handleCommon(params.index, params.row, 'retry')
                            //                 }
                            //             }
                            //         },
                            //         '重试'
                            //     )
                            // )
                            buttons.push(
                                h(
                                    'Poptip',
                                    {
                                        props: {
                                            confirm: true,
                                            title: '确定要重试此任务吗？',
                                            placement: 'top',
                                            transfer: true
                                        },
                                        style: {
                                            marginRight: '8px'
                                        },
                                        on: {
                                            'on-ok': () => {
                                                this.handleCommon(params.index, params.row, 'retry')
                                            }
                                        }
                                    },
                                    [
                                        h(
                                            'Button',
                                            {
                                                props: {
                                                    type: 'error',
                                                    size: 'small',
                                                    loading: this.curIndex === params.index ? this.comLoadding : false
                                                }
                                            },
                                            '重试'
                                        )
                                    ]
                                )
                            )
                        }

                        // 跳转Jenkins按钮 - 所有状态都可以跳转Jenkins
                        buttons.push(
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'info',
                                        size: 'small',
                                        loading: this.curIndex === params.index ? this.jekLoadding : false
                                    },
                                    style: {
                                        marginRight: '8px'
                                    },
                                    on: {
                                        click: () => {
                                            this.handleJenkins(params.index, params.row)
                                        }
                                    }
                                },
                                '跳转Jenkins'
                            )
                        )

                        return h('div', buttons)
                    }
                }
            ],
            tableData: [],
            showPage: false,
            page_num: 1,
            page_size: 10,
            total: 0,
            // 状态：0初始化，1审核中，2审核通过，3审核不通过，4执行中，5执行成功，6执行失败
            status_list: [
                {
                    value: '0',
                    label: '初始化'
                },
                {
                    value: '1',
                    label: '审核中'
                },
                {
                    value: '2',
                    label: '审核通过'
                },
                {
                    value: '3',
                    label: '审核不通过'
                },
                {
                    value: '4',
                    label: '执行中'
                },
                {
                    value: '5',
                    label: '执行成功'
                },
                {
                    value: '6',
                    label: '执行失败'
                }
            ],
            comLoadding: false,
            jekLoadding: false,
            curIndex: null
        }
    },
    methods: {
        // 根据状态值返回对应的颜色
        getStatusColor(status) {
            const statusColorMap = {
                '0': 'default', // 初始化
                '1': 'blue', // 审核中
                '2': 'green', // 审核通过
                '3': 'red', // 审核不通过
                '4': 'orange', // 执行中
                '5': 'success', // 执行成功
                '6': 'error' // 执行失败
            }
            return statusColorMap[status.toString()] || 'default'
        },
        handleSearch() {
            console.log('查询')
            page_reboot_flow({
                page_num: this.page_num,
                page_size: this.page_size,
                ...this.formInline
            }).then(res => {
                if (res.data.status === 'success') {
                    console.log('res.data.data', res.data.data)
                    this.tableData = res.data.data.app_list
                    this.total = res.data.data.page.total
                } else {
                    this.$Message.error(res.data.msg)
                }
            })
        },
        changePage(page) {
            console.log('切换到第', page, '页')
        },
        handleEdit(index, row) {
            this.$router.push(`/ops_service/server_batch_edit?id=${row.id}`)
        },
        handleCommon(index, row, action_type) {
            this.curIndex = index
            this.comLoadding = true
            reboot_flow_action({ id: row.id, action_type })
                .then(res => {
                    console.log('res', res, res.data.status)
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                        this.handleSearch()
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .finally(() => {
                    this.comLoadding = false
                })
        },
        handleJenkins(index, row) {
            this.curIndex = index
            this.jekLoadding = true
            console.log('跳转Jenkins', index, row)
            // 这里可以添加跳转Jenkins的逻辑
            get_reboot_flow_jenkins({ id: row.id })
                .then(res => {
                    if (res.data.status === 'success') {
                        window.open(res.data.data, '_blank')
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .finally(() => {
                    this.jekLoadding = false
                })
        },
        add() {
            this.$router.push('/ops_service/server_batch_edit')
        }
    },
    mounted() {
        get_all_team_lead_info({}).then(res => {
            if (res.data.status === 'success') {
                console.log('res.data.data', res.data.data, this.$store.state.user.userName)
                if (res.data.data.includes(this.$store.state.user.userName)) {
                    this.showPage = true
                    this.handleSearch()
                } else {
                    this.$Message.error('您没有权限访问该页面')
                }
            } else {
                this.$Message.error(res.data.msg)
            }
        })
    }
}
</script>

<style lang="less" scoped>
.service-publish-card {
    background-color: #fff;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    display: block;
    .card-body {
        // padding: 10px;
        background-color: #fff;
        // margin-bottom: 20px;
        margin-bottom: 10px;

        .service-app-list {
            display: flex;
            padding: 10px;
            overflow: auto;
            min-height: 65px;
            margin-bottom: 10px;
            margin-top: 10px;
            border: 1px dashed #fff;
        }

        .dragged {
            border: 1px dashed red;
        }
    }
}
.main-container {
    display: flex;
    border: 1px solid #00000014;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
}
.container-box {
    text-align: center;
}
.container {
    border: 1px solid #ccc;
    margin: 5px;
    padding: 10px;
    min-width: 200px;
    height: 250px;
    overflow-y: auto;
}
.item {
    border: 1px solid #00000008;
    background-color: #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    padding: 5px;
    margin: 5px;
    cursor: grab;
    min-width: 140px;
}
.save_btn {
    display: flex;
    justify-content: flex-end;
    margin-right: 10px;
}
.btn_item {
    margin-right: 10px;
}
.header_box {
    display: flex;
    margin: 10px 0;
    align-items: center;
}
</style>
