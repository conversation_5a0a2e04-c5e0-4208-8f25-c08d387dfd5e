/*
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-06-30 10:01:08
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-09-29 14:48:33
 * @FilePath: /website_web/deploy_website/src/view/server_batch_edit/api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from '@/libs/api.request'

export const get_java_app = params => {
    return axios.request({
        url: '/spider/app_mgt/get_java_app',
        params,
        method: 'get'
    })
}
export const get_active_suite = params => {
    return axios.request({
        url: '/spider/env_mgt/get_active_suite',
        params,
        method: 'get'
    })
}
export const get_team_name_list = params => {
    return axios.request({
        url: '/spider/team_mgt/get_team_name_list',
        params,
        method: 'get'
    })
}
export const batch_publish_app_list = params => {
    return axios.request({
        url: '/spider/app_mgt/batch_publish_app_list',
        params,
        method: 'get'
    })
}
export const save_restart_app = params => {
    return axios.request({
        url: '/spider/publish_mgt/save_reboot_app/',
        data: params,
        method: 'post'
    })
}

export const get_reboot_app_list = params => {
    return axios.request({
        url: '/spider/publish_mgt/get_reboot_app_list',
        params,
        method: 'get'
    })
}
export const save_reboot_flow = params => {
    return axios.request({
        url: '/spider/publish_mgt/save_reboot_flow/',
        data: params,
        method: 'post'
    })
}
export const check_reboot_flow = params => {
    return axios.request({
        url: '/spider/publish_mgt/check_reboot_flow/',
        data: params,
        method: 'post'
    })
}
export const get_reboot_flow = params => {
    return axios.request({
        url: 'spider/publish_mgt/get_reboot_flow',
        params,
        method: 'get'
    })
}
