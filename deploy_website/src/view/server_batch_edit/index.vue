<!--
 * @Author: chao.bao <EMAIL>
 * @Date: 2025-06-27 09:55:26
 * @LastEditors: chao.bao <EMAIL>
 * @LastEditTime: 2025-09-29 17:36:10
 * @FilePath: /website_web/deploy_website/src/view/server_batch_reset/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
    <Card class="service-publish-card">
        <div class="card-body">
            <!-- 编排 -->
            <div class="header_box">
                <h3 class="btn_item">通道</h3>
                <Button class="btn_item" type="primary" @click="add">添加通道</Button>
                <Button class="btn_item" type="primary" @click="handleShowModal">选应用</Button>
                <Form inline :model="formItem" :label-width="80">
                    <FormItem class="cutom-ivu-form-item" label="环境:">
                        <CheckboxGroup v-model="formItem.onlineEnv">
                            <Checkbox label="1">灾备</Checkbox>
                            <Checkbox label="2">产线</Checkbox>
                        </CheckboxGroup>
                    </FormItem>
                    <FormItem class="cutom-ivu-form-item" label="重启描述:">
                        <Input v-model="formItem.rebootDesc"></Input>
                    </FormItem>
                </Form>
            </div>
            <div class="main-container" @dragover="allowDrop">
                <div
                    v-for="(container, containerIndex) in containers"
                    :key="container.id"
                    class="container-box"
                    @drop="drop($event, container)"
                    @dragover="allowDrop"
                >
                    <div>通道-{{ containerIndex + 1 }}</div>
                    <div :class="isDragged ? 'container dragged' : 'container'">
                        <div
                            v-for="(item, index) in container.items"
                            :key="index"
                            class="item"
                            draggable="true"
                            @dragstart="drag($event, item, container)"
                        >
                            <div class="item_box">
                                <div>
                                    <div>{{ item.module_name }}</div>
                                    <!-- <div>{{ item.node_ip }}</div> -->
                                    <div>灾备节点数-{{ item.node_zb_count }}</div>
                                    <div>生产节点数-{{ item.node_prod_count }}</div>
                                </div>
                                <!-- 删除icon -->
                                <Icon
                                    type="ios-close"
                                    size="20"
                                    color="#f56c6c"
                                    style="cursor:auto"
                                    @click="deleteItem(containerIndex, index)"
                                />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="save_btn">
            <Poptip confirm :title="titleText" @on-ok="saveGroup">
                <!-- @click="saveGroup" -->
                <Button class="btn_item" type="primary" :disabled="formItem.onlineEnv.length === 0" :loading="loading"
                    >保存</Button
                >
            </Poptip>
        </div>
        <Modal v-model="showModal" title="选应用" width="1000">
            <Spin v-if="modalLoading" fix></Spin>
            <Form ref="formInline" :model="formInline" inline>
                <FormItem prop="user">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.module_name"
                        style="width:200px"
                        placeholder="按应用名筛选"
                    >
                        <Option v-for="item in app_name_list" :value="item" :key="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem prop="user">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.department_name"
                        style="width:200px"
                        placeholder="按部门筛选"
                    >
                        <Option v-for="item in department_list" :value="item" :key="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem prop="password">
                    <Select
                        clearable
                        filterable
                        v-model="formInline.team_name"
                        style="width:200px"
                        placeholder="按团队筛选"
                    >
                        <Option v-for="item in team_list" :value="item" :key="item">{{ item }}</Option>
                    </Select>
                </FormItem>
                <FormItem>
                    <Button type="primary" @click="handleFilter">查询</Button>
                </FormItem>
            </Form>
            <Table
                border
                ref="selection"
                :columns="columns"
                :data="curPageData"
                height="400"
                @on-selection-change="handleSelectionChange"
                @on-select-cancel="handleSelectionCancel"
            ></Table>
            <Page
                style="margin-top: 10px"
                :total="total"
                :page-size="page_size"
                :current="page_num"
                show-total
                @on-change="changePage"
            ></Page>
            <div slot="footer">
                <Button @click="cancel">取消</Button>
                <Button type="primary" @click="ok">确定</Button>
            </div>
        </Modal>
    </Card>
</template>

<script>
import { batch_publish_app_list, save_reboot_flow, get_reboot_flow, check_reboot_flow } from './api'
import { deepClone } from '@/utils'
export default {
    data() {
        return {
            modalLoading: false,
            formItem: {
                onlineEnv: ['1'],
                rebootDesc: ''
            },
            id: '',
            loading: false,
            // 下方容器数据
            containers: [
                {
                    id: 1,
                    items: []
                },
                {
                    id: 2,
                    items: []
                }
            ],
            isDragged: false,
            showModal: false,
            page_num: 1,
            page_size: 50,
            total: 0,
            formInline: {
                module_name: '',
                // suite_code: '',
                team_name: '',
                department_name: ''
            },
            columns: [
                {
                    type: 'selection',
                    width: 60,
                    align: 'center'
                },
                {
                    title: '应用名',
                    key: 'module_name'
                },
                {
                    title: '产线版本',
                    key: 'prod_br_name'
                },
                {
                    title: '灾备版本',
                    key: 'zb_br_name'
                },
                {
                    title: '所属部门',
                    key: 't1_name'
                },
                {
                    title: '所属团队',
                    key: 't2_name'
                }
            ],
            tableData: [],
            curPageData: [],
            app_name_list: [],
            department_list: [],
            team_list: [],
            selectedData: [] // 当前选中的数据
        }
    },
    computed: {
        titleText() {
            if (this.formItem.onlineEnv.length === 1) {
                if (this.formItem.onlineEnv[0] === '1') {
                    return '灾备'
                } else if (this.formItem.onlineEnv[0] === '2') {
                    return '生产'
                }
            } else if (this.formItem.onlineEnv.length === 2) {
                return '灾备+生产'
            }
            return ''
        }
    },
    methods: {
        saveGroup() {
            console.log('saveGroup-----------', this.containers)
            // 获取编排数据的数量
            let phase_num = 0
            this.containers.forEach(container => {
                phase_num += container.items.length
            })
            if (phase_num > 2000) {
                this.$Message.warning('编排节点数量不能超过2000')
                return
            }
            const flow_json = {}
            this.containers.map(container => {
                flow_json[`phase_${container.id}`] = container.items
            })
            console.log('flow_json-----------', flow_json)
            check_reboot_flow({
                flow_json
            }).then(res => {
                if (res.data.status === 'success') {
                    // 保存编排
                    save_reboot_flow({
                        id: this.id || '',
                        flow_json
                    }).then(saveRes => {
                        if (saveRes.data.status === 'success') {
                            this.$Message.success('保存成功')
                            this.$router.push('/ops_service/server_batch_reset')
                        } else {
                            this.$Message.error(saveRes.data.msg)
                        }
                    })
                } else {
                    // 弹出确认框，是否保存
                    this.$Modal.confirm({
                        title: '提示',
                        content: res.data.msg,
                        onOk: () => {
                            save_reboot_flow({
                                id: this.id || '',
                                flow_json
                            }).then(saveRes => {
                                if (saveRes.data.status === 'success') {
                                    this.$Message.success('保存成功')
                                    this.$router.push('/ops_service/server_batch_reset')
                                } else {
                                    this.$Message.error(saveRes.data.msg)
                                }
                            })
                        }
                    })
                }
            })
        },
        drag(event, item, container) {
            // container当前拖拽的对象所在的数组集合
            console.log('drag-----拖拽开始', event, item, container)

            this.isDragged = true
            event.dataTransfer.setData('itemData', JSON.stringify(item))
        },
        allowDrop(event) {
            console.log('allowDrop-----拖拽中')
            event.preventDefault()
        },
        drop(event, targetContainer, n) {
            // targetContainer是推拽对象的新容器
            console.log('drop-----拖拽鼠标释放', targetContainer)
            event.preventDefault()
            this.isDragged = false
            // 获取当前拖拽对象
            const itemData = JSON.parse(event.dataTransfer.getData('itemData'))
            const isExit = targetContainer.items.find(item => item.module_name === itemData.module_name)
            if (!isExit) {
                // 从原容器移除
                this.containers.forEach(container => {
                    container.items = container.items.filter(item => item.module_name !== itemData.module_name)
                })
                // 添加item到目标容器
                this.containers.forEach(container => {
                    if (container.id === targetContainer.id) {
                        container.items.unshift(itemData)
                    }
                })
            }
            console.log('this.containers-----------', this.containers)
        },
        add() {
            this.containers.push({
                id: this.containers.length + 1,
                // isApp: false,
                items: []
            })
        },
        // 删除item
        deleteItem(containerIndex, index) {
            this.containers[containerIndex].items.splice(index, 1)
            console.log('deleteItem-----------', containerIndex, index, this.containers)
        },
        // 弹框--------------------------------------------------------------------------------------------------------
        handleShowModal() {
            this.modalLoading = true
            // 状态重置
            this.formInline = {
                module_name: '',
                // suite_code: '',
                team_name: '',
                department_name: ''
            }
            this.tableData = []
            this.showModal = true
            this.handleSearch()
        },
        get_app_name_list() {
            this.tableData.forEach(item => {
                if (!this.app_name_list.includes(item.module_name)) {
                    this.app_name_list.push(item.module_name)
                }
            })
        },
        get_department_list() {
            this.tableData.forEach(item => {
                if (!this.department_list.includes(item.t1_name)) {
                    this.department_list.push(item.t1_name)
                }
            })
        },
        get_team_list() {
            this.tableData.forEach(item => {
                if (!this.team_list.includes(item.t2_name)) {
                    this.team_list.push(item.t2_name)
                }
            })
        },
        handleSearch() {
            batch_publish_app_list({
                restart_mode: 2
            })
                .then(res => {
                    console.log('batch_publish_app_list-----------', res)
                    if (res.data.status === 'success') {
                        this.tableData = res.data.data.app_list
                        this.total = this.tableData.length
                        // 获取应用下拉数据
                        this.get_app_name_list()
                        // 获取部门下拉数据
                        this.get_department_list()
                        // 获取团队下拉数据
                        this.get_team_list()
                        // 分页处理
                        this.page_num = 1
                        this.getList()
                        // this.handleFilter()
                        // 设置默认选中（比如选中第一条数据）
                        this.setDefaultSelection()
                    }
                })
                .finally(() => {
                    this.modalLoading = false
                })
        },
        getList() {
            // 根据筛选条件过滤数据
            const arr = this.tableData.filter(item => {
                // 根据formInline中的字段过滤数据
                const moduleNameMatch = this.formInline.module_name
                    ? item.module_name === this.formInline.module_name
                    : true
                const departmentNameMatch = this.formInline.department_name
                    ? item.t1_name === this.formInline.department_name
                    : true
                const teamNameMatch = this.formInline.team_name ? item.t2_name === this.formInline.team_name : true

                return moduleNameMatch && teamNameMatch && departmentNameMatch
            })
            this.total = arr.length
            // 使用过滤后的数据进行分页
            this.curPageData = arr.slice((this.page_num - 1) * this.page_size, this.page_num * this.page_size)
            this.curPageData.forEach(item => {
                if (this.selectedData.some(curItem => curItem.module_name === item.module_name)) {
                    item._checked = true
                }
            })
        },
        handleFilter() {
            this.page_num = 1
            this.getList()
        },
        // 设置默认选中的数据
        setDefaultSelection() {
            // 将containers中的数据转换成一维数组
            const defaultSelection = this.containers.reduce((acc, container) => {
                return acc.concat(container.items)
            }, [])
            this.curPageData.forEach(item => {
                if (defaultSelection.some(curItem => curItem.module_name === item.module_name)) {
                    item._checked = true
                }
            })
        },
        handleSelectionCancel(selection, row) {
            console.log('取消勾选', selection, row)
            this.selectedData = this.selectedData.filter(item => item.module_name !== row.module_name)
        },
        // 处理选择变化
        handleSelectionChange(selection) {
            console.log('勾选', selection)
            // this.selectedData = selection
            selection.forEach(item => {
                if (!this.selectedData.some(selectedItem => selectedItem.module_name === item.module_name)) {
                    this.selectedData.push(item)
                }
            })
        },
        ok() {
            this.containers.forEach(container => {
                container.items = []
            })
            this.containers[0].items = deepClone(this.selectedData)
            this.showModal = false
            this.selectedData = []
        },
        cancel() {
            this.showModal = false
            this.tableData = []
            this.total = 0
        },
        changePage(page) {
            this.page_num = page
            this.getList()
        },
        getRebootFlow() {
            get_reboot_flow({ id: this.id }).then(res => {
                if (res.data.status === 'success') {
                    for (const key in res.data.data.flow_json) {
                        this.containers.push({
                            id: key.split('_')[1],
                            items: res.data.data.flow_json[key]
                        })
                    }
                }
            })
        }
    },
    mounted() {
        this.id = this.$route.query.id
        if (this.id) {
            // 获取编排数据
            this.getRebootFlow()
        }
    }
}
</script>

<style lang="less" scoped>
.service-publish-card {
    background-color: #fff;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    display: block;
    .card-body {
        // padding: 10px;
        background-color: #fff;
        // margin-bottom: 20px;
        margin-bottom: 10px;

        .dragged {
            border: 1px dashed red;
        }
    }
}
.main-container {
    display: flex;
    border: 1px solid #00000014;
    border-radius: 4px;
    padding: 10px;
    overflow: auto;
    min-height: 300px;
}
.container-box {
    text-align: center;
}
.container {
    border: 1px solid #ccc;
    margin: 5px;
    padding: 10px;
    min-width: 200px;
    height: 400px;
    overflow-y: auto;
}
.item {
    border: 1px solid #00000008;
    background-color: #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    padding: 5px;
    margin: 5px;
    cursor: grab;
    min-width: 140px;
}
.item_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.save_btn {
    display: flex;
    justify-content: flex-end;
    margin-right: 10px;
}
.btn_item {
    margin-right: 10px;
}
.header_box {
    display: flex;
    margin: 10px 0;
    align-items: center;
    .cutom-ivu-form-item {
        margin-bottom: 0 !important;
    }
}
</style>
