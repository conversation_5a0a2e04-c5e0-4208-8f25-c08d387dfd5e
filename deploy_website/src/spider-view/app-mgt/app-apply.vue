<template>
    <div>
        <div style="margin-bottom: 15px">
            <h2>制品列表 <a href="http://gitlab-lib.howbuy.pa/lib_repo" target="abc">下载</a></h2>
        </div>
        <Card>
            <Row type="flex" justify="start" align="top" class="code-row-bg">
                <Col span="6">
                    <Select
                        placeholder="选择应用"
                        v-model="app_name_search"
                        filterable
                        clearable
                        style="width:200px"
                        @on-change="changeQuery"
                    >
                        <Option v-for="item in app_name_list" :value="item" :label="item" :key="item">
                            {{ item }}
                        </Option>
                    </Select>
                </Col>
                <Col span="6">
                    <Select
                        placeholder="选择分支名"
                        v-model="br_name_search"
                        filterable
                        clearable
                        style="width:200px"
                        @on-change="changeQueryBranch"
                    >
                        <Option v-for="item in br_name_list" :value="item" :label="item" :key="item">
                            {{ item }}
                        </Option>
                    </Select>
                </Col>
                <Col span="4">
                    <RadioGroup v-model="status" size="large" type="button" @on-change="changeQuery">
                        <Radio label="close"><span style="color:red;">历史制品</span></Radio>
                        <Radio label="open"><span>待发布制品</span></Radio>
                    </RadioGroup>
                </Col>
                <Col span="2">
                    <Button @click="test_suite_init" type="info">跳转Jenkins</Button>
                </Col>
                <!-- <Col span="2">
        <Button  @click="openModal(1)" type="info">上线申请</Button>
        </Col> -->
            </Row>
            <Divider />

            <Row justify="start">
                <Table ref="selection" highlight-row :columns="repos_column" :data="product_repos"></Table>
                <i-col>
                    <Page
                        style="margin: 5px;"
                        :total="pageTotal"
                        :current="pageNum"
                        @page-size="pageSize"
                        @on-change="changePage"
                        show-total
                    ></Page>
                </i-col>
            </Row>

            <Modal v-model="modal1" v-bind:title="m_title" width="720" @on-ok="ok" @on-cancel="cancel">
                <Row v-show="gr_show" style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">产线发布确认人</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Select
                            placeholder="邮箱地址"
                            style="margin-top: 5px"
                            v-model="allNotifyMails"
                            filterable
                            multiple
                        >
                            <Option v-for="item in allFilterMails" :value="item" :label="item" :key="item">
                                {{ item }}
                            </Option>
                        </Select>
                    </i-col>
                </Row>

                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">功能描述</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Input type="textarea" placeholder="功能描述" v-model="feature" />
                    </i-col>
                </Row>

                <Row>
                    <Table highlight-row :columns="select_column" :data="select_repos_data"></Table>
                </Row>
                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">发布时间</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <DatePicker
                            type="date"
                            v-model="date"
                            placeholder="Select date"
                            style="width: 8em"
                        ></DatePicker>
                        <TimePicker
                            format="HH:mm"
                            v-model="time"
                            placeholder="Select time"
                            style="margin: 1em;width: 6em;"
                        ></TimePicker>
                    </i-col>
                </Row>
                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">SQL</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Input type="textarea" placeholder="SQL内容" v-model="sql_content" />
                    </i-col>
                </Row>
                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">调度</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Input type="textarea" placeholder="调度内容" v-model="schedule" />
                    </i-col>
                </Row>
                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">配置</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Input type="textarea" placeholder="文件配置或ccms配置" v-model="file_ccms_config" />
                    </i-col>
                </Row>
                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">注意事项</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Input
                            type="textarea"
                            :autosize="{ minRows: 2, maxRows: 5 }"
                            placeholder="注意事项"
                            v-model="notice"
                        />
                    </i-col>
                </Row>
                <Row style="margin: 5px">
                    <i-col style="margin: 1em" span="2">
                        <span style="text-align: left; display: inline-block;">邮件列表</span>
                    </i-col>
                    <i-col style="margin: 5px" span="18">
                        <Select
                            placeholder="邮箱地址"
                            style="margin-top: 5px"
                            v-model="allSelectedMails"
                            filterable
                            multiple
                        >
                            <Option v-for="item in allFilterMails" :value="item" :label="item" :key="item">
                                {{ item }}
                            </Option>
                        </Select>
                    </i-col>
                </Row>

                <div slot="footer">
                    <Row style="margin: 5px">
                        <Button @click="cancel">取消</Button>
                        <Button v-show="gr_show" @click="saveReposPlan">保存</Button>
                        <Button v-show="gr_show" @click="ok">提交</Button>
                        <Button v-show="!gr_show" @click="confirm">确认</Button>
                    </Row>
                </div>
            </Modal>

            <Modal :mask-closable="false" :closable="false" v-model="apply_modal" width="60em" title="申请反馈">
                <div v-scrolltop style="height:300px; overflow-y: auto;">
                    <p v-for="(item, index) in apply_log" :key="index">{{ item }}</p>
                </div>
                <div slot="footer">
                    <Button @click="closeApplyModal">关闭</Button>
                </div>
            </Modal>

            <!-- 推送历史版本 -->
            <Modal v-model="test_publish_modal" width="768px">
                <p slot="header" style="color:gray;">
                    <Icon type="md-clipboard"></Icon>
                    <span>历史版本推送</span>
                </p>
                <Form
                    ref="test_publish_ref"
                    :model="test_publish_form"
                    :rules="test_publish_rule"
                    label-position="right"
                    :label-width="85"
                >
                    <FormItem prop="iteration_id" label="迭代号">
                        <Input
                            v-model="test_publish_form.iteration_id"
                            style="width: 24em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            placeholder="迭代号"
                            disabled
                        />
                    </FormItem>
                    <FormItem prop="app_name" label="应用名">
                        <Input
                            v-model="test_publish_form.app_name"
                            style="width: 24em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            placeholder="应用名"
                            disabled
                        />
                    </FormItem>
                    <FormItem prop="br_name" label="制品分支">
                        <Input
                            v-model="test_publish_form.br_name"
                            style="width: 24em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            placeholder="制品分支"
                            disabled
                        />
                    </FormItem>
                    <div style="display: flex; justify-content: flex-start; padding-left: 100px;">
                        <Button type="text" size="small" style="margin-right: 5px;" @click="checkAll">全选</Button>
                        <Button type="text" size="small" style="margin-right: 5px;" @click="checkAllCancel"
                            >清空</Button
                        >
                        <Button type="text" size="small" style="margin-right: 5px;" @click="checkOther">反选</Button>
                    </div>

                    <FormItem prop="suite_list" label="可用环境套">
                        <CheckboxGroup
                            ref="suite_list_ckg"
                            v-model="test_publish_form.suite_list"
                            style="width: 24em; font-size: 1rem; padding-left: 1em; color: #2d8cf0;"
                            @on-change="suite_change"
                        >
                            <Checkbox
                                v-for="suite_item in suite_item_list"
                                :label="suite_item.suite_code"
                                :key="suite_item.suite_code"
                                v-show="suite_item.is_show"
                            >
                                {{ suite_item.suite_code }}
                            </Checkbox>
                        </CheckboxGroup>
                    </FormItem>
                    <AgentComponent ref="agentRef" />
                    <div style="height: 10px;"></div>
                    <!-- <FormItem prop="is_mock_agent" label="mock开关">
                        <RadioGroup v-model="test_publish_form.is_mock_agent">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="is_jacoco_agent" label="jacoco开关">
                        <RadioGroup v-model="test_publish_form.is_jacoco_agent">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="is_arex_agent" label="arex开关">
                        <RadioGroup v-model="test_publish_form.is_arex_agent">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="is_pinpoint_agent" label="pinpoint开关">
                        <RadioGroup v-model="test_publish_form.is_pinpoint_agent">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem>
                    <FormItem prop="is_api_agent" label="api扫描开关">
                        <RadioGroup v-model="test_publish_form.is_api_agent">
                            <Radio :label="1">是</Radio>
                            <Radio :label="0">否</Radio>
                        </RadioGroup>
                    </FormItem> -->
                    <!-- <FormItem prop="is_sharding_agent" label="sharding扫描开关">
            <RadioGroup v-model="test_publish_form.is_sharding_agent">
              <Radio :label="1">是</Radio>
              <Radio :label="0">否</Radio>
            </RadioGroup>
          </FormItem> -->
                    <Table ref="node_table" :columns="node_column" :data="node_data"></Table>
                </Form>

                <div slot="footer">
                    <Button @click="order_cancel">关闭</Button>
                    <Button type="primary" @click="order_confirm">确定</Button>
                </div>
            </Modal>
        </Card>
    </div>
</template>
<script>
import { getProductReposApi } from '@/spider-api/app-mgt'
import { getEmailAddresses, publishApply, getApplyNotice } from '@/spider-api/iter-plan'
import {
    saveReposPlanApi,
    getReposPlanApi,
    productPublishConfirmApi,
    getConfirmIterationApi,
    updateConfirmInfo
} from '@/spider-api/product-publish-apply'

import { getAgentUseInfo, getSuiteList, getTestNodeBind, pipelineTestPublishSuiteApi } from '@/spider-api/mgt-env'
import { getSuiteCodes } from '@/spider-api/es-backup'
import { pushTestEnv } from '@/spider-api/pipeline'
import { forEach } from '../../libs/tools'
import { getAppModule } from '@/spider-api/mgt-app'
import AgentComponent from '@/spider-components/AgentComponent/index.vue'

export default {
    components: {
        AgentComponent
    },
    data() {
        return {
            modal1: false,
            pageNum: 1,
            pageSize: 10,
            pageTotal: 0,
            status: 'close',
            gr_show: true,
            m_title: '上线申请单',
            apply_modal: false,
            all_repos_data: [],
            app_name_search: '',
            app_name_list: [],
            apply_log: [],
            br_name_search: '',
            br_name_list: [],
            allSelectedMails: [],
            allFilterMails: [],
            select_repos_data: [],
            iteration_id_list: [],
            sql_content: '',
            schedule: '',
            file_ccms_config: '',
            notice: '',
            date: '',
            time: '',
            feature: '',
            md5_str: '',
            allNotifyMails: '',
            repos_column: [
                { type: 'selection', width: 60, align: 'center' },
                { title: '迭代号', key: 'iteration_id' },
                { title: '应用名', key: 'app_name' },
                { title: '制品分支', key: 'br_name' },
                { title: '包类型', key: 'packageType' },
                {
                    title: '是否最近归档版本',
                    key: 'online_br_name',
                    align: 'center',
                    render: (h, params) => {
                        if (params.row.online_br_name != null) {
                            return h(
                                'span',
                                {
                                    style: {
                                        color: '#187'
                                    }
                                },
                                '是'
                            )
                        }
                    }
                },
                { title: '制品库版本', key: 'repos_version' },
                { title: '归档时间', key: 'br_end_date' },
                {
                    title: '推送测试环境',
                    key: 'handle',
                    align: 'center',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    attrs: {
                                        class: 'ivu-btn ivu-btn-primary ivu-btn-small'
                                    }, //button-编辑-attrs
                                    props: {
                                        type: 'primary',
                                        size: 'small'
                                    }, //button-编辑-props
                                    style: {
                                        marginRight: '6px'
                                    }, //button-编辑-style
                                    on: {
                                        click: () => {
                                            this.test_publish_modal = true
                                            this.test_publish_form.iteration_id = params.row.iteration_id
                                            this.test_publish_form.app_name = params.row.app_name
                                            this.test_publish_form.br_name = params.row.br_name
                                            this.testPublishPush()
                                            this.$refs.agentRef.queryList(
                                                [this.test_publish_form.app_name],
                                                this.test_publish_form.suite_list
                                            )
                                        }
                                    } //button-编辑-on
                                },
                                '推送'
                            ) //button-编辑
                        ]) //return
                    } //render
                } //操作
            ],
            select_column: [
                { title: '迭代号', key: 'iteration_id' },
                { title: '应用名', key: 'app_name' },
                { title: '制品分支', key: 'br_name' }
            ],
            product_repos_data: [],
            product_repos: [],

            test_publish_modal: false,
            test_publish_form: {
                iteration_id: '',
                app_name: '',
                br_name: '',
                suite_list: [],
                is_mock_agent: 0,
                is_jacoco_agent: 0,
                is_arex_agent: 0,
                is_pinpoint_agent: 0,
                is_api_agent: 0
                // is_sharding_agent: 0
            },
            test_publish_rule: {
                iteration_id: [{ required: true, message: '迭代号不能为空', trigger: 'blur' }],
                app_name: [{ required: true, message: '应用名不能为空', trigger: 'blur' }],
                br_name: [{ required: true, message: '制品分支不能为空', trigger: 'blur' }],
                suite_list: [{ required: true, message: '环境套不能为空', trigger: 'blur' }],
                is_mock_agent: [{ required: true, message: 'mock agent不能为空', trigger: 'blur' }],
                is_jacoco_agent: [{ required: true, message: 'jacoco agent不能为空', trigger: 'blur' }],
                is_arex_agent: [{ required: true, message: 'arex agent不能为空', trigger: 'blur' }],
                is_pinpoint_agent: [{ required: true, message: 'pinpoint agent不能为空', trigger: 'blur' }],
                is_api_agent: [{ required: true, message: 'api agent不能为空', trigger: 'blur' }]
                // is_sharding_agent: [{ required: true, message: 'sharding agent不能为空', trigger: 'blur' }]
            },
            suite_item_list: [],
            node_column: [
                { type: 'selection', width: 60, align: 'center' },
                { title: '环境套', key: 'suite_code' },
                { title: '节点类型', key: 'node_type' },
                { title: '节点编码', key: 'node_code' },
                { title: '节点名', key: 'node_name' }
            ],
            node_data: [],
            suite_code_list: [],
            suite_list_model: []
        }
    }, //data

    methods: {
        checkAll() {
            // 全选
            this.test_publish_form.suite_list = []
            this.suite_item_list.forEach(item => {
                this.test_publish_form.suite_list.push(item.suite_code)
            })
            this.suite_change()
        },
        checkAllCancel() {
            // 清除
            this.test_publish_form.suite_list = []
            this.suite_change()
        },
        checkOther() {
            // 反选
            const arr = []
            this.suite_item_list.forEach(item => {
                if (!this.test_publish_form.suite_list.includes(item.suite_code)) {
                    arr.push(item.suite_code)
                }
            })
            this.test_publish_form.suite_list = arr
            this.suite_change()
        },
        test_suite_init() {
            window.open('http://jkp-master.howbuy.pa/jenkins/view/TD(TEI)/job/test_suite_init_history/')
        },
        changePage(idx) {
            let page_data = {}
            this.pageNum = idx
            page_data['pageNum'] = idx
            page_data['pageSize'] = this.pageSize
            page_data['pageTotal'] = this.pageTotal
            let params = {
                status: this.status,
                page_data: page_data,
                app_name: this.app_name_search,
                br_name: this.br_name_search
            }
            getProductReposApi(params).then(res => {
                this.product_repos = res.data.data['data_list']
                this.all_repos_data = res.data.data['data_list']
                this.br_name_list = res.data.data['br_name_list']
                this.page_list = res.data.data['page_list']
                this.pageTotal = this.page_list['page_total']
            })
        },
        getSuiteItemList() {
            let data = {
                suite_is_active: 1,
                region_id: 32 //pd-test zt@2022-04-18
            }
            getSuiteList(data).then(res => {
                this.suite_item_list = res.data.data['data_list']
            })
        },
        // 推送测试环境
        testPublishPush() {
            this.test_publish_form.suite_list = []
            this.suite_code_list = []

            let data = {
                app_name: this.test_publish_form.app_name
            }

            getTestNodeBind(data).then(res => {
                // 从后端获取节点数据
                let node_list = res.data.data['data_list']
                // 初始化所有节点不能被直接选中
                for (let i = 0; i < node_list.length; i++) {
                    let node_obj = node_list[i]
                    node_obj['_disabled'] = true

                    let suite_code = node_obj['suite_code']
                    if (!this.suite_code_list.includes(suite_code)) {
                        this.suite_code_list.push(suite_code)
                    }
                }
                this.node_data = node_list
                // 反向筛选出要显示的环境套
                for (let i = 0; i < this.suite_item_list.length; i++) {
                    let suite_item = this.suite_item_list[i]
                    let suite_code = suite_item['suite_code']
                    if (this.suite_code_list.includes(suite_code)) {
                        suite_item['is_show'] = true
                    } else {
                        suite_item['is_show'] = false
                    }
                }
            })
        },
        // 环境套变化
        suite_change() {
            this.$refs.agentRef.queryList([this.test_publish_form.app_name], this.test_publish_form.suite_list)
            for (let i = 0; i < this.node_data.length; i++) {
                let node_obj = this.node_data[i]
                let suite_code = node_obj['suite_code']
                if (this.test_publish_form.suite_list.includes(suite_code)) {
                    node_obj['_checked'] = true
                    node_obj['_disabled'] = false
                } else {
                    node_obj['_checked'] = false
                    node_obj['_disabled'] = true
                }
            }
            let suite_code = this.test_publish_form.suite_list[0]
            let app_name = this.test_publish_form.app_name
            // 取运行时的agent使用信息
            let agent_type = '2'
            getAgentUseInfo(suite_code, app_name, agent_type).then(res => {
                let agent_use_info_list = res.data.data
                let mock_type = 0
                let jacoco_type = 0
                let arex_type = 0
                let pinpoint_type = 0
                let api_type = 0
                for (let i = 0; i < agent_use_info_list.length; i++) {
                    if (agent_use_info_list[i].agent_name === 'howbuy-mock-agent') {
                        mock_type = parseInt(agent_use_info_list[i].is_use)
                    } else if (agent_use_info_list[i].agent_name === 'JaCoCo') {
                        jacoco_type = parseInt(agent_use_info_list[i].is_use)
                    } else if (agent_use_info_list[i].agent_name === 'arex-agent') {
                        arex_type = parseInt(agent_use_info_list[i].is_use)
                    } else if (agent_use_info_list[i].agent_name === 'howbuy-interface-scan-agent') {
                        api_type = parseInt(agent_use_info_list[i].is_use)
                    } else if (agent_use_info_list[i].agent_name === 'pinpoint-agent') {
                        pinpoint_type = parseInt(agent_use_info_list[i].is_use)
                    }
                }
                this.test_publish_form.is_mock_agent = mock_type
                this.test_publish_form.is_jacoco_agent = jacoco_type
                this.test_publish_form.is_arex_agent = arex_type
                this.test_publish_form.is_pinpoint_agent = pinpoint_type
                this.test_publish_form.is_api_agent = api_type
            })
        },
        // 确定
        order_confirm() {
            this.test_publish_modal = false
            let data = {
                pipeline_id: this.test_publish_form.iteration_id,
                app_name: this.test_publish_form.app_name,
                br_name: this.test_publish_form.br_name,
                suite_list: this.test_publish_form.suite_list,
                mock_type: this.test_publish_form.is_mock_agent,
                jacoco_type: this.test_publish_form.is_jacoco_agent,
                api_type: this.test_publish_form.is_api_agent,
                arex_type: this.test_publish_form.is_arex_agent,
                pinpoint_type: this.test_publish_form.is_pinpoint_agent
                // 'node_list': this.$refs.node_table.getSelection(),
            }
            pipelineTestPublishSuiteApi(data)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error('发生异常')
                })
        },
        // 取消
        order_cancel() {
            this.test_publish_modal = false
        },
        changeQuery() {
            let page_data = {}
            this.pageNum = 1
            this.pageSize = 10
            this.pageTotal = 0
            this.br_name_search = ''
            page_data['pageNum'] = this.pageNum
            page_data['pageSize'] = this.pageSize
            page_data['pageTotal'] = this.pageTotal
            let params = {
                status: this.status,
                page_data: page_data,
                app_name: this.app_name_search,
                br_name: this.br_name_search
            }
            getProductReposApi(params).then(res => {
                this.product_repos = res.data.data['data_list']
                this.page_list = res.data.data['page_list']
                this.pageTotal = this.page_list['page_total']
                this.br_name_list = res.data.data['br_name_list']
            })
        },
        changeQueryBranch() {
            let page_data = {}
            this.pageNum = 1
            this.pageSize = 10
            this.pageTotal = 0
            page_data['pageNum'] = this.pageNum
            page_data['pageSize'] = this.pageSize
            page_data['pageTotal'] = this.pageTotal
            let params = {
                status: this.status,
                page_data: page_data,
                app_name: this.app_name_search,
                br_name: this.br_name_search
            }
            getProductReposApi(params).then(res => {
                this.product_repos = res.data.data['data_list']
                this.page_list = res.data.data['page_list']
                this.pageTotal = this.page_list['page_total']
            })
        },
        get_history_data(pipeline_id, env_name) {
            getApplyNotice(pipeline_id, env_name)
                .then(res => {
                    this.apply_log = this.apply_log.concat(res.data.data)
                    if ('end' == res.data.msg) {
                        //alert("上线申请结束，请查收邮件")
                        this.apply_log = this.apply_log.concat('上线申请结束，请查收邮件')
                    } else {
                        let vm = this
                        //setTimeout(function(){vm.getStatus(sid)},2000)
                        setTimeout(function() {
                            vm.get_history_data(pipeline_id, env_name)
                        }, 2000)
                    }
                })
                .catch(err => {})
        },
        closeApplyModal() {
            this.apply_modal = false
            if (this.switch_history) {
                clearInterval(this.switch_history)
            }
        },

        saveReposPlan() {
            let data = {}
            data['receivers'] = this.allSelectedMails.join(',')
            data['repos_info'] = this.$refs.selection.getSelection()
            data['date'] = this.date
            data['time'] = this.time
            data['feature'] = this.feature
            data['sql_content'] = this.sql_content
            data['schedule'] = this.schedule
            data['file_ccms_config'] = this.file_ccms_config
            data['notice'] = this.notice
            saveReposPlanApi(data)
                .then(res => {
                    console.log(JSON.stringify(res))
                    if (res.data.status == 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
            return data
        },
        ok() {
            let data = this.saveReposPlan()
            data['domain'] = window.location.host
            data['assert_receiver'] = this.allNotifyMails.join(',')
            data['env'] = 'prod'
            productPublishConfirmApi(data)
                .then(res => {
                    console.log(JSON.stringify(res))
                    if (res.data.status == 'success') {
                        this.$Message.success(res.data.msg)
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
            console.log(this.$refs.selection.getSelection())
            this.modal1 = false
            this.$Message.info('Clicked ok')
        },
        confirm() {
            this.env_name = 'prod'
            this.$Spin.show({
                render: h => {
                    return h('div', [
                        h('Icon', {
                            class: 'demo-spin-icon-load',
                            props: {
                                type: 'ios-loading',
                                size: 18
                            }
                        }),
                        h('div', '产线申请中...')
                    ])
                }
            })
            console.log(this.iteration_id_list)
            for (let i = 0; i < this.iteration_id_list.length; i++) {
                console.log(this.iteration_id_list[i])
                console.log(this.env_name)
                publishApply(this.iteration_id_list[i], this.env_name)
                    .then(result => {
                        if (result.data.status === 'success') {
                            this.$Message.success(result.data.msg)
                            this.apply_modal = true
                            this.get_history_data(this.iteration_id_list[i], this.env_name)
                            //this.switch_history = setInterval(this.get_history_data(this.iteration_id_list[i], this.env_name), 3000)
                        } else {
                            alert(result.data.msg)
                        }

                        if (i == this.iteration_id_list.length - 1) {
                            this.$Spin.hide()
                            this.cancel()
                            updateConfirmInfo({
                                md5_str: this.md5_str,
                                status: 'failure'
                            })
                                .then(res => {
                                    console.log(result.data.msg)
                                })
                                .catch(err => {
                                    this.$Message.error(err.response.data.msg)
                                })
                        }
                    })
                    .catch(errs => {
                        this.$Spin.hide()
                        console.log(errs.response)
                    })
            }
        },
        cancel() {
            this.modal1 = false
            this.select_repos_data = []

            if (!this.gr_show) {
                this.gr_show = true
                this.$router.push({ name: 'app_apply' })
            }
            this.$Message.info('Clicked cancel')
        },

        openModal(repos_data) {
            this.modal1 = true
            if (repos_data == 1) {
                this.select_repos_data = this.$refs.selection.getSelection()
                //校验不能出现相同的应用
                let app_list = []
                for (let i = 0; i < this.select_repos_data.length; i++) {
                    if (app_list.includes(this.select_repos_data[i]['app_name'])) {
                        alert('存在不同版本的相同应用' + this.select_repos_data[i]['app_name'])
                        this.modal1 = false
                    }
                    console.log(this.select_repos_data[i]['app_name'])
                    app_list.push(this.select_repos_data[i]['app_name'])
                }
            } else {
                this.select_repos_data = repos_data
            }

            getReposPlanApi(this.select_repos_data)
                .then(res => {
                    if (res.data.status === 'success') {
                        this.sql_content = res.data.data['sql_content']
                        this.schedule = res.data.data['schedule']
                        this.file_ccms_config = res.data.data['file_ccms_config']
                        this.notice = res.data.data['notice']
                        this.feature = res.data.data['feature']
                        this.date = res.data.data['date']
                        this.time = res.data.data['time']
                        this.allSelectedMails = res.data.data['receivers']
                        this.app_detail = res.data.data['app_detail']
                    } else {
                        this.$Message.error(res.data.msg)
                    }
                })
                .catch(err => {
                    this.$Message.error(err.response.data.msg)
                })
        }
    }, //methods
    mounted() {
        this.getSuiteItemList()

        getEmailAddresses().then(res => {
            if (res.data.status === 'success') {
                this.allFilterMails = res.data.data
                //this.allFilterMails = this.allMails;
            }
        })

        getAppModule().then(res => {
            let module_info_list = res.data.data['data_list']
            module_info_list.forEach(item => {
                this.app_name_list.push(item['module_name'])
            })
        })

        if (JSON.stringify(this.$route.query) !== '{}') {
            let data_list = []
            let page_data = {}
            page_data['pageNum'] = this.pageNum
            page_data['pageSize'] = this.pageSize
            page_data['pageTotal'] = this.pageTotal
            this.md5_str = this.$route.query['md5Str']
            getConfirmIterationApi(this.md5_str).then(res => {
                if (res.data.status === 'success') {
                    this.status = res.data.data['br_status']
                    let iter_str = res.data.data['iteration_id_list'].replace(/'/g, '"')
                    console.log(iter_str)
                } else {
                    alert(res.data.msg)
                    this.$router.push({ name: 'app_apply' })
                }
            })
        } else {
            let page_data = {}
            page_data['pageNum'] = this.pageNum
            page_data['pageSize'] = this.pageSize
            page_data['pageTotal'] = this.pageTotal
            getProductReposApi({ status: 'close', page_data: page_data }).then(res => {
                this.page_list = res.data.data['page_list']
                this.pageTotal = this.page_list['page_total']
                this.product_repos = res.data.data['data_list']
                this.all_repos_data = res.data.data['data_list']
                this.br_name_list = res.data.data['br_name_list']
            })
        }
        //alert(JSON.stringify(this.$route.query))
        //this.doProdPublishApply(this.$route.query);
    } //mounted
} //default
</script>

<style></style>
