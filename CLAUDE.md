# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Environment

### Python Environment
- **Python Version**: 3.7 (required for production), but modern projects use 3.9+
- **Package Management**: Dual support for both Pipenv and UV
  - Pipenv: `Pipfile`/`Pipfile.lock` (legacy)
  - UV: `pyproject.toml` with dependency groups (preferred for new development)
- **Dependencies**: Production deps in `be-scripts/requirements.txt`, dev deps managed via package managers
- **Execution**: Use `python37` and `pip37` commands on script server (`/usr/be-scripts/be-scripts`)

### Code Quality Tools
- **Linting**: Pylint with custom configurations
  - Main config: `pylint.conf` (general settings, 100 char line length)
  - Test config: `pylint_ztst.conf` (SCM Python standards, 125 char line length)
  - Run: `pylint --rcfile=pylint.conf <module>` or `pylint --rcfile=pylint_ztst.conf <test_module>`
- **Code Style**: 
  - 4-space indentation
  - 125 character max line length (SCM standard)
  - Snake_case naming for functions/variables
  - PascalCase for classes
  - SCM standards require `max-statements=50` per method

## Project Structure

### Core Architecture
This is a backend script repository for CI/CD pipeline automation and application management. The codebase follows a modular architecture with clear separation of concerns using a layered service pattern:

#### Domain Modules (`be-scripts/`)
- **app_mgt**: Application management (API documentation, sharding, branch operations)
- **db_mgt**: Database management operations (backup, restore, schema management)
- **iter_mgt**: Iteration management (publishing plans, pipeline coordination)
- **jenkins_mgt**: Jenkins job management and pipeline creation
- **product_mgt**: Product management and artifact repository operations
- **qc_mgt**: Quality control management
- **test_mgt**: Test environment management and data preparation

#### Pipeline Integration
- **ci_pipeline**: Core pipeline execution framework with template strategies
  - BasePipeline class with step recording and rollback mechanisms
  - Template strategies for serial, parallel, and one-n deployment patterns
- **test_publish_aio**: Modern test environment initialization pipelines (async)
- **test_pipeline**: Legacy test environment pipelines
- **publish_tool**: Deployment publishing utilities and artifact management

#### Infrastructure Integration
- **common/**: Shared utilities and external service integrations
  - `call_api/`: External service APIs (Jenkins, GitLab, SaltStack, Nacos)
  - `files/`: File and XML processing utilities
  - `ext_cmd/`: External command execution (shell, git)
- **utils/**: Core utility functions (K8s, test environment, compilation)
- **job/**: Job definitions and execution logic for Jenkins integration

### Data Access Layer Architecture
The project uses a comprehensive 3-tier architecture pattern:

**DAO Layer (`dao/`)**:
- **`get/mysql/`**: MySQL data access objects with connection pooling
- **`connect/`**: Database connection management (MySQL, Oracle, PostgreSQL)
- **`base_model.py`**: Common database models and utilities
- **Connection Management**: Uses singleton pattern for connection reuse

**Service Layer (`*/service/`)**:
- Business logic coordination between DAO and BO layers
- Transaction management and error handling
- Integration with external services

**Business Object Layer (`*/bo/`)**:
- Data transfer objects and business entities
- Validation and business rule enforcement
- API contracts between layers

### Key Architectural Patterns

#### Pipeline Template Pattern
The `ci_pipeline` module implements a strategy pattern for different deployment scenarios:
- **Serial Execution**: Sequential step execution for simple deployments
- **Parallel Execution**: Concurrent step execution for performance optimization
- **One-n Pattern**: Specialized handling for single-node deployments
- **Step Recording**: Automatic execution tracking via `PipelineRecorder` decorator

#### Configuration Management
- **Centralized Settings**: `settings.py` loads configuration from `settings.ini`
- **Environment-specific configs**: Database, email, Jenkins credentials separated
- **Logging**: Configured via `log.ini`, outputs to Jenkins console only (no local files)
- **Encoding Awareness**: GBK on Windows, UTF-8 otherwise

#### External API Integration
- **Jenkins**: Build/job management via `common/call_api/be_jenkins/`
- **GitLab**: Merge request and repository operations via `python-gitlab`
- **SaltStack**: Configuration management and orchestration
- **Nacos**: Service discovery and configuration management
- **Connection Management**: All API clients use singleton pattern

## Common Development Commands

### Environment Setup
```bash
# Install dependencies using UV (preferred)
uv sync

# Or using Pipenv (legacy)
pipenv install

# Or using pip directly
pip37 install -r be-scripts/requirements.txt

# Run individual scripts
python37 be-scripts/path/to/script.py
```

### Code Quality
```bash
# Lint code with main config (general code)
pylint --rcfile=pylint.conf be-scripts/module/

# Lint test code with SCM standards (stricter rules)
pylint --rcfile=pylint_ztst.conf be-scripts/test_module/

# Check specific file
pylint --rcfile=pylint.conf be-scripts/common/files/xml/pom.py
```

### Testing
```bash
# Run individual test files
python37 be-scripts/be_test/test_specific_module.py

# Test environment initialization scripts (typically executed via Jenkins pipelines)
# No centralized test runner - tests are executed as needed
```

### Database Operations
```bash
# Database migrations are handled via service layer, not direct SQL
# Use existing DAO patterns for database operations
```

## Configuration Management

### Settings Structure
- **settings.py**: Main configuration loader with logging setup and database connections
- **settings.ini**: Environment-specific configuration (database, email, Jenkins, AI helper)
- **log.ini**: Logging configuration for Jenkins console output

### Key Configuration Sections
- **Database**: MySQL (primary), Oracle (CDC operations), PostgreSQL
- **Jenkins API**: Server credentials and job management
- **Email Service**: Notification and alerting
- **AI Helper**: Configuration for AI-assisted development
- **Product Store**: Artifact repository management
- **Archive Paths**: SQL and configuration archive locations

### Database Connection Patterns
```python
# Use existing DAO patterns - don't create direct connections
from dao.get.mysql import common_service_artifactinfo
from dao.connect.mysql import DBConnectionManager

# Connection pooling is handled at the service layer
# Always use existing transaction management patterns
```

## Pipeline Architecture

### Base Pipeline Framework
The `ci_pipeline` module provides a flexible pipeline execution framework:

**Core Components**:
- **BasePipeline**: Abstract base class with step execution and recording
- **Template Strategies**: Deployment pattern implementations
- **PipelineRecorder**: Decorator for automatic step tracking
- **Rollback Mechanisms**: Error handling and state recovery

### Execution Flow
1. Pipeline configuration loaded from settings and database
2. Step execution via `run_step()` method with validation
3. Status tracking through `PipelineRecorder` decorator
4. Automatic rollback on failure with state restoration
5. Result reporting and logging to Jenkins console

## Important Implementation Notes

### Script Execution Environment
- **Server Location**: Scripts run on dedicated script server at `/usr/be-scripts/be-scripts`
- **Output Management**: All output goes to Jenkins console (no local file logging)
- **Step Descriptions**: Use `step_desc` decorator for pipeline step descriptions
- **Encoding Handling**: System encoding varies (GBK on Windows, UTF-8 otherwise)

### Database Integration Patterns
- **Primary Database**: MySQL for application data
- **Secondary**: Oracle for CDC operations and legacy systems
- **Connection Management**: Use `DBConnectionManager` for connection pooling
- **Transaction Management**: Handle transactions at service layer, not DAO layer
- **Existing Patterns**: Always use existing DAO patterns, don't create direct connections

### External API Integration Guidelines
- **Jenkins Integration**: Use `common/call_api/be_jenkins/` for all Jenkins operations
- **Git Operations**: Use `common/ext_cmd/git_cmd.py` for Git operations
- **Singleton Pattern**: All API clients use singleton pattern for connection management
- **Error Handling**: Implement proper error handling and retry mechanisms

### Code Quality and Standards
- **Method Length**: SCM standards require max 50 statements per method
- **Line Length**: 125 characters max for SCM compliance
- **Documentation**: Use Chinese comments for business logic, English for technical code
- **Error Messages**: Provide detailed error messages in Chinese for user-facing errors

### Threading and Concurrency
- **Thread Safety**: Use `threading.RLock()` for shared resource protection
- **Concurrent Processing**: Use `ThreadPoolExecutor` for parallel operations
- **Resource Management**: Implement proper resource cleanup in finally blocks
- **Performance**: Consider ramdisk usage for I/O intensive operations

### Security Considerations
- **Credential Management**: Never hardcode credentials in code
- **Configuration**: Use settings.ini for all sensitive configuration
- **Access Control**: Implement proper access controls for external API calls
- **Logging**: Don't log sensitive information or credentials