#!groovy
/********************************
# Descript：
#   「DevOps」系统自动发布脚本（测试环境）。
#   目前只支持新阵列的「be-scripts、hm-scripts」。
#   sh「多行模式」不支持「局部变量」只能使用「环境变量」。
#   sh「多行模式」不支持「env.、params.」写法。
#   sh「多行模式」局部变量：一定要使用「双引号」，单引号不行！！！（2024-05-13多次尝试后得到结论）
#   sh「多行模式」单引号：如果命令中有单引号，只能使用三个单引号，不能使用三个双引号！！！（2025-08-05）
# History:
# 	2022-11-21  Zt  First Release。
# 	2022-12-13  Zt  简化日志输出。
# 	2022-12-13  Zt  优化结果列表。
# 	2022-12-19  Zt  简化 & Debug模式。
# 	2023-04-17  Zt  新增「p-scripts」。
# 	2024-05-10  Zt  添加DevOps平台的服务类应用发布支持。
# 	2024-05-13  Zt  优化使用公共方法，减少jenkins_file的长度。
# 	2025-07-31  Zt  尝试添加服务类应用：spider、mantis。
# 	2025-08-04  Zt  DevOps服务类应用发布支持。
# 	2025-08-05  Zt  尝试Scripted Pipeline。
********************************/

node('nfs1') {
    // 参数定义
    properties([
        parameters([
            choice(
                name:'PUBLISH_APP',
                choices:['', 'be-scripts', 'hm-scripts', 'p-scripts', '3-scripts', 'spider', 'mantis', '2-servers', 'ALL'],
                description:'需要发布的「应用名」，ALL代表所有应用。'
            ),
            string(
                name: 'PUBLISH_BRANCH',
                defaultValue: '2.112.0',
                description: "需要发布的「分支」。"
            ),
            choice(
                name:'PUBLISH_SUITE',
                choices:['test'],
                description:'需要发布的「环境」：test、prod'
            ),
            booleanParam(
                name: 'IS_REBUILD_IMAGE',
                defaultValue: true,
                description: '是否重新构建镜像'
            ),
            booleanParam(
                name: 'IS_DEBUG',
                defaultValue: false,
                description: '调试模式：打印环境信息。'
            )
        ])
    ])

    // 环境变量设置
    env.PYTHON_CMD="python3.x"
    //同步「根」目录（同步目录汇总，但本身并不会同步）
    env.SYNC_PATH="/data/devops_sync"
    //同步「环境」目录（具体的一个同步目录）
    env.SYNC_ENV_PATH="$SYNC_PATH/devops_sync_${params.PUBLISH_SUITE}"
    // Git仓库地址：
    env.SCM_BES_GIT_URL="*************************:scm/be-scripts.git"
    env.SCM_HMS_GIT_URL="*************************:scm/hm-scripts.git"
    env.SCM_PS_GIT_URL="*************************:scm/be-scripts.git"
    env.SCM_SPIDER_GIT_URL="*************************:scm/spider.git"
    env.SCM_MANTIS_GIT_URL="*************************:scm-qa/mantis.git"
    //harbor仓库地址：
    env.HARBOR_REPO_URL="harbor-test.inner.howbuy.com"
    env.HARBOR_PROJ_NAME="pa"

    try {
        stage('FORMAT') {
            manager.addShortText("${env.BUILD_USER}",'black','lightgreen','5px','yellow')
            currentBuild.description = "发布应用: ${params.PUBLISH_APP}\n"
            currentBuild.description += "发布环境: ${params.PUBLISH_SUITE}\n"
            currentBuild.description += "发布版本: ${params.PUBLISH_BRANCH}\n"
            currentBuild.description += "构建镜像: ${params.IS_REBUILD_IMAGE}"
            println "==== NODE_NAME: ${env.NODE_NAME}"
        }

        stage('DEBUG') {
            if (params.IS_DEBUG) {
                def debugTasks = [:]

                debugTasks['节点'] = {
                    stage('01-node') {
                        println "==== NODE_NAME: ${env.NODE_NAME}"
                    }
                    stage('02-ssh') {
                        println "==== SSH_CONNECTION: ${env.SSH_CONNECTION}"
                    }
                }

                debugTasks['环境'] = {
                    stage('01-「PATH」') {
                        println "==== PATH(os): ${env.PATH}"
                    }
                    stage('02-环境变量') {
                        sh "printenv"
                    }
                }

                // 执行并行构建
                parallel debugTasks
            }
        }

        stage('参数验证') {
            if (!params.PUBLISH_APP || params.PUBLISH_APP.trim().isEmpty()) {
                error "❌ 参数验证失败：发布应用参数「PUBLISH_APP」不能为空！"
            }
            if (!params.PUBLISH_BRANCH || params.PUBLISH_BRANCH.trim().isEmpty()) {
                error "❌ 参数验证失败：发布分支参数「PUBLISH_BRANCH」不能为空！"
            }
            if (!params.PUBLISH_SUITE || params.PUBLISH_SUITE.trim().isEmpty()) {
                error "❌ 参数验证失败：发布环境参数「PUBLISH_SUITE」不能为空！"
            }
            echo "✅ 参数验证通过：发布应用(${params.PUBLISH_APP})，分支(${params.PUBLISH_BRANCH})，环境(${params.PUBLISH_SUITE})"
        }

        stage('一、环境准备') {
            echo "================ 一、环境准备（Start）：zt@2025-08-05 ================"
            def envTasks = [:]

            envTasks['配置处理'] = {
                stage('宙斯配置') {
                    if ("${params.PUBLISH_SUITE}" == "prod") {
                        env.SUITE_CODE = "pd-prod"
                        println "==== 「产线(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                    } else if ("${params.PUBLISH_SUITE}" == "test") {
                        env.SUITE_CODE = "test"
                        println "==== 「测试(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                    } else {
                        env.SUITE_CODE = "${params.PUBLISH_SUITE}"
                        println "==== 「其它(${params.PUBLISH_SUITE})」发布 --> SUITE_CODE: ${env.SUITE_CODE} ===="
                    }
                }
            }

            parallel envTasks
        }

        stage('二、前置准备') {
            echo "================ 二、准备（Start）：zt@2025-08-05 ================"
            def beforeTasks = [:]

            def _dateStr = getDateStr()

            // be-scripts
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '3-scripts' || params.PUBLISH_APP == 'be-scripts') {
                beforeTasks['be-s'] = {
                    def bes_folder = "be-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                    def bes_abs_path = "${env.SYNC_ENV_PATH}/${bes_folder}"
                    def bes_ln_name = "be-scripts_${params.PUBLISH_SUITE}"
                    def bes_abs_ln = "${env.SYNC_ENV_PATH}/${bes_ln_name}"

                    stage('01-dir') {
                        mkdir_evn_path('be-scripts')
                    }

                    stage('02-bak') {
                        if (params.PUBLISH_SUITE == 'prod') {
                            // 备份文件夹
                            bes_bak_folder = "bak_be-scripts_${_dateStr}"
                            // 备份绝对路径（父目录）
                            bes_bak_path = "${env.SYNC_ENV_PATH}/${bes_bak_folder}"
                            // 备份绝对路径（具体目录，多放一级方便还原）
                            bes_bak_abs_path = "${bes_bak_path}/${bes_ln_name}"

                            if (!fileExists("${bes_bak_abs_path}")) {
                                println "==== 当日无备份: ${bes_bak_path}"
                                if (!fileExists("${bes_bak_path}")) {
                                    sh "mkdir -p ${bes_bak_path}"
                                    println "==== 创建当日备份目录: ${bes_bak_path}"
                                }

                                sh """
                                cd ${env.SYNC_ENV_PATH}
                                cp -a ${bes_abs_ln}/ ${bes_bak_path}/
                                """
                                println "==== 成功备份目录: ${bes_bak_path}"
                            } else {
                                println "==== 当日已存在备份: ${bes_bak_abs_path}"
                            }
                        }
                    }

                    stage('03-pull') {
                        if (fileExists("${bes_abs_path}")) {
                            println "==== 老版本「重发」：${bes_folder}"
                            sh """
                                cd ${bes_abs_path} && git status
                                git checkout . && git pull
                            """
                        } else {
                            println "==== 新版本「首发」：${bes_folder}"
                            sh """
                                cd ${env.SYNC_ENV_PATH}
                                git clone -b ${params.PUBLISH_BRANCH} ${env.SCM_BES_GIT_URL} ${bes_folder}
                            """
                        }
                    }

                    stage('04-conf') {
                        println "==== 重新生成「配置」：${bes_folder}"
                        sh """
                            cd ${bes_abs_path}
                            ${PYTHON_CMD} generate_config.py ${bes_abs_path}/be-scripts/ ${params.PUBLISH_BRANCH} ${env.SUITE_CODE}
                            git status
                        """
                    }
                }
            }
            // hm-scripts
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '3-scripts' || params.PUBLISH_APP == 'hm-scripts') {
                beforeTasks['hm-s'] = {
                    def hms_folder = "hm-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                    def hms_abs_path = "${env.SYNC_ENV_PATH}/${hms_folder}"
                    def hms_ln_name = "hm-scripts_${params.PUBLISH_SUITE}"
                    def hms_abs_ln = "${env.SYNC_ENV_PATH}/${hms_ln_name}"

                    stage('01-dir') {
                        mkdir_evn_path('hm-scripts')
                    }

                    stage('02-bak') {
                        if (params.PUBLISH_SUITE == 'prod') {
                            // 备份文件夹
                            hms_bak_folder = "bak_hm-scripts_${_dateStr}"
                            // 备份绝对路径（父目录）
                            hms_bak_path = "${env.SYNC_ENV_PATH}/${hms_bak_folder}"
                            // 备份绝对路径（具体目录，多放一级方便还原）
                            hms_bak_abs_path = "${hms_bak_path}/${hms_ln_name}"

                            if (!fileExists("${hms_bak_abs_path}")) {
                                println "==== 当日无备份: ${hms_bak_path}"
                                if (!fileExists("${hms_bak_path}")) {
                                    sh "mkdir -p ${hms_bak_path}"
                                    println "==== 创建当日备份目录: ${hms_bak_path}"
                                }

                                sh """
                                    cd ${env.SYNC_ENV_PATH}
                                    cp -a ${hms_abs_ln}/ ${hms_bak_path}/
                                """
                                println "==== 成功备份目录: ${hms_bak_path}"
                            } else {
                                println "==== 当日已存在备份: ${hms_bak_abs_path}"
                            }
                        }
                    }

                    stage('03-pull') {
                        if (fileExists("${hms_abs_path}")) {
                            println "==== 老版本「重发」：${hms_folder}"
                            sh """
                                cd ${hms_abs_path} && git status
                                git checkout . && git pull
                            """
                        } else {
                            println "==== 新版本「首发」：${hms_folder}"
                            sh """
                                cd ${env.SYNC_ENV_PATH}
                                git clone -b ${params.PUBLISH_BRANCH} ${env.SCM_HMS_GIT_URL} ${hms_folder}
                            """
                        }
                    }

                    stage('04-conf') {
                        println "==== 重新生成「配置」：${hms_folder}"
                        sh """
                            cd ${hms_abs_path}
                            ${PYTHON_CMD} get_zeus_config.py ${params.PUBLISH_BRANCH} ${env.SUITE_CODE} ${hms_abs_path}
                            git status
                        """
                    }
                }
            }
            // p-scripts
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '3-scripts' || params.PUBLISH_APP == 'p-scripts') {
                beforeTasks['p-s'] = {
                    def ps_folder = "p-scripts_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                    def ps_abs_path = "${env.SYNC_ENV_PATH}/${ps_folder}"
                    def ps_ln_name = "p-scripts_${params.PUBLISH_SUITE}"
                    def ps_abs_ln = "${env.SYNC_ENV_PATH}/${ps_ln_name}"

                    stage('01-dir') {
                        mkdir_evn_path('p-scripts')
                    }

                    stage('02-bak') {
                        if (params.PUBLISH_SUITE == 'prod') {
                            // 备份文件夹
                            ps_bak_folder = "bak_p-scripts_${_dateStr}"
                            // 备份绝对路径（父目录）
                            ps_bak_path = "${env.SYNC_ENV_PATH}/${ps_bak_folder}"
                            // 备份绝对路径（具体目录，多放一级方便还原）
                            ps_bak_abs_path = "${ps_bak_path}/${ps_ln_name}"

                            if (!fileExists("${ps_bak_abs_path}")) {
                                println "==== 当日无备份: ${ps_bak_path}"
                                if (!fileExists("${ps_bak_path}")) {
                                    sh "mkdir -p ${ps_bak_path}"
                                    println "==== 创建当日备份目录: ${ps_bak_path}"
                                }

                                sh """
                                    cd ${env.SYNC_ENV_PATH}
                                    cp -a ${ps_abs_ln}/ ${ps_bak_path}/
                                """
                                println "==== 成功备份目录: ${ps_bak_path}"
                            } else {
                                println "==== 当日已存在备份: ${ps_bak_abs_path}"
                            }
                        }
                    }

                    stage('03-pull') {
                        if (fileExists("${ps_abs_path}")) {
                            println "==== 老版本「重发」：${ps_folder}"
                            sh """
                                cd ${ps_abs_path} && git status
                                git checkout . && git pull
                            """
                        } else {
                            println "==== 新版本「首发」：${ps_folder}"
                            sh """
                                cd ${env.SYNC_ENV_PATH}
                                git clone -b ${params.PUBLISH_BRANCH} ${env.SCM_PS_GIT_URL} ${ps_folder}
                            """
                        }
                    }

                    stage('04-conf') {
                        println "==== 重新生成「配置」：${ps_folder}"
                        sh """
                            cd ${ps_abs_path}
                            ${PYTHON_CMD} generate_config.py ${ps_abs_path}/be-scripts/ ${params.PUBLISH_BRANCH} ${env.SUITE_CODE}
                            git status
                        """
                    }
                }
            }
            // spider-L
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-servers' || params.PUBLISH_APP == 'spider') {
                beforeTasks['spider-L'] = {
                    def spider_srv_ver_suite_folder = "spider-srv_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                    def spider_srv_abs_path = "${env.SYNC_ENV_PATH}/${spider_srv_ver_suite_folder}"
                    def spider_srv_ln_name = "spider-srv_${params.PUBLISH_SUITE}"
                    def spider_srv_abs_ln = "${env.SYNC_ENV_PATH}/${spider_srv_ln_name}"

                    stage('01-dir') {
                        mkdir_evn_path('spider-srv')
                    }

                    stage('02-bak') {
                        if (params.PUBLISH_SUITE == 'prod') {
                            // 备份文件夹
                            def spider_srv_bak_folder = "bak_spider-srv_${_dateStr}"
                            // 备份绝对路径（父目录）
                            def spider_srv_bak_path = "${env.SYNC_ENV_PATH}/${spider_srv_bak_folder}"
                            // 备份绝对路径（具体目录，多放一级方便还原）
                            def spider_srv_bak_abs_path = "${spider_srv_bak_path}/${spider_srv_ln_name}"

                            if (!fileExists("${spider_srv_bak_abs_path}")) {
                                println "==== 当日无备份: ${spider_srv_bak_path}"
                                if (!fileExists("${spider_srv_bak_path}")) {
                                    sh "mkdir -p ${spider_srv_bak_path}"
                                    println "==== 创建当日备份目录: ${spider_srv_bak_path}"
                                }

                                sh """
                                    cd ${env.SYNC_ENV_PATH}
                                    cp -a ${spider_srv_ln_name}/ ${spider_srv_bak_folder}/
                                """
                                println "==== 成功备份目录: ${spider_srv_bak_folder}"
                            } else {
                                println "==== 当日已存在备份: ${spider_srv_bak_abs_path}"
                            }
                        }
                    }

                    stage('03-pull') {
                        if (fileExists("${spider_srv_abs_path}")) {
                            println "==== 老版本「重发」：${spider_srv_ver_suite_folder}"
                            sh """
                                cd ${spider_srv_abs_path} && git status
                                git checkout . && git pull
                            """
                        } else {
                            println "==== 新版本「首发」：${spider_srv_ver_suite_folder}"
                            sh """
                                cd ${env.SYNC_ENV_PATH}
                                git clone -b ${params.PUBLISH_BRANCH} ${env.SCM_SPIDER_GIT_URL} ${spider_srv_ver_suite_folder}
                            """
                        }
                    }

                    stage('04-conf') {
                        println "==== 重新生成「配置」：${spider_srv_ver_suite_folder}"
                        sh """
                            cd ${spider_srv_abs_path}
                            ${PYTHON_CMD} generate_config.py ${spider_srv_abs_path}/spider/ ${params.PUBLISH_BRANCH} ${env.SUITE_CODE}
                            git status
                        """
                    }
                }
            }
            // spider-I
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-servers' || params.PUBLISH_APP == 'spider') {
                beforeTasks['spider-I'] = {
                    node('img') {
                        stage('01-dir') {
                            println "==== NODE_NAME: ${env.NODE_NAME}"
                            sh """
                                pwd
                                rm -rf zt-git zt-img zt-tmp
                                mkdir -p {zt-git,zt-img,zt-tmp}
                                mkdir -p zt-img/spider
                            """
                        }

                        stage('02-git') {
                            sh """
                                cd zt-git
                                git clone -b ${env.PUBLISH_BRANCH} ${env.SCM_SPIDER_GIT_URL}
                            """
                        }

                        stage('03-img') {
                            sh """
                                cp zt-git/spider/docker/Dockerfile zt-img/
                                cp zt-git/spider/.python-version zt-img/spider/
                                cp zt-git/spider/pyproject.toml zt-img/spider/
                                cp zt-git/spider/uv.lock zt-img/spider/
                            """
                        }

                        stage('04-build') {
                            sh """
                                cd zt-img
                                sudo docker build -t spider:${params.PUBLISH_BRANCH} .
                            """
                        }

                        stage('05-tag') {
                            sh """
                                sudo docker tag spider:${params.PUBLISH_BRANCH} ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/spider:${params.PUBLISH_BRANCH}
                                sudo docker tag spider:${params.PUBLISH_BRANCH} ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/spider:latest
                            """
                        }

                        stage('06-push') {
                            sh """
                                sudo docker push ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/spider:${params.PUBLISH_BRANCH}
                                sudo docker push ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/spider:latest
                            """
                        }
                    }
                }
            }
            // mantis-L
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-servers' || params.PUBLISH_APP == 'mantis') {
                beforeTasks['mantis-L'] = {
                    def mantis_srv_ver_suite_folder = "mantis-srv_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
                    def mantis_srv_abs_path = "${env.SYNC_ENV_PATH}/${mantis_srv_ver_suite_folder}"
                    def mantis_srv_ln_name = "mantis-srv_${params.PUBLISH_SUITE}"
                    def mantis_srv_abs_ln = "${env.SYNC_ENV_PATH}/${mantis_srv_ln_name}"

                    stage('01-dir') {
                        mkdir_evn_path('mantis-srv')
                    }

                    stage('02-bak') {
                        if (params.PUBLISH_SUITE == 'prod') {
                            // 备份文件夹
                            def mantis_srv_bak_folder = "bak_mantis-srv_${_dateStr}"
                            // 备份绝对路径（父目录）
                            def mantis_srv_bak_path = "${env.SYNC_ENV_PATH}/${mantis_srv_bak_folder}"
                            // 备份绝对路径（具体目录，多放一级方便还原）
                            def mantis_srv_bak_abs_path = "${mantis_srv_bak_path}/${mantis_srv_ln_name}"

                            if (!fileExists("${mantis_srv_bak_abs_path}")) {
                                println "==== 当日无备份: ${mantis_srv_bak_path}"
                                if (!fileExists("${mantis_srv_bak_path}")) {
                                    sh "mkdir -p ${mantis_srv_bak_path}"
                                    println "==== 创建当日备份目录: ${mantis_srv_bak_path}"
                                }

                                sh """
                                    cd ${env.SYNC_ENV_PATH}
                                    cp -a ${mantis_srv_ln_name}/ ${mantis_srv_bak_folder}/
                                """
                                println "==== 成功备份目录: ${mantis_srv_bak_folder}"
                            } else {
                                println "==== 当日已存在备份: ${mantis_srv_bak_abs_path}"
                            }
                        }
                    }

                    stage('03-pull') {
                        if (fileExists("${mantis_srv_abs_path}")) {
                            println "==== 老版本「重发」：${mantis_srv_ver_suite_folder}"
                            sh """
                                cd ${mantis_srv_abs_path} && git status
                                git checkout . && git pull
                            """
                        } else {
                            println "==== 新版本「首发」：${mantis_srv_ver_suite_folder}"
                            sh """
                                cd ${env.SYNC_ENV_PATH}
                                git clone -b ${params.PUBLISH_BRANCH} ${env.SCM_MANTIS_GIT_URL} ${mantis_srv_ver_suite_folder}
                            """
                        }
                    }

                    stage('04-conf') {
                        println "==== 重新生成「配置」：${mantis_srv_ver_suite_folder}"
                        sh """
                            cd ${mantis_srv_abs_path}
                            ${PYTHON_CMD} generate_config.py ${mantis_srv_abs_path}/mantis/ ${params.PUBLISH_BRANCH} ${env.SUITE_CODE}
                            git status
                        """
                    }
                }
            }
            // mantis-I
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-servers' || params.PUBLISH_APP == 'mantis') {
                beforeTasks['mantis-I'] = {
                    node('img') {
                        stage('01-dir') {
                            println "==== NODE_NAME: ${env.NODE_NAME}"
                            sh """
                                pwd
                                rm -rf zt-git zt-img zt-tmp
                                mkdir -p {zt-git,zt-img,zt-tmp}
                                mkdir -p zt-img/mantis
                            """
                        }

                        stage('02-git') {
                            sh """
                                cd zt-git
                                git clone -b ${env.PUBLISH_BRANCH} ${env.SCM_MANTIS_GIT_URL}
                            """
                        }

                        stage('03-img') {
                            sh """
                                cp zt-git/mantis/docker/Dockerfile zt-img/
                                cp zt-git/mantis/.python-version zt-img/mantis/
                                cp zt-git/mantis/pyproject.toml zt-img/mantis/
                                cp zt-git/mantis/uv.lock zt-img/mantis/
                            """
                        }

                        stage('04-build') {
                            sh """
                                cd zt-img
                                sudo docker build -t mantis:${params.PUBLISH_BRANCH} .
                            """
                        }

                        stage('05-tag') {
                            sh """
                                sudo docker tag mantis:${params.PUBLISH_BRANCH} ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/mantis:${params.PUBLISH_BRANCH}
                                sudo docker tag mantis:${params.PUBLISH_BRANCH} ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/mantis:latest
                            """
                        }

                        stage('06-push') {
                            sh """
                                sudo docker push ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/mantis:${params.PUBLISH_BRANCH}
                                sudo docker push ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/mantis:latest
                            """
                        }
                    }
                }
            }

            parallel beforeTasks
        }

        // 三、更新制品
        stage('三、更新制品') {
            echo "================ 三、更新制品（Start）：zt@2025-08-05 ================"
            def libTasks = [:]
            
            // be-scripts
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '3-scripts' || params.PUBLISH_APP == 'be-scripts') {
                libTasks['be-s'] = {
                    stage('05-ln') {
                        ln_switch('be-scripts')
                    }
                }
            }
            // hm-scripts
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '3-scripts' || params.PUBLISH_APP == 'hm-scripts') {
                libTasks['hm-s'] = {
                    stage('05-ln') {
                        ln_switch('hm-scripts')
                    }
                }
            }
            // p-scripts
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '3-scripts' || params.PUBLISH_APP == 'p-scripts') {
                libTasks['p-s'] = {
                    stage('05-ln') {
                        ln_switch('p-scripts')
                    }
                }
            }
            // spider
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'spider') {
                libTasks['spider'] = {
                    stage('05-ln') {
                        ln_switch('spider-srv')
                    }
                }
            }
            // mantis
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'mantis') {
                libTasks['mantis'] = {
                    stage('05-ln') {
                        ln_switch('mantis-srv')
                    }
                }
            }

            parallel libTasks
        }

        stage('首次等待') {
            
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'spider' || params.PUBLISH_APP == 'mantis') {
                echo "================ 首次等待（Start）：zt@2025-08-06 ================"
                sleep 3
            }

        }

        stage('四、服务A(192.168.2xx.x)') {
            
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'spider' || params.PUBLISH_APP == 'mantis') {
                echo "================ 四、服务A（Start）：zt@2025-08-06 ================"

                def srvATask = [:]
            
                // spider
                if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'spider') {
                
                    srvATask['s-207'] = {
                        node('spider-211_207') {
                            stage('01-pull') {
                                println "==== NODE_NAME: ${env.NODE_NAME}"
                                sh """
                                    sudo docker pull ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/spider
                                """
                            }
                            stage('02-ps') {
                                sh """
                                    sudo docker images
                                    sudo docker ps -a
                                """
                            }
                            stage('03-stop') {
                                sh '''
                                    sudo docker ps --format '{{.Names}}' | grep -q '^spider$' && sudo docker stop spider || true
                                '''
                            }
                            stage('04-rm') {
                                sh '''
                                    sudo docker ps -a --format '{{.Names}}' | grep -q '^spider$' && sudo docker rm spider || true
                                '''
                            }
                            stage('05-start') {
                                sh """
                                    sudo docker run -itd --name spider -p 9000:9000 -v /data/app/spider:/data/app/spider/ -v /data/logs/spider:/data/logs/spider/ ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/spider
                                """
                            }
                            stage('06-check') {
                                sh """
                                    sudo docker images
                                    sudo docker ps -a
                                """
                            }
                        }
                    }
                }
                // mantis
                if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'mantis') {
                
                    srvATask['m-134'] = {
                        node('mantis-209_134') {
                            stage('01-pull') {
                                println "==== NODE_NAME: ${env.NODE_NAME}"
                                sh """
                                    sudo docker pull ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/mantis
                                """
                            }
                            stage('02-ps') {
                                sh """
                                    sudo docker images
                                    sudo docker ps -a
                                """
                            }
                            stage('03-stop') {
                                sh '''
                                    sudo docker ps --format '{{.Names}}' | grep -q '^mantis$' && sudo docker stop mantis || true
                                '''
                            }
                            stage('04-rm') {
                                sh '''
                                    sudo docker ps -a --format '{{.Names}}' | grep -q '^mantis$' && sudo docker rm mantis || true
                                '''
                            }
                            stage('05-start') {
                                sh """
                                    sudo docker run -itd --name mantis -p 9000:9000 -v /data/app/mantis:/data/app/mantis/ -v /data/logs/mantis:/data/logs/mantis/ ${env.HARBOR_REPO_URL}/${env.HARBOR_PROJ_NAME}/mantis
                                """
                            }
                            stage('06-check') {
                                sh """
                                    sudo docker images
                                    sudo docker ps -a
                                """
                            }
                        }
                    }
                }

                parallel srvATask
            }

        }

        stage('再次等待') {
            
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'spider' || params.PUBLISH_APP == 'mantis') {
                echo "================ 再次等待（Start）：zt@2025-08-06 ================"
                sleep 3
            }
            
        }

        stage('五、服务B(192.168.2xx.x)') {
            if (params.PUBLISH_APP == 'ALL' || params.PUBLISH_APP == '2-server' || params.PUBLISH_APP == 'spider' || params.PUBLISH_APP == 'mantis') {
                echo "================ 五、服务B（Start）：zt@2025-08-06 ================"
            }
        }

        // 六、后置处理
        stage('六、后置处理') {
            echo "================ 六、后置处理（Start）：zt@2025-08-05 ================"
            def afterTasks = [:]
            
            afterTasks['01-step1'] = {
                sh "echo '>>>> 后置处理1:' $PYTHON_CMD"
            }
            afterTasks['02-step2'] = {
                sh "echo '>>>> 后置处理2:' $PYTHON_CMD"
            }
            afterTasks['03-step3'] = {
                sh "echo '>>>> 后置处理3:' $PYTHON_CMD"
            }

            parallel afterTasks
        }

    } catch (Exception e) {
        echo "================ Exception()：zt@2025-08-05 ================"
        currentBuild.result = 'FAILURE'
        throw e
    } finally {
        echo "================ finally()：zt@2025-08-05 ================"
    }
}

// 辅助函数定义
def getDateStr() {
    return new Date().format('yyyyMMdd')
}

void mkdir_evn_path(para_app_name) {
    echo "==== para_app_name: ${para_app_name}"
    if (!fileExists("${env.SYNC_ENV_PATH}")) {
        echo "==== 创建环境目录: ${env.SYNC_ENV_PATH}"
        sh "mkdir -p ${env.SYNC_ENV_PATH}"
    } else {
        echo "==== 已存在环境目录: ${env.SYNC_ENV_PATH}"
    }
}

void ln_switch(para_app_name) {
    echo "==== para_app_name: ${para_app_name}"
    def APP_VER_SUITE_FOLDER = "${para_app_name}_${params.PUBLISH_BRANCH}_${params.PUBLISH_SUITE}"
    def APP_ABS_PATH = "$SYNC_ENV_PATH/$APP_VER_SUITE_FOLDER"
    def APP_LN_NAME = "${para_app_name}_${params.PUBLISH_SUITE}"
    def APP_ABS_LN = "$SYNC_ENV_PATH/$APP_LN_NAME"
    echo "==== APP_VER_SUITE_FOLDER: ${APP_VER_SUITE_FOLDER}"
    echo "==== APP_ABS_PATH: ${APP_ABS_PATH}"
    echo "==== APP_LN_NAME: ${APP_LN_NAME}"
    echo "==== APP_ABS_LN: ${APP_ABS_LN}"

    if (fileExists("${APP_ABS_LN}")) {
        echo "==== 老版本「重发」：${APP_ABS_LN}"
        sh """
            cd ${SYNC_ENV_PATH}
            rm -rf ${APP_ABS_LN}
            ln -s ${APP_VER_SUITE_FOLDER} ${APP_LN_NAME}
        """
    } else {
        echo "==== 新版本「首发」：${APP_ABS_LN}"
        sh """
            cd ${SYNC_ENV_PATH}
            ln -sf ${APP_VER_SUITE_FOLDER} ${APP_LN_NAME}
        """
    }
}

// 返回脚本
return this