# 应用注册

<cite>
**本文档引用的文件**  
- [app_register.py](file://app_mgt/app_register.py)
- [models.py](file://app_mgt/models.py)
</cite>

## 目录
1. [简介](#简介)
2. [应用注册流程](#应用注册流程)
3. [应用元数据模型](#应用元数据模型)
4. [注册接口详解](#注册接口详解)
5. [同步与异步注册模式](#同步与异步注册模式)
6. [高级行为与后续流程](#高级行为与后续流程)
7. [常见注册失败原因及解决方案](#常见注册失败原因及解决方案)
8. [结论](#结论)

## 简介

应用注册功能是系统中用于创建和管理应用的核心功能。通过`app_register.py`中的`AppRegisterApi`类，系统支持Java和非Java应用的注册，能够处理单个应用和多模块应用的创建。注册过程中会生成应用唯一标识、校验基础信息、绑定团队信息，并初始化默认配置。该功能还支持从SVN或Git仓库自动解析应用信息，简化注册流程。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

## 应用注册流程

应用注册流程根据应用类型（Java或非Java）和数量（单个或多个）采用不同的处理逻辑。核心流程包括应用唯一标识生成、基础信息校验、团队关联绑定和默认配置初始化。

对于Java应用，系统首先检查应用是否已存在，防止重复注册。然后根据版本控制系统类型（SVN或Git）分别处理。如果是SVN仓库，系统会验证仓库存在性并匹配SVN组；如果是Git仓库，则验证仓库存在性并检查Git组。随后，系统会创建`AppInfo`、`AppModule`、`AppTeam`和`AppBuild`四个核心对象，完成应用的完整注册。

对于非Java应用，流程类似，但构建配置相对简化。系统同样会进行存在性检查和仓库验证，然后创建相应的数据库记录。

```mermaid
flowchart TD
Start([开始注册]) --> ValidateInput["验证输入参数"]
ValidateInput --> AppExists{"应用已存在?"}
AppExists --> |是| ReturnError["返回'应用已存在'错误"]
AppExists --> |否| CheckRepoType{"仓库类型?"}
CheckRepoType --> |SVN| ValidateSVN["验证SVN仓库"]
CheckRepoType --> |Git| ValidateGit["验证Git仓库"]
ValidateSVN --> SVNExists{"SVN仓库存在?"}
ValidateGit --> GitExists{"Git仓库存在?"}
SVNExists --> |否| ReturnError
GitExists --> |否| ReturnError
SVNExists --> |是| CreateApp["创建应用记录"]
GitExists --> |是| CreateApp
CreateApp --> CreateAppInfo["创建AppInfo"]
CreateAppInfo --> CreateAppModule["创建AppModule"]
CreateAppModule --> CreateAppTeam["创建AppTeam"]
CreateAppTeam --> CreateAppBuild["创建AppBuild"]
CreateAppBuild --> Success["注册成功"]
ReturnError --> Fail["注册失败"]
Success --> End([结束])
Fail --> End
```

**Diagram sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

## 应用元数据模型

应用元数据由`models.py`中的多个模型类定义，主要包括`AppInfo`、`AppModule`、`AppBuild`和`AppTeam`四个核心模型。

### AppInfo模型

`AppInfo`模型定义了应用的基本信息：

```mermaid
classDiagram
class AppInfo {
+app_name : string
+app_cname : string
+app_status : boolean
+git_url : string
+git_path : string
+svn_url : string
+svn_path : string
+app_jdk_version : string
+lib_location : string
+create_user : string
+create_time : datetime
+update_user : string
+update_time : datetime
+stamp : int
}
```

**Diagram sources**
- [models.py](file://app_mgt/models.py#L3-L28)

### AppModule模型

`AppModule`模型定义了应用模块的详细信息：

```mermaid
classDiagram
class AppModule {
+app_id : int
+module_name : string
+module_status : boolean
+module_desc : string
+need_online : boolean
+need_check : boolean
+lib_repo : string
+create_user : string
+create_time : datetime
+update_user : string
+update_time : datetime
+stamp : int
}
```

**Diagram sources**
- [models.py](file://app_mgt/models.py#L31-L61)

### AppBuild模型

`AppBuild`模型定义了应用的构建配置：

```mermaid
classDiagram
class AppBuild {
+app_id : int
+module_name : string
+package_type : string
+package_name : string
+package_full : boolean
+build_jdk_version : string
+create_user : string
+create_time : datetime
+update_user : string
+update_time : datetime
+stamp : int
}
```

**Diagram sources**
- [models.py](file://app_mgt/models.py#L64-L87)

**Section sources**
- [models.py](file://app_mgt/models.py#L3-L87)

## 注册接口详解

应用注册接口通过`create`方法实现，支持POST请求。接口的请求参数结构如下：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| app_category | string | 否 | 应用类别，"非JAVA"表示非Java应用 |
| app_name | string | 是 | 应用英文名（唯一标识） |
| app_name_cn | string | 是 | 应用中文名 |
| app_type | string | 是 | 应用类型（pom, war, jar, tar） |
| jdk_version | string | 是 | JDK版本 |
| team | string | 是 | 所属团队 |
| trunk_path | string | 是 | 仓库主干路径 |
| repo_path | string | 否 | 制品库路径 |
| jar_name | string | 否 | 包名 |

数据验证规则包括：
1. **唯一性验证**：通过`AppInfo.objects.get(app_name=app_name)`检查应用是否已存在
2. **仓库存在性验证**：对于SVN使用`svn list`命令，对于Git使用GitLab API验证仓库存在性
3. **必填字段验证**：确保关键字段如应用名、团队、仓库路径等不为空
4. **格式验证**：确保仓库路径格式正确

异常处理机制包括：
- **重复注册**：捕获`AppInfo.DoesNotExist`异常，返回"应用已存在"错误
- **仓库不存在**：执行SVN/Git命令后检查返回码，返回"仓库不存在"错误
- **数据库插入失败**：捕获所有异常，记录错误日志并返回具体失败原因
- **参数缺失**：检查`request.data`是否存在，返回"没有应用信息"错误

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "AppRegisterApi"
participant DB as "数据库"
Client->>Api : POST /api/register
Api->>Api : 验证用户权限
Api->>Api : 解析请求数据
Api->>Api : 检查应用是否已存在
Api-->>Client : 应用已存在
Api->>Api : 验证仓库存在性
Api-->>Client : 仓库不存在
Api->>DB : 创建AppInfo记录
DB-->>Api : 成功/失败
Api->>DB : 创建AppModule记录
DB-->>Api : 成功/失败
Api->>DB : 创建AppTeam记录
DB-->>Api : 成功/失败
Api->>DB : 创建AppBuild记录
DB-->>Api : 成功/失败
Api-->>Client : 注册成功
```

**Diagram sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

## 同步与异步注册模式

系统支持同步和异步两种注册模式，适用于不同的使用场景。

### 同步注册模式

同步注册模式适用于简单的应用注册场景，客户端发送请求后等待服务器完成所有操作并返回结果。这种模式实现简单，适合在用户界面中直接使用。

```python
# 同步注册示例
data = [{
    'app_name': 'demo-app',
    'app_name_cn': '演示应用',
    'app_type': 'jar',
    'jdk_version': '1.8',
    'team': 'dev-team',
    'trunk_path': 'gitlab.com/demo/demo-app',
    'jar_name': 'demo-app.jar'
}]
# 调用create方法，立即返回结果
```

### 异步注册模式

异步注册模式适用于复杂的多模块应用注册或需要从仓库解析大量应用信息的场景。系统提供了`put`方法用于仓库解析，可以先异步解析仓库中的所有应用，然后再进行注册。

```python
# 异步注册示例
# 第一步：解析Git仓库
parse_data = {
    'team': 'dev-team',
    'repo_type': 'GIT',
    'trunk_path': 'gitlab.com/demo/demo-parent'
}
# 调用put方法，返回解析结果
# 第二步：使用解析结果进行注册
# 调用create方法，完成注册
```

异步模式的优势在于可以处理复杂的多模块Maven项目，自动发现所有子模块并生成注册数据，大大简化了批量注册的流程。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

## 高级行为与后续流程

应用注册功能包含多种高级行为，确保系统的完整性和一致性。

### 重复注册检测

系统通过`AppInfo.objects.get(app_name=app_name)`进行重复注册检测。如果应用已存在，会直接返回"应用已存在"的错误信息，防止数据重复。

### 权限校验

虽然代码中没有显式的权限校验逻辑，但通过`request.user`获取当前用户信息，确保只有认证用户才能执行注册操作。`create`方法中会根据用户身份设置`create_user`和`update_user`字段。

### 自动触发后续流程

注册成功后，系统会自动初始化构建配置，为后续的CI/CD流程做好准备。`AppBuild`对象的创建就是这一过程的体现，它定义了应用的构建方式和输出产物。

### 默认配置初始化

在应用创建过程中，系统会初始化一系列默认配置：
- **状态初始化**：`app_status`和`module_status`默认设置为1（使用中）
- **时间戳初始化**：`create_time`和`update_time`设置为当前时间
- **版本初始化**：`stamp`设置为0
- **维护标志初始化**：`need_check`设置为1（需要维护）
- **构建标志初始化**：`package_full`设置为1（完整包）

这些默认配置确保新注册的应用可以直接参与后续的构建和部署流程。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

## 常见注册失败原因及解决方案

### 命名冲突

**原因**：注册的应用名已存在于系统中。

**解决方案**：选择一个唯一的应用名，或先删除已存在的同名应用再重新注册。

### 权限不足

**原因**：当前用户没有注册应用的权限。

**解决方案**：联系系统管理员获取相应的权限，或使用有权限的账户进行注册。

### 仓库不存在

**原因**：提供的SVN或Git仓库路径不存在或无法访问。

**解决方案**：检查仓库路径是否正确，确保仓库存在且当前用户有访问权限。

### SVN组未匹配

**原因**：SVN路径无法匹配到已知的SVN组。

**解决方案**：确保SVN路径格式正确，或联系管理员添加相应的SVN组配置。

### 团队信息不存在

**原因**：指定的团队在系统中不存在。

**解决方案**：检查团队名称是否正确，或先创建相应的团队再进行应用注册。

### 网络问题

**原因**：与GitLab或SVN服务器的网络连接出现问题。

**解决方案**：检查网络连接，确保服务器地址可访问，或稍后重试。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L13-L675)

## 结论

应用注册功能通过`app_register.py`实现了完整的应用创建流程，结合`models.py`中的数据模型，提供了健壮的应用管理基础。该功能支持多种应用类型和注册模式，具备完善的验证和错误处理机制。通过理解注册流程、数据模型和接口规范，开发者可以有效地使用和扩展这一功能，满足不同的应用管理需求。