# 应用管理

<cite>
**本文档引用的文件**   
- [models.py](file://app_mgt/models.py)
- [app_register.py](file://app_mgt/app_register.py)
- [app_compare.py](file://app_mgt/app_compare.py)
- [app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
- [app_mgt.py](file://app_mgt/app_mgt.py)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心模型设计](#核心模型设计)
3. [应用注册流程](#应用注册流程)
4. [版本差异比对](#版本差异比对)
5. [接口管理](#接口管理)
6. [应用信息维护](#应用信息维护)
7. [高级功能](#高级功能)
8. [复杂场景实践](#复杂场景实践)
9. [常见问题与解决方案](#常见问题与解决方案)
10. [附录](#附录)

## 简介
应用管理模块是系统的核心组件，负责应用的全生命周期管理。该模块提供应用注册、信息维护、版本对比和接口管理等功能，支持Java和非Java应用的统一管理。通过与团队、构建记录的关联，实现了应用的精细化管理。本文档详细介绍了应用管理模块的设计与实现，为开发和运维人员提供全面的指导。

## 核心模型设计

### AppInfo模型
`AppInfo`模型是应用管理的基础，存储应用的核心信息。该模型包含应用的英文名、中文名、状态、代码仓库地址、JDK版本、创建和修改信息等字段。通过`app_status`字段标识应用的使用状态，`platform_type`字段标识是否接入平台。

```mermaid
classDiagram
class AppInfo {
+str app_name
+str app_cname
+bool app_status
+str git_url
+str git_path
+str svn_url
+str svn_path
+str app_jdk_version
+str app_desc
+str create_user
+datetime create_time
+str update_user
+str lib_location
+datetime update_time
+int stamp
+bool platform_type
+datetime platform_time
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L10-L45)

**本节来源**
- [models.py](file://app_mgt/models.py#L10-L45)

### AppModule模型
`AppModule`模型用于管理应用的模块信息。每个应用可以包含多个模块，模块信息包括模块名、状态、描述、JDK版本、端口、容器名、打包路径、发布路径等。通过`app_id`字段与`AppInfo`模型关联，实现应用与模块的一对多关系。

```mermaid
classDiagram
class AppModule {
+int app_id
+str module_name
+str module_code
+bool module_status
+str module_desc
+str module_svn_path
+str module_jdk_version
+bool need_online
+bool need_check
+int app_port
+str container_name
+str create_path
+str lib_repo
+str deploy_path
+str extend_attr
+str create_user
+datetime create_time
+str update_user
+datetime update_time
+int stamp
+bool zeus_type
+bool need_ops
+int is_component
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L47-L88)

**本节来源**
- [models.py](file://app_mgt/models.py#L47-L88)

### 关联模型
应用管理模块还包含多个关联模型，用于管理应用与团队、构建、接口等的关系。`AppTeam`模型用于关联应用与团队，`AppBuild`模型存储构建信息，`AppMgtInterfaceInfo`模型管理接口信息。

```mermaid
classDiagram
class AppTeam {
+int app_id
+int team_id
+str create_user
+datetime create_time
+str update_user
+datetime update_time
+int stamp
}
class AppBuild {
+int app_id
+str module_name
+str module_code
+str module_version
+str package_type
+str package_name
+bool package_full
+str build_jdk_version
+str create_user
+datetime create_time
+str update_user
+datetime update_time
+int stamp
+bool need_mock
+str mock_build_cmd
+str build_cmd
}
class AppMgtInterfaceInfo {
+str module_name
+str branch_name
+str interface_name
+str interface_name_dev
+str interface_path
+str interface_method
+str interface_type
+str content_type
+str encryption
+JSONField request_params
+JSONField response_params
+JSONField defines_params
+int status
+str create_version
+str create_user
+datetime create_time
+str update_user
+datetime update_time
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L90-L130)
- [models.py](file://app_mgt/models.py#L132-L171)
- [models.py](file://app_mgt/models.py#L315-L352)

**本节来源**
- [models.py](file://app_mgt/models.py#L90-L171)
- [models.py](file://app_mgt/models.py#L315-L352)

## 应用注册流程

### 注册流程概述
应用注册流程是应用管理的入口，通过`AppRegisterApi`类实现。注册流程支持单应用和多应用的注册，能够自动解析代码仓库中的POM文件，提取应用信息。注册流程包括校验、创建和关联三个主要步骤。

```mermaid
flowchart TD
Start([开始]) --> ValidateInput["校验输入参数"]
ValidateInput --> CheckExist{"应用是否存在?"}
CheckExist --> |是| ReturnError["返回应用已存在错误"]
CheckExist --> |否| CheckRepo{"仓库类型?"}
CheckRepo --> |SVN| HandleSVN["处理SVN仓库"]
CheckRepo --> |GIT| HandleGIT["处理GIT仓库"]
HandleSVN --> CreateAppInfo["创建AppInfo"]
HandleGIT --> CreateAppInfo
CreateAppInfo --> CreateAppModule["创建AppModule"]
CreateAppModule --> CreateAppTeam["创建AppTeam"]
CreateAppTeam --> CreateAppBuild["创建AppBuild"]
CreateAppBuild --> End([结束])
```

**图表来源**
- [app_register.py](file://app_mgt/app_register.py#L100-L300)

**本节来源**
- [app_register.py](file://app_mgt/app_register.py#L100-L300)

### 校验规则
应用注册时执行严格的校验规则，确保数据的完整性和一致性。主要校验规则包括：
- 应用名称唯一性校验
- 代码仓库存在性校验
- 团队信息有效性校验
- 必填字段完整性校验

校验规则通过`handle_single_app`和`handle_multi_app`方法实现，使用Django ORM进行数据库查询和异常处理。

```mermaid
flowchart TD
Start([开始]) --> CheckAppName["校验应用名称"]
CheckAppName --> CheckRepoExist["校验仓库存在性"]
CheckRepoExist --> CheckTeam["校验团队信息"]
CheckTeam --> CheckRequired["校验必填字段"]
CheckRequired --> End([结束])
```

**图表来源**
- [app_register.py](file://app_mgt/app_register.py#L100-L150)

**本节来源**
- [app_register.py](file://app_mgt/app_register.py#L100-L150)

### 创建逻辑
应用创建逻辑是注册流程的核心，通过`create`方法实现。创建逻辑根据应用类型（Java或非Java）执行不同的处理流程。对于Java应用，会解析POM文件提取模块信息；对于非Java应用，直接创建基础信息。

创建过程使用Django ORM的`create`方法，确保数据的一致性和完整性。创建完成后，会自动关联团队信息和构建信息。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "AppRegisterApi"
participant DB as "数据库"
Client->>Api : 发送注册请求
Api->>Api : 解析请求数据
Api->>Api : 执行校验规则
Api->>DB : 创建AppInfo记录
DB-->>Api : 返回AppInfo ID
Api->>DB : 创建AppModule记录
Api->>DB : 创建AppTeam记录
Api->>DB : 创建AppBuild记录
DB-->>Api : 返回创建结果
Api-->>Client : 返回响应
```

**图表来源**
- [app_register.py](file://app_mgt/app_register.py#L300-L400)

**本节来源**
- [app_register.py](file://app_mgt/app_register.py#L300-L400)

## 版本差异比对

### 比对算法
版本差异比对功能通过`AppCompareApi`类实现，使用`get_compare_info`方法获取对比信息。比对算法基于Salt命令执行，通过`du`命令计算文件大小，实现MD5值的对比。

比对算法支持JAR和WAR包类型，对于JAR包，使用`find`命令查找包文件并计算MD5值；对于WAR包，计算`WEB-INF/lib`和`WEB-INF/classes/com`目录的大小。

```mermaid
flowchart TD
Start([开始]) --> GetPublishInfo["获取发布信息"]
GetPublishInfo --> GenerateCmd["生成执行命令"]
GenerateCmd --> ExecuteCmd["执行Salt命令"]
ExecuteCmd --> ProcessResult["处理执行结果"]
ProcessResult --> CompareResult["比较结果"]
CompareResult --> End([结束])
```

**图表来源**
- [app_compare.py](file://app_mgt/app_compare.py#L100-L200)

**本节来源**
- [app_compare.py](file://app_mgt/app_compare.py#L100-L200)

### 执行流程
版本差异比对的执行流程包括信息获取、命令生成、命令执行和结果比较四个步骤。信息获取通过`get_app_publish_info`方法实现，命令生成通过`generate_cmd`方法实现，命令执行通过`run_compare_cmd`方法实现，结果比较通过`compile_res`方法实现。

执行流程使用多线程技术，提高比对效率。比对结果以字典形式返回，包含各节点的对比信息和差异数量。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "AppCompareApi"
participant Salt as "Salt任务"
Client->>Api : 发送比对请求
Api->>Api : 获取发布信息
Api->>Api : 生成执行命令
Api->>Salt : 执行Salt命令
Salt-->>Api : 返回执行结果
Api->>Api : 处理执行结果
Api->>Api : 比较结果
Api-->>Client : 返回比对结果
```

**图表来源**
- [app_compare.py](file://app_mgt/app_compare.py#L200-L300)

**本节来源**
- [app_compare.py](file://app_mgt/app_compare.py#L200-L300)

## 接口管理

### 接口信息管理
接口管理功能通过`AppMgtInterfaceApi`类实现，提供接口信息的增删改查操作。接口信息包括接口路径、方法、类型、请求参数、响应参数等。接口信息存储在`AppMgtInterfaceInfo`和`AppMgtInterfaceParamInfo`两个模型中。

接口管理支持批量操作，通过`batch_insert_interface_info`方法实现。接口信息的更新使用`update_or_create`方法，确保数据的一致性。

```mermaid
classDiagram
class AppMgtInterfaceInfo {
+str module_name
+str branch_name
+str interface_name
+str interface_path
+str interface_method
+str interface_type
+str content_type
+str encryption
+JSONField request_params
+JSONField response_params
+JSONField defines_params
+int status
+str create_version
+str create_user
+datetime create_time
+str update_user
+datetime update_time
}
class AppMgtInterfaceParamInfo {
+str module_name
+str branch_name
+str interface_path
+str interface_method
+str interface_type
+str field_name
+str field_type
+int is_required
+str enum_values
+str create_user
+datetime create_time
+str update_user
+datetime update_time
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L315-L352)
- [models.py](file://app_mgt/models.py#L354-L388)

**本节来源**
- [models.py](file://app_mgt/models.py#L315-L388)

### 接口扫描
接口扫描功能通过`create`方法实现，支持HTTP和DUBBO接口的自动扫描。扫描过程包括数据接收、临时表插入、主表合并和数据稽核四个步骤。

扫描结果存储在`AppMgtInterfaceInfoTemp`临时表中，通过`__merge_interface_info`方法与主表数据合并。数据稽核通过`audit_app_branch_api`方法实现，确保接口信息的准确性。

```mermaid
flowchart TD
Start([开始]) --> ReceiveData["接收扫描数据"]
ReceiveData --> InsertTemp["插入临时表"]
InsertTemp --> MergeMain["合并主表数据"]
MergeMain --> AuditData["数据稽核"]
AuditData --> End([结束])
```

**图表来源**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L100-L200)

**本节来源**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L100-L200)

## 应用信息维护

### 查询API
应用信息查询API通过`AppMgtApi`类实现，提供分页查询功能。查询接口支持按应用名称、团队、环境等条件过滤，返回应用的详细信息。

查询结果包含应用的基本信息、模块信息、构建信息和团队信息。查询过程使用原生SQL语句，通过`get_app_info`方法实现。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "AppMgtApi"
participant DB as "数据库"
Client->>Api : 发送查询请求
Api->>DB : 执行SQL查询
DB-->>Api : 返回查询结果
Api->>Api : 处理分页信息
Api-->>Client : 返回应用列表
```

**图表来源**
- [app_mgt.py](file://app_mgt/app_mgt.py#L50-L100)

**本节来源**
- [app_mgt.py](file://app_mgt/app_mgt.py#L50-L100)

### 更新API
应用信息更新API通过`create`方法实现，支持应用信息的批量更新。更新接口接收JSON格式的请求数据，包括模块名称、是否上线、是否需要维护、制品库、容器名等字段。

更新过程使用Django ORM的`update`方法，确保数据的一致性。更新完成后，会自动更新相关联的构建信息和团队信息。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "AppMgtApi"
participant DB as "数据库"
Client->>Api : 发送更新请求
Api->>DB : 更新AppModule记录
Api->>DB : 更新AppBuild记录
Api->>DB : 更新AppTeam记录
DB-->>Api : 返回更新结果
Api-->>Client : 返回响应
```

**图表来源**
- [app_mgt.py](file://app_mgt/app_mgt.py#L100-L150)

**本节来源**
- [app_mgt.py](file://app_mgt/app_mgt.py#L100-L150)

### 删除API
应用信息删除API通过`destroy`方法实现，支持应用的逻辑删除。删除操作不会物理删除数据，而是通过更新状态字段实现。

删除过程使用Django ORM的`update`方法，将应用状态设置为已废弃。删除操作需要管理员权限，确保数据安全。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "AppMgtApi"
participant DB as "数据库"
Client->>Api : 发送删除请求
Api->>Api : 验证权限
Api->>DB : 更新应用状态
DB-->>Api : 返回删除结果
Api-->>Client : 返回响应
```

**图表来源**
- [app_mgt.py](file://app_mgt/app_mgt.py#L150-L160)

**本节来源**
- [app_mgt.py](file://app_mgt/app_mgt.py#L150-L160)

## 高级功能

### 应用标签管理
应用标签管理功能通过`AppTeamBind`模型实现，支持应用与团队的多对多关联。标签信息包括绑定说明、创建和修改信息等。

标签管理提供API接口，支持标签的增删改查操作。标签信息用于权限控制和统计分析，提高应用管理的灵活性。

```mermaid
classDiagram
class AppTeamBind {
+int app_id
+int team_id
+str bind_desc
+str create_user
+datetime create_time
+str update_user
+datetime update_time
+int stamp
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L173-L186)

**本节来源**
- [models.py](file://app_mgt/models.py#L173-L186)

### MD5校验
MD5校验功能通过`AppMd5Info`模型实现，存储应用的MD5值信息。MD5值用于验证应用的完整性和一致性，防止应用被篡改。

MD5校验在应用发布和更新时自动执行，校验结果存储在数据库中。校验过程使用`md5sum`命令，确保校验的准确性。

```mermaid
classDiagram
class AppMd5Info {
+int node_ip_bind_id
+str md5
+datetime update_time
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L200-L208)

**本节来源**
- [models.py](file://app_mgt/models.py#L200-L208)

### 自动归档配置
自动归档配置功能通过`JenkinsAutomaticArchiveInfo`模型实现，存储自动归档的相关信息。自动归档在满足条件时自动执行，减少人工操作。

自动归档配置包括归档时间、创建和修改信息等。归档过程通过`automatic_archive`方法实现，支持代码和配置的自动归档。

```mermaid
classDiagram
class JenkinsAutomaticArchiveInfo {
+str pipeline_id
+datetime create_time
+datetime update_time
}
```

**图表来源**
- [models.py](file://app_mgt/models.py#L210-L219)

**本节来源**
- [models.py](file://app_mgt/models.py#L210-L219)

## 复杂场景实践

### 应用迁移
应用迁移场景需要特别注意数据的一致性和完整性。迁移过程包括数据备份、数据迁移和数据验证三个步骤。

数据备份使用数据库导出工具，确保数据的完整性。数据迁移使用数据库导入工具，注意表结构的兼容性。数据验证通过对比新旧数据，确保迁移的准确性。

迁移过程中需要暂停相关服务，防止数据不一致。迁移完成后，需要更新配置文件和代码中的相关引用。

### 批量导入
批量导入场景需要处理大量数据，对性能和稳定性有较高要求。批量导入使用批处理技术，分批处理数据，避免内存溢出。

导入过程包括数据校验、数据转换和数据插入三个步骤。数据校验确保数据的合法性，数据转换将数据转换为数据库格式，数据插入使用批量插入技术，提高导入效率。

批量导入需要记录导入日志，便于问题排查。导入完成后，需要进行数据验证，确保导入的准确性。

## 常见问题与解决方案

### 注册冲突
注册冲突问题通常是由于应用名称重复导致的。解决方案是在注册前执行应用名称唯一性校验，如果发现重复，提示用户修改应用名称。

```python
try:
    AppInfo.objects.get(app_name=app_name)
    return False, '应用已存在'
except AppInfo.DoesNotExist:
    pass
```

### 依赖缺失
依赖缺失问题通常是由于代码仓库或团队信息不存在导致的。解决方案是在注册前执行依赖校验，如果发现缺失，提示用户补充相关信息。

```python
try:
    team_obj = TeamInfo.objects.get(team_short_name=team)
except Exception as e:
    logger.error(str(e))
    return False, '插入app_team失败'
```

## 附录

### API使用示例
#### 查询应用信息
```http
GET /api/app_mgt/list?pipeline_id={"module_name":"test","pageNum":1,"pageSize":10,"pageTotal":0}
```

#### 更新应用信息
```http
POST /api/app_mgt/create
Content-Type: application/json

{
    "module_name": "test",
    "need_online": 1,
    "need_check": 1,
    "lib_repo": "repo",
    "container_name": "container",
    "app_port": 8080,
    "create_path": "path",
    "url": "git_url",
    "path": "git_path",
    "package_name": "package",
    "team_name": "team",
    "platform_type": 1
}
```

#### 执行版本比对
```http
POST /api/app_compare/create
Content-Type: application/json

{
    "ip_list": ["***********", "***********"],
    "app_name": "test"
}
```