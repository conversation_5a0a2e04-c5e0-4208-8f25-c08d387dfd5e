# 应用版本对比

<cite>
**本文档引用的文件**
- [app_compare.py](file://app_mgt/app_compare.py)
- [models.py](file://app_mgt/models.py)
- [app_compare_ser.py](file://app_mgt/app_compare_ser.py)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心数据模型](#核心数据模型)
3. [差异比对算法实现](#差异比对算法实现)
4. [对比结果的数据结构与分类](#对比结果的数据结构与分类)
5. [API接口与调用示例](#api接口与调用示例)
6. [性能优化与边界情况处理](#性能优化与边界情况处理)
7. [总结](#总结)

## 简介
本文档详细阐述了应用版本对比功能的实现机制，重点分析了位于`app_mgt`模块中的`app_compare.py`文件所实现的差异比对算法。该功能旨在通过对比不同版本的应用构建信息，识别出模块组成、依赖关系和配置项的变更。文档将深入解析其核心数据模型、比对算法逻辑、结果数据结构、API接口设计，并涵盖性能优化策略和对异常情况的处理。

## 核心数据模型
应用版本对比功能依赖于`AppBuild`和`AppModule`两个核心数据模型来获取应用的构建和模块信息。

```mermaid
erDiagram
AppInfo {
int id PK
string app_name
string app_cname
bool app_status
string git_url
string git_path
string svn_url
string svn_path
string app_jdk_version
string app_desc
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
bool platform_type
datetime platform_time
}
AppModule {
int app_id FK
string module_name
string module_code
bool module_status
string module_desc
string module_svn_path
string module_jdk_version
bool need_online
bool need_check
int app_port
string container_name
string create_path
string lib_repo
string deploy_path
string extend_attr
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
bool zeus_type
bool need_ops
int is_component
}
AppBuild {
int app_id FK
string module_name
string module_code
string module_version
string package_type
string package_name
bool package_full
string build_jdk_version
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
bool need_mock
string mock_build_cmd
string build_cmd
}
AppInfo ||--o{ AppModule : "包含"
AppModule ||--o{ AppBuild : "对应"
```

**数据模型说明**
- **AppInfo (应用信息表)**: 存储应用的基本信息，如英文名、中文名、代码仓库地址（Git/SVN）等。
- **AppModule (应用模块表)**: 定义了应用的各个模块，包含模块名、发布路径、容器名、是否需要上线等关键属性。`deploy_path`字段是进行文件系统对比的关键。
- **AppBuild (应用构建表)**: 记录了应用的构建详情，包括包类型（如`jar`, `war`）、包名、是否为完整包等。这些信息用于生成具体的对比命令。

**Diagram sources**
- [models.py](file://app_mgt/models.py#L1-L100)

**Section sources**
- [models.py](file://app_mgt/models.py#L1-L100)

## 差异比对算法实现
差异比对的核心逻辑在`app_compare.py`文件中的`AppCompareApi`类里实现。其工作流程如下：

1.  **获取构建信息**: 通过`get_app_publish_info`服务，根据输入的IP列表和应用名，从数据库中查询出所有相关节点的模块名、IP、Minion ID、发布路径、包名、包类型等信息。
2.  **生成对比命令**: `generate_cmd`方法根据包类型（`jar`或`war`）和配置，生成在目标服务器上执行的Shell命令。
    *   对于`war`包，通常使用`du -s -b`命令计算`/WEB-INF/lib`和`/WEB-INF/classes/com`目录的大小。
    *   对于`jar`包，如果为完整包，则查找JAR文件并计算其MD5值；否则，计算整个发布路径的大小。
3.  **执行远程命令**: 使用`SaltTask`工具在指定的服务器节点上并行执行生成的对比命令。
4.  **收集结果**: 将每个节点的执行结果（如目录大小或MD5值）收集到一个字典`res_dict`中，以IP地址为键。
5.  **比较差异**: `compile_res`方法将第一个节点（作为基准）的结果与其他所有节点的结果进行逐一对比，统计出结果不一致的节点数量。

```mermaid
flowchart TD
Start([开始对比任务]) --> GetInfo["获取应用构建信息<br/>(get_app_publish_info)"]
GetInfo --> GenerateCmd["生成对比命令<br/>(generate_cmd)"]
GenerateCmd --> ExecuteCmd["在各节点执行命令<br/>(run_compare_cmd)"]
ExecuteCmd --> CollectRes["收集各节点结果<br/>(res_dict)"]
CollectRes --> Compare["比较结果差异<br/>(compile_res)"]
Compare --> End([返回对比结果])
```

**Diagram sources**
- [app_compare.py](file://app_mgt/app_compare.py#L100-L200)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L200-L300)

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L100-L250)

## 对比结果的数据结构与分类
对比操作完成后，系统会生成一个结构化的结果，其数据结构设计如下：

| 字段 | 类型 | 描述 |
| :--- | :--- | :--- |
| `res_dict` | `dict` | 一个字典，键为节点IP地址，值为该节点上执行对比命令后返回的结果列表（如目录大小）。 |
| `diff_num` | `int` | 整数，表示与基准节点结果不一致的节点数量。 |

**差异级别分类**
该功能主要通过`diff_num`的值来体现差异级别：
- **无差异**: `diff_num`为0，表示所有节点的对比结果一致。
- **存在差异**: `diff_num`大于0，表示有`diff_num`个节点与基准节点存在差异。该功能本身不区分“新增”、“删除”或“变更”的具体类型，而是将所有不一致的结果都归类为“存在差异”。

**可视化呈现方式**
目前的实现主要通过API返回结构化数据，前端应用可以基于`res_dict`和`diff_num`进行可视化呈现，例如：
- 以表格形式列出每个节点的IP和其对应的对比结果。
- 用图表（如柱状图）展示不同节点上关键目录的大小差异。
- 当`diff_num > 0`时，以醒目的颜色（如红色）提示用户存在不一致。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L250-L300)

## API接口与调用示例
应用版本对比功能通过`AppCompareApi.create`方法提供RESTful API接口。

**API调用示例**

```python
import requests

# API端点
url = "http://your-api-server/app_compare/"

# 请求头
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer your_token"  # 如果需要认证
}

# 请求体
payload = {
    "ip_list": ["*************", "*************", "*************"],
    "app_name": "gateway-web"
}

# 发送POST请求
response = requests.post(url, json=payload, headers=headers)

# 处理响应
if response.status_code == 200:
    result = response.json()
    print("调用成功")
    print(f"差异数量: {result['data']['diff_num']}")
    print("各节点结果:")
    for ip, info in result['data']['res_dict'].items():
        print(f"  {ip}: {info}")
else:
    print(f"调用失败: {response.text}")
```

**响应示例**
```json
{
  "code": 0,
  "msg": "调用成功",
  "data": {
    "res_dict": {
      "*************": ["74477708", "625576"],
      "*************": ["74477708", "625576"],
      "*************": ["74477708", "625576"]
    },
    "diff_num": 0
  }
}
```

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L300-L350)

## 性能优化与边界情况处理
### 性能优化策略
1.  **异步计算**: `JenkinsAutomaticArchiveApi.create`方法使用了Python的`threading.Thread`来启动一个后台线程执行`main`方法。这使得API可以立即返回，而耗时的对比和归档任务在后台异步执行，极大地提升了用户体验和系统响应速度。
    ```python
    threading.Thread(target=self.main, args=(user, suite_list, mail_to), daemon=True).start()
    ```
2.  **缓存机制**: 虽然当前代码中`recode_md5`方法被注释掉了，但其设计意图是将对比结果（如MD5值）缓存到数据库（`AppMd5Info`表）中。这可以避免对同一节点的重复计算，是典型的缓存优化策略。

### 边界情况处理
代码中通过自定义异常和条件判断，对多种边界情况进行了妥善处理：
- **版本不存在**: 在`get_app_publish_info`的SQL查询中，通过`WHERE f.module_name = "{}"`和`h.node_ip IN ("{}")`进行精确匹配。如果查询结果为空，`get_compare_info`方法会正常返回一个空的`res_dict`，并在`create`方法中返回`diff_num: 0`。
- **数据不完整**:
    - **缺少包名**: 抛出`PackageNoneException`异常。
    - **缺少Minion ID**: 抛出`MinionIdNoneException`异常。
    - **不支持的包类型**: 抛出`WarException`异常。
    - **命令执行失败**: 捕获`CmdRetrunException`异常。
- **网络或执行错误**: 通过`try...except`块捕获所有未预期的异常，并返回失败信息。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L350-L496)

## 总结
本文档全面解析了应用版本对比功能的实现。该功能通过`AppBuild`和`AppModule`模型获取应用的构建与部署信息，利用Salt远程执行命令来对比不同节点上的文件系统状态。其结果以`res_dict`和`diff_num`的形式结构化返回，能够有效识别部署不一致的问题。通过采用异步计算等策略，系统在保证功能完整性的同时也兼顾了性能。完善的异常处理机制确保了在面对数据缺失或网络问题时，系统能够稳定运行并给出明确的错误提示。