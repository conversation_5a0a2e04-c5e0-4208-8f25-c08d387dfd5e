# 应用接口管理

<cite>
**本文档引用的文件**   
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py)
- [models.py](file://app_mgt/models.py)
- [settings.py](file://spider/settings.py)
</cite>

## 目录
1. [应用接口定义与维护](#应用接口定义与维护)
2. [API文档自动生成与元数据同步](#api文档自动生成与元数据同步)
3. [接口版本控制与路由配置](#接口版本控制与路由配置)
4. [安全策略实现](#安全策略实现)
5. [接口生命周期管理](#接口生命周期管理)
6. [与外部系统集成](#与外部系统集成)

## 应用接口定义与维护

该模块通过 `AppMgtInterfaceApi` 类实现应用接口的定义与维护。系统支持 HTTP 和 DUBBO 两种接口类型，通过 `create` 接口接收来自扫描代理的接口信息。

接口信息首先被写入临时表 `app_mgt_interface_info_temp`，然后与主表 `app_mgt_interface_info` 进行合并。系统通过 `__insert_interface_temp` 方法处理扫描数据，将接口路径、方法、参数等信息存入临时表。对于 HTTP 接口，系统会自动从 `app_mgt_interface_map` 表中获取内容类型（content-type）。

系统实现了并发控制机制，通过 `AppMgtInterfaceScanLock` 表防止同一应用、分支和接口类型的并发扫描。在处理过程中，系统会检查待删除的接口数量是否超过阈值（由 `INTERFACE_SCAN.interface_check_threshold` 配置），如果超过则发送警告邮件。

**Section sources**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L227-L360)
- [models.py](file://app_mgt/models.py#L332-L357)

## API文档自动生成与元数据同步

系统通过 `app_mgt_apidoc.py` 文件中的 `AppMgtInterfaceApiForMring` 类提供API文档自动生成功能。该功能通过 `__get_api_doc_api` 方法从 `app_mgt_apidoc_info` 和 `app_mgt_apidoc_param_info` 表中获取API文档信息。

系统实现了API文档与内部接口元数据的同步策略。在 `__merge_api` 方法中，系统会将来自扫描代理的接口信息（agent api）与API文档信息（api_doc）进行合并。如果API文档中缺少某个接口，则会将该接口从扫描代理的数据中补充进来。同时，系统会将扫描代理提供的 content-type 和 encryption 信息补充到API文档中。

对于HTTP请求头的处理，系统在 `__get_extra_param` 方法中进行了优化，尝试处理 content-type 大小写不一致的问题，并为没有指定 content-type 的请求头设置默认值。

```mermaid
sequenceDiagram
participant 外部服务 as 外部Java服务
participant API文档 as AppMgtInterfaceApiForMring
participant 扫描代理 as 扫描代理数据
participant API元数据 as app_mgt_apidoc_info
外部服务->>API文档 : list请求
API文档->>API元数据 : 查询API文档信息
API文档->>扫描代理 : 获取扫描代理数据
API文档->>API文档 : __merge_api 合并数据
API文档-->>外部服务 : 返回合并后的API信息
```

**Diagram sources**
- [app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L130-L184)
- [models.py](file://app_mgt/models.py#L408-L457)

## 接口版本控制与路由配置

系统通过 `branch_name` 字段实现接口版本控制。在 `create` 方法中，接口信息与特定的分支版本关联。系统提供了 `get_archive_branch_name_by_app` 函数，用于根据应用名获取归档版本的分支名。

对于路由配置，系统通过 `interface_path` 字段存储接口路径。在处理多方法HTTP接口时，系统通过 `handle_mult_method_http_api` 方法确定哪个HTTP方法是主要的。系统会优先选择 GET 方法，如果没有 GET 方法则依次选择 POST、PUT、DELETE 方法，并将其他方法的状态设置为 '2'（非主要）。

```mermaid
flowchart TD
Start([开始]) --> CheckGET{"是否存在GET方法?"}
CheckGET --> |是| SetGETMain["设置GET方法为主方法"]
CheckGET --> |否| CheckPOST{"是否存在POST方法?"}
CheckPOST --> |是| SetPOSTMain["设置POST方法为主方法"]
CheckPOST --> |否| CheckPUT{"是否存在PUT方法?"}
CheckPUT --> |是| SetPUTMain["设置PUT方法为主方法"]
CheckPUT --> |否| CheckDELETE{"是否存在DELETE方法?"}
CheckDELETE --> |是| SetDELETEMain["设置DELETE方法为主方法"]
CheckDELETE --> |否| NoMain["无主方法"]
SetGETMain --> End([结束])
SetPOSTMain --> End
SetPUTMain --> End
SetDELETEMain --> End
NoMain --> End
```

**Diagram sources**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L423-L472)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L474-L519)

## 安全策略实现

系统实现了多种安全策略，包括白名单机制和认证方式。在数据库脚本中，可以看到为 `iter_whitelist_group` 和 `iter_whitelist_app` 表添加了与 apidoc 相关的白名单记录，用于控制哪些应用或应用组可以进行接口扫描。

系统通过 `authentication_classes = []` 设置了空的认证类列表，表示这些接口不需要额外的认证。但在实际使用中，这些接口可能受到网络层或代理层的安全控制。

对于敏感操作，系统记录了操作日志。在 `AgentMgtExecLog` 表中，记录了接口扫描的成功和失败情况，包括执行批次、代理类型、状态和错误信息等。

**Section sources**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L322-L337)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L337-L360)
- [db/迭代3.0.1/02-生产过程-白名单数据.sql](file://db/迭代3.0.1/02-生产过程-白名单数据.sql#L15-L17)

## 接口生命周期管理

系统提供了完整的接口注册、更新和下线操作指南。通过 `create` 方法实现接口注册，系统会先检查是否已存在相同的应用、分支和接口类型的数据，如果不存在则从归档版本创建初始接口信息。

接口更新通过 `AppInterfaceApi` 类的 `create` 方法实现，支持更新接口名称。系统区分了测试环境和开发环境的接口名称，分别存储在 `interface_name` 和 `interface_name_dev` 字段中。

接口下线通过将 `status` 字段设置为 0 来实现。在数据合并过程中，如果某个接口存在于主表但不存在于临时表，则会被添加到临时表中并设置状态为 0，表示该接口已被删除。

系统还提供了查询接口，如 `list` 方法用于获取接口列表，`AppBranchInfoApi` 用于根据应用名查询分支列表。

**Section sources**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L519-L553)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L648-L679)

## 与外部系统集成

系统设计了与外部网关或服务发现组件集成的实践建议。通过 `AppBranchInfoExistApiForMring` 类，系统提供了给外部Java服务的接口，返回参数使用驼峰命名法，便于Java客户端使用。

系统通过 `INTERFACE_URL` 配置了与多个外部系统的接口地址，包括宙斯（zeus）、蜘蛛（spider）、CMDB 等。在 `app_mgt_apidoc.py` 中，系统通过 `get_archive_branch_name_by_app_before_date` 函数从迭代管理系统获取归档分支信息，实现了与迭代管理系统的集成。

对于日志记录，系统将请求报文转储到指定目录，便于后续分析和审计。转储路径由 `INTERFACE_SCAN.agent_request_message_log_dir` 配置，文件以执行批次为名保存为JSON格式。

**Section sources**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L679-L700)
- [app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L100-L115)
- [settings.py](file://spider/settings.py#L643-L670)