# 核心模块

<cite>
**本文档引用的文件**   
- [app_mgt/models.py](file://app_mgt/models.py)
- [env_mgt/models.py](file://env_mgt/models.py)
- [iter_mgt/models.py](file://iter_mgt/models.py)
- [publish/models.py](file://publish/models.py)
- [app_mgt/app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
</cite>

## 目录
1. [应用管理](#应用管理)
2. [环境管理](#环境管理)
3. [迭代管理](#迭代管理)
4. [发布管理](#发布管理)
5. [模块协同工作](#模块协同工作)
6. [技术决策与权衡](#技术决策与权衡)

## 应用管理

应用管理模块是整个系统的基础，负责维护所有应用的元数据信息。该模块通过`AppInfo`和`AppModule`两个核心模型来管理应用及其模块的详细信息。

`AppInfo`模型存储应用的基本信息，包括应用名称、状态、代码仓库地址、JDK版本、应用描述等。`AppModule`模型则存储应用模块的详细信息，包括模块名称、状态、部署路径、端口、容器名称等。这两个模型通过`app_id`字段建立关联，形成应用与模块的一对多关系。

应用管理模块提供了丰富的API接口，如`AppModuleApi`用于获取应用模块列表，`AppInfoApiForMock`为mock系统提供应用查询接口。这些接口通过`app_mgt_ser`服务层实现业务逻辑，采用原生SQL查询以提高性能。

该模块还包含应用构建信息管理（`AppBuild`模型）、移动端构建信息管理（`MobileBuild`模型）以及应用团队绑定管理（`AppTeamBind`模型）等功能，全面支持应用的全生命周期管理。

**模块来源**
- [app_mgt/models.py](file://app_mgt/models.py#L0-L480)
- [app_mgt/app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L0-L799)

## 环境管理

环境管理模块负责管理系统的物理和逻辑环境结构，包括可用域、环境套、节点等核心概念。该模块通过一系列模型构建了完整的环境管理体系。

`Region`模型表示可用域，包含机房信息、类型信息和可用域说明。`Suite`模型表示环境套，与可用域关联，定义了环境套的编码、名称和状态。`Node`模型表示具体的物理或虚拟节点，包含节点IP、操作系统、状态等信息。

核心的`NodeBind`模型实现了应用模块与具体节点的绑定关系，这是实现应用部署的关键。通过`module_name`、`suite_id`和`node_id`三个字段的组合，系统能够精确地确定每个应用模块在哪个环境套的哪个节点上运行。

环境管理模块还提供了丰富的查询服务，如`get_region_info`获取可用域信息，`get_suite_info`获取环境套信息，`get_node_info`获取节点详细信息等。这些服务通过原生SQL查询实现，确保了查询性能。

**模块来源**
- [env_mgt/models.py](file://env_mgt/models.py#L0-L560)
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L0-L525)

## 迭代管理

迭代管理模块是CI/CD流程的核心，负责管理软件开发的迭代周期。该模块通过`Branches`、`BranchIncludeSys`和`PublishOrder`等核心模型构建了完整的迭代管理体系。

`Branches`模型（对应`iter_mgt_iter_info`表）存储迭代的基本信息，包括迭代版本、分支类型、创建时间、完成时间、预期上线时间等。`BranchIncludeSys`模型（对应`iter_mgt_iter_app_info`表）记录了迭代中包含的应用信息，包括应用状态、发布日期、申请人等。

`PublishOrder`模型（对应`iter_mgt_publish_application`表）管理发布申请，包含申请人、申请时间、发布环境、状态等信息。该模块还包含发布申请SQL管理（`PublishApplicationSQL`）、发布申请抄送管理（`PublishApplicationCC`）等辅助模型。

迭代管理模块提供了创建迭代、修改迭代信息等API接口。通过`call_script.ScriptCaller`执行脚本，实现了与外部系统的集成。该模块还支持迭代白名单管理、组件关系管理等功能，确保迭代过程的合规性和可追溯性。

**模块来源**
- [iter_mgt/models.py](file://iter_mgt/models.py#L0-L423)

## 发布管理

发布管理模块负责应用的部署和发布操作，是CI/CD流程的执行终端。该模块通过`SaltCmd`、`DeployInfo`、`ProductInfo`等核心模型构建了完整的发布管理体系。

`SaltCmd`模型（对应`publish_exec_salt_cmd`表）存储salt自定义命令，用于执行部署相关的操作。`DeployInfo`模型存储部署信息，包括部署路径、配置路径、脚本路径等。`ProductInfo`模型存储制品信息，包括制品库地址、版本、大小等。

`OpsOperateHistory`模型记录服务器操作历史，`SaltReturn`模型存储salt执行返回值，这些模型为发布操作提供了完整的审计追踪能力。`TransitReposInfo`模型管理中转库信息，支持复杂的部署场景。

发布管理模块还包含发布白名单管理（`PublishAppWhiteList`）、特殊非交易日设置（`PublishOddDates`）等安全控制机制。`IterMgtTradeTimePublishLog`模型记录交易时间的发布历史，确保关键时段的发布操作可追溯。

**模块来源**
- [publish/models.py](file://publish/models.py#L0-L384)

## 模块协同工作

四大核心模块通过数据模型和API接口紧密协作，共同支持完整的CI/CD流程。应用管理模块提供应用元数据，环境管理模块提供部署环境，迭代管理模块规划发布周期，发布管理模块执行具体部署操作。

在典型的发布流程中，首先通过应用管理模块获取应用信息，然后通过环境管理模块确定目标部署环境，接着在迭代管理模块创建发布申请，最后由发布管理模块执行具体的部署操作。各模块通过共享的数据库表（如`env_mgt_node_bind`）实现数据交互。

例如，发布操作需要同时访问`app_mgt_app_module`表获取应用模块信息，访问`env_mgt_node_bind`表获取节点绑定信息，访问`iter_mgt_iter_info`表获取迭代信息。这种设计确保了各模块的独立性，同时通过数据关联实现了功能的完整集成。

**模块来源**
- [app_mgt/models.py](file://app_mgt/models.py#L0-L480)
- [env_mgt/models.py](file://env_mgt/models.py#L0-L560)
- [iter_mgt/models.py](file://iter_mgt/models.py#L0-L423)
- [publish/models.py](file://publish/models.py#L0-L384)

## 技术决策与权衡

系统在设计过程中做出了多项关键技术决策。首先，采用Django框架作为基础，利用其ORM特性简化数据库操作，同时在性能关键路径上使用原生SQL查询以保证性能。

其次，采用模块化设计，将应用管理、环境管理、迭代管理和发布管理分离为独立模块，既保证了功能的内聚性，又降低了模块间的耦合度。这种设计使得各模块可以独立演进和维护。

在数据存储方面，选择关系型数据库MySQL，利用其事务特性和完整性约束确保数据一致性。通过合理的表结构设计和索引优化，保证了系统的查询性能。

在安全控制方面，引入发布白名单、交易时间发布限制等机制，确保关键操作的安全性。同时，通过详细的操作日志记录，提供了完整的审计追踪能力。

这些技术决策在系统性能、可维护性和安全性之间取得了良好的平衡，为CI/CD流程的稳定运行提供了坚实的基础。

**模块来源**
- [app_mgt/models.py](file://app_mgt/models.py#L0-L480)
- [env_mgt/models.py](file://env_mgt/models.py#L0-L560)
- [iter_mgt/models.py](file://iter_mgt/models.py#L0-L423)
- [publish/models.py](file://publish/models.py#L0-L384)