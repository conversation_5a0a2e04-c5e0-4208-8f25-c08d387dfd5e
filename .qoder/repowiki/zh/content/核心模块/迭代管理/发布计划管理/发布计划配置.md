# 发布计划配置

<cite>
**本文档引用的文件**   
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py)
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py)
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py)
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py)
- [publish_plan_phase_config.py](file://iter_mgt/publish_plan/config/publish_plan_phase_config.py)
</cite>

## 目录
1. [简介](#简介)
2. [发布流程模板设计](#发布流程模板设计)
3. [标准发布流程配置](#标准发布流程配置)
4. [紧急发布流程配置](#紧急发布流程配置)
5. [快速发布流程配置](#快速发布流程配置)
6. [新发布流程模板配置指南](#新发布流程模板配置指南)
7. [配置文件与实例映射关系](#配置文件与实例映射关系)
8. [API动态加载与验证](#apidynamic加载与验证)

## 简介
本文档详细介绍了发布计划配置功能，涵盖标准发布、紧急发布和快速发布等不同发布流程的配置策略。通过分析核心配置文件的设计与实现，说明如何定义发布流程模板、配置节点依赖关系以及集成自动化任务。文档还提供了创建新发布流程的最佳实践，并描述了配置文件与发布计划实例之间的映射机制。

## 发布流程模板设计

`publish_plan_config.py` 文件定义了发布计划配置的基础类 `PublishPlanConfig`，该类为所有具体发布流程提供了通用的处理逻辑。其核心功能包括：

- **阶段配置获取**：通过 `get_phase_configs` 方法获取发布流程的阶段配置
- **节点顺序处理**：`result_post_handler` 方法负责计算每个节点的全局顺序号和下一节点顺序号
- **顺序号生成**：`get_order_no` 和 `get_next_node_order_no` 静态方法用于生成节点的唯一顺序标识

```mermaid
classDiagram
class PublishPlanConfig {
+__init__()
+get_phase_configs(publish_config)
+result_post_handler(phase_list)
+get_order_no(origin_node_order, phase_order)
+get_next_node_order_no(origin_node_order, phase_count, node_order, next_phase_order)
}
PublishPlanConfig <|-- NormalPublishPlanConfig
PublishPlanConfig <|-- ExpressPublishPlanConfig
PublishPlanConfig <|-- RapidPublishPlanConfig
```

**图示来源**
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py#L1-L30)

**本节来源**
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py#L1-L30)

## 标准发布流程配置

`normal_publish_plan_config.py` 文件实现了标准发布流程的配置，继承自 `PublishPlanConfig` 基类。该配置根据发布类型定义了不同的阶段序列：

当发布类型为 `PUBLISH` 时，标准发布流程包含以下三个主要阶段：
1. **上线前阶段**：执行产线申请和发布决策
2. **上线中阶段**：执行节点发布
3. **上线后阶段**：执行归档操作

当发布类型为 `PUBLISH_BEFORE` 时，流程仅包含自动化测试阶段，用于预发布验证。

```mermaid
flowchart TD
A[上线前阶段] --> B[产线申请]
A --> C[发布决策]
B --> D[上线中阶段]
C --> D
D --> E[节点发布]
E --> F[上线后阶段]
F --> G[归档]
```

**图示来源**
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L6-L32)
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py#L1-L137)

**本节来源**
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L6-L32)
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py#L1-L137)

## 紧急发布流程配置

`express_publish_plan_config.py` 文件定义了紧急发布流程的配置，适用于需要快速修复生产问题的场景。该流程包含五个连续阶段：

1. **验证阶段**：检查 release 分支状态
2. **自动化测试阶段**：执行关键路径的自动化测试
3. **上线前阶段**：准备发布前的必要检查
4. **上线中阶段**：执行实际的节点发布
5. **上线后阶段**：完成发布后的归档工作

紧急发布流程的特点是线性执行，各阶段按顺序进行，确保快速而可靠的发布过程。

```mermaid
sequenceDiagram
participant 验证 as 验证阶段
participant 自动化测试 as 自动化测试阶段
participant 上线前 as 上线前阶段
participant 上线中 as 上线中阶段
participant 上线后 as 上线后阶段
验证->>自动化测试 : 完成验证
自动化测试->>上线前 : 测试通过
上线前->>上线中 : 准备就绪
上线中->>上线后 : 发布完成
```

**图示来源**
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py#L6-L32)
- [publish_plan_phase_config.py](file://iter_mgt/publish_plan/config/publish_plan_phase_config.py#L1-L24)

**本节来源**
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py#L6-L32)
- [publish_plan_phase_config.py](file://iter_mgt/publish_plan/config/publish_plan_phase_config.py#L1-L24)

## 快速发布流程配置

`rapid_publish_plan_config.py` 文件实现了快速发布流程的配置，旨在最大限度地缩短发布周期。该流程的关键特性包括：

- **并行执行**：自动化测试阶段与上线前准备阶段可以并行执行
- **简化流程**：减少了不必要的审批环节
- **灵活配置**：自动化测试节点标记为非必需，可根据实际情况跳过

快速发布流程包含五个阶段，其中自动化测试可以在发布准备的同时进行，从而显著缩短整体发布时间。

```mermaid
flowchart TD
A[验证阶段] --> B[上线前阶段]
A --> C[自动化测试阶段]
B --> D[上线中阶段]
C --> D
D --> E[上线后阶段]
```

**图示来源**
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py#L6-L32)
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py#L1-L137)

**本节来源**
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py#L6-L32)
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py#L1-L137)

## 新发布流程模板配置指南

创建新的发布流程模板需要遵循以下步骤：

### 节点定义
在 `publish_plan_node_config.py` 中通过 `NodeEnum` 枚举定义各种节点类型，每个节点包含：
- **节点名称**：如"构建/部署"、"产线申请"等
- **参数配置**：定义节点所需的输入参数及其显示属性
- **执行类型**：指定节点的执行方式

### 审批规则
通过 `PhaseEnum` 枚举定义不同阶段及其包含的节点序列，每个阶段可以配置：
- **阶段名称**：如"上线前"、"上线中"等
- **节点顺序**：定义阶段内节点的执行顺序

### 自动化任务集成
在节点参数配置中，可以通过 `param_options_type` 字段集成外部系统：
- **用户选择**：`user` 类型可从系统用户中选择
- **环境选择**：`env` 类型可从可用环境中选择
- **业务分支**：`biz_iter_branch` 类型可选择业务迭代分支

**本节来源**
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py#L1-L137)
- [publish_plan_phase_config.py](file://iter_mgt/publish_plan/config/publish_plan_phase_config.py#L1-L24)

## 配置文件与实例映射关系

发布计划配置文件与实际发布计划实例之间存在明确的映射关系：

- **模板到实例**：每个配置类（如 `NormalPublishPlanConfig`）作为模板，用于生成具体的发布计划实例
- **阶段映射**：配置中的 `phase_configs` 方法返回的阶段列表直接映射到发布计划的阶段结构
- **节点映射**：每个阶段包含的节点配置映射到发布计划中的具体执行节点

配置通过 `__dict__` 转换将对象属性转换为字典格式，便于序列化和传输。

```mermaid
erDiagram
发布计划模板 {
string 模板名称
string 模板类型
}
发布计划实例 {
uuid 实例ID
string 状态
timestamp 创建时间
timestamp 更新时间
}
发布计划阶段 {
int 阶段顺序
string 阶段名称
string 下一阶段
}
发布计划节点 {
int 节点顺序
string 节点名称
json 参数配置
}
发布计划模板 ||--o{ 发布计划实例 : "生成"
发布计划实例 ||--o{ 发布计划阶段 : "包含"
发布计划阶段 ||--o{ 发布计划节点 : "包含"
```

**图示来源**
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py#L1-L30)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L6-L32)

**本节来源**
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py#L1-L30)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L6-L32)

## API动态加载与验证

系统通过以下机制实现发布计划配置的动态加载和验证：

1. **配置加载**：调用 `get_phase_configs` 方法获取配置，该方法会触发 `result_post_handler` 进行后处理
2. **顺序验证**：通过 `get_order_no` 和 `get_next_node_order_no` 确保节点顺序的正确性
3. **深拷贝保护**：使用 `copy.deepcopy` 返回配置副本，防止原始配置被意外修改

配置验证主要检查：
- 阶段顺序的连续性
- 节点顺序号的唯一性
- 必需节点的存在性
- 参数配置的完整性

**本节来源**
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py#L1-L30)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L6-L32)