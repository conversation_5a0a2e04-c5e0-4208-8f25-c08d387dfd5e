# 发布计划管理

<cite>
**本文档引用文件**  
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py)
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py)
- [plan_ser.py](file://iter_mgt/plan_ser.py)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py)
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py)
- [normal_bugfix_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_bugfix_publish_plan_config.py)
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py)
- [express_bugfix_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_bugfix_publish_plan_config.py)
</cite>

## 目录
1. [引言](#引言)
2. [发布计划领域模型设计](#发布计划领域模型设计)
3. [发布状态流转机制](#发布状态流转机制)
4. [节点调度算法分析](#节点调度算法分析)
5. [特殊发布流程配置策略](#特殊发布流程配置策略)
6. [API使用示例](#api使用示例)
7. [关联验证机制](#关联验证机制)
8. [常见问题排查指南](#常见问题排查指南)
9. [最佳实践](#最佳实践)

## 引言
本文档详细阐述发布计划管理功能的核心机制，涵盖发布计划的创建、节点调度与状态跟踪。重点解析发布计划与发布节点的领域模型设计、状态流转逻辑、基于依赖关系的调度算法，并提供API使用示例及问题排查指南。

## 发布计划领域模型设计

发布计划管理的核心数据模型定义在 `iter_mgt_publish_plan.py` 文件中，主要包含 `IterMgtPublishPlan` 和 `BizTestAppNeed` 两个模型。

`IterMgtPublishPlan` 模型用于存储发布计划的元数据，其关键字段包括：
- **id**: 主键，自增
- **batch_no**: 批次号，用于标识一次发布
- **module_name**: 模块名称
- **branch_name**: 分支名称
- **iter_id**: 迭代ID，关联所属迭代
- **plan_type**: 计划类型（如普通、快速、紧急等）
- **plan_status**: 计划当前状态
- **start_time / end_time**: 计划的开始与结束时间
- **create_user / update_user**: 创建与更新用户
- **create_time / update_time**: 创建与更新时间戳

该模型通过 `(module_name, iter_id, batch_no)` 的唯一性约束确保同一模块在同一次迭代中的发布批次不重复。

`BizTestAppNeed` 模型则用于定义业务应用的自动化测试需求，包括是否必须进行自动化测试、通过率阈值等配置。

**Section sources**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L48)

## 发布状态流转机制

发布计划的状态管理通过枚举类 `IterMgtPublishPlanEnum` 和 `NodeStatusEnum` 实现，定义在 `publish_plan_status_enum.py` 文件中。

### 发布计划状态（IterMgtPublishPlanEnum）
- **RUNNING** (`running`): 正在执行
- **SUCCESS** (`success`): 执行成功
- **FAIL** (`fail`): 执行失败
- **STOP** (`stop`): 已终止

### 节点状态（NodeStatusEnum）
- **READY** (`ready`): 等待调用
- **RUNNING** (`running`): 正在执行
- **SUCCESS** (`success`): 执行成功
- **FAIL** (`fail`): 执行失败

状态流转由系统在执行过程中自动更新。例如，当一个发布计划被触发时，其状态从初始状态变为 `RUNNING`；当所有节点成功执行后，状态变为 `SUCCESS`；若任一节点失败，则整体状态变为 `FAIL`。

```mermaid
stateDiagram-v2
[*] --> READY
READY --> RUNNING : 启动发布
RUNNING --> SUCCESS : 所有节点成功
RUNNING --> FAIL : 任一节点失败
RUNNING --> STOP : 手动终止
```

**Diagram sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)

**Section sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)

## 节点调度算法分析

发布节点的调度逻辑主要由 `plan_ser.py` 文件中的 `get_depend_relation` 函数实现。该算法基于应用间的依赖关系进行拓扑排序，确保依赖项先于被依赖项发布。

### 核心调度逻辑
1. **依赖关系查询**：通过SQL查询获取指定迭代ID（`iter_id`）下所有应用的依赖关系。
2. **过滤可发布应用**：筛选出 `need_online` 字段为0的应用（即无需上线的应用），这些通常是基础库或中间件。
3. **构建依赖列表**：将查询结果中的应用名加入依赖列表，作为调度的前置条件。

```python
def get_depend_relation(iter_id):
    depend_list = []
    sql = '''
        SELECT i.app_name 
        FROM app_mgt_app_module t 
        LEFT JOIN app_mgt_app_info i ON t.app_id = i.id 
        WHERE t.module_name IN (
            SELECT appName FROM iter_mgt_iter_app_info 
            WHERE pipeline_id = "{}"
        ) 
        GROUP BY i.app_name 
        HAVING SUM(t.need_online) = 0;
    '''.format(iter_id)
    # 执行查询并填充 depend_list
    return depend_list
```

该算法确保了在发布一个应用之前，其所有依赖的基础组件都已准备就绪，从而避免了因依赖缺失导致的发布失败。

**Section sources**
- [plan_ser.py](file://iter_mgt/plan_ser.py#L200-L220)

## 特殊发布流程配置策略

系统支持多种发布计划类型，每种类型对应不同的配置策略，通过 `PublishPlanEnum` 枚举和相应的配置类实现。

### 支持的发布类型
- **NORMAL** (`normal`): 普通发布
- **NORMAL_BUGFIX** (`normal_bugfix`): 普通紧急修复
- **RAPID** (`rapid`): 快速发布
- **EXPRESS** (`express`): 紧急发布
- **EXPRESS_BUGFIX** (`express_bugfix`): 紧急修复发布

### 配置策略示例：普通发布（NormalPublishPlanConfig）
以 `normal_publish_plan_config.py` 中的 `NormalPublishPlanConfig` 为例，其 `phase_configs` 方法定义了发布流程的阶段：

```python
def phase_configs(self, publish_config):
    if PublishConfigEnum.PUBLISH == publish_config:
        return [
            PublishPlanPhaseConfig(
                phase_parallel="2-1", 
                phase_order=2, 
                next_phase_order=3,
                phase_config=PhaseEnum.BEFORE_PUBLISH,
                next_phase_name=PhaseEnum.PROCESS_PUBLISH.phase
            ),
            PublishPlanPhaseConfig(
                phase_parallel="2-2", 
                phase_order=3, 
                next_phase_order=4,
                phase_config=PhaseEnum.PROCESS_PUBLISH,
                next_phase_name=PhaseEnum.AFTER_PUBLISH.phase
            ),
            PublishPlanPhaseConfig(
                phase_parallel="3-1", 
                phase_order=4, 
                next_phase_order=-1,
                phase_config=PhaseEnum.AFTER_PUBLISH, 
                next_phase_name=None
            ),
        ]
    else:
        return [
            PublishPlanPhaseConfig(
                phase_parallel="1-1", 
                phase_order=1, 
                next_phase_order=2,
                phase_config=PhaseEnum.AUTO_TEST, 
                next_phase_name=PhaseEnum.BEFORE_PUBLISH.phase
            )
        ]
```

此配置定义了发布流程包含三个阶段：发布前准备、发布中处理、发布后收尾。每个阶段有明确的顺序和并行标识，系统根据此配置进行节点调度。

**Section sources**
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L1-L33)
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)

## API使用示例

### 创建发布计划
```http
POST /api/publish-plan/
{
  "batch_no": "20231001",
  "module_name": "user-service",
  "branch_name": "release/v1.2",
  "iter_id": "PROJ_202310",
  "plan_type": "rapid",
  "start_time": "2023-10-01T10:00:00Z"
}
```

### 更新节点状态
```http
PUT /api/publish-plan/{plan_id}/node/{node_id}/
{
  "status": "success"
}
```

### 查询发布进度
```http
GET /api/publish-plan/{plan_id}/status/
```
响应：
```json
{
  "plan_status": "running",
  "nodes": [
    {"id": "node1", "status": "success"},
    {"id": "node2", "status": "running"},
    {"id": "node3", "status": "ready"}
  ]
}
```

## 关联验证机制

发布计划与迭代、环境、测试结果的关联通过以下机制验证：
1. **迭代关联**：通过 `iter_id` 字段与 `iter_mgt_iter_info` 表关联，确保发布计划属于有效迭代。
2. **环境关联**：在发布申请时通过 `env` 字段指定目标环境，系统校验环境状态。
3. **测试结果关联**：通过 `BizTestAppNeed` 模型中的 `pass_rate_threshold` 字段，强制要求自动化测试通过率达到阈值方可发布。

## 常见问题排查指南

### 发布阻塞
- **现象**：发布计划长时间处于 `READY` 状态。
- **排查步骤**：
  1. 检查 `get_depend_relation` 返回的依赖列表是否为空。
  2. 确认依赖项是否已成功发布。
  3. 检查数据库中 `app_mgt_app_module` 表的 `need_online` 字段配置。

### 节点依赖错误
- **现象**：节点执行失败，提示依赖缺失。
- **排查步骤**：
  1. 使用 `get_all_app_by_gitpath` 函数验证应用与Git路径的映射关系。
  2. 检查 `app_mgt_app_info` 和 `app_mgt_app_module` 表的数据一致性。
  3. 确认 `iter_mgt_iter_app_info` 中的应用名拼写正确。

## 最佳实践
1. **明确依赖关系**：在发布前，确保所有依赖项的 `need_online` 配置正确。
2. **合理选择发布类型**：根据业务紧急程度选择 `rapid` 或 `express` 类型，避免滥用紧急发布。
3. **设置合理的测试阈值**：在 `BizTestAppNeed` 中配置适当的 `pass_rate_threshold`，平衡质量与效率。
4. **监控状态流转**：通过API定期查询发布状态，及时发现并处理异常。