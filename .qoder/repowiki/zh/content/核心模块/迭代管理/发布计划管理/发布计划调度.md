# 发布计划调度

<cite>
**本文档引用文件**   
- [plan_ser.py](file://iter_mgt/plan_ser.py)
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py)
</cite>

## 目录
1. [简介](#简介)
2. [发布节点调度器设计](#发布节点调度器设计)
3. [节点依赖关系与拓扑排序](#节点依赖关系与拓扑排序)
4. [复杂调度场景处理](#复杂调度场景处理)
5. [外部系统集成](#外部系统集成)
6. [节点调度操作指南](#节点调度操作指南)
7. [常见问题排查与解决方案](#常见问题排查与解决方案)
8. [结论](#结论)

## 简介
本文档详细阐述了发布计划调度功能的核心机制，重点介绍基于依赖关系的发布节点调度算法。通过分析 `plan_ser.py` 中的发布节点调度器设计和 `iter_mgt_publish_plan_node.py` 中的节点依赖存储结构，全面说明系统如何实现并行、串行及条件发布等复杂调度逻辑。同时涵盖与Jenkins、SaltStack等外部系统的集成方式，提供节点调度操作指南，并针对调度死锁、循环依赖等问题提供可视化排查工具和模拟调度功能。

## 发布节点调度器设计

`plan_ser.py` 文件中实现了发布计划调度的核心逻辑，主要通过 `PlanSerializer` 类完成调度信息的持久化与管理。该调度器负责接收发布计划参数，包括流水线ID、接收人、发布时间、功能描述、SQL内容等，并将其保存至数据库。

调度器通过 `branch_info_save` 方法更新分支信息表中的发布时间、通知内容和调度配置；通过 `sql_content_save` 方法记录SQL执行状态；通过 `receivers_save` 方法维护发布抄送人员列表。这些操作确保了发布计划的完整性和可追溯性。

调度器还提供了多个辅助函数用于查询发布历史和依赖关系。例如 `get_last_finish_info` 用于获取应用最近一次生产环境发布的申请时间，`get_apply_app_list` 用于获取指定迭代已完成发布的应用列表，`get_depend_relation` 用于获取迭代内无需上线的应用依赖关系。

**Section sources**
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L257)

## 节点依赖关系与拓扑排序

发布节点的依赖关系存储在 `iter_mgt_publish_plan_node` 表中，对应模型为 `IterMgtPublishPlanNode`。该模型定义了发布节点的核心属性，包括批次号（batch_no）、模块名（module_name）、迭代ID（iter_id）、阶段（phase）、阶段顺序（phase_order）、阶段并行标识（phase_parallel）、节点名称（node_name）、节点参数（node_param）、调度时间（schedule_time）、执行顺序（order_no）以及节点状态（node_status）等。

节点之间的依赖关系通过 `phase` 和 `order_no` 字段实现有序控制。同一阶段（phase）内的节点可通过 `phase_parallel` 字段标识是否支持并行执行。`next_phase` 和 `next_phase_order` 字段用于定义跨阶段的依赖关系，形成阶段间的有向连接。

虽然当前模型定义中未直接体现显式的依赖字段（如 parent_node），但通过 `phase_order` 和 `order_no` 的组合，系统可构建出节点执行的拓扑序列。调度引擎在执行时会根据这些字段进行排序，确保前置节点完成后再执行后续节点，从而实现基于拓扑排序的调度逻辑。

```mermaid
erDiagram
iter_mgt_publish_plan_node {
string batch_no
string module_name
string iter_id
string phase
int phase_order
string phase_parallel
string next_phase
int next_phase_order
string node_name
json node_param
string schedule_time
int order_no
int next_node_order_no
json query_node_run_result_param
json node_run_result
string node_status
datetime start_time
datetime end_time
string create_user
datetime create_time
string update_user
datetime update_time
string action_type
bigint stamp
}
```

**Diagram sources **
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py#L1-L41)

**Section sources**
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py#L1-L41)

## 复杂调度场景处理

系统通过灵活的阶段（phase）和节点（node）配置支持多种复杂调度场景：

### 并行发布
通过设置 `phase_parallel` 字段为允许并行的值（如 "true" 或 "yes"），同一阶段内的多个节点可并行执行。调度器会同时触发这些节点的任务，提升发布效率。适用于无依赖关系或弱依赖的应用模块批量发布。

### 串行发布
默认情况下，节点按 `phase_order` 和 `order_no` 严格串行执行。前一节点成功完成后，调度器才会启动下一节点。适用于有明确先后顺序的关键服务发布，如数据库变更必须在应用部署前完成。

### 条件发布
虽然当前模型未直接支持条件表达式，但可通过 `node_param` 字段注入条件逻辑参数，并在调度执行时由外部服务解析判断。例如，根据环境变量或前置节点的执行结果（`node_run_result`）决定是否跳过或执行特定节点。

## 外部系统集成

发布调度器通过标准接口与Jenkins、SaltStack等外部系统深度集成，实现自动化发布流程。

### Jenkins 集成
系统通过 `jenkins_mgt` 模块管理Jenkins实例信息，并利用 `get_jenkins_info` 方法获取可用的Jenkins服务器。发布任务被封装为Jenkins Job，通过API触发执行。执行结果通过 `JenkinsResults` 表记录，包含任务状态、开始/结束时间等信息，实现执行过程的闭环追踪。

### SaltStack 集成
系统通过 `task_mgt/http_task.py` 中的 `salt_run` 方法调用SaltStack API执行远程命令。Salt命令、目标节点（minion_id）和操作类型（如 deploy, restart）存储在 `publish_exec_salt_cmd` 表中。调度器在执行节点时，根据 `node_param` 中的配置调用Salt API，实现应用的部署、启停等操作。执行结果记录在 `SaltOperationResults` 表中，便于后续审计和排查。

```mermaid
sequenceDiagram
participant 调度器
participant Jenkins
participant SaltStack
participant 目标节点
调度器->>Jenkins : 触发构建任务
Jenkins-->>调度器 : 返回任务ID
调度器->>调度器 : 等待构建完成
调度器->>SaltStack : 执行部署命令
SaltStack->>目标节点 : 分发并执行脚本
目标节点-->>SaltStack : 返回执行结果
SaltStack-->>调度器 : 返回操作状态
调度器->>调度器 : 更新节点状态
```

**Diagram sources **
- [jenkins_mgt/jenkins_job_mgt.py](file://jenkins_mgt/jenkins_job_mgt.py#L116-L137)
- [task_mgt/http_task.py](file://task_mgt/http_task.py#L404-L431)
- [db/分支2_x/2.26.0/publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L2386-L2390)

## 节点调度操作指南

### 通过API触发节点调度
调用发布计划API，传入 `pipeline_id`、`receivers`、`date`、`time` 等参数，系统将自动生成调度计划并启动执行。可通过 `PlanSerializer` 的 `save` 方法完成整个流程。

### 手动重试失败节点
在调度界面或通过管理API，定位到状态为“失败”的节点，选择“重试”操作。系统会重置该节点的 `node_status` 和时间戳，并重新执行其 `node_param` 定义的任务。

### 跳过特定节点
对于非关键节点或已手动处理的节点，可在调度界面选择“跳过”。系统会将其 `node_status` 更新为“已跳过”，并根据 `next_node_order_no` 直接执行后续节点，确保发布流程继续。

## 常见问题排查与解决方案

### 调度死锁与循环依赖
当节点间存在循环依赖（A依赖B，B又依赖A）时，可能导致调度死锁。系统可通过分析 `iter_mgt_publish_plan_node` 表中的 `phase` 和 `order_no` 关系，构建依赖图谱，使用拓扑排序算法检测环路。

### 依赖关系可视化
开发专用的依赖关系可视化工具，读取 `iter_mgt_publish_plan_node` 数据，生成有向图展示各节点间的执行顺序和依赖关系。帮助运维人员直观理解发布流程，快速定位瓶颈。

### 模拟调度功能
提供“模拟运行”模式，在不实际执行任务的情况下，根据当前节点状态和依赖关系，预测调度流程的执行路径和预计时间。用于发布前的流程验证，降低生产风险。

## 结论
本文档详细解析了发布计划调度功能的设计与实现。基于 `plan_ser.py` 的调度逻辑和 `iter_mgt_publish_plan_node` 的依赖存储，系统实现了灵活可靠的发布节点调度能力。通过阶段化、有序化的节点管理，支持并行、串行和条件发布等多种复杂场景。与Jenkins、SaltStack的集成确保了自动化发布的可行性。提供的操作指南和排查工具进一步增强了系统的可用性和稳定性，为高效、安全的发布管理提供了坚实保障。