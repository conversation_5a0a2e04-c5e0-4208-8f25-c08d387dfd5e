# 发布计划状态管理

<cite>
**本文档中引用的文件**   
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py)
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py)
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py)
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py)
- [express_bugfix_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_bugfix_publish_plan_config.py)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py)
- [normal_bugfix_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_bugfix_publish_plan_config.py)
</cite>

## 目录
1. [引言](#引言)
2. [发布计划状态定义](#发布计划状态定义)
3. [状态流转机制](#状态流转机制)
4. [状态字段设计与事务处理](#状态字段设计与事务处理)
5. [状态变更事件与通知机制](#状态变更事件与通知机制)
6. [API查询与监控方法](#api查询与监控方法)
7. [状态异常排查指南](#状态异常排查指南)
8. [结论](#结论)

## 引言
本文档详细阐述发布计划全生命周期的状态管理机制，重点分析发布计划在不同发布模式下的状态定义、流转规则、数据模型设计及异常处理策略。通过深入解析核心枚举类和数据模型，为开发、运维及测试人员提供全面的状态管理参考。

## 发布计划状态定义

发布计划状态由 `IterMgtPublishPlanEnum` 枚举类定义，该类位于 `iter_mgt/publish_plan/enums/publish_plan_status_enum.py` 文件中，具体状态如下：

- **RUNNING** (`running`): 发布计划正在执行中。
- **SUCCESS** (`success`): 发布计划已成功执行完成。
- **FAIL** (`fail`): 发布计划执行失败。
- **STOP** (`stop`): 发布计划被手动终止。

每个状态包含一个内部标识符（如 `running`）和一个可读的中文描述（如 `正在执行`），便于在用户界面和日志中展示。

**Section sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)

## 状态流转机制

发布计划的状态流转并非简单的线性过程，而是与具体的发布计划类型（`PublishPlanEnum`）及其配置紧密相关。系统支持多种发布模式，每种模式定义了不同的执行阶段（phase）和节点（node），状态的流转依赖于这些阶段的执行结果。

### 核心发布模式与状态流转

1.  **快速发布 (Rapid)**: 适用于紧急发布。其配置 `RapidPublishPlanConfig` 定义了从验证、预发布、处理发布到发布后的一系列阶段。状态从 `INIT`（隐式）开始，当任一阶段启动时变为 `RUNNING`，所有阶段成功完成后变为 `SUCCESS`，任一阶段失败则变为 `FAIL`。

2.  **特快发布 (Express)**: 适用于 `release` 分支的发布。其配置 `ExpressPublishPlanConfig` 在预发布前增加了自动化测试检查环节。状态流转逻辑与快速发布类似，但流程更严格。

3.  **特快修复发布 (Express Bugfix)**: 适用于 `bugfix` 分支的发布。其配置 `ExpressBugfixPublishPlanConfig` 调整了自动化测试的执行时机。状态流转同样遵循“任一失败即失败，全部成功即成功”的原则。

4.  **普通发布 (Normal) 与 普通修复发布 (Normal Bugfix)**: 适用于常规迭代发布，流程更为完整。状态流转机制与上述模式一致。

```mermaid
flowchart TD
A[发布计划创建] --> B{状态: INIT}
B --> C[启动发布流程]
C --> D{状态: RUNNING}
D --> E[执行各阶段任务]
E --> F{所有阶段成功?}
F --> |是| G{状态: SUCCESS}
F --> |否| H{状态: FAIL}
D --> I[用户手动终止]
I --> J{状态: STOP}
```

**Diagram sources **
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py#L1-L23)
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py#L1-L25)
- [express_bugfix_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_bugfix_publish_plan_config.py#L1-L25)

**Section sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)
- [rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py#L1-L23)
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py#L1-L25)

## 状态字段设计与事务处理

发布计划的状态信息在数据库中通过 `iter_mgt_publish_plan` 表进行持久化存储。

### 数据模型设计

`IterMgtPublishPlan` 模型（位于 `iter_mgt/publish_plan/model/iter_mgt_publish_plan.py`）定义了核心字段：
- `id`: 主键。
- `batch_no`, `module_name`, `branch_name`, `iter_id`: 用于唯一标识发布计划的业务键。
- `plan_type`: 记录发布计划的类型（如 `rapid`, `express`）。
- `plan_status`: **核心状态字段**，存储当前状态的字符串值（如 `running`, `success`）。
- `start_time`, `end_time`: 记录发布计划的开始和结束时间。
- `create_user`, `update_user`, `create_time`, `update_time`: 审计信息。

该表的 `unique_together` 约束确保了在特定迭代、模块和批次下的发布计划唯一性。

### 事务处理

状态变更操作通常伴随着其他字段的更新（如 `update_time`, `end_time`），这些操作需要在数据库事务中完成，以保证数据的一致性。虽然模型本身未显式定义 `on_delete` 等行为，但其 `managed = False` 的设置表明该表由外部系统（如Django迁移或直接SQL）管理，状态变更的事务逻辑应在上层服务（如 `publish_ser.py`）中通过显式事务控制来实现。

**Section sources**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L48)

## 状态变更事件与通知机制

当发布计划的状态发生变更时，系统会触发相应的事件并可能发送通知。

### 事件触发

状态变更事件通常由工作流引擎或任务调度器在完成一个阶段或整个发布流程后触发。例如，当 `PROCESS_PUBLISH` 阶段成功执行后，系统会调用服务层方法，将 `plan_status` 更新为 `SUCCESS`，并记录 `end_time`。此更新操作本身即为一个事件。

### 通知机制

虽然核心状态枚举和模型文件未直接包含通知代码，但可以推断出通知机制是通过以下方式实现的：
1.  **服务层集成**: 在 `publish_ser.py` 或类似的业务服务中，当检测到 `plan_status` 从 `RUNNING` 变为 `SUCCESS` 或 `FAIL` 时，会调用消息通知服务（如邮件、企业微信机器人）。
2.  **监听器模式**: 系统可能注册了数据库监听器或模型信号（Django Signals），当 `IterMgtPublishPlan` 对象被保存且 `plan_status` 字段发生变化时，自动触发通知逻辑。

**Section sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L41)
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L48)

## API查询与监控方法

系统提供了API接口来查询发布计划的状态、历史和进度。

### 查询发布计划状态

可以通过 `GET` 请求调用 `publish_plan_view.py` 中的视图，传入 `iter_id`、`module_name` 等参数，查询特定发布计划的当前状态 (`plan_status`) 及其他相关信息。

### 获取状态变更历史

状态变更历史通常不直接存储在 `iter_mgt_publish_plan` 表中。要获取历史，需要查询操作日志表（如 `opt_log_main`）或专门的审计日志，通过 `iter_id` 进行关联，筛选出所有对 `plan_status` 字段的修改记录。

### 监控发布进度

监控发布进度主要依赖于：
1.  **API轮询**: 客户端或监控系统定期调用状态查询API，根据 `plan_status` 和 `start_time` 判断发布是否超时。
2.  **任务队列监控**: 监控底层任务队列（如Celery、Jenkins任务队列）的积压情况，间接反映发布流程的执行效率。
3.  **数据库监控**: 监控 `iter_mgt_publish_plan` 表中 `plan_status = 'RUNNING'` 的记录数量和持续时间。

**Section sources**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L48)
- [publish_plan_view.py](file://iter_mgt/publish_plan/publish_plan_view.py)

## 状态异常排查指南

当发布计划长时间处于 `RUNNING` 状态时，应按以下步骤进行排查。

### 1. 数据库状态一致性检查

首先，确认数据库中的状态是否准确。
- **查询核心表**: 执行 `SELECT * FROM iter_mgt_publish_plan WHERE plan_status = 'RUNNING' AND update_time < NOW() - INTERVAL 1 HOUR;` 查找长时间未更新的记录。
- **检查关联表**: 核对 `iter_mgt_jenkins_publish_pipeline_info` 等关联表的状态，确认是否存在 `lock_status = 'lock'` 但长时间无更新的记录，这可能表示任务卡住。

### 2. 任务队列监控

- **检查队列**: 登录任务队列管理界面（如Celery Flower或Jenkins控制台），查看是否有与该发布计划相关的任务处于 `RUNNING` 或 `QUEUED` 状态。
- **分析任务**: 检查卡住任务的执行日志，定位具体在哪个步骤失败。

### 3. 日志分析方法

- **定位日志**: 根据 `iter_id` 和 `batch_no` 在应用日志中搜索相关记录。
- **关键日志点**:
    - 搜索 `Starting publish plan for iteration_id: [ID]` 确认流程是否启动。
    - 搜索 `Executing phase: [PHASE_NAME]` 确认流程执行到了哪个阶段。
    - 搜索 `Error` 或 `Exception` 关键字，查找具体的错误堆栈。
    - 搜索 `Update publish status to: [STATUS]` 确认状态变更的最终结果。

**Section sources**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L48)
- [publish_ser.py](file://publish/publish_ser.py#L520-L551)

## 结论
发布计划状态管理是发布系统的核心功能。通过 `IterMgtPublishPlanEnum` 枚举定义了清晰的状态，`IterMgtPublishPlan` 模型提供了可靠的数据存储。状态流转由不同发布模式的配置驱动，并通过事务保证数据一致性。在出现 `IN_PROGRESS`（即 `RUNNING`）状态异常时，应结合数据库查询、任务队列监控和日志分析进行综合排查，确保发布流程的稳定与可靠。