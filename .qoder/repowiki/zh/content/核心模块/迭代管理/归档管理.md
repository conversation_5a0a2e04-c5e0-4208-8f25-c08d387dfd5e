# 归档管理

<cite>
**本文档引用的文件**  
- [archive_step_enum.py](file://iter_mgt/archive_step_enum.py)
- [archive_view.py](file://iter_mgt/archive_view.py)
- [zeus_view.py](file://task_mgt/zeus_view.py)
- [roll_back_view.py](file://iter_mgt/roll_back_view.py)
</cite>

## 目录
1. [引言](#引言)
2. [归档流程与状态机](#归档流程与状态机)
3. [事务处理与一致性保障](#事务处理与一致性保障)
4. [同步锁定机制](#同步锁定机制)
5. [API使用示例](#api使用示例)
6. [归档与版本标签、发布记录的关联](#归档与版本标签发布记录的关联)
7. [特殊场景处理策略](#特殊场景处理策略)
8. [常见问题诊断与解决方案](#常见问题诊断与解决方案)
9. [结论](#结论)

## 引言
归档管理是软件生命周期中的关键环节，用于将已完成开发和测试的迭代版本正式下线，释放资源并确保系统稳定性。本系统通过多步骤流程实现归档操作，涵盖配置检查、代码合并、数据更新、流水线删除等多个阶段，并通过状态机进行精确控制。归档过程涉及代码、配置、数据库等多方面的变更，需保证事务一致性与操作原子性。本文档详细阐述归档管理的核心机制，包括归档流程、事务处理、锁定机制、API使用及异常处理策略。

## 归档流程与状态机

归档操作遵循严格的多阶段流程，每个阶段对应特定的任务和状态。`archive_step_enum.py` 文件中定义了归档状态机，使用枚举类 `ArchiveStepEnum` 表示各个归档步骤。

```mermaid
stateDiagram-v2
[*] --> CHECK_CONFIG
CHECK_CONFIG --> ARCHIVE_CONFIG : 宙斯配置检查通过
ARCHIVE_CONFIG --> PRE_ITER_ARCHIVE : 宙斯配置归档完成
PRE_ITER_ARCHIVE --> COMMON_PRE_ITER_ARCHIVE : 迭代预归档完成
COMMON_PRE_ITER_ARCHIVE --> CODE_MERGE : 依赖包预归档完成
CODE_MERGE --> ITER_DATA_UPDATE : 代码合并完成
ITER_DATA_UPDATE --> DELETE_JENKINS_PIPELINE : 迭代数据更新完成
DELETE_JENKINS_PIPELINE --> UPDATE_ARCHIVE_MSG : Jenkins流水线删除完成
UPDATE_ARCHIVE_MSG --> SEND_ARCHIVE_INFO_TO_RMQ : 归档信息更新完成
SEND_ARCHIVE_INFO_TO_RMQ --> ITER_SQL_ARCHIVE : 归档信息发送至RMQ
ITER_SQL_ARCHIVE --> [*] : SQL归档完成，归档结束
COMMON_PRE_ITER_ARCHIVE --> COMMON_CODE_MERGE : 依赖包预归档完成
COMMON_CODE_MERGE --> COMMON_ITER_DATA_UPDATE : 依赖包代码合并完成
COMMON_ITER_DATA_UPDATE --> UPDATE_COMMON_ARCHIVE_MSG : 依赖包数据更新完成
UPDATE_COMMON_ARCHIVE_MSG --> SEND_ARCHIVE_INFO_TO_RMQ : 依赖包归档信息更新完成
```

**状态机来源**
- [archive_step_enum.py](file://iter_mgt/archive_step_enum.py#L1-L17)

**归档阶段说明**

| 阶段编码 | 阶段名称 | 描述 |
| :--- | :--- | :--- |
| `1-1-0` | check_config | 检查宙斯配置是否符合归档要求 |
| `1-1-1` | archive_config | 执行宙斯配置的归档操作 |
| `2-1-0` | pre_iter_archive | 对主迭代进行预归档处理 |
| `2-1-1` | common_pre_iter_archive | 对依赖包迭代进行预归档处理 |
| `2-2-0` | code_merge | 将迭代分支代码合并至目标分支 |
| `2-2-1` | iter_data_update | 更新迭代相关的数据库记录 |
| `2-2-2` | delete_jenkins_pipeline | 删除与该迭代关联的Jenkins流水线 |
| `2-2-3` | update_archive_msg | 更新主迭代的归档状态信息 |
| `2-3-1` | common_code_merge | 合并依赖包的代码 |
| `2-3-2` | common_iter_data_update | 更新依赖包的迭代数据 |
| `2-3-3` | update_common_archive_msg | 更新依赖包的归档状态信息 |
| `2-4-0` | send_archive_info_to_rmq | 将归档完成信息发送到消息队列(RMQ) |
| `2-4-1` | iter_sql_archive | 执行与迭代相关的SQL脚本归档 |

**Section sources**
- [archive_step_enum.py](file://iter_mgt/archive_step_enum.py#L1-L17)

## 事务处理与一致性保障

归档操作的事务一致性由 `archive_view.py` 中的 `ArchiveViewSet` 类保障。该视图通过调用外部服务和Jenkins任务来执行归档，确保每一步操作都按顺序执行。

```mermaid
sequenceDiagram
participant 用户
participant ArchiveViewSet
participant ExternalService
participant Jenkins
participant 数据库
用户->>ArchiveViewSet : 发起归档请求 (POST /archive)
ArchiveViewSet->>ArchiveViewSet : 验证迭代ID存在性
ArchiveViewSet->>ArchiveViewSet : 检查归档任务是否已在运行
ArchiveViewSet->>ExternalService : 调用外部归档服务
ExternalService->>Jenkins : 触发归档Jenkins Job
Jenkins->>Jenkins : 执行归档脚本 (shell命令)
loop 每个归档步骤
Jenkins->>数据库 : 执行数据库变更
Jenkins->>代码仓库 : 执行代码合并
Jenkins->>配置中心 : 更新配置状态
end
Jenkins-->>ExternalService : 返回执行结果
ExternalService-->>ArchiveViewSet : 返回服务调用结果
ArchiveViewSet-->>用户 : 返回归档触发结果
```

**Diagram sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L45-L100)

归档流程的关键在于其原子性和顺序性。系统通过以下方式保障一致性：
1.  **前置检查**：在创建归档任务前，会检查迭代是否存在以及是否有其他归档任务正在运行，防止并发冲突。
2.  **外部服务调用**：归档的核心逻辑被封装在外部服务中，由 `ExternalService` 统一调用，保证了逻辑的集中和可控。
3.  **日志追踪**：归档操作的日志被记录在服务器特定目录下，可通过API查询日志内容，便于跟踪归档进度和诊断问题。

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L45-L100)

## 同步锁定机制

在归档过程中，为防止代码、配置和数据库的并发修改导致数据不一致，系统采用了同步锁定机制。该机制主要体现在对关键资源的独占访问上。

1.  **Jenkins任务锁定**：当一个归档任务被触发后，系统会检查是否有相同命令的进程正在运行 (`is_apply_running` 方法)。如果有，则拒绝新的归档请求，确保同一迭代的归档操作是串行的。
2.  **数据库记录状态**：在 `DumpRestoreAction` 类中，当执行 `dump` 还原操作时，会先在 `DbMgtDumpRestore` 表中插入或更新一条记录，并将其 `lock_status` 设置为 `LockStatus.lock`。这相当于对目标数据库和环境进行了加锁，防止其他操作同时进行还原。
3.  **配置中心同步**：归档配置的变更通过调用 `zeus_view.py` 中的接口完成。该接口在执行时会对配置进行更新，其内部实现可能包含分布式锁或乐观锁机制，以保证配置变更的原子性。

```mermaid
flowchart TD
A[用户发起归档] --> B{检查归档任务是否运行}
B --> |是| C[返回失败: 归档正在执行]
B --> |否| D[调用外部归档服务]
D --> E[Jenkins执行归档脚本]
E --> F[更新数据库状态]
F --> G[合并代码]
G --> H[删除Jenkins流水线]
H --> I[发送归档消息]
I --> J[归档完成]
```

**Diagram sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L20-L44)
- [task_mgt/zeus_view.py](file://task_mgt/zeus_view.py#L84-L107)

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L20-L44)
- [task_mgt/zeus_view.py](file://task_mgt/zeus_view.py#L84-L107)

## API使用示例

### 触发归档
通过 `POST` 请求调用 `/archive/` 接口来触发归档。

**请求示例**
```http
POST /archive/ HTTP/1.1
Content-Type: application/json

{
  "pipeline_id": "ITER_2024_001"
}
```

**响应示例 (成功)**
```json
{
  "code": 0,
  "msg": "正在执行归档..",
  "data": null
}
```

**响应示例 (失败 - 任务已在运行)**
```json
{
  "code": 1,
  "msg": "归档正在执行，请稍后再试。",
  "data": null
}
```

### 查询归档进度
通过 `GET` 请求调用 `/archive/` 接口来查询归档日志。

**请求示例**
```http
GET /archive/?pipeline_id=ITER_2024_001 HTTP/1.1
```

**响应示例**
```json
{
  "code": 0,
  "msg": "",
  "data": [
    "2024-05-20 10:00:01 INFO: 开始执行归档...",
    "2024-05-20 10:00:05 INFO: 正在检查宙斯配置...",
    "2024-05-20 10:00:10 INFO: 宙斯配置检查通过",
    "...",
    "2024-05-20 10:05:00 INFO: 调用结束"
  ]
}
```

### 处理归档失败
当归档失败时，系统会记录详细的错误日志。管理员应：
1.  使用 `GET /archive/` 接口查询最新的日志，定位失败原因。
2.  根据日志信息，手动修复问题（如修复数据库连接、修正代码冲突等）。
3.  在问题解决后，可重新发起归档请求。

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L45-L100)

## 归档与版本标签、发布记录的关联

归档操作与版本管理和发布流程紧密关联：
1.  **版本标签**：归档的迭代通常对应一个或多个版本标签（如 `v1.2.0`）。归档过程会确保该版本的代码、配置和数据库脚本都已正确合并和记录，形成一个不可变的、可追溯的版本快照。
2.  **发布记录**：归档是发布流程的最终环节。当一个迭代成功归档后，其发布记录的状态会被更新为“已归档”或“已完成”。这标志着该版本的生命周期正式结束，为后续的审计和回溯提供了依据。

## 特殊场景处理策略

### 归档回滚
系统提供了回滚功能，用于撤销错误的归档操作。`roll_back_view.py` 中的 `RollbackViewSet` 类负责处理回滚请求。回滚过程同样使用数据库事务 (`transaction.atomic()`) 来保证操作的原子性，确保所有相关状态（如应用状态、回滚信息表）能被一致地恢复。

```mermaid
sequenceDiagram
participant 用户
participant RollbackViewSet
participant 数据库
用户->>RollbackViewSet : 发起回滚请求
activate RollbackViewSet
RollbackViewSet->>数据库 : 开启事务
RollbackViewSet->>数据库 : 查询并锁定回滚中的记录
RollbackViewSet->>数据库 : 批量更新应用状态
RollbackViewSet->>数据库 : 更新回滚信息表状态
alt 更新成功
RollbackViewSet->>数据库 : 提交事务
RollbackViewSet-->>用户 : 返回成功
else 更新失败
RollbackViewSet->>数据库 : 回滚事务
RollbackViewSet-->>用户 : 返回失败
end
deactivate RollbackViewSet
```

**Diagram sources**
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L73-L104)

### 部分归档
当前系统设计倾向于全量归档。对于需要部分归档的场景，建议通过以下方式处理：
1.  **拆分迭代**：将大迭代拆分为多个小迭代，分别进行归档。
2.  **手动干预**：对于特殊情况，可通过数据库脚本或直接调用底层API进行部分操作，但这需要谨慎操作并做好记录。

**Section sources**
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L73-L104)

## 常见问题诊断与解决方案

### 归档阻塞
**现象**：归档请求返回“归档正在执行，请稍后再试”。
**原因**：可能有前一个归档任务卡住或未正常退出。
**解决方案**：
1.  检查服务器上是否有残留的归档进程 (`pgrep -f 'archive_apply'`)。
2.  如果有，手动终止该进程。
3.  清理可能残留的锁状态（如果存在）。

### 数据不一致
**现象**：归档后，代码、配置或数据库状态与预期不符。
**原因**：归档脚本执行到一半失败，导致部分变更已提交。
**解决方案**：
1.  立即停止任何新的归档或发布操作。
2.  查看归档日志，确定失败点。
3.  执行回滚操作，将系统恢复到归档前的状态。
4.  修复导致失败的根本原因后，重新执行归档。

## 结论
本文档详细介绍了系统的归档管理功能，涵盖了从状态机定义、事务处理、锁定机制到API使用和异常处理的各个方面。归档流程设计严谨，通过多阶段状态机和外部服务调用保证了操作的有序性和一致性。同步锁定机制有效防止了并发冲突。对于归档失败等异常情况，提供了回滚等补救措施。理解和掌握这些机制对于保障系统稳定和数据安全至关重要。