# 迭代管理

<cite>
**本文档引用文件**  
- [models.py](file://iter_mgt/models.py)
- [archive_view.py](file://iter_mgt/archive_view.py)
- [plan_ser.py](file://iter_mgt/plan_ser.py)
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py)
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py)
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概览](#架构概览)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考量](#性能考量)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档详细阐述了迭代管理模块的设计与实现，重点涵盖分支创建、迭代流程控制、归档管理及发布计划功能。文档深入解析了`Iteration`、`PublishPlan`等核心模型的状态机机制，描述了从需求提出到上线发布的完整生命周期。同时，分析了归档操作的事务处理逻辑与发布节点的依赖调度算法，并提供了API使用示例、特殊流程支持机制以及常见问题的解决方案。

## 项目结构
迭代管理模块位于`iter_mgt`目录下，主要包含以下子模块：
- `publish_plan`：负责发布计划的配置、节点定义与执行流程
- `models.py`：定义核心数据模型，如迭代、分支、发布计划等
- `archive_view.py`：处理归档相关API请求与事务逻辑
- `plan_ser.py`：提供发布计划相关的服务逻辑与数据库查询
- `apply_view.py`、`plan_view.py`：处理迭代申请与发布计划视图逻辑

该模块与其他系统（如Jenkins、CMDB、测试环境管理）通过API和服务调用进行集成，形成完整的CI/CD闭环。

```mermaid
graph TB
subgraph "迭代管理模块"
A[iter_mgt]
A --> B[publish_plan]
A --> C[models.py]
A --> D[archive_view.py]
A --> E[plan_ser.py]
A --> F[apply_view.py]
A --> G[plan_view.py]
end
B --> H[Jenkins]
C --> I[数据库]
D --> J[外部服务]
E --> K[数据库查询]
```

**图示来源**  
- [models.py](file://iter_mgt/models.py#L1-L423)
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

**本节来源**  
- [iter_mgt](file://iter_mgt)

## 核心组件
迭代管理模块的核心组件包括`Iteration`（迭代）、`PublishPlan`（发布计划）、`Archive`（归档）三大功能模块。其中，`Iteration`模型定义了迭代的基本属性与状态流转；`PublishPlan`通过状态机实现复杂的发布流程控制；`Archive`模块则负责归档操作的事务性与一致性保障。

**本节来源**  
- [models.py](file://iter_mgt/models.py#L1-L423)
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

## 架构概览
系统采用分层架构设计，自上而下分为：
- **API层**：提供RESTful接口，处理HTTP请求
- **服务层**：封装业务逻辑，协调数据访问与外部调用
- **数据层**：持久化存储，通过Django ORM操作数据库
- **外部集成层**：与Jenkins、CMDB、测试平台等系统交互

```mermaid
graph TD
A[客户端] --> B[API层]
B --> C[服务层]
C --> D[数据层]
C --> E[外部集成层]
E --> F[Jenkins]
E --> G[CMDB]
E --> H[测试平台]
```

**图示来源**  
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)
- [models.py](file://iter_mgt/models.py#L1-L423)

## 详细组件分析

### 迭代与发布计划模型设计
`Iteration`和`PublishPlan`是系统的核心数据模型，分别对应`Branches`和`IterMgtPublishPlan`类。

#### 模型关系图
```mermaid
classDiagram
class IterMgtPublishPlan {
+str batch_no
+str module_name
+str branch_name
+str iter_id
+str plan_type
+str plan_status
+datetime start_time
+datetime end_time
+str create_user
+datetime create_time
}
class IterMgtPublishPlanNode {
+str batch_no
+str module_name
+str iter_id
+str phase
+int phase_order
+str phase_parallel
+str next_phase
+int next_phase_order
+str node_name
+JSON node_param
+str schedule_time
+int order_no
+int next_node_order_no
+JSON node_run_result
+str node_status
+datetime start_time
+datetime end_time
}
class Branches {
+str pipeline_id
+str br_name
+str project_group
+str br_style
+str br_start_date
+str br_end_date
+str duedate
+str br_status
+int is_new
+str description
+str releaseNotic
+str schedule
+str file_ccms_config
+str tapd_id
+str archive_msg
+datetime create_date
+datetime test_end_date
}
IterMgtPublishPlan --> IterMgtPublishPlanNode : "包含多个"
Branches --> IterMgtPublishPlan : "关联发布计划"
```

**图示来源**  
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L49)
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py#L1-L42)
- [models.py](file://iter_mgt/models.py#L1-L423)

#### 状态机实现
发布计划通过`PublishPlanConfig`类实现状态机控制，支持多种发布模式（如常规发布、紧急修复）。`NormalPublishPlanConfig`定义了标准发布流程的阶段配置，包括预发布、发布中、发布后三个阶段，每个阶段可并行或串行执行多个节点。

```mermaid
stateDiagram-v2
[*] --> 待发布
待发布 --> 预发布 : 触发发布
预发布 --> 发布中 : 前置检查通过
发布中 --> 发布后 : 发布成功
发布后 --> 已完成 : 后置任务完成
发布中 --> 发布失败 : 发布异常
发布失败 --> 待发布 : 人工干预后重试
```

**图示来源**  
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L1-L34)
- [publish_plan_config.py](file://iter_mgt/publish_plan/config/publish_plan_config.py#L1-L32)

### 归档操作事务处理机制
`archive_view.py`中的`ArchiveViewSet`负责归档操作的事务处理。系统通过SSH调用远程脚本执行归档，并利用文件锁和进程检查防止并发操作。归档日志记录在`iter_mgt_archive_log`表中，确保操作可追溯。

#### 归档流程序列图
```mermaid
sequenceDiagram
participant 用户
participant ArchiveApi
participant ExternalService
participant Jenkins
用户->>ArchiveApi : PUT /archive
ArchiveApi->>ArchiveApi : 验证权限与参数
ArchiveApi->>ExternalService : 调用归档服务
ExternalService->>Jenkins : 触发归档Job
Jenkins-->>ExternalService : 返回执行ID
ExternalService-->>ArchiveApi : 返回成功
ArchiveApi-->>用户 : 返回SID
```

**图示来源**  
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)

### 发布节点依赖调度算法
`plan_ser.py`中的`get_depend_relation`函数实现了发布节点的依赖分析算法。该算法通过SQL查询检测当前迭代中是否存在非上线组件（即`need_online=0`），若存在则将其加入依赖列表，确保发布顺序正确。

#### 依赖分析流程图
```mermaid
flowchart TD
Start([开始]) --> Query["查询迭代包含的应用"]
Query --> Check["检查应用是否为上线组件"]
Check --> |是| AddToDepend["加入依赖列表"]
Check --> |否| NextApp["下一个应用"]
AddToDepend --> NextApp
NextApp --> EndLoop{"所有应用处理完毕?"}
EndLoop --> |否| Query
EndLoop --> |是| Return["返回依赖列表"]
Return --> End([结束])
```

**图示来源**  
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

## 依赖分析
迭代管理模块依赖以下外部系统：
- **Jenkins**：执行构建、归档、发布等自动化任务
- **CMDB**：获取应用部署信息与环境配置
- **测试平台**：集成自动化测试结果
- **数据库管理模块**：处理SQL变更与数据初始化

```mermaid
graph LR
A[迭代管理] --> B[Jenkins]
A --> C[CMDB]
A --> D[测试平台]
A --> E[数据库管理]
```

**图示来源**  
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

**本节来源**  
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

## 性能考量
- **数据库查询优化**：使用原生SQL替代ORM以提高复杂查询性能
- **并发控制**：通过`is_apply_running`方法防止同一迭代的并发归档
- **异步处理**：关键操作（如归档、发布）采用异步执行，避免阻塞API响应
- **日志分级**：仅在关键路径记录详细日志，减少I/O开销

## 故障排除指南
### 常见问题及解决方案
| 问题现象 | 可能原因 | 解决方案 |
|--------|--------|--------|
| 归档操作无法启动 | 存在并发归档任务 | 检查`pgrep -f`确认是否有运行中的归档进程 |
| 发布计划节点未执行 | 依赖检查失败 | 检查`get_depend_relation`返回的依赖列表 |
| Jenkins任务触发失败 | 权限或网络问题 | 验证Jenkins服务状态与API密钥有效性 |
| 数据库还原冲突 | 多个任务同时操作同一库 | 检查`DbMgtDumpRestore`表的锁状态 |

### 最佳实践
1. **避免手动干预**：尽量通过API操作，减少直接数据库修改
2. **定期清理日志**：归档日志文件可能占用大量磁盘空间
3. **发布前验证依赖**：使用`GetDependRelation`接口提前检查依赖关系
4. **监控关键指标**：关注归档耗时、发布成功率等核心指标

**本节来源**  
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

## 结论
迭代管理模块通过清晰的模型设计与状态机控制，实现了从需求到上线的完整生命周期管理。其核心优势在于：
- **可扩展的发布流程**：通过配置化支持多种发布模式
- **强事务保证**：归档操作具备完善的错误处理与回滚机制
- **智能依赖分析**：自动识别组件依赖关系，确保发布顺序正确
- **良好的可维护性**：分层架构与清晰的接口定义便于后续扩展

未来可进一步优化的方向包括引入发布审批流、增强灰度发布能力、集成更丰富的质量门禁等。