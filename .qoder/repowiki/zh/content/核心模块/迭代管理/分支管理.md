# 分支管理

<cite>
**本文档引用的文件**   
- [apply_view.py](file://iter_mgt/apply_view.py)
- [repo_diff_view.py](file://iter_mgt/repo_diff_view.py)
- [models.py](file://iter_mgt/models.py)
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py)
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py)
</cite>

## 目录
1. [引言](#引言)
2. [分支创建机制](#分支创建机制)
3. [分支同步与差异分析](#分支同步与差异分析)
4. [Iteration模型与分支生命周期](#iteration模型与分支生命周期)
5. [分支申请审批流程](#分支申请审批流程)
6. [代码差异比对算法](#代码差异比对算法)
7. [API创建分支使用示例](#api创建分支使用示例)
8. [分支与迭代计划、代码审查的关联](#分支与迭代计划代码审查的关联)
9. [分支保护与最佳实践](#分支保护与最佳实践)
10. [常见问题解决方案](#常见问题解决方案)

## 引言
本文档详细阐述了分支管理功能的核心内容，重点介绍分支创建、同步和差异分析机制。文档深入解析了Iteration模型中的分支命名规范、生命周期状态机及与代码仓库的集成方式。通过分析`apply_view.py`中的分支申请审批流程和`repo_diff_view.py`中的代码差异比对算法，提供了通过API创建各类分支的使用示例。同时，文档说明了分支与迭代计划、代码审查流程的关联关系，并提供了分支保护策略、自动同步机制和冲突解决的最佳实践，针对分支命名混乱、分支滞留等常见问题提供了解决方案。

## 分支创建机制

分支创建是迭代开发流程的起点，系统通过`IterApplyApi`类实现分支的申请与创建。当用户发起分支创建请求时，系统首先验证请求参数的合法性，包括分支名称、GitLab组名、分支类型、截止日期、仓库列表和描述等信息。系统根据GitLab组名和分支名称生成唯一的迭代ID（pipeline_id），格式为`{gitlab_group}_{branch_name}`。

在创建分支前，系统会进行严格的包类型校验，确保同一迭代内的应用包类型一致。系统定义了三类包类型：服务端应用（jar, tar, war）、Python应用（py）和移动端应用（remote, static, dist等）。通过`get_package_type_by_pipeline_id`函数获取迭代内所有应用的包类型，并与请求中的应用列表进行比对，确保类型兼容。

此外，系统还实现了分支数量限制机制，防止过度创建分支。通过`__check_iter_pipeline_total`方法，系统检查当前GitLab组下处于“open”状态的新建分支（is_new=1）数量是否超过平台限制。该限制可通过`ITERATION_LIMIT`配置项进行全局设置，也可通过`IterWhitelistGroup`表为特定组设置独立的限制值。当分支类型为“release”且数量达到上限时，系统将拒绝新的分支创建请求。

**Section sources**
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L140-L250)
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L300-L350)

## 分支同步与差异分析

分支同步与差异分析是确保代码质量和发布安全的核心环节。系统通过`RepoDiffViewSet`和`RepoDiffApi`两个视图集提供差异分析功能。`RepoDiffViewSet`负责分析单个应用在特定迭代分支与生产环境之间的代码差异，而`RepoDiffApi`则支持跨多个环境套（suite）的批量差异分析。

差异分析的核心逻辑是通过SSH连接到代码服务器，执行`git diff --stat`命令来获取两个版本之间的变更统计。系统首先通过`app_artifact_version`方法获取迭代分支中应用的制品版本（git_repo_version），再通过`app_online_artifact_version`方法查询该应用在生产环境（env="prod"）最后一次成功发布的制品版本。如果两个版本不一致，系统将构造`git diff`命令，计算从生产版本到分支版本的变更内容。

`RepoDiffApi`的分析范围更广，它会遍历与指定迭代相关的所有环境套（suite_ids），通过`get_app_suite_node`方法获取每个环境套中该应用部署的所有节点及其当前运行的制品版本。然后，系统会为每个节点执行差异分析，生成包含应用名、节点IP、环境套代码和差异内容的详细报告列表。这种机制使得开发和运维团队能够全面了解代码变更在不同环境中的分布情况。

```mermaid
flowchart TD
Start([开始差异分析]) --> ValidateInput["验证请求参数"]
ValidateInput --> InputValid{"参数有效?"}
InputValid --> |否| ReturnError["返回参数错误"]
InputValid --> |是| GetBranchVersion["获取分支制品版本"]
GetBranchVersion --> GetOnlineVersion["获取生产环境制品版本"]
GetOnlineVersion --> VersionsExist{"版本存在?"}
VersionsExist --> |否| ReturnError
VersionsExist --> |是| CompareVersions{"版本相同?"}
CompareVersions --> |是| ReturnEmpty["返回空差异"]
CompareVersions --> |否| ExecuteGitDiff["执行 git diff 命令"]
ExecuteGitDiff --> DiffSuccess{"执行成功?"}
DiffSuccess --> |否| ReturnError
DiffSuccess --> |是| FormatOutput["格式化输出结果"]
FormatOutput --> ReturnResult["返回差异内容"]
ReturnEmpty --> End([结束])
ReturnResult --> End
```

**Diagram sources **
- [repo_diff_view.py](file://iter_mgt/repo_diff_view.py#L20-L150)
- [models.py](file://iter_mgt/models.py#L100-L150)

**Section sources**
- [repo_diff_view.py](file://iter_mgt/repo_diff_view.py#L20-L150)
- [models.py](file://iter_mgt/models.py#L100-L150)

## Iteration模型与分支生命周期

系统中的分支管理围绕`Iteration`模型构建，该模型由`Branches`和`BranchIncludeSys`两个核心数据模型组成。`Branches`模型代表一个迭代分支，其核心字段包括：
- `pipeline_id`: 迭代的唯一标识符，由`{gitlab_group}_{branch_name}`构成。
- `br_name`: 分支名称。
- `project_group`: GitLab项目组名。
- `br_style`: 分支类型，如`release`、`bugfix`、`feature`。
- `br_start_date` 和 `br_end_date`: 分支的创建和结束时间。
- `br_status`: 分支状态，`open`表示进行中，`close`表示已关闭。
- `is_new`: 标记是否为新建分支，用于数量限制统计。

`BranchIncludeSys`模型则表示一个迭代分支所包含的应用，它建立了迭代与应用之间的多对多关系。其核心字段有：
- `pipeline_id`: 关联的迭代ID。
- `appName`: 应用名称。
- `sys_status`: 应用在该迭代中的状态，如“测试中”、“已推送制品库”、“已上线”、“已归档”等。
- `git_repo_version`: 该应用在该迭代分支下的制品版本。
- `proposer`: 该应用的申请人。

分支的生命周期由`sys_status`状态机驱动。一个应用在迭代中的典型生命周期为：`测试中` -> `已推送制品库` -> `计划上线` -> `已上线` -> `已归档`。当迭代结束时，所有关联的应用状态将被更新为“已归档”，标志着该迭代的终结。系统通过`PublishApplicationConfirmation`模型记录分支的发布确认状态，确保发布流程的可追溯性。

```mermaid
stateDiagram-v2
[*] --> open
open --> close : 归档
close --> open : 重新打开(罕见)
state "open" {
[*] --> 测试中
测试中 --> 已推送制品库 : 推送成功
已推送制品库 --> 发布私服 : 发布成功
发布私服 --> 计划上线 : 计划发布
计划上线 --> 仿真通过 : 仿真成功
仿真通过 --> 已上线 : 发布成功
已上线 --> 已归档 : 迭代结束
测试中 --> 测试中-回滚中 : 回滚
已归档 --> 已归档-回滚中 : 回滚
}
state "close" {
[*] --> 已归档
已归档 --> open : 重新激活
}
```

**Diagram sources **
- [models.py](file://iter_mgt/models.py#L50-L200)

**Section sources**
- [models.py](file://iter_mgt/models.py#L50-L200)

## 分支申请审批流程

分支申请的审批流程主要由`ApplyViewSet`和`ApplyNoticeViewSet`两个视图集实现。`ApplyViewSet`负责处理分支的发布申请，而`ApplyNoticeViewSet`则负责发送和处理发布确认邮件。

当用户发起发布申请时，`ApplyViewSet.create`方法被调用。该方法首先调用`get_apply_iteration_info`获取迭代的详细信息，包括分支名、应用列表、SQL变更内容、注意事项、抄送人等。然后，系统构造一个外部服务调用参数`params`，其中包含迭代ID、环境、申请人、SQL地址等关键信息。最后，通过`ExternalService`调用名为`publish_apply`的业务服务，启动发布流程。

真正的审批环节由`ApplyNoticeViewSet`处理。当需要二次确认时，`create`方法被调用。系统首先生成一个唯一的MD5标识符（`md5_str`），并将其与迭代ID、创建时间、接收者等信息一起存入`PublishApplicationConfirmation`数据库表。随后，系统向所有接收者发送包含确认链接的邮件。确认链接的URL中包含了MD5标识符，用于唯一标识此次确认请求。

当用户点击邮件中的确认链接时，前端会调用`put`方法。系统根据传入的`md5_str`查找对应的确认记录，验证确认人是否在接收者列表中，并检查该迭代是否已被确认。如果所有检查通过，系统将更新`PublishApplicationConfirmation`记录的状态为“success”，并记录确认人和确认时间，从而完成整个审批流程。

```mermaid
sequenceDiagram
participant User as "用户"
participant ApplyViewSet as "ApplyViewSet"
participant ExternalService as "外部服务"
participant ApplyNoticeViewSet as "ApplyNoticeViewSet"
participant DB as "数据库"
participant Email as "邮件系统"
User->>ApplyViewSet : POST /apply
ApplyViewSet->>ApplyViewSet : get_apply_iteration_info()
ApplyViewSet->>ExternalService : call_service(publish_apply)
ExternalService-->>ApplyViewSet : 返回执行结果
ApplyViewSet-->>User : 返回申请结果
User->>ApplyNoticeViewSet : POST /send_confirm_email
ApplyNoticeViewSet->>DB : 生成MD5并创建记录
DB-->>ApplyNoticeViewSet : 返回MD5
ApplyNoticeViewSet->>Email : 发送确认邮件
Email-->>User : 收到邮件
User->>ApplyNoticeViewSet : 点击确认链接
ApplyNoticeViewSet->>DB : 查询MD5记录
DB-->>ApplyNoticeViewSet : 返回记录
ApplyNoticeViewSet->>ApplyNoticeViewSet : 验证权限和状态
ApplyNoticeViewSet->>DB : 更新状态为"success"
DB-->>ApplyNoticeViewSet : 更新成功
ApplyNoticeViewSet-->>User : 返回确认成功
```

**Diagram sources **
- [apply_view.py](file://iter_mgt/apply_view.py#L200-L500)
- [models.py](file://iter_mgt/models.py#L250-L300)

**Section sources**
- [apply_view.py](file://iter_mgt/apply_view.py#L200-L500)
- [models.py](file://iter_mgt/models.py#L250-L300)

## 代码差异比对算法

代码差异比对算法的核心实现在`repo_diff_view.py`文件中，主要由`RepoDiffViewSet`和`RepoDiffApi`两个类提供服务。算法的逻辑流程如下：

1.  **参数验证**: 首先，`list`方法使用`RepoDiffSerializer`对请求参数（`pipeline_id`和`app_name`）进行验证。
2.  **存在性检查**: 调用`is_exist`方法，通过查询`Branches`模型确认指定的迭代ID是否存在且状态为`open`。
3.  **获取版本信息**: 
    -   调用`app_artifact_version`方法，从`BranchIncludeSys`模型中查询该应用在指定迭代分支下的制品版本（`git_repo_version`）。
    -   调用`app_online_artifact_version`方法，从`PublishOrder`模型中查询该应用在生产环境（`env="prod"`）最后一次成功发布（`status="已完成"`）的制品版本。
4.  **获取仓库路径**: 从`Artifactinfo`模型中获取该应用的Git仓库路径（`gitRepo`）。
5.  **执行差异计算**: 
    -   如果两个版本相同，则返回空的差异信息。
    -   如果两个版本不同且均存在，系统通过`SSHConnectionManager`建立到代码服务器的SSH连接。
    -   在服务器上执行`cd /data/git-code/{repo_path} && git diff --stat {online_version} {repo_version}`命令，计算从生产版本到分支版本的变更统计。
    -   将命令的输出（`out`）中的换行符替换为`<br>`，以便在前端HTML中正确显示。
6.  **返回结果**: 将差异内容封装在API响应中返回给前端。

`RepoDiffApi`的算法更为复杂，它支持跨环境套的分析。它首先通过`get_current_repo_version`获取迭代在特定环境套中的当前制品版本，然后通过`get_app_suite_node`获取该应用在该环境套中所有节点的IP和当前运行的版本。最后，它为每个节点调用`get_repo_diff_content`方法，通过`ExternalService`在远程服务器上执行`git diff`命令，收集所有节点的差异报告。

**Section sources**
- [repo_diff_view.py](file://iter_mgt/repo_diff_view.py#L20-L150)

## API创建分支使用示例

系统提供了RESTful API来创建不同类型的分支。以下是通过API创建特性分支、修复分支和发布分支的使用示例。

### 创建特性分支 (Feature Branch)
```bash
curl -X POST "http://your-api-host/iter_mgt/iter_apply/" \
  -H "Content-Type: application/json" \
  -d '{
    "branch_name": "feature_user_login_optimization",
    "gitlab_group": "backend",
    "branch_type": "feature",
    "deadline": "2023-12-31T23:59:59Z",
    "repos_str": [
      {"module_name": "user-service"},
      {"module_name": "auth-service"}
    ],
    "desc": "优化用户登录流程，提升用户体验",
    "tapd_id": "TAPD-12345"
  }'
```
此请求将在`backend`组下创建一个名为`feature_user_login_optimization`的特性分支，包含`user-service`和`auth-service`两个应用。

### 创建修复分支 (Bugfix Branch)
```bash
curl -X POST "http://your-api-host/iter_mgt/iter_apply/" \
  -H "Content-Type: application/json" \
  -d '{
    "branch_name": "bugfix_payment_timeout",
    "gitlab_group": "payment",
    "branch_type": "bugfix",
    "deadline": "2023-11-20T18:00:00Z",
    "repos_str": [
      {"module_name": "payment-gateway"}
    ],
    "desc": "修复支付超时导致的订单状态不一致问题",
    "tapd_id": "TAPD-67890"
  }'
```
此请求将在`payment`组下创建一个名为`bugfix_payment_timeout`的修复分支，用于紧急修复支付网关的超时问题。

### 创建发布分支 (Release Branch)
```bash
curl -X POST "http://your-api-host/iter_mgt/iter_apply/" \
  -H "Content-Type: application/json" \
  -d '{
    "branch_name": "release_2023Q4",
    "gitlab_group": "frontend",
    "branch_type": "release",
    "deadline": "2023-12-15T23:59:59Z",
    "repos_str": [
      {"module_name": "web-app"},
      {"module_name": "mobile-app"}
    ],
    "desc": "2023年第四季度产品发布",
    "tapd_id": "TAPD-54321"
  }'
```
此请求将在`frontend`组下创建一个名为`release_2023Q4`的发布分支，用于准备季度发布。请注意，创建发布分支受数量限制保护。

**Section sources**
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L140-L250)

## 分支与迭代计划、代码审查的关联

分支管理与迭代计划和代码审查流程紧密集成，形成了一个完整的开发工作流。

### 与迭代计划的关联
每一个分支（`Branches`）都直接对应一个迭代计划。`pipeline_id`既是分支的标识，也是迭代的标识。迭代计划中的关键信息，如预期上线时间（`duedate`）、功能描述（`description`）、调度信息（`schedule`）和注意事项（`releaseNotic`），都直接存储在`Branches`模型中。`BranchIncludeSys`模型则将具体的开发任务（应用）与迭代计划关联起来，明确了该迭代需要修改哪些应用。通过`iter_mgt_ser.py`中的`get_iter_info`等服务函数，系统可以查询用户有权限访问的所有进行中的迭代计划，并展示其包含的应用列表。

### 与代码审查的关联
系统通过`PublishApplicationConfirmation`模型实现了发布前的代码审查（或发布确认）环节。当一个迭代分支准备发布到生产环境时，必须经过审批流程。`ApplyNoticeViewSet`会向指定的接收者（通常是团队负责人或架构师）发送包含确认链接的邮件。只有当接收者点击链接并完成确认后，`PublishApplicationConfirmation`记录的状态才会被更新为“success”，发布流程才能继续。这确保了关键的发布操作必须经过人工审查和授权，是代码审查流程在发布阶段的延伸。此外，`BranchIncludeSys`模型中的`simulate_identifier`字段记录了仿真验证人，也是代码审查流程的一部分。

**Section sources**
- [models.py](file://iter_mgt/models.py#L50-L300)
- [apply_view.py](file://iter_mgt/apply_view.py#L400-L500)

## 分支保护与最佳实践

为了维护代码库的健康和发布流程的稳定，系统实施了多项分支保护策略和最佳实践。

### 分支保护策略
1.  **数量限制**: 如前所述，系统通过`IterWhitelistGroup`和`GuardSwitchInfo`模型实现了分支数量限制。这可以有效防止因过度创建分支而导致的管理混乱。
2.  **状态机控制**: `BranchIncludeSys`模型中的`sys_status`是一个严格的状态机。应用的状态只能按照预定义的路径进行转换（如从“测试中”到“已上线”），防止了状态的随意变更。
3.  **权限控制**: 分支的创建、修改和删除操作都与用户权限挂钩。只有拥有相应GitLab组权限的用户才能创建或修改该组下的分支。发布确认邮件的接收者和确认者也受到权限校验。
4.  **发布确认**: 所有生产环境的发布都必须经过`ApplyNoticeViewSet`的二次确认流程，确保了发布操作的可追溯性和安全性。

### 自动同步机制
系统通过`ExternalService`机制实现了与外部系统的自动同步。例如，当创建新分支时，`IterApplyApi`会调用脚本服务（`ScriptCaller`），该服务可以触发与GitLab、Jenkins、Zeus等系统的同步操作，确保分支在所有相关平台中被正确创建和配置。

### 冲突解决最佳实践
虽然系统本身不直接解决代码合并冲突，但它提供了最佳实践指导：
-   **小步提交**: 鼓励开发人员频繁提交小的、功能完整的变更，减少大型合并时的冲突概率。
-   **及时同步**: 要求开发人员定期从主干（或开发分支）同步最新代码到自己的特性分支，尽早发现并解决潜在冲突。
-   **清晰的沟通**: 利用TAPD ID（`tapd_id`）将分支与具体的任务或缺陷关联起来，便于团队成员了解变更的上下文。

**Section sources**
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L200-L250)
- [apply_view.py](file://iter_mgt/apply_view.py#L400-L500)
- [models.py](file://iter_mgt/models.py#L50-L300)

## 常见问题解决方案

### 分支命名混乱
**问题**: 分支名称随意，缺乏规范，难以理解其用途。
**解决方案**: 
1.  **强制命名规范**: 在`IterApplySerializer`中，对`branch_name`字段进行校验，要求其必须符合预定义的模式，如`{type}_{description}`（`feature_login`, `bugfix_timeout`）。
2.  **自动化检查**: 在分支创建脚本中加入命名检查逻辑，对不符合规范的名称进行拦截或自动修正。
3.  **文档化规范**: 在团队内部文档中明确分支命名规则，并进行培训。

### 分支滞留
**问题**: 已完成或废弃的分支未被及时关闭和归档，导致分支列表臃肿。
**解决方案**:
1.  **生命周期管理**: 严格执行`Branches`模型的`br_status`状态机。在迭代结束后，通过归档流程（`archive_view.py`）将`br_status`从`open`改为`close`。
2.  **定期清理**: 建立定期的清理任务，通过`get_mobile_iter_info`等API查询长时间处于`open`状态的旧分支，并通知负责人进行处理。
3.  **自动化归档**: 当检测到分支的最后提交时间超过一定期限（如3个月）且状态为`已上线`或`已归档`时，系统可自动触发归档流程。

**Section sources**
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L140-L250)
- [models.py](file://iter_mgt/models.py#L50-L100)
- [apply_view.py](file://iter_mgt/apply_view.py#L400-L500)