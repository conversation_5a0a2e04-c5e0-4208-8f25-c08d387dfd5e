# 资源管理

<cite>
**本文档引用文件**  
- [resource_ser.py](file://publish/resource_ser.py)
- [env_info_ser.py](file://env_mgt/env_info_ser.py)
- [publish_ser.py](file://publish/publish_ser.py)
- [models.py](file://publish/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [资源分配与冲突检测机制](#资源分配与冲突检测机制)
3. [资源锁定、预检与冲突检测算法](#资源锁定预检与冲突检测算法)
4. [发布记录模型设计](#发布记录模型设计)
5. [环境管理模块集成](#环境管理模块集成)
6. [资源配额与优先级调度](#资源配额与优先级调度)
7. [灰度发布中的资源管理](#灰度发布中的资源管理)
8. [发布模式使用示例](#发布模式使用示例)
9. [常见问题与优化建议](#常见问题与优化建议)
10. [结论](#结论)

## 引言
本文档详细阐述了发布过程中资源管理的核心机制，重点介绍资源分配、冲突检测、环境集成、配额调度及灰度发布策略。系统通过`resource_ser.py`实现资源锁定与预检，利用`env_info_ser.py`获取环境资源信息，并结合`PublishRecord`模型进行发布状态追踪。文档还说明了单环境、多环境及跨区域发布的使用场景，并针对资源争用、配额不足等常见问题提供解决方案。

## 资源分配与冲突检测机制
系统在发布流程中实施严格的资源分配与冲突检测机制，确保发布操作的原子性和一致性。当用户发起发布请求时，系统首先对目标应用和环境进行资源锁定，防止并发发布导致的资源冲突。通过`publish_ser.py`中的`check_pipeline_status`函数，系统查询`iter_mgt_jenkins_publish_pipeline_info`表，检查是否存在相同应用和环境的运行中流水线，若存在则拒绝新的发布请求。

资源分配基于环境套（suite_code）和节点绑定关系，通过`env_mgt_node_bind`表维护应用与节点的映射。发布前，系统调用`get_ip_list_by_suite_code`获取目标环境下的所有节点IP，确保资源可用性。冲突检测不仅限于应用级别，还扩展至环境级别，防止同一环境在不同发布任务中被同时操作。

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L500-L550)
- [models.py](file://publish/models.py#L300-L350)

## 资源锁定预检与冲突检测算法
### 资源锁定机制
资源锁定通过`iter_mgt_jenkins_publish_pipeline_info`表的`lock_status`字段实现。当发布任务启动时，系统将`lock_status`设为"lock"，并在`lock_time`记录锁定时间。若任务超时（默认60分钟），系统自动解锁，防止死锁。`update_publish_lock`函数负责清理超时锁定，确保系统健壮性。

### 资源预检流程
资源预检在发布前执行，主要验证以下内容：
1. **应用状态检查**：确认应用未处于发布中状态
2. **环境可用性**：通过`get_prod_regin`获取生产环境信息，验证环境状态
3. **节点健康检查**：调用CMDB接口获取节点状态，确保节点可访问
4. **制品可用性**：验证制品库中存在对应版本的构建产物

### 冲突检测算法
冲突检测算法通过`get_publishing_app_list`函数实现，其核心逻辑如下：
1. 查询`iter_mgt_jenkins_publish_pipeline_detail`表，获取所有运行中的发布任务
2. 联合`task_mgt_deploy_result`表，检查部署结果状态
3. 对目标应用列表进行交集运算，若存在匹配记录则判定为冲突
4. 返回冲突的应用名和环境信息，供前端提示用户

该算法支持批量检查，可一次性验证多个应用的发布状态，提高检测效率。

```mermaid
flowchart TD
Start([发布请求]) --> PreCheck["资源预检"]
PreCheck --> CheckAppStatus["检查应用状态"]
CheckAppStatus --> AppValid{"应用空闲?"}
AppValid --> |否| ReturnConflict["返回冲突信息"]
AppValid --> |是| CheckEnv["检查环境可用性"]
CheckEnv --> EnvValid{"环境可用?"}
EnvValid --> |否| ReturnError["返回环境错误"]
EnvValid --> |是| CheckNode["检查节点健康"]
CheckNode --> NodeValid{"节点健康?"}
NodeValid --> |否| ReturnNodeError["返回节点错误"]
NodeValid --> |是| CheckArtifact["检查制品可用性"]
CheckArtifact --> ArtifactValid{"制品存在?"}
ArtifactValid --> |否| ReturnArtifactError["返回制品错误"]
ArtifactValid --> |是| LockResource["锁定资源"]
LockResource --> ExecutePublish["执行发布"]
ExecutePublish --> UnlockResource["释放资源"]
UnlockResource --> End([发布完成])
ReturnConflict --> End
ReturnError --> End
ReturnNodeError --> End
ReturnArtifactError --> End
```

**Diagram sources**
- [publish_ser.py](file://publish/publish_ser.py#L500-L600)
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L50-L100)

**Section sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L40)
- [publish_ser.py](file://publish/publish_ser.py#L400-L700)

## 发布记录模型设计
`PublishRecord`模型在`models.py`中定义，用于记录发布过程中的关键信息。核心字段设计如下：

| 字段名称 | 类型 | 描述 | 约束 |
|--------|------|------|------|
| module_name | CharField | 应用名 | 最大长度100 |
| suite_code | CharField | 环境套编码 | 最大长度100 |
| iteration_id | CharField | 迭代号 | 最大长度200 |
| create_time | DateTimeField | 创建时间 | 自动添加 |
| update_time | DateTimeField | 更新时间 | 自动更新 |
| status | CharField | 发布状态 | running, success, failure, aborted |
| action_id | IntegerField | 操作ID | 外键关联 |
| stamp | BigIntegerField | 版本戳 | 用于乐观锁 |

模型通过`iter_mgt_jenkins_publish_pipeline_info`表持久化存储，支持发布状态追踪和历史记录查询。`execute_status`字段采用枚举类型，包含CHECK、CHECK_SUCCESS、RUNNING、SUCCESS、FAILURE、ABORTED等状态，完整覆盖发布生命周期。

**Section sources**
- [models.py](file://publish/models.py#L250-L300)

## 环境管理模块集成
### 环境信息获取
系统通过`env_mgt/env_info_ser.py`模块与环境管理模块集成，主要提供以下功能：

1. **环境套查询**：`get_suite_name`函数根据应用名和环境类型获取环境套信息
2. **节点信息获取**：`get_code_info`函数查询指定应用在特定环境下的节点IP
3. **生产环境列表**：`get_prod_regin`函数获取所有生产环境信息
4. **逻辑环境查询**：`get_logic_suite_list_by_type`根据应用类型获取逻辑发布环境

### 集成方式
集成通过数据库直连方式实现，使用Django的`connection.cursor()`执行原生SQL查询。这种方式避免了ORM性能开销，适合高频查询场景。环境信息存储在`env_mgt_suite`、`env_mgt_node`等表中，通过外键关联形成完整拓扑。

```mermaid
classDiagram
class EnvMgtSuite {
+id : BigIntegerField
+suite_code : CharField
+logic_suite_code : CharField
+region_id : ForeignKey
}
class EnvMgtRegion {
+id : BigIntegerField
+region_name : CharField
+region_group : CharField
+type_short_name : CharField
}
class EnvMgtNodeBind {
+id : BigIntegerField
+module_name : CharField
+suite_id : ForeignKey
+node_id : ForeignKey
+enable_bind : CharField
}
class EnvMgtNode {
+id : BigIntegerField
+node_ip : CharField
+minion_id : CharField
+node_status : IntegerField
}
EnvMgtSuite --> EnvMgtRegion : "region_id"
EnvMgtNodeBind --> EnvMgtSuite : "suite_id"
EnvMgtNodeBind --> EnvMgtNode : "node_id"
```

**Diagram sources**
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L150)
- [models.py](file://publish/models.py#L100-L200)

**Section sources**
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L150)

## 资源配额与优先级调度
### 资源配额管理
系统通过`iter_mgt_group_publish_global_strategy`表实现资源配额管理，支持按Git分组设置发布配额。配额策略包含：
- **发布模式**：串行（0）或均分（2）
- **顺序号**：定义发布步骤优先级
- **应用白名单**：指定可发布应用

配额检查在发布预检阶段执行，通过`get_app_publish_strategy_list`函数获取策略，验证发布请求是否符合配额限制。

### 优先级调度
优先级调度基于`step_num`字段实现，数值越小优先级越高。系统按优先级分组执行发布任务，确保高优先级应用优先部署。调度器支持动态调整，管理员可实时修改策略而无需重启服务。

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L700-L750)
- [models.py](file://publish/models.py#L200-L250)

## 灰度发布中的资源管理
### 流量分配策略
灰度发布采用渐进式流量分配策略：
1. **初始阶段**：10%流量导向新版本
2. **观察阶段**：监控关键指标（错误率、响应时间）
3. **扩展阶段**：每15分钟增加20%流量
4. **全量阶段**：100%流量切换至新版本

流量分配通过Nginx配置动态更新实现，利用`publish_exec_salt_cmd`表存储Salt命令，执行`rsync`同步配置文件。

### 资源隔离机制
灰度发布实施严格的资源隔离：
- **网络隔离**：灰度节点使用独立VLAN
- **配置隔离**：灰度环境使用独立Nacos命名空间
- **数据隔离**：数据库读写分离，灰度节点只读
- **监控隔离**：独立Prometheus实例采集灰度指标

隔离通过`EnvMgtNodeBindDynamic`模型实现，记录灰度节点的动态配置，包括`node_docker_service_ip`、`node_docker_dubbo_port`等字段。

**Section sources**
- [models.py](file://publish/models.py#L150-L200)
- [publish_ser.py](file://publish/publish_ser.py#L300-L400)

## 发布模式使用示例
### 单环境发布
```python
# 发布应用到prod环境
publish_request = {
    "app_name": "howbuy-gateway",
    "suite_code": "prod",
    "iteration_id": "20240101"
}
# 系统自动锁定资源，执行发布，完成后释放
```

### 多环境发布
```python
# 同时发布到prod和bs-prod环境
publish_request = {
    "app_name": "howbuy-gateway",
    "suite_codes": ["prod", "bs-prod"],
    "iteration_id": "20240101"
}
# 系统并行执行两个环境的发布任务
```

### 跨区域发布
```python
# 从唐镇发布到外高桥灾备环境
publish_request = {
    "app_name": "howbuy-gateway",
    "src_suite": "tz-prod",
    "target_suite": "wgq-hd",
    "iteration_id": "20240101"
}
# 系统先验证源环境版本，再执行跨区域同步
```

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L100-L300)

## 常见问题与优化建议
### 资源争用问题
**现象**：多个发布任务竞争同一环境资源  
**解决方案**：
1. 实施发布窗口管理，错峰发布
2. 增加环境套数量，分散发布压力
3. 优化锁定超时时间，避免长时间占用

### 配额不足问题
**现象**：发布任务超出配额限制  
**优化建议**：
1. 动态调整配额策略，按业务需求分配
2. 实施优先级队列，保障核心应用发布
3. 提供配额预警，提前通知管理员

### 性能优化
1. **查询优化**：为`suite_code`、`module_name`等字段添加数据库索引
2. **缓存机制**：缓存环境信息查询结果，减少数据库压力
3. **异步处理**：将非关键操作（如日志记录）异步化

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L750-L800)
- [models.py](file://publish/models.py#L350-L380)

## 结论
本文档详细介绍了发布系统中资源管理的核心机制，包括资源分配、冲突检测、环境集成、配额调度和灰度发布策略。系统通过精细化的资源控制和智能的冲突检测算法，确保发布过程的安全性和可靠性。建议在实际使用中结合业务特点，合理配置资源配额和发布策略，充分发挥系统优势。