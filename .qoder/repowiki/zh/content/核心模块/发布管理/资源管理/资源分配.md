# 资源分配

<cite>
**本文档中引用的文件**  
- [resource_ser.py](file://publish/resource_ser.py)
- [env_info_ser.py](file://env_mgt/env_info_ser.py)
- [models.py](file://publish/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [资源分配机制概述](#资源分配机制概述)
3. [资源锁定与预检实现原理](#资源锁定与预检实现原理)
4. [发布记录模型中的资源字段设计](#发布记录模型中的资源字段设计)
5. [与环境管理模块的集成方式](#与环境管理模块的集成方式)
6. [资源分配策略](#资源分配策略)
7. [资源分配调用流程示例](#资源分配调用流程示例)
8. [常见问题与解决方案](#常见问题与解决方案)
9. [优化建议](#优化建议)
10. [附录](#附录)

## 引言
本文档详细阐述了系统中资源分配的核心机制，重点围绕发布流程中的资源管理展开。内容涵盖资源锁定、资源预检、环境信息获取、多环境发布策略以及实际调用流程。通过分析 `resource_ser.py` 和 `env_info_ser.py` 等关键模块，旨在为开发和运维人员提供清晰的资源分配逻辑和最佳实践指导。

## 资源分配机制概述
资源分配是发布流程中的关键环节，确保应用在正确的环境中使用正确的资源进行部署。该机制通过协调发布服务、环境管理服务和配置中心，实现资源的精准匹配与安全分配。核心流程包括：发布请求触发 -> 环境信息查询 -> 资源预检 -> 资源锁定 -> 部署执行。

**Section sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L145)

## 资源锁定与资源预检实现原理
`resource_ser.py` 模块中的 `AndroidPushPackage` 类实现了特定场景下的资源操作。其核心方法 `push_package_for_android` 展示了资源分配的典型流程：
1.  **资源预检**：通过检查源目录 `heel_dir` 是否存在目标文件，验证待发布资源的完整性。
2.  **临时资源创建**：根据当前时间戳和应用版本号创建临时目录 `tem_dir`，用于存放待同步的资源包，避免直接操作生产环境。
3.  **资源锁定与同步**：使用 `rsync` 命令将资源从临时目录同步到目标服务器。`rsync` 的 `--delete` 选项确保了目标目录与源目录的一致性，实现了资源的“锁定”和更新。
4.  **资源清理**：同步成功后，立即删除本地临时目录，释放空间，完成资源分配的原子性操作。

此过程通过 `SSHConnectionManager` 安全地执行远程命令，确保了资源传输的可靠性和安全性。

```mermaid
sequenceDiagram
participant 发布系统
participant resource_ser as resource_ser.py
participant 目标服务器
发布系统->>resource_ser : 发起Android资源发布请求
resource_ser->>resource_ser : 预检本地资源完整性
resource_ser->>resource_ser : 创建临时目录并复制资源
resource_ser->>目标服务器 : 通过SSH执行rsync同步命令
目标服务器-->>resource_ser : 返回同步结果
alt 同步成功
resource_ser->>resource_ser : 删除本地临时目录
resource_ser-->>发布系统 : 返回成功状态
else 同步失败
resource_ser->>resource_ser : 记录错误日志
resource_ser-->>发布系统 : 返回失败状态及错误信息
end
```

**Diagram sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)

**Section sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)

## 发布记录模型中的资源字段设计
`publish/models.py` 文件定义了与资源分配相关的数据模型。`TransitReposInfo` 模型是资源分配过程中的核心数据载体，其关键字段设计如下：
- **`module_name` (应用名)**: 标识资源所属的应用模块，是资源分配的基本单位。
- **`suite_name` (环境套)**: 定义资源将要部署的目标环境，如“生产”、“测试”等，实现资源与环境的绑定。
- **`transit_ip` (中转库IP)**: 指定资源在部署前的临时存放位置，作为资源分发的中转站。
- **`product_path` (制品库路径)**: 记录资源在制品库中的具体路径，确保资源来源的可追溯性。
- **`product_version` (制品版本)**: 记录本次分配的资源版本号，用于版本控制和回滚。

这些字段共同构成了资源分配的元数据，确保了每次发布操作的精确性和可审计性。

**Section sources**
- [models.py](file://publish/models.py#L1-L383)

## 与环境管理模块的集成方式
资源分配系统通过 `env_mgt/env_info_ser.py` 模块与环境管理模块深度集成，以获取实时、准确的环境资源信息。主要集成方式如下：
1.  **API调用**：`resource_ser.py` 通过直接导入和调用 `env_info_ser.py` 中的函数来获取数据。
2.  **数据库直连**：`env_info_ser.py` 使用 `django.db.connection` 直接执行SQL查询，绕过ORM，高效地从 `env_mgt` 相关表中获取数据。
3.  **关键信息获取**：
    - **`get_suite_name`**: 根据应用名列表和环境类型，查询其对应的环境套（suite）信息。
    - **`get_code_info`**: 查询特定应用在指定环境下的节点IP、环境套编码等详细信息。
    - **`get_prod_regin`**: 获取所有生产环境（prod）的区域和环境套信息。

这种集成方式保证了资源分配时能够动态获取最新的环境拓扑和节点状态。

```mermaid
flowchart TD
A[发布请求] --> B{resource_ser.py}
B --> C[调用 env_info_ser.py]
C --> D[get_suite_name()]
C --> E[get_code_info()]
C --> F[get_prod_regin()]
D --> G[执行SQL查询 env_mgt_node_bind]
E --> G
F --> H[执行SQL查询 env_mgt_region]
G --> I[返回环境与节点信息]
H --> I
I --> J[resource_ser.py 使用信息进行资源分配]
```

**Diagram sources**
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L145)
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)

**Section sources**
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L145)

## 资源分配策略
系统支持多种发布场景下的资源分配策略：

### 单环境发布
将资源分配到单一指定环境。例如，将 `fund-android` 应用的 `beta` 版本发布到 `android_target_dir` 目录。
```python
# 示例代码
source_dir = '/data/h5_resource/h5_pkg/fund-android/beta/tag_v852/30.0.2-08091835/'
target_dir = RSYNC_NGINX_LIB_REPO['android_target_dir']
AndroidPushPackage.push_package_for_android(source_dir, target_dir)
```

### 多环境发布
通过循环调用单环境发布逻辑，实现资源向多个环境的分发。例如，先发布到预发环境，验证通过后再发布到生产环境。

### 跨区域发布
利用 `get_prod_regin` 等函数获取不同区域（如“唐镇”、“外高桥”）的生产环境信息，然后分别向这些区域的环境套执行资源分配操作，实现跨区域的统一部署。

**Section sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L145)

## 资源分配调用流程示例
以下是一个完整的资源分配调用流程示例：

1.  **初始化参数**：确定应用名、版本号、源目录和目标环境。
2.  **获取环境信息**：调用 `env_info_ser.get_code_info(app_name, 'prod')` 获取生产环境的节点IP和环境套编码。
3.  **执行资源分配**：调用 `AndroidPushPackage.push_package_for_android(source_dir, app_version, target_dir)` 执行资源同步。
4.  **处理结果**：根据返回的布尔值和日志信息判断操作是否成功，并进行后续处理。

```mermaid
graph TD
A[开始] --> B[设置应用名、版本、源路径]
B --> C[调用 get_code_info 获取生产环境信息]
C --> D{获取成功?}
D -- 是 --> E[构建目标路径 target_dir]
D -- 否 --> H[记录错误并退出]
E --> F[调用 push_package_for_android]
F --> G{同步成功?}
G -- 是 --> I[清理临时文件]
G -- 否 --> H
I --> J[返回成功]
H --> K[返回失败]
```

**Diagram sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L145)

## 常见问题与解决方案
### 资源分配失败
- **现象**：`push_package_for_android` 返回 `False`。
- **原因**：
  - 源目录 `heel_dir` 不存在或无读取权限。
  - 目标服务器SSH连接失败（IP、用户名、密码错误）。
  - `rsync` 命令执行失败（磁盘空间不足、网络中断）。
- **解决方案**：
  1.  检查 `logger.info(stderr)` 输出的详细错误信息。
  2.  验证源路径和目标路径的正确性。
  3.  检查SSH连接配置（`NGINX_LIB_REPO`）。
  4.  登录目标服务器检查磁盘空间和网络状况。

### 资源不足
- **现象**：`rsync` 命令因磁盘空间不足而失败。
- **原因**：目标服务器的存储空间不足以容纳新资源。
- **解决方案**：
  1.  通过 `get_code_info` 获取的节点信息，监控各节点的磁盘使用率。
  2.  建立资源清理机制，定期归档或删除旧版本资源。
  3.  在资源分配前，增加磁盘空间预检步骤。

**Section sources**
- [resource_ser.py](file://publish/resource_ser.py#L1-L41)

## 优化建议
1.  **引入资源池管理**：将 `TransitReposInfo` 模型扩展为资源池，实现资源的统一管理和动态调度。
2.  **增强预检机制**：在 `push_package_for_android` 执行前，增加对目标服务器磁盘空间、内存等资源的预检。
3.  **实现幂等性**：改进 `rsync` 命令或增加校验逻辑，确保重复执行资源分配不会产生副作用。
4.  **日志与监控**：完善日志记录，将关键步骤（如预检、同步、清理）的状态上报到监控系统，便于问题追踪。

## 附录
无