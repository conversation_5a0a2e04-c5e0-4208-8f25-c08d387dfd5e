# 发布管理

<cite>
**本文档引用的文件**  
- [models.py](file://publish/models.py)
- [app_publish_views.py](file://publish/app_publish_views.py)
- [resource_ser.py](file://publish/resource_ser.py)
- [publish_ser.py](file://publish/publish_ser.py)
- [publish_record.py](file://publish/utils/publish_record.py)
- [group_publish_record.py](file://publish/utils/group_publish_record.py)
</cite>

## 目录
1. [核心模型设计](#核心模型设计)  
2. [发布流程与状态转换](#发布流程与状态转换)  
3. [发布申请与验证逻辑](#发布申请与验证逻辑)  
4. [资源分配与冲突检测](#资源分配与冲突检测)  
5. [执行调度策略](#执行调度策略)  
6. [回滚机制](#回滚机制)  
7. [发布模式使用示例](#发布模式使用示例)  
8. [与CI/CD流水线集成](#与cicd流水线集成)  
9. [高级特性](#高级特性)  
10. [故障排查与恢复](#故障排查与恢复)

## 核心模型设计

发布管理模块的核心数据模型包括 `PublishRecord` 和 `GroupPublishStrategy`，分别用于记录单次发布操作和管理分组发布的调度策略。

`PublishRecord` 模型（在代码中体现为 `IterMgtJenkinsPublishPipelineInfo`）用于记录每次发布的完整上下文，包括发布ID、应用名称、环境信息、执行状态、锁状态、创建/更新时间等。该模型通过 `action_id` 与任务队列系统关联，确保发布过程的可追溯性。状态字段 `execute_status` 使用枚举定义了从检查到执行完成的完整生命周期。

`GroupPublishStrategy` 模型（对应 `IterMgtGroupPublishGlobalStrategy`）用于定义分组发布的全局策略。它通过 `git_group` 字段关联特定的代码仓库分组，`step_num` 定义发布顺序，`publish_mode` 指定发布模式（0为串行，2为均分），从而实现对大型项目或微服务集群的有序、可控发布。

**Section sources**  
- [models.py](file://publish/models.py#L200-L250)

## 发布流程与状态转换

发布流程遵循严格的状态机模型，确保操作的原子性和一致性。流程从用户发起发布请求开始，经过审批、资源检查、执行调度、执行、验证到最终完成。

状态转换流程如下：
1.  **初始化 (INIT)**：用户提交发布申请，系统创建发布记录，状态为“初始化”。
2.  **审核中 (REVIEWING)**：发布申请进入审批队列，等待相关负责人审批。
3.  **审核通过 (APPROVED)**：审批通过后，发布流程进入待执行状态。
4.  **执行中 (EXECUTING)**：系统获取发布锁，开始执行发布任务，状态变为“执行中”。
5.  **执行成功 (SUCCESS)**：所有节点发布、重启、验证均成功，状态更新为“执行成功”。
6.  **执行失败 (FAILURE)**：任一环节出现错误，状态更新为“执行失败”，并触发告警。
7.  **已终止 (ABORTED)**：用户手动取消或系统超时，状态更新为“已终止”。

关键的审批机制通过 `PublishMgtRebootAudit` 模型实现，它记录了审核人、审核时间、审核状态等信息，确保了发布操作的可审计性。

```mermaid
stateDiagram-v2
[*] --> 初始化
初始化 --> 审核中 : 提交申请
审核中 --> 审核通过 : 审批通过
审核中 --> 审核不通过 : 审批拒绝
审核通过 --> 执行中 : 开始执行
执行中 --> 执行成功 : 全部成功
执行中 --> 执行失败 : 出现错误
执行中 --> 已终止 : 手动取消/超时
执行成功 --> [*]
执行失败 --> [*]
已终止 --> [*]
```

**Diagram sources**  
- [models.py](file://publish_mgt/models.py#L50-L70)

## 发布申请与验证逻辑

发布申请的验证逻辑主要在 `app_publish_views.py` 文件中的 `MultiNodeOperateApi` 类中实现。该逻辑确保了发布的安全性和合规性。

核心验证步骤包括：
1.  **交易时间检查**：通过 `check_trade_time` 方法，结合 `PublishOddDates` 表中的特殊非交易日设置和 `TRADE_TIME` 配置，判断当前时间是否为允许发布的交易时间。对于 `release` 类型的分支，交易时间内禁止发布。
2.  **发布锁检查**：调用 `get_publishing_app_list` 方法查询数据库，检查目标应用是否已在其他发布流程中，防止并发发布导致的资源冲突。
3.  **门禁开关检查**：通过 `GuardSwitchInfo` 模型检查全局门禁开关（如 `check_trade_time`），只有开关打开时才进行相应的检查。
4.  **白名单校验**：对于特定应用或团队，通过 `get_app_or_group_write_list` 查询白名单，决定是否豁免某些检查规则。

```mermaid
flowchart TD
Start([发布申请]) --> CheckTime["检查交易时间"]
CheckTime --> IsTradeTime{"是否在交易时间?"}
IsTradeTime --> |是| CheckBranch{"分支类型为release?"}
IsTradeTime --> |否| CheckLock["检查发布锁"]
CheckBranch --> |是| Block["禁止发布"]
CheckBranch --> |否| CheckLock
CheckLock --> IsPublishing{"应用正在发布?"}
IsPublishing --> |是| Block
IsPublishing --> |否| CheckGuard["检查门禁开关"]
CheckGuard --> IsGuardOn{"开关已打开?"}
IsGuardOn --> |是| CheckWhiteList["检查白名单"]
IsGuardOn --> |否| Allow["允许发布"]
CheckWhiteList --> IsInWhiteList{"在白名单内?"}
IsInWhiteList --> |是| Allow
IsInWhiteList --> |否| Block
Allow --> End([发布流程继续])
Block --> End
```

**Diagram sources**  
- [app_publish_views.py](file://publish/app_publish_views.py#L400-L500)
- [publish_ser.py](file://publish/publish_ser.py#L300-L350)

**Section sources**  
- [app_publish_views.py](file://publish/app_publish_views.py#L300-L600)
- [publish_ser.py](file://publish/publish_ser.py#L300-L400)

## 资源分配与冲突检测

资源分配与冲突检测是发布过程中的关键环节，确保了应用能够正确部署到指定的服务器节点。

`resource_ser.py` 文件中的 `AndroidPushPackage` 类展示了资源推送的实现逻辑。它通过 `rsync` 命令将构建好的Android安装包从源目录同步到Nginx服务器的指定目标目录。该过程包括创建临时目录、执行同步、清理临时文件等步骤，并通过SSH连接管理器与远程服务器交互。

冲突检测主要通过数据库查询实现：
1.  **节点绑定查询**：`get_bind_info_by_suite` 方法根据应用名和环境套代码查询所有已绑定的节点IP，确保发布目标的准确性。
2.  **制品信息查询**：`get_repo_info_by_app_and_env_node` 方法查询特定应用在特定节点上最后一次部署的制品信息（如分支、迭代ID），用于版本对比和回滚。
3.  **并发发布检测**：`get_publishing_app_list` 方法通过查询 `iter_mgt_jenkins_publish_pipeline_info` 和 `task_mgt_deploy_result` 表，找出所有状态为“running”的发布任务，从而检测到潜在的资源竞争。

**Section sources**  
- [resource_ser.py](file://publish/resource_ser.py#L1-L40)
- [publish_ser.py](file://publish/publish_ser.py#L500-L600)

## 执行调度策略

执行调度策略由 `GroupPublishStrategy` 模型驱动，支持多种发布模式。

`GroupPublishRecorder` 工具类（位于 `publish/utils/group_publish_record.py`）负责记录分组发布的执行历史。其核心方法 `_add_batch_deploy_queue` 实现了“配置更新 -> 发布 -> 验证”的原子性操作序列。该方法会为节点列表中的每个节点依次添加这三个操作任务到执行队列中，确保了操作的顺序性。

发布模式由 `publish_mode` 字段控制：
-   **串行模式 (0)**：所有节点按顺序依次执行发布流程。
-   **均分模式 (2)**：将节点列表平均分成若干组，组内节点并行执行，组间串行执行，以平衡发布速度和系统稳定性。

调度器通过 `TaskQueue` 组件将任务分发给不同的执行器（如Salt执行器、脚本执行器、接口调用器），实现了异步、非阻塞的发布执行。

```mermaid
sequenceDiagram
participant User as 用户
participant API as 发布API
participant TaskQueue as 任务队列
participant Executor as 执行器
participant Node as 服务器节点
User->>API : 发起发布请求
API->>API : 验证请求
API->>TaskQueue : 创建任务队列
API->>TaskQueue : 添加检查任务
API->>TaskQueue : 添加发布任务序列
TaskQueue->>Executor : 异步执行任务
Executor->>Node : 执行Salt命令
Node-->>Executor : 返回执行结果
Executor-->>TaskQueue : 报告任务状态
TaskQueue-->>API : 更新发布状态
API-->>User : 返回发布成功
```

**Diagram sources**  
- [app_publish_views.py](file://publish/app_publish_views.py#L550-L650)
- [group_publish_record.py](file://publish/utils/group_publish_record.py#L1-L20)

## 回滚机制

回滚机制是发布安全的重要保障。`PublishMgtRollbackInfo` 模型专门用于记录回滚操作。

回滚流程如下：
1.  **查询可回滚版本**：`get_last_can_rollback_info` 方法通过复杂的SQL查询，找到目标应用在指定节点上“上一次成功发布”的版本信息（包括制品commit ID、分支、迭代ID等）。该查询通过 `OFFSET 1` 跳过当前版本，找到前一个可回滚的版本。
2.  **创建回滚记录**：系统创建 `PublishMgtRollbackInfo` 记录，状态初始为 `RUNNING`。
3.  **执行回滚**：系统使用查询到的旧版本信息，执行与发布相反的部署流程。
4.  **更新状态**：回滚成功后，将 `rollback_status` 更新为 `END`。

此机制确保了在发布新版本出现问题时，能够快速、准确地恢复到已知的稳定状态。

**Section sources**  
- [models.py](file://publish/models.py#L250-L280)
- [publish_ser.py](file://publish/publish_ser.py#L600-L650)

## 发布模式使用示例

### 单应用发布
```python
# 请求数据示例
{
    "op_type": "deploy",
    "iteration_id": "IT20231001",
    "app_name": "user-service",
    "node_list": ["192.168.1.101", "192.168.1.102"],
    "suite_code": "prod"
}
```
此请求将 `user-service` 应用发布到 `prod` 环境的两个指定节点。

### 批量发布
```python
# 请求数据示例
{
    "op_type": "update_and_deploy_and_verify",
    "iteration_id": "IT20231001",
    "app_name": "order-service",
    "node_list": ["192.168.1.201", "192.168.1.202", "192.168.1.203"],
    "suite_code": "prod"
}
```
此请求将对 `order-service` 应用的三个节点依次执行“配置更新 -> 发布 -> 验证”的完整流程。

### 灰度发布
灰度发布通过 `suite_code` 的精细化控制实现。例如，先将应用发布到 `beta` 环境进行验证，再发布到 `prod` 环境。`HdPublishOperate` 类专门处理 `hd` (灰度) 环境的发布逻辑。

**Section sources**  
- [app_publish_views.py](file://publish/app_publish_views.py#L200-L300)

## 与CI/CD流水线集成

发布模块与CI/CD流水线深度集成，主要体现在：
1.  **状态同步**：`H5DeployStatusCollector` 类负责从CI/CD系统收集发布状态，并通过 `DeployResult` 模型进行记录和同步。
2.  **触发机制**：`TaskMgtJenkinsPublishPipelineInfo` 模型存储了Jenkins流水线的构建信息（`job_name`, `build_id`），实现了发布操作与CI流水线的关联。
3.  **自动化测试集成**：`SpiderPublish` 类中的 `__check_app_prod_node_display` 方法会调用 `AppAutoTestCheckResult` 检查应用的自动化测试结果，只有测试通过的应用才能在生产环境显示其节点，防止未经测试的代码上线。

**Section sources**  
- [app_publish_views.py](file://publish/app_publish_views.py#L100-L150)
- [publish_ser.py](file://publish/publish_ser.py#L100-L150)

## 高级特性

### 发布锁控制
发布锁通过 `IterMgtJenkinsPublishPipelineInfo` 表的 `lock_status` 字段实现。当一个发布流程启动时，会将相关记录的 `lock_status` 设为 `lock`。其他并发请求在执行前会检查此锁，如果已被锁定，则拒绝发布。系统还设置了超时机制（60分钟），防止因异常导致的死锁。

### 冷备节点支持
冷备节点的支持体现在 `NodeBind` 模型的设计中。通过 `enable_bind` 字段控制节点的启用/禁用状态。在发布时，`get_ip_list_by_suite_code` 查询会过滤掉 `enable_bind = 0` 的节点，从而实现对冷备节点的隔离。

**Section sources**  
- [publish_ser.py](file://publish/publish_ser.py#L400-L450)
- [models.py](file://env_mgt/models.py#L100-L120)

## 故障排查与恢复

### 常见问题与排查步骤

#### 发布失败
1.  **检查日志**：查看 `task_mgt_deploy_result` 表中的 `message` 字段和 `publish_salt_returns` 表中的 `full_ret` 字段，定位具体错误。
2.  **检查网络**：确认发布服务器与目标节点之间的网络连通性。
3.  **检查Salt命令**：通过 `get_salt_cmd_by_app_name` 确认目标节点的Salt命令配置是否正确。
4.  **检查资源**：确认目标节点的磁盘空间、内存等资源是否充足。

#### 版本错乱
1.  **核对制品信息**：查询 `product_mgt_product_info` 表，确认部署的制品版本是否与预期一致。
2.  **检查绑定关系**：查询 `env_mgt_node_bind` 表，确认应用与节点的绑定关系是否正确。
3.  **检查发布历史**：查询 `task_mgt_deploy_result` 表，追溯该节点的发布历史，找出版本变更的源头。

### 恢复方案
1.  **立即回滚**：使用“回滚机制”将应用恢复到上一个稳定版本。
2.  **紧急修复**：如果问题由代码缺陷引起，立即修复并发布新版本。
3.  **隔离故障**：如果问题影响范围大，可暂时将故障节点从负载均衡中移除。

**Section sources**  
- [publish_ser.py](file://publish/publish_ser.py#L650-L700)
- [models.py](file://publish/models.py#L100-L150)