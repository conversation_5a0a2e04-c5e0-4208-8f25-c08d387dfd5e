# 执行调度

<cite>
**本文档引用的文件**   
- [publish_ser.py](file://publish/publish_ser.py)
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)
- [task_queue.py](file://task_mgt/task_queue.py)
- [base.py](file://task_mgt/base.py)
</cite>

## 目录
1. [发布任务调度流程](#发布任务调度流程)
2. [任务管理与队列机制](#任务管理与队列机制)
3. [发布计划生成算法](#发布计划生成算法)
4. [CI/CD流水线集成](#cicd流水线集成)
5. [执行过程监控与异常处理](#执行过程监控与异常处理)
6. [高级调度模式配置](#高级调度模式配置)
7. [性能优化与故障排查](#性能优化与故障排查)

## 发布任务调度流程

发布任务从审批通过到最终执行的调度过程始于`publish_ser.py`中的调度逻辑。当发布任务通过审批后，系统会触发发布调度流程，该流程首先验证发布环境的可用性，检查是否存在正在运行的发布任务以避免冲突。系统通过`get_publishing_app_list`函数查询当前正在发布的应用列表，确保同一应用在同一环境不会并行发布。发布任务的锁定机制通过`update_publish_lock`和`update_publish_status`函数实现，确保任务的原子性和状态一致性。调度器会根据应用的发布策略和环境配置，确定发布的时间窗口和执行顺序。

**本节来源**
- [publish_ser.py](file://publish/publish_ser.py#L500-L550)

## 任务管理与队列机制

任务管理模块`task_mgt`负责创建和管理后台执行队列。在`app_ci_pipeline.py`中，`AppCIPipeline`类通过`AppTaskQueue`创建任务队列实例。任务队列支持多种任务类型，包括脚本执行、Jenkins任务、邮件发送等，这些类型由`TaskTypeObject`枚举定义。任务通过`enter_queue`方法加入队列，然后通过`async_run`方法异步执行。`task_queue.py`文件中的`TaskQueue`类实现了队列的核心逻辑，包括任务的入队、出队和状态管理。任务执行采用生产者-消费者模式，确保任务的有序执行和资源的合理分配。

```mermaid
classDiagram
class TaskQueue {
+list task_queue
+enter_queue(task_type, task_name, action_id, params)
+async_run()
}
class TaskTypeObject {
+SCRIPT
+JENKINS
+EMAIL
+INTERFACE
}
class AppTaskQueue {
+__init__(task_queue)
+enter_queue()
+async_run()
}
TaskQueue <|-- AppTaskQueue
AppTaskQueue --> TaskTypeObject
```

**图示来源**
- [task_mgt/task_queue.py](file://task_mgt/task_queue.py)
- [ci_cd_mgt/h5/app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)

**本节来源**
- [task_mgt/task_queue.py](file://task_mgt/task_queue.py)
- [ci_cd_mgt/h5/app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)

## 发布计划生成算法

发布计划的生成涉及时间窗口计算、资源预检和冲突检测等关键环节。系统首先通过`get_app_publish_strategy_list`函数获取应用的发布策略，包括发布模式（如灰度发布、分批发布）和步骤编号。时间窗口计算基于应用的发布频率和业务高峰期避让策略，确保发布在业务低峰期进行。资源预检通过`get_bind_info_by_suite`函数获取目标环境的节点信息，验证节点的可用性和资源充足性。冲突检测机制通过查询`iter_mgt_jenkins_publish_pipeline_info`表，检查是否有其他发布任务正在同一环境执行，避免资源竞争和配置冲突。

**本节来源**
- [publish_ser.py](file://publish/publish_ser.py#L400-L450)

## CI/CD流水线集成

与CI/CD流水线的集成主要通过`app_ci_pipeline.py`文件中的`AppCIPipeline`类实现。该类继承自`H5CIPipelineApi`，重写了`ci_publish_info`方法以处理不同包类型的发布请求。构建与部署任务的触发机制基于Jenkins，通过`call_job_param_dict`字典将发布参数映射到相应的Jenkins任务。`run_stage`方法负责执行发布阶段，将Jenkins任务加入队列并异步运行。系统还集成了邮件通知功能，通过`get_email_info`方法生成发布通知邮件的内容和主题，在发布申请阶段自动发送给相关人员。

```mermaid
sequenceDiagram
participant 用户
participant AppCIPipeline
participant TaskQueue
participant Jenkins
用户->>AppCIPipeline : 提交发布请求
AppCIPipeline->>AppCIPipeline : 验证请求参数
AppCIPipeline->>TaskQueue : 添加检查任务
AppCIPipeline->>TaskQueue : 添加Jenkins任务
AppCIPipeline->>TaskQueue : 添加邮件任务
TaskQueue->>Jenkins : 触发构建任务
Jenkins-->>TaskQueue : 返回构建结果
TaskQueue->>用户 : 发送发布结果通知
```

**图示来源**
- [ci_cd_mgt/h5/app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)

**本节来源**
- [ci_cd_mgt/h5/app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)

## 执行过程监控与异常处理

发布执行过程中的状态同步、日志记录和异常处理策略确保了发布的可靠性和可追溯性。系统通过`update_publish_status_by_action_id`函数实时更新发布任务的状态，包括"running"、"success"、"failure"等。日志记录通过Django的logger模块实现，关键操作如任务入队、状态更新等都会被记录。异常处理策略包括超时检测和自动解锁，`update_publish_lock`函数会定期检查长时间未更新的锁定任务并自动解锁，防止死锁。对于执行失败的任务，系统会记录详细的错误信息，并提供重试机制。

**本节来源**
- [publish_ser.py](file://publish/publish_ser.py#L300-L350)

## 高级调度模式配置

系统支持灰度发布、分批发布等高级调度模式。灰度发布通过`AppTagCIPipeline`类实现，为不同标签的应用提供独立的发布通道。分批发布则通过`get_app_step_dict_list`函数获取应用的发布步骤，将发布任务分解为多个批次依次执行。配置示例包括在`business_name_dict`中定义不同平台的发布业务名称，以及在发布策略中设置`publish_mode`字段来指定发布模式。这些配置使得系统能够灵活应对不同的发布需求，支持从全量发布到精细化灰度的多种场景。

**本节来源**
- [ci_cd_mgt/h5/app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L20-L30)

## 性能优化与故障排查

针对执行调度中的常见问题如任务堆积、资源不足等，提供以下性能优化建议：首先，优化数据库查询，对频繁查询的字段如`action_id`、`status`等建立索引；其次，调整任务队列的并发数，根据系统负载动态调整消费者数量；最后，实施任务优先级机制，确保关键任务优先执行。故障排查方案包括：检查`task_mgt_salt_operation_results`表中的执行记录，分析失败任务的`request_result`；监控Jenkins任务的执行日志，定位构建失败原因；以及检查网络连接和Salt Master的可用性，确保命令能够正确下发。

**本节来源**
- [task_mgt/migrations/0003_auto_20210104_1116.py](file://task_mgt/migrations/0003_auto_20210104_1116.py)
- [publish_ser.py](file://publish/publish_ser.py#L200-L250)