# 审批机制

<cite>
**本文档引用的文件**
- [publish_ser.py](file://publish/publish_ser.py)
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py)
</cite>

## 目录
1. [发布流程中的多级审批体系](#发布流程中的多级审批体系)
2. [审批流程的触发与状态管理](#审批流程的触发与状态管理)
3. [审批节点的串联与条件判断](#审批节点的串联与条件判断)
4. [自动审批策略实现逻辑](#自动审批策略实现逻辑)
5. [审批人分配规则](#审批人分配规则)
6. [审批超时与驳回处理](#审批超时与驳回处理)
7. [审批配置管理接口](#审批配置管理接口)
8. [故障排查指南](#故障排查指南)

## 发布流程中的多级审批体系

本系统实现了发布流程中的多级审批机制，通过`publish_ser.py`和`iter_mgt_ser.py`两个核心模块协同工作，确保发布操作的安全性和可控性。审批体系基于迭代管理（iter_mgt）和发布管理（publish）两个维度构建，支持从单应用发布到批量发布的多种场景。

审批流程采用分阶段执行模式，每个阶段包含多个并行或串行的审批节点。系统通过`PublishPlanConfig`类定义审批计划配置，其中`RapidPublishPlanConfig`实现了快速发布计划的审批流程，包含验证、预发布、自动化测试、处理发布和发布后等五个阶段。

审批流程的执行顺序由`phase_order`字段控制，每个阶段可以配置`next_phase_order`指定下一个执行阶段。关键审批节点包括：
- **验证阶段（VERIFY）**：对发布请求进行初步验证
- **预发布阶段（BEFORE_PUBLISH）**：执行发布前的准备工作
- **处理发布阶段（PROCESS_PUBLISH）**：核心发布操作执行
- **发布后阶段（AFTER_PUBLISH）**：发布完成后的收尾工作

**Section sources**
- [iter_mgt/publish_plan/config/rapid_publish_plan_config.py](file://iter_mgt/publish_plan/config/rapid_publish_plan_config.py#L0-L23)

## 审批流程的触发与状态管理

### 触发机制

审批流程的触发主要通过`publish_ser.py`中的`exec_publish`方法实现。当用户发起发布请求时，系统首先进行前置条件检查，包括交易时间限制和并发发布检查。

```mermaid
sequenceDiagram
participant 用户
participant 发布视图
participant 审批服务
participant 数据库
用户->>发布视图 : 发起发布请求
发布视图->>审批服务 : 调用exec_publish
审批服务->>审批服务 : 检查交易时间限制
审批服务->>数据库 : 查询正在发布的应用
数据库-->>审批服务 : 返回查询结果
审批服务->>发布视图 : 返回审批状态
发布视图->>用户 : 显示审批结果
```

**Diagram sources**
- [publish/app_publish_views.py](file://publish/app_publish_views.py#L460-L478)
- [publish/app_publish_views.py](file://publish/app_publish_views.py#L832-L862)

### 状态管理

审批状态管理通过`update_publish_status`和`update_publish_status_by_action_id`函数实现，支持对发布流程的状态进行动态更新。系统维护了多种状态，包括：
- `lock`：锁定状态，表示审批流程正在进行
- `unlock`：解锁状态，表示审批流程可执行
- `running`：运行状态，表示发布任务正在执行
- `success`：成功状态，表示发布任务完成
- `failure`：失败状态，表示发布任务失败

状态更新机制包含超时处理，当审批流程超过指定时间未完成时，系统会自动将其状态重置为`unlock`，防止审批流程长时间阻塞。

**Section sources**
- [publish/publish_ser.py](file://publish/publish_ser.py#L0-L799)

## 审批节点的串联与条件判断

### 节点串联机制

`iter_mgt_ser.py`文件中的`get_product_info`函数实现了审批节点的串联逻辑。系统通过查询迭代信息表和应用信息表的关联关系，构建完整的审批节点链。

```mermaid
flowchart TD
A[开始] --> B{获取迭代信息}
B --> C[查询迭代应用信息]
C --> D[关联制品版本信息]
D --> E{是否存在在线版本}
E --> |是| F[标记为在线分支]
E --> |否| G[标记为空]
F --> H[返回结果]
G --> H
H --> I[结束]
```

**Diagram sources**
- [iter_mgt/iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L272-L296)

### 条件判断逻辑

系统实现了复杂的条件判断机制，通过SQL查询和Python逻辑相结合的方式进行决策。主要条件判断包括：

1. **发布时间限制**：检查是否在交易时间范围内
2. **并发发布检查**：防止同一应用在多个环境中同时发布
3. **权限验证**：检查用户是否有权限操作目标应用
4. **环境状态检查**：验证目标环境是否可用

条件判断的核心函数是`get_publishing_app_list`，它通过查询`iter_mgt_jenkins_publish_pipeline_info`和`task_mgt_deploy_result`表，获取当前正在发布的应用列表，避免发布冲突。

**Section sources**
- [publish/publish_ser.py](file://publish/publish_ser.py#L0-L799)
- [iter_mgt/iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L0-L799)

## 自动审批策略实现逻辑

自动审批策略基于代码质量、测试覆盖率等指标进行决策，主要通过以下机制实现：

### 指标采集

系统通过`get_lib_product_info`函数获取制品信息，包括：
- 代码分支信息（lib_repo_branch）
- 构建时间（create_time）
- 迭代ID（iteration_id）
- 模块名称（module_name）

这些信息作为自动审批决策的基础数据。

### 决策规则

自动审批决策规则主要体现在`get_need_publish_app_info`函数中，该函数根据以下条件判断是否需要人工审批：

```python
# 无需人工审批的条件
(a.guard_switch = 0 or 
 b.wl_group_pass = 1 or 
 e.is_cold_standby_node = 1 or 
 m.jenkins_batch_publish = 0 or 
 m.jenkins_batch_publish IS NULL)
```

当满足以上任一条件时，系统将自动通过审批，无需人工干预。

### 白名单机制

系统实现了白名单机制，通过`iter_whitelist_group`和`iter_whitelist_app`表管理白名单应用和组。白名单内的应用可以享受自动审批待遇，提高发布效率。

**Section sources**
- [publish/publish_ser.py](file://publish/publish_ser.py#L0-L799)
- [iter_mgt/iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L0-L799)

## 审批人分配规则

系统支持多种审批人分配模式，确保审批流程的灵活性和适应性。

### 固定审批人

固定审批人模式通过`app_mgt_app_module`表中的`need_ops`字段控制。当`need_ops=1`时，表示该应用需要运维人员审批。

### 角色审批

角色审批基于用户权限体系实现，通过`user_gitlab_members`表中的`permission`字段确定用户角色。系统要求审批人权限级别大于29才能执行审批操作。

### 动态指派

动态指派机制通过`get_user_gitlab_info`函数实现，根据用户所属的GitLab组动态确定其可审批的应用范围。系统查询用户有权限访问的Git仓库，将其作为可审批应用的候选集。

```mermaid
classDiagram
class ApprovalAssignment {
+get_user_gitlab_info(user_name)
+get_app_step_dict_list(git_group_name)
+get_batch_publish_access(module_name)
+get_user_repos_info(username)
}
class UserPermission {
+permission_level
+gitlab_groups
+access_rights
}
ApprovalAssignment --> UserPermission : "uses"
```

**Diagram sources**
- [publish/publish_ser.py](file://publish/publish_ser.py#L0-L799)
- [iter_mgt/iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L0-L799)

## 审批超时与驳回处理

### 超时处理机制

系统实现了完善的审批超时处理机制，通过`update_publish_lock`函数定期清理超时的审批锁。超时规则如下：

- **执行超时**：当`execute_status='check_success'`且超过5分钟未更新时，将状态设置为`failure`
- **整体超时**：当`lock_status='lock'`且超过60分钟未完成时，自动解锁

```sql
UPDATE iter_mgt_jenkins_publish_pipeline_info 
SET lock_status = 'unlock', update_time = NOW()
WHERE job_name = '{job_name}' 
AND lock_status = 'lock' 
AND (TIMEDIFF(NOW(),lock_time)/60 > 60 
OR status in ('failure', 'success','aborted'))
```

### 驳回处理流程

当审批被驳回时，系统会记录驳回原因并通知申请人。驳回处理主要通过`PublishReasonView`类实现，支持批量创建驳回原因。

驳回后的处理流程包括：
1. 记录驳回原因到`PublishReasonInfo`表
2. 更新待办事项状态为"已驳回"
3. 通知申请人重新提交申请
4. 保留历史审批记录供审计

**Section sources**
- [publish/publish_ser.py](file://publish/publish_ser.py#L0-L799)
- [iter_mgt/publish_reason/publish_reason_view.py](file://iter_mgt/publish_reason/publish_reason_view.py#L0-L28)

## 审批配置管理接口

系统提供了完整的审批配置管理接口，支持对审批策略的动态调整。

### 接口说明

| 接口名称 | HTTP方法 | 路径 | 功能描述 |
|---------|--------|------|---------|
| 创建审批原因 | POST | /create_publish_reason | 创建发布驳回原因 |
| 更新测试截止日期 | POST | /update_test_end_date | 更新迭代测试截止日期 |
| 查询迭代信息 | GET | /get_iter_search_info | 搜索迭代信息 |
| 获取应用列表 | GET | /get_app_list_by_iteration_id | 根据迭代ID获取应用列表 |

### 使用示例

```python
# 创建审批原因示例
def create_publish_reason(self, request):
    publish_reason_list = request.data.get("publish_reason_list")
    opt_user = str(request.user)
    cur_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    for publish_reason in publish_reason_list:
        opt_reason = publish_reason.get("opt_reason")
        if len(opt_reason) < 10:
            return Response(data=ApiResult.failed_dict('原因至少包含10个字符，请重新输入！'))
        
        # 保存审批原因
        PublishReasonInfo.objects.create(
            publish_order_id=publish_reason.get("publish_order_id"),
            reason=opt_reason,
            create_user=opt_user,
            create_time=cur_time
        )
```

**Section sources**
- [iter_mgt/publish_reason/publish_reason_view.py](file://iter_mgt/publish_reason/publish_reason_view.py#L0-L28)
- [iter_mgt/iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L202-L228)

## 故障排查指南

### 审批人缺失

**问题现象**：审批流程无法启动，提示"无可用审批人"

**排查步骤**：
1. 检查`user_gitlab_members`表中用户权限是否大于29
2. 确认用户所属GitLab组是否正确
3. 验证`app_mgt_app_module`表中`need_ops`字段设置
4. 检查白名单配置是否影响审批人选择

**解决方案**：
```sql
-- 检查用户权限
SELECT username, permission FROM user_gitlab_members 
WHERE username = 'xxx' AND git_group_name = 'yyy';

-- 检查应用审批配置
SELECT module_name, need_ops, jenkins_batch_publish 
FROM app_mgt_app_module WHERE module_name = 'zzz';
```

### 状态不一致

**问题现象**：审批状态显示异常，如长时间处于"锁定"状态

**排查步骤**：
1. 检查`iter_mgt_jenkins_publish_pipeline_info`表中的`lock_status`和`status`字段
2. 验证`update_time`是否正常更新
3. 检查是否有定时任务未正常执行
4. 确认数据库连接是否正常

**解决方案**：
```python
# 手动修复状态不一致
def fix_publish_status():
    # 清理超时的锁定状态
    update_publish_lock(job_name)
    
    # 同步数据库状态
    connection.commit()
```

### 常见错误代码

| 错误代码 | 含义 | 解决方案 |
|--------|-----|---------|
| 4001 | 交易时间禁止发布 | 在非交易时间重新提交 |
| 4002 | 应用正在发布中 | 等待当前发布完成 |
| 4003 | 无发布权限 | 联系管理员提升权限 |
| 4004 | 制品信息不存在 | 检查构建是否成功 |
| 4005 | 环境套配置错误 | 检查环境套绑定关系 |

**Section sources**
- [publish/publish_ser.py](file://publish/publish_ser.py#L0-L799)
- [iter_mgt/iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L0-L799)