# 发布申请

<cite>
**本文档引用的文件**   
- [app_publish_views.py](file://publish/app_publish_views.py)
- [publish_ser.py](file://publish/publish_ser.py)
</cite>

## 目录
1. [发布申请概述](#发布申请概述)
2. [API发布请求处理流程](#api发布请求处理流程)
3. [发布申请参数验证与权限校验](#发布申请参数验证与权限校验)
4. [发布申请创建与初始化逻辑](#发布申请创建与初始化逻辑)
5. [发布申请关键参数说明](#发布申请关键参数说明)
6. [发布申请状态机与迭代关联](#发布申请状态机与迭代关联)
7. [发布申请示例代码](#发布申请示例代码)
8. [故障排查指南](#故障排查指南)
9. [最佳实践建议](#最佳实践建议)

## 发布申请概述

本部分介绍发布申请的整体流程和核心概念。系统通过API接口接收发布申请，经过参数验证和权限校验后，创建并初始化发布申请。发布申请包含应用ID、版本号、目标环境等关键参数，并与迭代管理模块进行关联。

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L0-L1853)
- [publish_ser.py](file://publish/publish_ser.py#L0-L999)

## API发布请求处理流程

发布请求的接收主要由`app_publish_views.py`中的视图类处理。系统通过REST API接收发布申请，主要的视图类包括`PublishInfoApi`、`PublishOperate`和`MultiNodeOperateApi`。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "发布API"
participant TaskQueue as "任务队列"
participant Database as "数据库"
Client->>API : POST /api/publish
API->>API : 验证请求参数
API->>API : 检查应用发布状态
API->>API : 创建操作记录
API->>Database : 查询节点IP列表
Database-->>API : 返回节点信息
API->>TaskQueue : 初始化执行队列
API->>TaskQueue : 添加检查任务
API->>TaskQueue : 添加发布任务
TaskQueue->>TaskQueue : 异步执行任务
API-->>Client : 返回处理中状态
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L0-L1853)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L0-L1853)

## 发布申请参数验证与权限校验

发布申请的参数验证和权限校验是确保发布安全的重要环节。系统在接收发布请求后，会进行多层次的验证和校验。

### 参数验证

系统首先验证请求中的必要参数，包括应用名称、迭代ID、节点列表和环境套代码等。如果缺少必要参数，系统会返回相应的错误信息。

### 权限校验

权限校验主要通过`get_app_or_group_write_list`函数实现，检查用户是否有权限对特定应用进行发布操作。同时，系统还会检查交易时间限制，防止在交易时段进行发布。

```mermaid
flowchart TD
Start([开始]) --> ValidateParams["验证请求参数"]
ValidateParams --> ParamsValid{"参数有效?"}
ParamsValid --> |否| ReturnError["返回参数错误"]
ParamsValid --> |是| CheckPublishing["检查应用是否正在发布"]
CheckPublishing --> IsPublishing{"应用正在发布?"}
IsPublishing --> |是| ReturnError
IsPublishing --> |否| CheckTradeTime["检查交易时间限制"]
CheckTradeTime --> TradeAllowed{"允许在交易时间发布?"}
TradeAllowed --> |否| ReturnError
TradeAllowed --> |是| CheckPermission["检查用户权限"]
CheckPermission --> HasPermission{"有权限?"}
HasPermission --> |否| ReturnError
HasPermission --> |是| CreateTask["创建发布任务"]
CreateTask --> End([结束])
ReturnError --> End
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L460-L478)
- [publish_ser.py](file://publish/publish_ser.py#L500-L550)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L460-L478)
- [publish_ser.py](file://publish/publish_ser.py#L500-L550)

## 发布申请创建与初始化逻辑

发布申请的创建与初始化逻辑主要在`publish_ser.py`文件中实现，通过`RepoInfoRecorder`类和相关函数完成。

### 发布申请创建流程

1. **创建操作记录**：系统首先创建一个操作记录，用于跟踪发布过程。
2. **初始化执行队列**：创建任务队列，准备执行发布相关的各项任务。
3. **添加检查任务**：将发布前的检查任务添加到队列中。
4. **添加发布任务**：将实际的发布任务添加到队列中。
5. **异步执行**：启动异步任务执行器，按顺序执行队列中的任务。

### 核心类与函数

- `RepoInfoRecorder`：制品信息记录者，负责记录发布相关的制品信息。
- `get_ip_list_by_suite_code`：根据应用名称和环境套代码获取节点IP列表。
- `get_publishing_app_list`：检查指定应用是否正在发布中。

```mermaid
classDiagram
class RepoInfoRecorder {
+app_name : str
+iteration_id : str
+node_ip : str
+suite_code : str
+node_docker : str
+record_info(lib_repo_id : int)
+_get_repo_id(module_name : str, iteration_id : str, suite_code : str)
+_get_env_node_bind_id(module_name : str, node_ip : str, node_docker : str, suite_code : str)
+_update_node_bind_lib_repo_info(node_bind_id_list : list, repo_info_id : int)
}
class PublishMgtBackupInfoRecorder {
+module_name : str
+node_ip : str
+backup_path : str
+salt_upd_log : str
+publish_user : str
+publish_iteration_id : str
+backup_desc : str
+node_docker : str
+suite_code : str
+opt_type : str
+record_info()
+_get_env_node_bind_info(module_name : str, node_ip : str, node_docker : str, suite_code : str)
+_get_repo_info(module_name : str, node_bind_repo_id : int)
}
class ConfigRepoInfoRecorder {
+record_info()
}
RepoInfoRecorder <|-- PublishMgtBackupInfoRecorder
RepoInfoRecorder <|-- ConfigRepoInfoRecorder
```

**Diagram sources**
- [publish_ser.py](file://publish/publish_ser.py#L0-L999)

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L0-L999)

## 发布申请关键参数说明

发布申请包含多个关键参数，这些参数定义了发布的基本信息和约束条件。

### 核心参数定义

- **应用ID (app_name)**：标识要发布的应用，必须存在于系统中。
- **版本号 (iteration_id)**：指定要发布的迭代版本ID。
- **目标环境 (suite_code)**：发布的目标环境套代码，如"prod"、"beta"等。
- **节点列表 (node_list)**：要发布的具体节点IP地址列表。
- **操作类型 (op_type)**：发布的操作类型，如"deploy"、"update"等。

### 参数约束

- **应用ID约束**：必须是系统中已注册的应用名称。
- **版本号约束**：必须是有效的迭代ID，且该迭代处于可发布状态。
- **环境约束**：目标环境必须与应用的部署配置相匹配。
- **节点约束**：节点必须属于指定的环境套，且处于可用状态。

```mermaid
erDiagram
PUBLISH_APPLICATION {
string app_name PK
string iteration_id PK
string suite_code PK
string op_type
datetime create_time
string status
string operator
}
NODE_INFO {
string node_ip PK
string suite_code PK
string app_name PK
string status
datetime last_update
}
ENVIRONMENT_SUITE {
string suite_code PK
string suite_name
string region_group
boolean is_active
}
PUBLISH_APPLICATION ||--o{ NODE_INFO : "包含"
ENVIRONMENT_SUITE ||--o{ NODE_INFO : "管理"
PUBLISH_APPLICATION }|--|| ENVIRONMENT_SUITE : "发布到"
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L832-L862)
- [publish_ser.py](file://publish/publish_ser.py#L300-L350)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L832-L862)
- [publish_ser.py](file://publish/publish_ser.py#L300-L350)

## 发布申请状态机与迭代关联

发布申请的状态机管理了发布过程的各个状态，同时与迭代管理模块紧密关联。

### 状态机转换机制

发布申请的状态机包含以下主要状态：
- **待处理 (pending)**：发布申请已创建，等待执行。
- **处理中 (running)**：发布任务正在执行中。
- **成功 (success)**：发布任务成功完成。
- **失败 (failure)**：发布任务执行失败。
- **已回滚 (rollback)**：发布后已回滚到之前版本。

状态转换规则：
1. 待处理 → 处理中：当发布任务开始执行时。
2. 处理中 → 成功：当所有发布步骤都成功完成时。
3. 处理中 → 失败：当任何发布步骤失败时。
4. 成功 → 已回滚：当执行回滚操作时。

### 迭代管理关联

发布申请与迭代管理模块通过迭代ID进行关联。每个发布申请都必须关联到一个具体的迭代，系统会验证该迭代的状态是否允许发布。

```mermaid
stateDiagram-v2
[*] --> Pending
Pending --> Running : "开始发布"
Running --> Success : "所有步骤成功"
Running --> Failure : "任一步骤失败"
Success --> Rollback : "执行回滚"
Rollback --> Success : "回滚成功"
Failure --> Pending : "修复后重试"
note right of Running
包含多个子状态：
- 配置更新
- 代码部署
- 服务验证
end note
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1276-L1301)
- [publish_ser.py](file://publish/publish_ser.py#L800-L850)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1276-L1301)
- [publish_ser.py](file://publish/publish_ser.py#L800-L850)

## 发布申请示例代码

以下是不同类型发布申请的示例代码，展示了如何通过API提交发布请求。

### 正常发布示例

```python
import requests
import json

# 正常发布请求
normal_publish_data = {
    "op_type": "deploy",
    "iteration_id": "release-2023-10-01",
    "app_name": "web-service",
    "node_list": ["192.168.1.101", "192.168.1.102"],
    "suite_code": "prod"
}

response = requests.post(
    "http://api.example.com/publish",
    json=normal_publish_data,
    headers={"Content-Type": "application/json"}
)
print(response.json())
```

### 紧急发布示例

```python
# 紧急发布请求
emergency_publish_data = {
    "op_type": "deploy",
    "iteration_id": "hotfix-2023-10-01",
    "app_name": "payment-service",
    "node_list": ["192.168.2.201"],
    "suite_code": "prod",
    "emergency": True,
    "reason": "修复支付关键bug"
}

response = requests.post(
    "http://api.example.com/publish",
    json=emergency_publish_data,
    headers={"Content-Type": "application/json"}
)
print(response.json())
```

### 定时发布示例

```python
# 定时发布请求
scheduled_publish_data = {
    "op_type": "deploy",
    "iteration_id": "feature-2023-10-01",
    "app_name": "mobile-api",
    "node_list": ["192.168.3.301", "192.168.3.302"],
    "suite_code": "prod",
    "schedule_time": "2023-10-02T02:00:00Z",
    "timezone": "Asia/Shanghai"
}

response = requests.post(
    "http://api.example.com/publish",
    json=scheduled_publish_data,
    headers={"Content-Type": "application/json"}
)
print(response.json())
```

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L0-L1853)

## 故障排查指南

本部分提供发布申请过程中常见问题的排查方法和解决方案。

### 常见参数错误

| 错误类型 | 可能原因 | 解决方案 |
|---------|--------|--------|
| 参数不正确 | 缺少必要参数或参数格式错误 | 检查请求JSON格式，确保所有必填参数都已提供 |
| 应用不存在 | 应用ID在系统中未注册 | 确认应用名称拼写正确，或联系管理员注册应用 |
| 版本不存在 | 迭代ID无效或不存在 | 检查迭代ID是否正确，确认迭代已创建 |
| 节点不可用 | 节点IP不在指定环境中 | 验证节点IP和环境套的对应关系 |

### 权限不足问题

- **问题现象**：返回"权限不足"错误
- **可能原因**：
  - 用户没有对指定应用的发布权限
  - 用户所属团队没有发布权限
  - 应用在交易时间禁止发布
- **解决方案**：
  1. 检查用户权限配置
  2. 确认应用是否在交易时间限制列表中
  3. 联系管理员申请相应权限

### 系统级问题排查

```mermaid
flowchart TD
Start([问题发生]) --> IdentifyIssue["识别问题类型"]
IdentifyIssue --> IsParamError{"参数错误?"}
IsParamError --> |是| CheckParams["检查请求参数"]
IsParamError --> |否| IsPermissionError{"权限问题?"}
IsPermissionError --> |是| CheckPermission["检查用户权限"]
IsPermissionError --> |否| IsSystemError{"系统错误?"}
IsSystemError --> |是| CheckLogs["检查系统日志"]
IsSystemError --> |否| IsNetworkError{"网络问题?"}
IsNetworkError --> |是| CheckNetwork["检查网络连接"]
IsNetworkError --> |否| ContactSupport["联系技术支持"]
CheckParams --> VerifyParams["验证参数格式和值"]
VerifyParams --> FixParams["修正参数"]
FixParams --> TestAgain["重新测试"]
CheckPermission --> VerifyRole["验证用户角色"]
VerifyRole --> RequestPermission["申请权限"]
RequestPermission --> TestAgain
CheckLogs --> AnalyzeLogs["分析错误日志"]
AnalyzeLogs --> FixSystem["修复系统问题"]
FixSystem --> TestAgain
CheckNetwork --> TestConnection["测试网络连通性"]
TestConnection --> FixNetwork["修复网络问题"]
FixNetwork --> TestAgain
TestAgain --> Success{"成功?"}
Success --> |是| End([问题解决])
Success --> |否| LoopBack["返回问题识别"]
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L460-L478)
- [publish_ser.py](file://publish/publish_ser.py#L500-L550)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L460-L478)
- [publish_ser.py](file://publish/publish_ser.py#L500-L550)

## 最佳实践建议

为确保发布申请的顺利进行，建议遵循以下最佳实践。

### 请求参数最佳实践

1. **完整参数验证**：在发送请求前，确保所有必要参数都已正确填写。
2. **参数格式规范**：遵循API文档中定义的参数格式和数据类型。
3. **环境匹配**：确保应用、节点和环境套的配置相互匹配。

### 权限管理最佳实践

1. **最小权限原则**：只授予用户完成工作所需的最小权限。
2. **定期审查**：定期审查和更新用户权限配置。
3. **权限分离**：将发布权限与开发权限分离，提高安全性。

### 发布流程最佳实践

1. **预发布验证**：在正式发布前，先在测试环境进行验证。
2. **分批发布**：对于重要应用，采用分批发布策略，降低风险。
3. **监控与回滚**：发布后立即监控应用状态，准备快速回滚方案。

4. **文档记录**：详细记录每次发布的参数、时间和结果，便于追溯。

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L0-L1853)
- [publish_ser.py](file://publish/publish_ser.py#L0-L999)