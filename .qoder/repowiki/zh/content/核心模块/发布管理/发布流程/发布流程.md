# 发布流程

<cite>
**本文档引用的文件**
- [app_publish_views.py](file://publish/app_publish_views.py)
- [publish_ser.py](file://publish/publish_ser.py)
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py)
</cite>

## 目录
1. [发布流程概述](#发布流程概述)
2. [发布请求的接收与验证](#发布请求的接收与验证)
3. [发布任务的创建与状态管理](#发布任务的创建与状态管理)
4. [与迭代管理模块的集成](#与迭代管理模块的集成)
5. [发布审批流程设计](#发布审批流程设计)
6. [发布计划的生成逻辑](#发布计划的生成逻辑)
7. [发布类型使用示例](#发布类型使用示例)
8. [常见问题排查指南](#常见问题排查指南)

## 发布流程概述

发布流程涵盖了从发布申请到执行完成的完整生命周期。该流程通过多个模块协同工作，确保发布操作的安全性、可控性和可追溯性。核心组件包括发布请求处理、任务调度、状态管理以及与迭代管理系统的深度集成。

## 发布请求的接收与验证

发布请求的接收主要由 `app_publish_views.py` 中的视图类处理。`PublishInfoApi` 类负责查询发布申请数据，通过 `list` 方法获取指定迭代下的发布信息。该方法调用 `H5DeployStatusCollector.get_info` 收集发布状态，并结合 `PublishApplyInfoCollector.find_all_email` 获取邮件通知信息，最终将两者合并返回。

发布操作的验证机制包含多个层面：
- **环境检查**：通过 `get_node_ip_list` 查询应用在指定环境套下的节点IP列表，若未找到节点信息则拒绝发布。
- **并发控制**：使用 `H5DeployStatusCollector.check_is_running` 检查应用是否正在发布，防止重复发布。
- **交易时间限制**：通过 `publish_restrictions` 方法检查当前时间是否处于交易时段，若在交易时段内则禁止发布，除非应用在白名单中。

```mermaid
sequenceDiagram
participant 前端 as 前端
participant 视图 as PublishOperate
participant 任务队列 as TaskQueue
participant 数据库 as H5DeployResult
前端->>视图 : 提交发布请求
视图->>视图 : 验证参数
视图->>视图 : 检查应用是否正在发布
视图->>视图 : 查询节点IP列表
alt 节点列表为空
视图-->>前端 : 返回错误：未找到节点信息
else 节点列表有效
视图->>数据库 : 记录发布状态为RUNNING
视图->>任务队列 : 初始化任务队列
视图->>任务队列 : 添加检查任务
视图->>任务队列 : 添加发布任务
视图->>任务队列 : 异步执行
视图-->>前端 : 返回处理中，附带action_id
end
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L200-L300)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L100-L400)

## 发布任务的创建与状态管理

发布任务的创建与状态管理由 `publish_ser.py` 中的 `RepoInfoRecorder` 类和相关函数实现。`RepoInfoRecorder` 作为制品信息记录者，负责将发布制品信息记录到节点绑定表中。

发布任务的状态管理通过以下机制实现：
- **状态记录**：在 `H5DeployResult` 表中记录每个发布操作的状态，包括开始时间、结束时间、操作类型、操作用户等。
- **状态更新**：通过 `update_publish_status` 函数更新发布任务的状态，支持 `lock` 和 `unlock` 操作。
- **超时处理**：`update_publish_lock` 函数负责处理超时的发布任务，自动解锁被锁定的任务。

```mermaid
classDiagram
class RepoInfoRecorder {
+app_name : str
+iteration_id : str
+node_ip : str
+suite_code : str
+node_docker : str
+record_info(lib_repo_id : int)
+_get_repo_id(module_name : str, iteration_id : str, suite_code : str) : int
+_get_env_node_bind_id(module_name : str, node_ip : str, node_docker : str, suite_code : str) : list[int]
+_update_node_bind_lib_repo_info(node_bind_id_list : list[int], repo_info_id : int)
}
class PublishMgtBackupInfoRecorder {
+module_name : str
+node_ip : str
+backup_path : str
+salt_upd_log : str
+publish_user : str
+publish_iteration_id : str
+backup_desc : str
+node_docker : str
+suite_code : str
+opt_type : str
+record_info()
}
class ConfigRepoInfoRecorder {
+record_info()
}
RepoInfoRecorder <|-- PublishMgtBackupInfoRecorder
RepoInfoRecorder <|-- ConfigRepoInfoRecorder
```

**Diagram sources**
- [publish_ser.py](file://publish/publish_ser.py#L100-L300)

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L50-L500)

## 与迭代管理模块的集成

发布流程与迭代管理模块通过 `iter_mgt_ser.py` 中的函数实现深度集成。`get_iter_info` 函数用于获取用户有权限的迭代信息，支持按项目类型（server、h5、py）过滤。

发布与迭代的关联主要体现在：
- **迭代信息查询**：通过 `get_repos_info` 获取指定迭代下的仓库信息，包括应用名称、状态、包类型等。
- **发布策略获取**：`get_app_publish_strategy_list` 函数根据迭代ID、环境列表和应用列表查询发布策略，支持分步发布和并行发布。
- **版本信息同步**：`get_branch_version_by_module_name` 函数获取应用在不同分支下的版本信息，确保发布版本与迭代分支一致。

```mermaid
sequenceDiagram
participant 前端 as 前端
participant 发布视图 as PublishInfoApi
participant 迭代服务 as iter_mgt_ser
participant 数据库 as iter_mgt_iter_info
前端->>发布视图 : 请求迭代信息
发布视图->>迭代服务 : get_iter_info(user, project_type)
迭代服务->>数据库 : 查询迭代信息
数据库-->>迭代服务 : 返回迭代列表
迭代服务-->>发布视图 : 返回格式化数据
发布视图-->>前端 : 返回迭代信息
```

**Diagram sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L200-L300)

**Section sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L100-L400)

## 发布审批流程设计

发布审批流程设计支持多级审批和自动审批策略。审批状态存储在 `PublishApplyInfoCollector` 中，通过 `find_all_email` 方法获取所有审批记录。

审批流程的关键特性包括：
- **多级审批**：支持多个审批人依次审批，只有所有审批人都同意后才能进入发布阶段。
- **自动审批**：对于灰度环境发布，若当天已有生产环境发布申请并获得确认，则自动通过审批。
- **时效性检查**：审批确认时间必须在当天有效，过期的确认需要重新申请。

```mermaid
flowchart TD
Start([发布申请]) --> CheckEnv["检查发布环境"]
CheckEnv --> |生产环境| CheckApproval["检查审批状态"]
CheckEnv --> |灰度环境| CheckAutoApproval["检查自动审批条件"]
CheckApproval --> HasApproval{"已有审批?"}
HasApproval --> |是| CheckTime["检查确认时间"]
HasApproval --> |否| ReturnNotApply["返回未申请"]
CheckTime --> IsToday{"确认时间是今天?"}
IsToday --> |是| ReturnAgree["返回同意"]
IsToday --> |否| ReturnExpired["返回已过期"]
CheckAutoApproval --> HasProdApproval{"已有生产审批?"}
HasProdApproval --> |是| ReturnAgree
HasProdApproval --> |否| ReturnNotApply
```

**Diagram sources**
- [publish_check.py](file://publish/publish_check.py#L100-L150)

**Section sources**
- [publish_check.py](file://publish/publish_check.py#L100-L150)

## 发布计划的生成逻辑

发布计划的生成逻辑由 `iter_mgt/publish_plan/model/iter_mgt_publish_plan.py` 中的 `IterMgtPublishPlan` 模型定义。该模型包含计划批次号、模块名称、分支名称、迭代ID、计划类型、计划状态、时间窗口等字段。

关键环节包括：
- **时间窗口**：通过 `start_time` 和 `end_time` 字段定义发布计划的时间窗口，确保发布在指定时间段内执行。
- **资源预检**：在计划生成时，通过 `get_ip_list_by_suite_code` 预先检查目标环境的节点资源，确保有足够的节点可供发布。
- **计划状态管理**：支持 `plan_status` 字段跟踪计划的执行状态，如待执行、执行中、已完成、已取消等。

```mermaid
erDiagram
ITER_MGT_PUBLISH_PLAN {
bigint id PK
varchar batch_no
varchar module_name
varchar branch_name
varchar iter_id
varchar plan_type
varchar plan_status
datetime start_time
datetime end_time
varchar create_user
datetime create_time
varchar update_user
datetime update_time
bigint stamp
}
```

**Diagram sources**
- [iter_mgt/publish_plan/model/iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L24)

**Section sources**
- [iter_mgt/publish_plan/model/iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L24)

## 发布类型使用示例

### 正常发布
正常发布流程需要完整的审批流程。用户在前端选择迭代和应用，系统检查审批状态，通过后进入发布执行阶段。

### 紧急发布
紧急发布通过 `opt_user` 参数指定操作人，绕过部分检查。但仍需检查应用是否正在发布，确保不会并发操作。

### 定时发布
定时发布通过 `IterMgtPublishPlan` 模型的 `start_time` 字段实现。系统在到达指定时间后自动触发发布任务，无需人工干预。

```mermaid
sequenceDiagram
participant 用户 as 用户
participant 前端 as 前端
participant 发布服务 as PublishOperate
participant 任务队列 as TaskQueue
用户->>前端 : 提交定时发布申请
前端->>发布服务 : create定时发布请求
发布服务->>发布服务 : 验证参数
发布服务->>发布服务 : 创建定时任务
发布服务->>任务队列 : 添加定时触发器
任务队列->>任务队列 : 等待到达start_time
任务队列->>发布服务 : 触发发布
发布服务->>任务队列 : 执行发布任务
发布服务-->>用户 : 返回发布结果
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L800-L900)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L800-L900)

## 常见问题排查指南

### 审批阻塞
当发布被阻塞时，首先检查审批状态：
1. 确认发布申请是否已提交
2. 检查审批人是否已同意
3. 验证确认时间是否在有效期内

使用以下SQL查询审批状态：
```sql
SELECT * FROM publish_apply_email_info 
WHERE pipeline_id = '指定迭代ID' 
ORDER BY create_time DESC;
```

### 状态不一致
当发布状态显示不一致时，检查以下方面：
1. 查看 `H5DeployResult` 表中的最新记录
2. 检查 `iter_mgt_jenkins_publish_pipeline_info` 中的锁状态
3. 验证任务队列是否正常执行

使用以下函数修复状态：
- `update_publish_lock`：解锁超时的发布任务
- `update_publish_status`：手动更新发布状态

```mermaid
flowchart TD
Problem([问题诊断]) --> CheckApproval["检查审批状态"]
Problem --> CheckStatus["检查发布状态"]
Problem --> CheckLock["检查任务锁"]
CheckApproval --> |阻塞| ResolveApproval["联系审批人"]
CheckStatus --> |不一致| ResolveStatus["手动更新状态"]
CheckLock --> |锁定| ResolveLock["调用update_publish_lock"]
ResolveApproval --> Done([问题解决])
ResolveStatus --> Done
ResolveLock --> Done
```

**Diagram sources**
- [publish_ser.py](file://publish/publish_ser.py#L700-L800)

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L700-L800)