# 手动回滚

<cite>
**本文档引用的文件**  
- [publish_mgt_ser.py](file://publish_mgt/publish_mgt_ser.py)
- [app_publish_views.py](file://publish/app_publish_views.py)
- [roll_back_view.py](file://iter_mgt/roll_back_view.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [回滚操作流程](#回滚操作流程)
4. [API请求处理机制](#api请求处理机制)
5. [发布历史集成](#发布历史集成)
6. [回滚模式与适用场景](#回滚模式与适用场景)
7. [使用示例](#使用示例)
8. [权限控制与操作审计](#权限控制与操作审计)
9. [并发冲突解决方案](#并发冲突解决方案)
10. [最佳实践](#最佳实践)

## 简介
本文档详细阐述了系统中手动回滚功能的实现机制，重点介绍运维人员主动触发的恢复操作流程。文档覆盖了从版本选择、回滚确认到执行调度的核心逻辑，分析了API接口的请求处理机制，并描述了与发布历史模块的集成方式。同时，文档还阐述了全量回滚、部分服务回滚和灰度回滚等不同操作模式的适用场景，提供了常规版本回滚、紧急故障恢复和配置错误修正等场景的使用示例。最后，文档针对权限控制、操作审计和并发冲突等常见问题提供了详细的解决方案和最佳实践。

## 核心组件
本系统中的手动回滚功能主要由三个核心组件构成：`publish_mgt_ser.py` 负责回滚相关的业务逻辑处理，`app_publish_views.py` 处理前端API请求，`roll_back_view.py` 提供回滚操作的视图接口。这些组件协同工作，实现了完整的回滚流程。

**Section sources**
- [publish_mgt_ser.py](file://publish_mgt/publish_mgt_ser.py#L1-L305)
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L1853)
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L1-L106)

## 回滚操作流程
手动回滚的核心逻辑主要在 `publish_mgt_ser.py` 文件中实现。该文件中的函数负责处理版本选择、回滚确认和执行调度等关键步骤。当运维人员发起回滚请求时，系统首先通过 `get_last_can_rollback_info` 函数获取最近可回滚的制品信息，然后通过 `get_lib_publish_info` 获取发布详情，最后将这些信息合并后返回给前端。

```mermaid
flowchart TD
Start([开始]) --> GetRollbackInfo["获取可回滚信息<br/>get_last_can_rollback_info"]
GetRollbackInfo --> CheckExistence{"信息存在?"}
CheckExistence --> |否| ReturnEmpty["返回空结果"]
CheckExistence --> |是| MergeInfo["合并发布信息<br/>get_lib_publish_info"]
MergeInfo --> HandleIteration["处理迭代分支情况"]
HandleIteration --> AddConfigDiff["添加配置差异信息"]
AddConfigDiff --> ReturnResult["返回回滚信息"]
ReturnEmpty --> End([结束])
ReturnResult --> End
```

**Diagram sources**
- [publish_mgt_ser.py](file://publish_mgt/publish_mgt_ser.py#L1-L305)

**Section sources**
- [publish_mgt_ser.py](file://publish_mgt/publish_mgt_ser.py#L1-L305)

## API请求处理机制
`app_publish_views.py` 文件中的 `RollBackView` 类负责处理手动回滚的API请求。该类继承自 `viewsets.ViewSet`，使用JWT认证和身份验证确保只有授权用户才能执行回滚操作。当收到回滚请求时，系统首先检查当前版本是否已经在回滚中，以防止重复提交。然后通过 `ExternalService` 调用外部服务执行实际的回滚操作。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant API as RollBackView
participant 服务 as ExternalService
participant 数据库 as 数据库
前端->>API : POST /rollback_api
API->>API : JWT认证
API->>API : 检查回滚状态
API->>数据库 : 查询PublishMgtRollbackInfo
数据库-->>API : 返回结果
API->>API : 验证是否重复回滚
API->>服务 : 调用rollback_apply服务
服务-->>API : 返回执行结果
API-->>前端 : 返回响应
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L1853)

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L1853)

## 发布历史集成
系统通过 `roll_back_view.py` 文件实现了与发布历史模块的集成。当执行回滚操作时，系统会创建或更新 `PublishMgtRollbackInfo` 记录，该记录存储在数据库的 `publish_mgt_rollback_info` 表中。该表包含应用名、回滚时间、回滚状态、回滚分支名称和回滚制品commit id等关键字段。通过这种方式，系统能够完整地追踪每次回滚操作的历史记录。

```mermaid
erDiagram
PUBLISH_MGT_ROLLBACK_INFO {
bigint id PK
varchar module_name
datetime rollback_date
varchar rollback_status
varchar branch_name
varchar lib_repo_version
}
```

**Diagram sources**
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L1-L106)
- [models.py](file://publish/models.py#L368-L382)

**Section sources**
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L1-L106)

## 回滚模式与适用场景
系统支持多种回滚模式，包括全量回滚、部分服务回滚和灰度回滚。全量回滚适用于重大故障需要立即恢复到稳定版本的场景；部分服务回滚适用于特定服务出现问题而其他服务正常运行的场景；灰度回滚则适用于需要逐步验证回滚效果的场景。不同的回滚模式通过参数配置来实现，运维人员可以根据实际情况选择合适的回滚策略。

## 使用示例
### 常规版本回滚
当新版本发布后发现非关键性问题时，可以执行常规版本回滚。运维人员通过前端界面选择需要回滚的版本，系统会自动执行回滚流程，并记录操作日志。

### 紧急故障恢复
当生产环境出现严重故障影响业务时，可以执行紧急故障恢复。此时应选择全量回滚模式，快速将所有服务恢复到上一个稳定版本。

### 配置错误修正
当发现配置文件错误导致服务异常时，可以执行配置错误修正回滚。这种情况下通常只需要回滚配置文件，而不需要回滚代码版本。

## 权限控制与操作审计
系统通过JWT认证和Django权限系统实现了严格的权限控制。只有具有相应权限的用户才能执行回滚操作。同时，系统通过 `OpLogs` 模块记录所有操作日志，包括操作人、操作时间、操作类型和操作结果等信息，实现了完整的操作审计功能。

## 并发冲突解决方案
为了解决并发冲突问题，系统在 `EndRollBackView` 中使用了数据库事务和行级锁。通过 `select_for_update()` 方法锁定相关记录，确保在同一时间只有一个操作能够修改回滚状态。此外，系统还通过检查 `rollback_status` 字段来防止重复提交回滚请求。

```mermaid
flowchart TD
Start([开始]) --> BeginTransaction["开始事务"]
BeginTransaction --> LockRecords["锁定BranchIncludeSys记录"]
LockRecords --> UpdateStatus["更新sys_status"]
UpdateStatus --> LockRollback["锁定PublishMgtRollbackInfo"]
LockRollback --> UpdateRollback["更新回滚状态"]
UpdateRollback --> Commit["提交事务"]
Commit --> End([结束])
Exception["异常处理"] --> Rollback["回滚事务"]
Rollback --> End
```

**Diagram sources**
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L73-L104)

**Section sources**
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L73-L104)

## 最佳实践
1. 在执行回滚操作前，务必确认回滚版本的稳定性
2. 对于重大变更，建议先在测试环境验证回滚流程
3. 回滚操作应尽量安排在业务低峰期进行
4. 执行回滚后，应及时通知相关团队并监控系统状态
5. 定期审查回滚日志，分析回滚原因，优化发布流程