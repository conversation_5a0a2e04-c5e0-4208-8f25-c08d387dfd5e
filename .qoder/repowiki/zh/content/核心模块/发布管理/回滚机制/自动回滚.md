# 自动回滚

<cite>
**本文档引用的文件**
- [publish_ser.py](file://publish/publish_ser.py)
- [app_publish_views.py](file://publish/app_publish_views.py)
- [models.py](file://publish/models.py)
- [roll_back_view.py](file://iter_mgt/roll_back_view.py)
</cite>

## 目录
1. [引言](#引言)
2. [核心组件](#核心组件)
3. [自动回滚触发机制](#自动回滚触发机制)
4. [监控系统集成与性能指标检测](#监控系统集成与性能指标检测)
5. [配置参数与执行策略](#配置参数与执行策略)
6. [使用示例](#使用示例)
7. [故障排查与优化建议](#故障排查与优化建议)
8. [结论](#结论)

## 引言
本文档详细阐述了基于预设条件的自动回滚机制，重点介绍在服务健康检查失败、性能指标异常或CI/CD流水线测试失败等场景下的自动恢复逻辑。通过分析 `publish_ser.py` 和 `app_publish_views.py` 中的核心实现，说明系统如何监控关键指标、判定健康状态并触发回滚操作。同时，文档描述了与监控系统的集成方式、配置参数设置、执行策略以及针对误触发和回滚风暴等问题的优化建议。

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L0-L799)
- [app_publish_views.py](file://publish/app_publish_views.py#L0-L799)

## 核心组件

本系统的核心组件包括用于处理自动回滚请求的 `app_publish_views.py` 和负责监控指标检测与回滚逻辑的 `publish_ser.py`。`PublishMgtRollbackInfo` 模型定义了回滚信息的数据结构，包含应用名、回滚时间、状态、分支名称和制品版本等字段。`RollbackStatus` 枚举类型定义了“running”和“end”两种状态，用于跟踪回滚过程。

**Section sources**
- [models.py](file://publish/models.py#L356-L382)
- [publish_ser.py](file://publish/publish_ser.py#L0-L799)

## 自动回滚触发机制

自动回滚的触发机制主要依赖于对服务健康状况和发布流程的实时监控。当检测到健康检查失败或CI/CD流水线测试未通过时，系统会自动启动回滚流程。

在 `app_publish_views.py` 中，`MultiNodeOperateApi` 类的 `exec_publish` 方法是核心处理逻辑。该方法首先检查当前是否处于交易时间，若处于交易时间且应用未在白名单中，则会拦截发布操作。接着，它会调用 `get_publishing_app_list` 函数查询是否有应用正在发布，以避免冲突。

```mermaid
sequenceDiagram
participant 用户
participant MultiNodeOperateApi
participant ExternalService
participant PublishMgtRollbackInfo
用户->>MultiNodeOperateApi : 发起回滚请求
MultiNodeOperateApi->>MultiNodeOperateApi : 检查交易时间与白名单
MultiNodeOperateApi->>MultiNodeOperateApi : 查询正在发布的应用
alt 无冲突
MultiNodeOperateApi->>ExternalService : 调用外部服务执行回滚
ExternalService-->>MultiNodeOperateApi : 返回执行结果
MultiNodeOperateApi->>PublishMgtRollbackInfo : 记录回滚信息
PublishMgtRollbackInfo-->>MultiNodeOperateApi : 确认记录成功
MultiNodeOperateApi-->>用户 : 返回成功响应
else 存在冲突
MultiNodeOperateApi-->>用户 : 返回失败响应
end
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1000-L1500)
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L41-L71)

## 监控系统集成与性能指标检测

系统通过集成外部监控服务来检测服务的健康状况和性能指标。在 `app_publish_views.py` 的 `MultiNodeOperateApi` 类中，通过 `ExternalService` 调用外部服务进行健康检查和配置一致性验证。

例如，在执行发布或回滚操作前，系统会通过 `task_queue.enter_queue(TaskTypeObject.interface, "check_config_consistent", ...)` 调用 `check_config_consistent` 接口来检查配置的一致性。此外，还会调用 `check_config_sync` 来检测配置是否为空，确保服务的正常运行。

实时性能指标（如错误率、响应时间）的检测主要依赖于外部监控系统。当这些指标超过预设阈值时，监控系统会触发告警，并可能通过API调用本系统的回滚接口。`get_last_verify_result` 函数用于获取最近一次的服务验证结果，包括请求状态码和响应内容，为健康检查提供数据支持。

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L331-L354)
- [app_publish_views.py](file://publish/app_publish_views.py#L1813-L1851)

## 配置参数与执行策略

自动回滚的配置参数主要包括触发阈值、白名单设置和交易时间窗口。这些参数通过数据库表和配置文件进行管理。

- **触发阈值**：通过 `check_mgt_check_switch` 表中的开关控制，例如 `check_trade_time` 开关用于控制是否在交易时间内禁止发布。
- **白名单**：通过 `get_app_or_group_write_list` 函数查询应用或组的白名单状态，允许特定应用在交易时间内发布。
- **交易时间**：在 `settings.py` 中通过 `TRADE_TIME` 配置项定义，如 `trade_start_time` 和 `trade_end_time`。

执行策略方面，系统采用异步任务队列（`TaskQueue`）来管理回滚操作。回滚任务被放入队列后，由后台工作进程异步执行，确保主流程的响应速度。同时，系统使用数据库事务（`transaction.atomic()`）来保证回滚操作的原子性，防止出现部分成功的情况。

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L800-L1000)
- [db/分支2_x/2.36.0/check_switch.sql](file://db/分支2_x/2.36.0/check_switch.sql#L0-L12)

## 使用示例

以下是基于不同场景的自动回滚使用示例：

### CI/CD流水线集成测试失败
当CI/CD流水线中的集成测试失败时，系统会自动触发回滚。`AppAutoTestCheckResult` 类负责检查自动化测试结果，若测试未通过，则阻止发布并记录失败原因。

### 服务健康检查失败
当服务的健康检查接口返回非200状态码时，监控系统会触发告警。告警系统调用本系统的回滚API，`ExternalService` 接收请求并启动回滚流程。

### 性能指标异常
当服务的错误率超过5%或平均响应时间超过1秒时，监控系统会触发告警。告警系统通过API调用本系统的回滚接口，执行回滚操作。

```mermaid
flowchart TD
Start([开始]) --> CheckTestResult["检查CI/CD测试结果"]
CheckTestResult --> TestFailed{"测试失败?"}
TestFailed --> |是| TriggerRollback["触发回滚"]
TestFailed --> |否| CheckHealth["检查服务健康"]
CheckHealth --> HealthFailed{"健康检查失败?"}
HealthFailed --> |是| TriggerRollback
HealthFailed --> |否| CheckPerformance["检查性能指标"]
CheckPerformance --> PerfAbnormal{"指标异常?"}
PerfAbnormal --> |是| TriggerRollback
PerfAbnormal --> |否| NoAction["无需操作"]
TriggerRollback --> End([结束])
NoAction --> End
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1813-L1851)
- [iter_mgt/roll_back_view.py](file://iter_mgt/roll_back_view.py#L73-L104)

## 故障排查与优化建议

### 常见问题
- **误触发**：由于监控系统误报或阈值设置过低，可能导致不必要的回滚。
- **回滚风暴**：在短时间内频繁触发回滚，可能导致服务不稳定。

### 故障排查步骤
1. 检查 `publish_mgt_rollback_info` 表，确认回滚记录的 `rollback_status` 和 `rollback_date`。
2. 查看 `action_record` 表，追踪回滚操作的发起者和时间。
3. 检查 `task_mgt_deploy_result` 表，确认最近的发布和回滚任务状态。
4. 审查 `ExternalService` 的调用日志，确认外部服务的响应。

### 配置优化建议
- **调整阈值**：根据历史数据合理设置错误率和响应时间的阈值，避免误触发。
- **增加确认机制**：在触发回滚前，增加人工确认或二次验证步骤。
- **限制频率**：设置回滚操作的最小间隔时间，防止回滚风暴。
- **完善日志**：增强日志记录，便于事后分析和问题定位。

**Section sources**
- [models.py](file://publish/models.py#L368-L382)
- [roll_back_view.py](file://iter_mgt/roll_back_view.py#L73-L104)

## 结论
本文档详细介绍了自动回滚机制的实现原理和配置方法。通过集成监控系统和CI/CD流水线，系统能够在服务出现异常时自动恢复到稳定状态，提高了系统的可靠性和可用性。合理的配置参数和执行策略是确保自动回滚有效运行的关键。未来可进一步优化误触发和回滚风暴等问题，提升系统的智能化水平。