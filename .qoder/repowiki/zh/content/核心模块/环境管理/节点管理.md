# 节点管理

<cite>
**本文档引用的文件**   
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py)
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py)
- [models.py](file://env_mgt/models.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
</cite>

## 目录
1. [引言](#引言)
2. [EnvNode模型设计](#envnode模型设计)
3. [节点生命周期管理](#节点生命周期管理)
4. [服务层处理流程](#服务层处理流程)
5. [节点批量导入与状态同步](#节点批量导入与状态同步)
6. [SaltStack集成与远程指令执行](#saltstack集成与远程指令执行)
7. [常见问题排查与恢复](#常见问题排查与恢复)
8. [结论](#结论)

## 引言

节点管理模块是系统基础设施管理的核心组件，负责节点的注册、绑定、解绑和状态监控等关键功能。该模块通过与SaltStack集成，实现了对节点的远程指令执行和自动化管理。本文档详细介绍了EnvNode模型的字段设计、节点生命周期管理的实现逻辑、服务层处理流程，以及节点与SaltStack的集成方式。

## EnvNode模型设计

EnvNode模型是节点管理模块的核心数据结构，定义了节点的标识、可用区归属、资源规格和健康状态等核心属性。

### 节点信息表 (env_mgt_node)

节点信息表存储了节点的基本信息，包括节点名、IP地址、minion_id、可用域ID、操作系统、节点状态等。

```mermaid
erDiagram
env_mgt_node {
BIGINT id PK
VARCHAR node_name
VARCHAR node_ip
VARCHAR minion_id
BIGINT region_id FK
VARCHAR node_os
TINYINT node_status
DATETIME node_recyled
VARCHAR node_desc
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
}
```

**图示来源**
- [models.py](file://env_mgt/models.py#L100-L120)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L100-L120)

### 节点绑定表 (env_mgt_node_bind)

节点绑定表存储了节点与应用模块的绑定关系，包括模块名、环境套ID、节点ID、端口号、部署路径等。

```mermaid
erDiagram
env_mgt_node_bind {
BIGINT id PK
VARCHAR module_name
BIGINT suite_id FK
BIGINT node_id FK
BIGINT node_port
VARCHAR node_docker
VARCHAR node_bind_desc
BIGINT deploy_group
TINYINT deploy_type
VARCHAR deploy_path
VARCHAR health_check_url
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
}
```

**图示来源**
- [models.py](file://env_mgt/models.py#L122-L142)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L122-L142)

### 可用区表 (env_mgt_zone)

可用区表存储了可用区的编码、名称、说明、是否可用等信息。

```mermaid
erDiagram
env_mgt_zone {
VARCHAR zone_code PK
VARCHAR zone_name
VARCHAR zone_desc
TINYINT zone_is_active
VARCHAR cmdb_provider_code
VARCHAR cmdb_provider_name
VARCHAR cmdb_region_code
VARCHAR cmdb_region_name
VARCHAR cmdb_zone_code
VARCHAR cmdb_zone_name
VARCHAR cmdb_env_code
VARCHAR cmdb_env_name
VARCHAR bread_domain_code
VARCHAR bread_domain_name
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
}
```

**图示来源**
- [models.py](file://env_mgt/models.py#L250-L270)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L250-L270)

## 节点生命周期管理

节点生命周期管理模块负责节点的注册、绑定、解绑和状态监控等操作。

### 节点注册

节点注册通过`NodeApplyOrderApi`类的`create`方法实现。该方法接收节点申请订单信息，生成订单编号，并将订单信息保存到数据库中。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant API as NodeApplyOrderApi
participant 数据库 as 数据库
前端->>API : 提交节点申请订单
API->>API : 生成订单编号
API->>数据库 : 保存订单信息
数据库-->>API : 保存成功
API-->>前端 : 返回订单编号
```

**图示来源**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L150-L200)

### 节点绑定

节点绑定通过`NodeApplyResult`类的`create`方法实现。该方法接收节点开通后的回调信息，创建节点和绑定关系。

```mermaid
sequenceDiagram
participant 面包系统 as 面包系统
participant API as NodeApplyResult
participant 数据库 as 数据库
面包系统->>API : 节点开通回调
API->>API : 解析节点信息
API->>数据库 : 创建节点
数据库-->>API : 节点创建成功
API->>数据库 : 创建绑定关系
数据库-->>API : 绑定关系创建成功
API-->>面包系统 : 处理成功
```

**图示来源**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L400-L450)

### 节点解绑

节点解绑通过`NodeRecycleOrderApi`类的`create`方法实现。该方法接收节点回收单信息，创建回收批次和回收订单。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant API as NodeRecycleOrderApi
participant 数据库 as 数据库
前端->>API : 提交节点回收单
API->>API : 生成回收批次号
API->>数据库 : 创建回收批次
数据库-->>API : 回收批次创建成功
API->>数据库 : 创建回收订单
数据库-->>API : 回收订单创建成功
API-->>前端 : 返回回收批次号
```

**图示来源**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L500-L550)

### 状态监控

状态监控通过`EnvNodeMgtApi`类的`list`方法实现。该方法查询所有产线节点的状态信息。

```mermaid
sequenceDiagram
participant 前端 as 前端应用
participant API as EnvNodeMgtApi
participant 数据库 as 数据库
前端->>API : 获取节点列表
API->>数据库 : 查询节点信息
数据库-->>API : 返回节点信息
API-->>前端 : 返回节点列表
```

**图示来源**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L50-L70)

## 服务层处理流程

服务层处理流程主要由`env_node_mgt_ser.py`文件中的函数实现，负责与数据库交互和业务逻辑处理。

### 获取产线节点

`get_prod_nodes`函数查询所有产线节点的信息。

```python
def get_prod_nodes(*args, **kwargs):
    search_sql = '''
            SELECT
            	mn.id, mn.node_ip, mn.minion_id, mn.region_id, nb.module_name
            FROM
	            env_mgt_node mn
	        left outer join
	            env_mgt_node_bind nb
	        on mn.id = nb.node_id and nb.enable_bind = '1'
            where mn.node_status = '0' and mn.node_ip like '10%'
    '''
    cursor = connection.cursor()
    cursor.execute(search_sql)
    return cursor
```

**代码来源**
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py#L10-L20)

### 获取VM信息

`get_vm_info_by_zone_code`函数根据可用区编码查询VM信息。

```python
def get_vm_info_by_zone_code(zone_code, zone_is_active):
    sql = '''
        select z.zone_code, z.relative_region_code, z.zone_name, b.vm_id, 
        v.vm_name, v.vm_cpu, v.vm_memory, v.vm_disk as vm_disk_display, 
		c11.code_value as vm_disk,c11.code_name as vm_disk_name, 
		c12.code_name as vm_template_name, c13.code_name as vm_conf_name
        from node_apply_zone z
        left join node_apply_vm_bind b on b.zone_id = z.id and b.bind_is_active = true
        left join node_apply_vm_type v on v.id = b.vm_id
        left join node_apply_code c11 on v.vm_disk_code = c11.code_value and c11.code_type = 11
        left join node_apply_code c12 on v.vm_template_code = c12.code_value and c12.code_type = 12
        left join node_apply_code c13 on v.vm_conf_code = c13.code_value and c13.code_type = 13
        where z.zone_is_active = '{}'
        and z.zone_code = '{}';
        '''.format(zone_is_active, zone_code)

    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor
```

**代码来源**
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py#L22-L40)

### 获取应用团队信息

`get_app_team_name_and_alias`函数根据模块名获取应用团队信息。

```python
def get_app_team_name_and_alias(module_name):
    sql = '''
        select i.cmdb_team_name, i.team_alias from tool_mgt_team_info i
        left join app_mgt_app_team t on i.id = t.team_id
        left join app_mgt_app_module m on m.app_id = t.app_id
        where m.module_name = '{}'
        '''.format(module_name)
    cursor = connection.cursor()
    cursor.execute(sql)
    team_name = None
    team_alias = None
    for row in cursor.fetchall():
        team_name = row[0]
        team_alias = row[1]
        return team_name, team_alias
    return team_name, team_alias
```

**代码来源**
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py#L42-L55)

## 节点批量导入与状态同步

节点批量导入与状态同步功能通过`NodeApplyOrderApi`和`NodeRecycleOrderApi`类实现。

### 批量导入

批量导入功能允许用户一次性导入多个节点。通过`NodeApplyOrderApi`的`create`方法，系统会为每个节点生成唯一的订单编号，并保存到数据库中。

### 状态同步

状态同步功能通过定时任务或事件驱动的方式，从CMDB系统同步节点状态。`EnvNodeMgtApi`的`list`方法提供了获取节点状态的接口，前端应用可以定期调用该接口获取最新的节点状态。

## SaltStack集成与远程指令执行

节点管理模块通过SaltStack实现远程指令执行和配置管理。

### SaltStack集成

系统通过`task_mgt.http_task.HttpTask`类与SaltStack API进行交互。在`spider/settings.py`中配置了不同环境的Salt API密码。

```python
SALT_API_PASSWORD = {
    'prod': local_settings.get('SALT_API_PASSWORD', 'prod'),
    'zb': local_settings.get('SALT_API_PASSWORD', 'zb'),
    'ucloud-prod': local_settings.get('SALT_API_PASSWORD', 'ucloud-prod'),
    'hk-prod': local_settings.get('SALT_API_PASSWORD', 'hk-prod'),
    'pd-prod': local_settings.get('SALT_API_PASSWORD', 'pd-prod'),
    'bs-zb': local_settings.get('SALT_API_PASSWORD', 'bs-zb'),
    'ucloud-hd': local_settings.get('SALT_API_PASSWORD', 'ucloud-hd'),
    'wgq-hd': local_settings.get('SALT_API_PASSWORD', 'wgq-hd'),
    'bs-hd': local_settings.get('SALT_API_PASSWORD', 'bs-hd'),
    'bs-uat': local_settings.get('SALT_API_PASSWORD', 'bs-uat'),
    'beta': local_settings.get('SALT_API_PASSWORD', 'zb'),
    'vph': local_settings.get('SALT_API_PASSWORD', 'hk-prod'),
    'pre': local_settings.get('SALT_API_PASSWORD', 'hk-prod'),
    'vps': local_settings.get('SALT_API_PASSWORD', 'hk-prod'),
    'bs-prod': local_settings.get('SALT_API_PASSWORD', 'bs-prod'),
    'tcloud-prod': local_settings.get('SALT_API_PASSWORD', 'tcloud-prod'),
    'wgq-zb': local_settings.get('SALT_API_PASSWORD', 'wgq-zb'),
}
```

**代码来源**
- [settings.py](file://spider/settings.py#L439-L459)

### 远程指令执行

远程指令执行通过`publish.models.SaltExecCmd`模型存储自定义命令，并通过minion_id执行。

```python
class SaltExecCmd(models.Model):
    salt_func = models.CharField(verbose_name='salt方法', max_length=24, default='cmd.run')
    app_name = models.CharField(verbose_name='应用名', max_length=64, default='')
    exec_cmd = models.CharField(verbose_name='定制命令', max_length=2048)
    operate_type = models.CharField(verbose_name='操作类型', max_length=16)
    minion_id = models.CharField(verbose_name='minion_id', max_length=64)
    suite_code = models.CharField(verbose_name='环境套编码', max_length=100)

    class Meta:
        db_table = 'publish_salt_exec_cmd'
        verbose_name = 'salt自定义命令'
```

**代码来源**
- [models.py](file://publish/models.py#L175-L190)

## 常见问题排查与恢复

### 节点资源不足

当节点资源不足时，可以通过以下步骤进行排查和恢复：

1. 检查节点的CPU、内存和磁盘使用情况。
2. 查看是否有异常进程占用资源。
3. 考虑扩容或迁移部分服务到其他节点。

### 心跳失联

当节点心跳失联时，可以按照以下步骤进行排查：

1. 检查网络连接是否正常。
2. 验证Salt minion服务是否正常运行。
3. 检查防火墙设置，确保Salt通信端口开放。
4. 重启Salt minion服务。

### 节点状态异常

当节点状态异常时，可以通过执行以下SQL语句更新节点状态：

```sql
update env_mgt_node set node_status = 3 where node_ip ='************' and node_name ='EHB00002';
```

**代码来源**
- [更新节点状态，数据来源李拓.sql](file://db/分支2_x/2.39.0/更新节点状态，数据来源李拓.sql#L13)

## 结论

节点管理模块通过完善的模型设计和清晰的处理流程，实现了对节点的全生命周期管理。通过与SaltStack的深度集成，系统能够高效地执行远程指令和管理节点配置。对于常见的节点问题，系统提供了有效的排查和恢复方案，确保了基础设施的稳定运行。