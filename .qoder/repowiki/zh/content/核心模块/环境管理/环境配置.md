# 环境配置

<cite>
**本文档引用的文件**
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [env_info_ser.py](file://env_mgt/env_info_ser.py)
- [models.py](file://env_mgt/models.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
</cite>

## 目录
1. [项目结构](#项目结构)
2. [核心组件](#核心组件)
3. [环境模型核心字段](#环境模型核心字段)
4. [环境创建与销毁主流程](#环境创建与销毁主流程)
5. [配置信息查询与更新服务逻辑](#配置信息查询与更新服务逻辑)
6. [环境变量管理与配置分发](#环境变量管理与配置分发)
7. [环境与迭代、应用的映射关系](#环境与迭代应用的映射关系)
8. [H5和Remote一体化改造配置适配](#h5和remote一体化改造配置适配)
9. [常见问题预防与修复](#常见问题预防与修复)

## 项目结构

```mermaid
graph TD
env_mgt[env_mgt] --> models[models.py]
env_mgt --> env_mgt_py[env_mgt.py]
env_mgt --> env_info_ser_py[env_info_ser.py]
env_mgt --> db[db/schema]
db --> schema[01-env_mgt_schema.sql]
```

**Diagram sources**
- [models.py](file://env_mgt/models.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [env_info_ser.py](file://env_mgt/env_info_ser.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)

**Section sources**
- [models.py](file://env_mgt/models.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [env_info_ser.py](file://env_mgt/env_info_ser.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)

## 核心组件

环境管理模块(env_mgt)是系统中负责环境配置管理的核心组件，主要包含环境模型定义、环境创建与销毁流程、配置信息查询与更新服务等关键功能。该模块通过Django框架实现RESTful API接口，提供环境配置的增删改查服务。

**Section sources**
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [models.py](file://env_mgt/models.py)

## 环境模型核心字段

环境管理模块定义了多个核心数据模型，主要包括Region(可用域)、Suite(环境套)、Node(节点)和NodeBind(节点绑定)等。

### Region(可用域)模型
```mermaid
classDiagram
class Region {
+addr_name : str
+addr_short_name : str
+type_name : str
+type_short_name : str
+region_name : str
+region_desc : str
+region_is_active : bool
+create_user : str
+create_time : datetime
+update_user : str
+update_time : datetime
+stamp : int
}
```

**Diagram sources**
- [models.py](file://env_mgt/models.py#L1-L50)

### Suite(环境套)模型
```mermaid
classDiagram
class Suite {
+id : int
+region_id : int
+suite_code : str
+suite_name : str
+suite_desc : str
+suite_is_active : bool
+support_docker : bool
+create_user : str
+create_time : datetime
+update_user : str
+update_time : datetime
+stamp : int
+node_bind_level : int
+suite_group : str
}
```

**Diagram sources**
- [models.py](file://env_mgt/models.py#L52-L100)

### Node(节点)模型
```mermaid
classDiagram
class Node {
+node_name : str
+node_ip : str
+minion_id : str
+region_id : int
+node_os : str
+node_status : int
+node_recyled : datetime
+node_desc : str
+create_user : str
+create_time : datetime
+update_user : str
+update_time : datetime
+stamp : int
+tomcat_password : str
+apply_order_code : str
}
```

**Diagram sources**
- [models.py](file://env_mgt/models.py#L152-L200)

### NodeBind(节点绑定)模型
```mermaid
classDiagram
class NodeBind {
+module_name : str
+suite_id : int
+node_id : int
+node_port : int
+node_docker : str
+node_bind_desc : str
+deploy_group : int
+deploy_type : int
+deploy_path : str
+health_check_url : str
+create_user : str
+create_time : datetime
+update_user : str
+update_time : datetime
+stamp : int
+lib_repo_info_id : int
+node_lib_repo_update_time : datetime
+config_repo_info_id : int
+node_config_repo_update_time : datetime
}
```

**Diagram sources**
- [models.py](file://env_mgt/models.py#L202-L250)

**Section sources**
- [models.py](file://env_mgt/models.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)

## 环境创建与销毁主流程

环境管理模块通过env_mgt.py文件中的NodeApplyApi类实现环境创建与销毁的主流程。

### 环境创建流程
```mermaid
sequenceDiagram
participant Client as "客户端"
participant NodeApplyApi as "NodeApplyApi"
participant NodeApply as "NodeApply"
participant HttpTask as "HttpTask"
participant CMDB as "CMDB系统"
Client->>NodeApplyApi : 创建节点申请
NodeApplyApi->>NodeApplyApi : 参数验证
NodeApplyApi->>NodeApplyApi : 生成订单号
NodeApplyApi->>NodeApply : 创建NodeApply记录
NodeApplyApi->>HttpTask : 调用CMDB接口
HttpTask->>CMDB : 提交节点申请
CMDB-->>HttpTask : 返回结果
HttpTask-->>NodeApplyApi : 处理响应
NodeApplyApi->>Client : 返回创建结果
```

**Diagram sources**
- [env_mgt.py](file://env_mgt/env_mgt.py#L500-L800)

### 环境销毁流程
环境销毁主要通过回收订单(RecycleOrder)和回收节点(RecycleNode)模型实现，当节点需要回收时，系统会创建回收订单并标记节点状态为待回收，最终完成节点的回收和清理工作。

**Section sources**
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [models.py](file://env_mgt/models.py)

## 配置信息查询与更新服务逻辑

env_info_ser.py文件提供了配置信息查询与更新的核心服务逻辑。

### 配置信息查询流程
```mermaid
flowchart TD
Start([获取配置信息]) --> GetSuiteName["get_suite_name()"]
GetSuiteName --> CheckParams["验证参数"]
CheckParams --> BuildSQL["构建SQL查询"]
BuildSQL --> ExecuteSQL["执行数据库查询"]
ExecuteSQL --> ProcessResult["处理查询结果"]
ProcessResult --> ReturnResult["返回结果"]
```

**Diagram sources**
- [env_info_ser.py](file://env_mgt/env_info_ser.py#L1-L50)

### 核心查询方法
- `get_suite_name()`: 根据应用名列表和环境获取环境套信息
- `get_code_info()`: 获取指定应用在特定环境下的配置信息
- `get_prod_regin()`: 获取生产环境区域信息
- `get_rmq_order_info()`: 获取RMQ订单信息

**Section sources**
- [env_info_ser.py](file://env_mgt/env_info_ser.py)

## 环境变量管理与配置分发

环境变量管理和配置分发主要通过NodeBind模型实现，该模型记录了应用与节点的绑定关系，包括部署路径、健康检查URL等关键配置。

### 跨环境一致性校验
系统通过以下机制确保跨环境的一致性：
1. 使用统一的环境套(Suite)模板
2. 通过节点绑定(NodeBind)记录配置变更历史
3. 定期执行配置同步任务
4. 提供配置差异对比功能

**Section sources**
- [models.py](file://env_mgt/models.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)

## 环境与迭代、应用的映射关系

环境管理模块通过多种方式实现环境与迭代、应用的映射关系：

### 映射关系模型
```mermaid
erDiagram
REGION ||--o{ SUITE : "包含"
SUITE ||--o{ NODE : "包含"
NODE ||--o{ NODE_BIND : "绑定"
NODE_BIND }o--|| APP_MODULE : "关联"
ITERATION ||--o{ NODE_BIND : "使用"
```

**Diagram sources**
- [models.py](file://env_mgt/models.py)

### 映射关系特点
- 每个可用域(Region)可包含多个环境套(Suite)
- 每个环境套可包含多个节点(Node)
- 节点通过NodeBind与应用模块关联
- 迭代可指定使用特定的环境套

**Section sources**
- [models.py](file://env_mgt/models.py)

## H5和Remote一体化改造配置适配

在H5和Remote一体化改造中，环境配置需要进行特殊适配：

### 配置适配策略
1. **统一环境标识**: 使用统一的环境编码规范
2. **配置模板共享**: H5和Remote共享相同的配置模板
3. **部署路径分离**: 虽然共享模板，但部署路径保持独立
4. **健康检查差异化**: 根据H5和Remote的特点配置不同的健康检查URL

### 适配实现
通过env_mgt模块的灵活配置能力，支持H5和Remote应用在同一个环境套中独立部署和管理，同时保持配置的一致性和可维护性。

**Section sources**
- [models.py](file://env_mgt/models.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)

## 常见问题预防与修复

### 配置丢失问题
**预防措施**:
- 启用配置变更审计日志
- 定期备份配置数据
- 实施配置变更审批流程

**修复方法**:
- 从备份中恢复配置
- 使用历史版本回滚
- 重新同步配置模板

### 环境漂移问题
**预防措施**:
- 定期执行环境一致性检查
- 实施配置即代码(Infrastructure as Code)
- 使用自动化配置同步工具

**修复方法**:
- 执行配置同步任务
- 重新应用配置模板
- 重建漂移的环境

**Section sources**
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [env_info_ser.py](file://env_mgt/env_info_ser.py)
- [models.py](file://env_mgt/models.py)