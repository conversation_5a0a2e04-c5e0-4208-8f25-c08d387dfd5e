# 分组策略

<cite>
**本文档引用文件**  
- [models.py](file://env_mgt/models.py)
- [group_mgt_ser.py](file://env_mgt/group_mgt_ser.py)
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
</cite>

## 目录
1. [引言](#引言)
2. [环境分组模型设计](#环境分组模型设计)
3. [分组管理服务实现](#分组管理服务实现)
4. [分组管理API接口设计](#分组管理api接口设计)
5. [分组容量规划与成员调整](#分组容量规划与成员调整)
6. [跨分组资源共享机制](#跨分组资源共享机制)
7. [分组与发布流程集成](#分组与发布流程集成)
8. [灰度发布与多租户应用](#灰度发布与多租户应用)
9. [常见问题与解决方案](#常见问题与解决方案)
10. [结论](#结论)

## 引言
本文档详细阐述了环境分组策略的设计与实现，重点介绍分组的创建、维护和权限控制机制。通过分析EnvGroup模型的设计原理、分组管理服务的实现逻辑以及API接口设计，为开发和运维人员提供全面的分组管理指导。文档还涵盖了分组容量规划、成员动态调整、跨分组资源共享等使用场景，并说明了分组与发布流程、权限体系的集成方式。

## 环境分组模型设计

### EnvGroup模型设计原理
EnvGroup模型基于`DeployGroup`实体实现，用于管理应用的部署分组。该模型设计遵循以下原则：

- **模块化管理**：每个分组与特定应用模块关联，通过`module_name`字段标识
- **命名规范**：分组名称具有唯一性，便于识别和管理
- **元数据支持**：包含创建人、修改人、时间戳等审计信息
- **描述性说明**：通过`deploy_group_desc`字段提供分组用途说明

```mermaid
classDiagram
class DeployGroup {
+string module_name
+string module_code
+string deploy_group_name
+string deploy_group_code
+string deploy_group_desc
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
}
DeployGroup : +create() 创建分组
DeployGroup : +update() 更新分组
DeployGroup : +delete() 删除分组
DeployGroup : +list() 查询分组列表
```

**图示来源**
- [models.py](file://env_mgt/models.py#L68-L85)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L69-L86)

### 分组类型与层级结构
系统支持多种分组类型，主要分为：

- **生产环境分组**：用于生产环境部署，具有最高优先级
- **测试环境分组**：用于测试环境，支持多版本并行
- **灾备环境分组**：用于灾难恢复场景，与主分组保持同步

分组层级结构采用扁平化设计，不支持嵌套分组，确保管理简单性和查询效率。

### 访问策略与权限控制
分组的访问策略通过以下机制实现：

- **基于应用的权限隔离**：用户只能管理其所属应用的分组
- **操作审计**：所有分组操作记录创建人和修改人信息
- **状态控制**：通过时间戳字段实现并发控制

**本节来源**
- [models.py](file://env_mgt/models.py#L68-L85)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L69-L86)

## 分组管理服务实现

### group_mgt_ser.py核心逻辑
分组管理服务层（`group_mgt_ser.py`）提供了底层数据访问功能，主要包含以下逻辑：

- **应用分组信息查询**：通过SQL查询获取指定应用的分组信息
- **数据关联**：联合查询节点、环境套、部署分组等多张表
- **生产环境过滤**：仅返回生产环境（region_group = "prod"）的分组信息
- **节点状态过滤**：仅返回使用中（node_status = 0）的节点

```python
def get_app_group_info(app_name):
    sql = '''SELECT b.module_name,n.node_ip,s.suite_name,g.deploy_group_name,n.node_status 
    FROM env_mgt_node_bind b LEFT JOIN env_mgt_deploy_group g ON g.id=b.deploy_group
    LEFT JOIN env_mgt_suite s ON s.id=b.suite_id LEFT JOIN env_mgt_region r ON r.id =s.region_id
    LEFT JOIN env_mgt_node n ON n.id = b.node_id
    WHERE b.module_name = "{}" AND r.region_group = "prod" AND n.node_status = 0'''.format(app_name)
    logger.debug(sql)
    cursor = connection.cursor()
    cursor.execute(sql)
    return cursor
```

该服务通过直接SQL查询实现高性能数据检索，避免了ORM的性能开销。

**本节来源**
- [group_mgt_ser.py](file://env_mgt/group_mgt_ser.py#L1-L15)

## 分组管理API接口设计

### API接口功能概览
`group_mgt_view.py`文件实现了RESTful API接口，提供完整的分组管理功能：

- **创建分组**：POST请求创建新的部署分组
- **查询分组**：GET请求获取应用的所有分组
- **修改分组名**：PUT请求更新分组名称
- **删除分组**：DELETE请求移除分组

### 接口实现细节
#### 创建分组接口
```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "AppGroupMgtApi"
participant DB as "数据库"
Client->>API : POST /groups {app_name, group_name}
API->>API : 验证用户身份
API->>API : 构建创建参数
API->>DB : update_or_create操作
DB-->>API : 返回操作结果
API-->>Client : 返回成功/失败响应
Note over API,Client : 使用Django ORM的update_or_create<br/>确保分组唯一性
```

**图示来源**
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L22-L42)

#### 分组管理接口
```mermaid
flowchart TD
Start([API请求]) --> Auth["身份验证"]
Auth --> Validate["参数验证"]
Validate --> Check["检查应用权限"]
Check --> Action["执行操作"]
Action --> Create["创建分组"]
Action --> Update["更新分组"]
Action --> Delete["删除分组"]
Action --> List["查询分组"]
Create --> Save["保存到数据库"]
Update --> Save
Delete --> Remove["从数据库删除"]
List --> Fetch["从数据库查询"]
Save --> Response["返回成功响应"]
Remove --> Response
Fetch --> Response
Response --> End([响应客户端])
```

**图示来源**
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L0-L75)

### 错误处理机制
API接口实现了完善的错误处理：

- **异常捕获**：使用try-catch块捕获数据库操作异常
- **统一响应格式**：通过`ApiResult.success_dict`和`ApiResult.failed_dict`标准化响应
- **详细错误信息**：返回异常的具体描述，便于问题排查

**本节来源**
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L0-L150)

## 分组容量规划与成员调整

### 容量规划策略
分组容量规划应考虑以下因素：

- **应用负载**：根据应用的预期负载确定分组规模
- **高可用性**：确保每个分组有足够的冗余节点
- **扩展性**：预留扩展空间以应对流量增长
- **成本控制**：在性能和成本之间取得平衡

### 成员动态调整
分组成员调整通过`AppGroupNodeMgtApi`实现：

- **节点换组**：将节点从一个分组迁移到另一个分组
- **批量操作**：支持同时调整多个节点的分组归属
- **事务性保证**：确保调整过程的原子性和一致性

```mermaid
graph TD
A[开始换组] --> B[验证源分组]
B --> C[验证目标分组]
C --> D[检查节点状态]
D --> E[更新节点绑定]
E --> F[记录操作日志]
F --> G[返回结果]
G --> H[结束]
```

**图示来源**
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L100-L120)

## 跨分组资源共享

### 资源共享机制
系统支持跨分组资源共享，主要通过以下方式实现：

- **共享配置**：公共配置可在多个分组间共享
- **共享服务**：基础服务支持跨分组调用
- **数据同步**：关键数据在分组间保持同步

### 使用示例
```python
# 获取应用在所有分组中的节点信息
def get_all_group_nodes(app_name):
    all_nodes = []
    for group in DeployGroup.objects.filter(module_name=app_name):
        nodes = get_app_group_info(app_name)
        all_nodes.extend(nodes)
    return all_nodes
```

**本节来源**
- [group_mgt_ser.py](file://env_mgt/group_mgt_ser.py#L1-L15)
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L80-L100)

## 分组与发布流程集成

### 发布流程集成
分组策略与发布流程深度集成：

- **分组感知发布**：发布时指定目标分组
- **灰度发布支持**：按分组逐步推进发布
- **回滚机制**：支持按分组回滚到指定版本

### 权限体系集成
分组管理与权限体系的集成方式：

- **基于角色的访问控制**：不同角色具有不同的分组管理权限
- **应用所有权**：应用负责人可管理其应用的所有分组
- **审计跟踪**：所有分组操作均记录操作人和时间

**本节来源**
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L22-L75)

## 灰度发布与多租户应用

### 灰度发布模式
分组策略支持多种灰度发布模式：

- **按比例灰度**：将流量按比例分配到不同分组
- **按用户灰度**：特定用户群体访问新版本分组
- **按地域灰度**：特定地域的用户访问新版本

### 多租户应用场景
在多租户环境下，分组策略的应用：

- **租户隔离**：每个租户拥有独立的分组
- **资源共享**：公共组件在租户间共享
- **定制化配置**：各租户可自定义分组配置

**本节来源**
- [group_mgt_ser.py](file://env_mgt/group_mgt_ser.py#L1-L15)
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L0-L150)

## 常见问题与解决方案

### 分组配置冲突
**问题描述**：多个用户同时修改同一分组配置导致冲突

**解决方案**：
- 使用版本戳（stamp字段）实现乐观锁
- 前端显示最后修改时间，提醒用户刷新
- 提供冲突解决向导

### 权限越界访问
**问题描述**：用户尝试访问非授权分组

**解决方案**：
- 在API层验证用户与应用的归属关系
- 记录越权访问尝试并告警
- 实施最小权限原则

### 分组数据不一致
**问题描述**：数据库分组数据与CMDB不一致

**解决方案**：
- 定期同步任务校验数据一致性
- 提供数据修复工具
- 建立变更通知机制

**本节来源**
- [group_mgt_view.py](file://env_mgt/group_mgt_view.py#L22-L75)
- [group_mgt_ser.py](file://env_mgt/group_mgt_ser.py#L1-L15)

## 结论
本文档详细介绍了环境分组策略的设计与实现，涵盖了从模型设计到API接口的各个方面。通过合理的分组管理，系统能够支持复杂的发布流程、实现高效的资源利用，并为灰度发布和多租户场景提供坚实基础。建议在实际使用中遵循文档中的最佳实践，确保分组管理的规范性和安全性。