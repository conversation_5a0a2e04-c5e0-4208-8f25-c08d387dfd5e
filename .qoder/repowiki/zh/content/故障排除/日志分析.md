# 日志分析

<cite>
**本文档引用的文件**
- [log_config.py](file://spider/log_config.py)
- [pipeline_log_view.py](file://pipeline/pipeline_log_view.py)
- [salt_view.py](file://task_mgt/salt_view.py)
- [OpLogs.py](file://common_middle/OpLogs.py)
</cite>

## 目录
1. [简介](#简介)
2. [日志配置与生成](#日志配置与生成)
3. [日志查看与诊断方法](#日志查看与诊断方法)
4. [操作日志追踪](#操作日志追踪)
5. [流水线与SaltStack日志结构](#流水线与saltstack日志结构)
6. [常见日志错误模式识别](#常见日志错误模式识别)
7. [日志聚合与搜索最佳实践](#日志聚合与搜索最佳实践)
8. [结论](#结论)

## 简介
本文档旨在提供一套完整的日志分析指南，涵盖系统日志的配置、生成、查看和诊断方法。文档详细解释了日志配置文件中的日志级别设置、输出格式和存储路径配置，说明了不同模块产生的日志结构和关键字段含义，并提供了使用操作日志进行追踪的方法。同时，文档指导开发者如何通过特定视图定位执行步骤的失败原因，包含常见日志错误模式的识别方法和解决方案，以及日志聚合、过滤和搜索的最佳实践。

## 日志配置与生成

### 日志级别设置
系统日志配置文件`log_config.py`中定义了详细的日志级别设置。系统使用`logging`模块进行日志管理，主要日志级别包括：
- **INFO**: 用于记录常规操作信息
- **WARNING**: 用于记录警告信息
- **ERROR**: 用于记录错误信息
- **CRITICAL**: 用于记录严重错误信息

在配置中，`console`处理器的级别设置为`INFO`，意味着所有`INFO`及以上级别的日志都会输出到控制台。同时，`timedErrorRotatingFileHandler`处理器的级别设置为`WARNING`，专门用于记录警告及以上级别的日志到错误日志文件。

### 输出格式配置
日志输出格式在`DictConfig`的`formatters`部分定义，使用名为`master_format`的格式器。该格式器的格式字符串为：
```
%(asctime)s - [%(pathname)s]--[%(module)s]-%(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s
```
此格式包含以下信息：
- **时间戳**: 使用`%(asctime)s`表示，格式为`%Y-%m-%d %H:%M:%S`
- **文件路径**: 使用`%(pathname)s`表示完整的文件路径
- **模块名**: 使用`%(module)s`表示模块名称
- **文件名**: 使用`%(filename)s`表示文件名
- **行号**: 使用`%(lineno)d`表示代码行号
- **日志级别**: 使用`%(levelname)s`表示日志级别
- **消息内容**: 使用`%(message)s`表示日志消息

### 存储路径配置
日志文件的存储路径在`log_config.py`中通过以下代码配置：
```python
BASE_DIR = "/data"
LOG_DIR = os.path.join(BASE_DIR, "logs")
```
系统将日志文件存储在`/data/logs`目录下。如果该目录不存在，系统会自动创建。具体日志文件包括：
- **spider_info.log**: 存储常规信息日志，由`timedRotatingFileHandler`处理器管理
- **spider_error.log**: 存储警告和错误日志，由`timedErrorRotatingFileHandler`处理器管理

日志文件采用按天轮转的策略，`when='d'`表示每天创建一个新的日志文件，`interval=1`表示每天轮转一次，`backupCount=2`表示保留最近2天的日志文件备份。

**Section sources**
- [log_config.py](file://spider/log_config.py#L1-L105)

## 日志查看与诊断方法

### 流水线日志查看
通过`pipeline_log_view.py`文件中的`PipeLineLogViewSet`类，系统提供了流水线操作日志的查看功能。该视图集处理创建日志记录的请求，主要功能包括：

1. **日志记录创建**: `create`方法接收HTTP请求参数，包括操作描述、操作类型、作业名称、流水线ID和操作用户等信息
2. **日志信息存储**: `insert_log_opt`方法将日志信息存储到数据库的`pipeline_log_opt`表中
3. **响应返回**: 成功创建日志后返回成功响应

日志记录包含以下关键字段：
- **opt_user**: 操作者
- **opt_time**: 操作时间
- **opt_type**: 操作类型
- **pipeline_id**: 流水线ID
- **job_name**: 作业名称
- **opt_info_str**: 操作日志描述

### SaltStack任务日志查看
`salt_view.py`文件中的`SlatLogApi`类提供了SaltStack任务日志的查看功能。该API的主要特点包括：

1. **并发控制**: 使用`MysqlQueue`实现日志查看的并发控制，防止过多用户同时查看同一节点的日志
2. **带宽限制**: 系统设置了`QUEUE_LIMIT["slat_log"]`限制同时打开的日志窗口数量
3. **进程信息获取**: 通过执行Salt命令获取进程ID、包路径和包修改时间等信息
4. **日志内容获取**: 根据请求的行号，使用`tail`或`sed`命令获取指定范围的日志内容

当用户首次查看日志时（`last_line_num=-1`），系统会清理超时队列并检查是否有其他用户正在查看同一日志。如果日志已被其他用户打开，系统会返回相应的提示信息。

**Section sources**
- [pipeline_log_view.py](file://pipeline/pipeline_log_view.py#L1-L32)
- [salt_view.py](file://task_mgt/salt_view.py#L1-L209)

## 操作日志追踪

### OpLogs操作日志系统
`common_middle/OpLogs.py`文件实现了系统的操作日志追踪功能，通过`AuditLogMiddleware`类作为Django中间件来记录所有HTTP请求的详细信息。

#### 日志记录内容
操作日志记录了以下关键信息：
- **request_user**: 请求用户
- **remote_addr**: 客户端IP地址
- **res_addr**: 服务器本地IP地址
- **request_method**: HTTP请求方法
- **request_path**: 请求路径
- **response_status**: 响应状态码
- **response_content_type**: 响应内容类型
- **request_param**: 请求参数
- **response_body**: 响应体内容
- **request_took**: 请求处理时间
- **time**: 日志记录时间

#### 忽略列表配置
系统支持配置忽略列表，某些API路径不会被记录操作日志。当前忽略的API列表包括：
- `/test_mgt`
- `app_interface_api`

#### 请求处理时间计算
系统通过记录请求开始和结束的时间戳来计算请求处理时间：
```python
start_time = time.time()
response = self.get_response(request)
end_time = time.time()
elapsed_time = end_time - start_time
```
处理时间以秒为单位记录在日志中。

#### 本地IP获取
`get_local_ip`方法通过Python的`socket`模块获取服务器的本地IP地址，排除了`127.0.0.1`回环地址，确保记录的是真实的网络接口IP。

**Section sources**
- [OpLogs.py](file://common_middle/OpLogs.py#L1-L86)

## 流水线与SaltStack日志结构

### 流水线日志结构
流水线日志主要存储在数据库的`pipeline_log_opt`表中，其结构定义在`pipeline/models.py`文件中。日志记录包含以下字段：

| 字段名 | 类型 | 描述 |
|-------|------|------|
| sid | AutoField | 主键 |
| opt_user | TextField | 操作者 |
| opt_type | TextField | 操作类型 |
| job_name | TextField | 作业名称 |
| opt_time | DateTimeField | 操作时间 |
| pipeline_id | TextField | 流水线ID |
| opt_info_str | TextField | 操作日志描述 |

日志记录的`opt_info_str`字段格式为" 触发了 "加上操作描述，这种格式便于识别操作动作。

### SaltStack任务日志结构
SaltStack任务日志通过API返回的JSON结构包含以下关键字段：

| 字段名 | 描述 |
|-------|------|
| line_num | 日志文件总行数 |
| process_id | 进程ID |
| log_path | 日志文件路径 |
| package_path | 包路径 |
| package_time | 包修改时间 |
| log_list | 日志内容列表 |

对于JAR包类型的应用，系统会执行更复杂的命令来查找包路径和进程ID。系统首先尝试精确匹配包名，如果找不到则使用通配符查找。对于WAR包，系统不支持获取包修改时间。

### 日志级别与用途
不同类型的日志有不同的用途和级别设置：

1. **应用日志**: 记录系统运行的常规信息，级别为INFO
2. **错误日志**: 记录警告和错误信息，级别为WARNING及以上
3. **操作日志**: 记录所有HTTP请求的详细信息，级别为ERROR（用于审计目的）
4. **审计日志**: 记录安全相关的操作，用于合规性检查

**Section sources**
- [pipeline/models.py](file://pipeline/models.py#L27-L52)
- [salt_view.py](file://task_mgt/salt_view.py#L1-L209)

## 常见日志错误模式识别

### 日志路径未维护错误
当用户尝试查看SaltStack日志时，如果系统未维护日志路径，会返回以下错误信息：
```
{}节点没有维护日志路径，如需查看，请联系SCM添加（env_mgt_node_log_bind表）。
```
**解决方案**: 联系SCM团队在`env_mgt_node_log_bind`表中添加相应的日志路径配置。

### 并发查看限制错误
当同时查看日志的用户数量超过系统限制时，会返回以下错误信息：
```
由于带宽限制，系统只允许打开{}个日志窗口，目前已经超过，如有疑问请联系冯伟敏。电话：18916598509
```
**解决方案**: 
1. 等待其他用户关闭日志窗口
2. 联系管理员增加`QUEUE_LIMIT["slat_log"]`的值
3. 分批查看日志，避免同时打开过多窗口

### 包路径查找失败
对于JAR包应用，如果系统无法找到包路径，会返回"找不到包路径"的错误。这通常发生在以下情况：
- 包名与模块名不匹配
- 部署路径不正确
- 包文件不存在

**解决方案**: 
1. 检查`package_name`和`module_name`配置是否正确
2. 验证部署路径`deploy_path`是否存在
3. 确认包文件是否已正确部署

### 进程查找异常
当系统查找到多个进程时，会记录警告日志：
```
查找出了多个进程 应用为{}，ip为{}
```
**解决方案**: 
1. 检查应用部署是否重复
2. 验证进程管理脚本是否正确
3. 确保应用名称在系统中唯一

### 日志文件不存在
当尝试读取不存在的日志文件时，系统会返回相应的错误信息。这通常发生在：
- 应用尚未生成日志
- 日志路径配置错误
- 日志文件已被清理

**解决方案**: 
1. 确认应用是否已成功启动
2. 检查日志路径配置是否正确
3. 验证日志轮转策略是否导致文件被删除

**Section sources**
- [salt_view.py](file://task_mgt/salt_view.py#L1-L209)

## 日志聚合与搜索最佳实践

### 日志聚合策略
系统采用多级日志聚合策略，将不同类型的日志分别存储和管理：

1. **应用日志聚合**: 所有应用日志通过`rotatingFileLogger`记录器聚合到`spider_info.log`文件中
2. **错误日志聚合**: 错误和警告日志通过`timedErrorRotatingFileHandler`聚合到`spider_error.log`文件中
3. **操作日志聚合**: 操作日志通过`audit_logger`记录器以JSON格式记录，便于后续分析

### 日志搜索技巧
基于系统的日志格式，推荐以下搜索技巧：

1. **按时间搜索**: 使用日志时间戳进行范围搜索，格式为`YYYY-MM-DD HH:MM:SS`
2. **按模块搜索**: 在日志中搜索`[module_name]`来定位特定模块的日志
3. **按级别搜索**: 使用`INFO:`、`WARNING:`、`ERROR:`等关键字搜索特定级别的日志
4. **按文件路径搜索**: 使用`[pathname]`搜索特定文件的日志记录

### 日志过滤方法
系统提供了多种日志过滤方法：

1. **内置过滤器**: `SpiderFilter`类实现了基于文件路径的过滤，只允许`publish_mgt`模块的日志通过
2. **级别过滤**: 通过处理器的`level`属性实现级别过滤
3. **自定义过滤**: 可以通过实现`logging.Filter`类创建自定义过滤器

### 日志分析工具
推荐使用以下工具进行日志分析：

1. **grep**: 用于基本的文本搜索
   ```bash
   grep "ERROR" /data/logs/spider_info.log
   ```
2. **awk**: 用于提取特定字段
   ```bash
   awk '{print $1, $2, $NF}' /data/logs/spider_info.log
   ```
3. **sed**: 用于文本替换和处理
4. **tail/follow**: 用于实时监控日志
   ```bash
   tail -f /data/logs/spider_info.log
   ```

### 性能优化建议
1. **合理设置日志级别**: 在生产环境中避免使用DEBUG级别，减少日志量
2. **定期清理日志**: 配置适当的`backupCount`值，避免磁盘空间耗尽
3. **使用异步日志**: 对于高并发场景，考虑使用异步日志记录
4. **日志压缩**: 对历史日志进行压缩存储，节省磁盘空间

**Section sources**
- [log_config.py](file://spider/log_config.py#L1-L105)

## 结论
本文档全面介绍了系统的日志分析体系，涵盖了从日志配置、生成、查看到诊断的完整流程。通过理解`log_config.py`中的配置细节，开发者可以更好地控制日志的输出格式和存储策略。`pipeline_log_view.py`和`salt_view.py`提供了查看流水线和SaltStack任务日志的有效方法，而`OpLogs.py`中的操作日志系统则为系统审计和问题追踪提供了重要支持。

掌握常见日志错误模式的识别方法和解决方案，可以帮助开发者快速定位和解决系统问题。同时，遵循日志聚合、过滤和搜索的最佳实践，可以提高日志分析的效率和准确性。建议开发者在日常工作中充分利用这些日志工具和方法，以提升系统的可维护性和稳定性。