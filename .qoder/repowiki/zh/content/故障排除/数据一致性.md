# 数据一致性

<cite>
**本文档引用文件**   
- [01-env_mgt_schema.sql](file://db/env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://app_mgt/models.py)
- [models.py](file://db_mgt/models.py)
- [app_sharding.py](file://sharding_mgt/app_sharding.py)
- [0001_initial.py](file://app_mgt/migrations/0001_initial.py)
- [0001_initial.py](file://db_mgt/migrations/0001_initial.py)
- [0001_initial.py](file://ci_cd_mgt/h5/migrations/0001_initial.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py)
- [publish_mgt_view.py](file://publish_mgt/publish_mgt_view.py)
- [pipeline_view.py](file://pipeline/pipeline_view.py)
- [saltcmd.sql](file://db/saltcmd.sql)
- [spider迭代3_3_1/01-领域整理-表名调整_for_怀天.sql](file://db/spider迭代3_3_1/01-领域整理-表名调整_for_怀天.sql)
- [分支2_x/2.4.0/02-ops表bak-common_service_cmdbdata.sql](file://db/分支2_x/2.4.0/02-ops表bak-common_service_cmdbdata.sql)
- [迭代2_x/迭代2.8.1/02-db版本化绑定表设计_for_zt.sql](file://db/迭代2_x/迭代2.8.1/02-db版本化绑定表设计_for_zt.sql)
- [迭代3_1_6/09-机房迁移-第二阶段-移动端prod转bs-prod.sql](file://db/迭代3_1_6/09-机房迁移-第二阶段-移动端prod转bs-prod.sql)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排查指南](#故障排查指南)
9. [结论](#结论)

## 引言
本指南旨在解决系统各模块间的数据一致性问题，涵盖应用、环境、迭代和数据库实例等核心模型之间的关联关系与数据同步机制。通过检查数据库迁移执行状态和数据初始化脚本，识别并修复外键约束、状态不匹配及缓存与数据库差异等问题。同时提供数据修复脚本的编写规范和安全执行流程，并强调事务完整性、并发更新和分布式状态管理的最佳实践。

## 项目结构
系统采用模块化设计，主要分为应用管理（app_mgt）、环境管理（env_mgt）、数据库管理（db_mgt）、CI/CD管理（ci_cd_mgt）等核心模块。每个模块包含独立的模型定义、业务逻辑和服务接口，支持高内聚低耦合的设计原则。

```mermaid
graph TB
subgraph "核心模块"
AppMgt[应用管理 app_mgt]
EnvMgt[环境管理 env_mgt]
DbMgt[数据库管理 db_mgt]
CiCdMgt[CI/CD管理 ci_cd_mgt]
Sharding[分库分表 sharding_mgt]
end
AppMgt --> DbMgt : "依赖"
EnvMgt --> DbMgt : "依赖"
CiCdMgt --> AppMgt : "调用"
CiCdMgt --> EnvMgt : "调用"
Sharding --> DbMgt : "集成"
```

**图示来源**
- [01-env_mgt_schema.sql](file://db/env_mgt/db/schema/01-env_mgt_schema.sql#L1-L50)
- [models.py](file://app_mgt/models.py#L1-L30)
- [models.py](file://db_mgt/models.py#L1-L30)

**本节来源**
- [01-env_mgt_schema.sql](file://db/env_mgt/db/schema/01-env_mgt_schema.sql#L1-L100)
- [models.py](file://app_mgt/models.py#L1-L50)
- [models.py](file://db_mgt/models.py#L1-L50)

## 核心组件

系统核心组件包括应用模型、环境配置、数据库绑定、分库分表规则等。这些组件通过Django ORM进行持久化管理，并通过API接口实现跨模块交互。

**本节来源**
- [models.py](file://app_mgt/models.py#L25-L100)
- [models.py](file://db_mgt/models.py#L15-L80)
- [env_mgt.py](file://env_mgt/env_mgt.py#L10-L40)

## 架构概述

系统采用分层架构，前端通过REST API与后端服务通信，后端服务通过Django框架组织业务逻辑，数据层使用MySQL存储结构化数据，并通过SaltStack实现配置同步。

```mermaid
graph TD
A[客户端] --> B[REST API]
B --> C{业务逻辑层}
C --> D[应用管理服务]
C --> E[环境管理服务]
C --> F[数据库管理服务]
D --> G[(MySQL数据库)]
E --> G
F --> G
G --> H[SaltStack配置同步]
```

**图示来源**
- [models.py](file://app_mgt/models.py#L1-L20)
- [models.py](file://db_mgt/models.py#L10-L30)
- [saltcmd.sql](file://db/saltcmd.sql#L1-L10)

## 详细组件分析

### 应用与环境绑定分析

应用与环境的绑定关系通过`env_mgt_node_bind`表维护，确保每个应用在特定环境中具有唯一的节点配置。该机制支持多环境部署和灰度发布。

```mermaid
erDiagram
APP_MODULE {
bigint id PK
varchar app_name
varchar module_name
timestamp create_time
timestamp update_time
}
ENVIRONMENT {
bigint id PK
varchar env_name
varchar env_code
varchar description
}
NODE_INFO {
bigint id PK
varchar node_ip
varchar minion_id
varchar node_type
bigint env_id FK
}
ENV_MGT_NODE_BIND {
bigint id PK
bigint app_module_id FK
bigint node_id FK
varchar bind_status
timestamp bind_time
}
APP_MODULE ||--o{ ENV_MGT_NODE_BIND : "包含"
ENVIRONMENT ||--o{ NODE_INFO : "包含"
NODE_INFO ||--o{ ENV_MGT_NODE_BIND : "被绑定"
```

**图示来源**
- [01-env_mgt_schema.sql](file://db/env_mgt/db/schema/01-env_mgt_schema.sql#L1-L50)
- [迭代2_x/迭代2.8.1/02-db版本化绑定表设计_for_zt.sql](file://db/迭代2_x/迭代2.8.1/02-db版本化绑定表设计_for_zt.sql#L85-L92)

### 数据库绑定与分库分表

数据库绑定通过`db_mgt_app_bind`表实现，支持一个应用模块绑定多个数据库实例。分库分表规则由`sharding_mgt/app_sharding.py`中的`parse_binding_tables`方法处理，确保数据分布一致性。

```mermaid
classDiagram
class DbMgtAppShardingRuleBindingTableConfig {
+int id
+int rule_config_id
+str binding_table
+str create_user
+datetime create_time
+delete_old_bindings()
+bulk_create_new_bindings()
}
class AppShardingService {
+parse_binding_tables(rule, bindingTables)
+apply_sharding_rule()
}
AppShardingService --> DbMgtAppShardingRuleBindingTableConfig : "使用"
```

**图示来源**
- [models.py](file://db_mgt/models.py#L173-L196)
- [app_sharding.py](file://sharding_mgt/app_sharding.py#L98-L107)

**本节来源**
- [models.py](file://db_mgt/models.py#L150-L200)
- [app_sharding.py](file://sharding_mgt/app_sharding.py#L90-L110)

### 迭代与发布管理

迭代管理模块（iter_mgt）负责版本控制和发布流程，通过`iteration_id`字段关联应用构建与部署记录。发布策略由`publish_mgt`模块定义，支持灰度、全量等多种模式。

```mermaid
sequenceDiagram
participant Dev as "开发人员"
participant IterMgt as "IterMgt模块"
participant PublishMgt as "PublishMgt模块"
participant Pipeline as "Pipeline模块"
Dev->>IterMgt : 提交迭代申请
IterMgt->>IterMgt : 创建迭代记录
IterMgt->>PublishMgt : 触发发布计划
PublishMgt->>Pipeline : 启动构建流水线
Pipeline->>Pipeline : 执行构建与测试
Pipeline-->>PublishMgt : 返回构建结果
PublishMgt-->>IterMgt : 更新发布状态
IterMgt-->>Dev : 通知发布完成
```

**图示来源**
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L20-L60)
- [publish_mgt_view.py](file://publish_mgt/publish_mgt_view.py#L30-L80)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L45-L120)

**本节来源**
- [iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L1-L100)
- [publish_mgt_view.py](file://publish_mgt/publish_mgt_view.py#L1-L50)

## 依赖分析

系统各模块之间存在明确的依赖关系，应用管理依赖数据库管理，环境管理依赖基础节点信息，CI/CD流程整合所有模块完成自动化部署。

```mermaid
graph LR
A[app_mgt] --> B[db_mgt]
C[env_mgt] --> B
D[ci_cd_mgt] --> A
D --> C
E[sharding_mgt] --> B
F[publish_mgt] --> A
F --> D
```

**图示来源**
- [models.py](file://app_mgt/models.py#L1-L10)
- [models.py](file://db_mgt/models.py#L1-L10)
- [models.py](file://env_mgt/models.py#L1-L10)

**本节来源**
- [models.py](file://app_mgt/models.py#L1-L30)
- [models.py](file://db_mgt/models.py#L1-L30)
- [models.py](file://env_mgt/models.py#L1-L30)

## 性能考虑

为保障数据一致性操作的性能，建议：
- 对关键表（如`env_mgt_node_bind`、`db_mgt_app_bind`）建立复合索引
- 使用批量操作替代逐条更新
- 在非高峰时段执行大规模数据修复
- 启用数据库查询缓存机制

## 故障排查指南

### 检查数据库迁移状态
使用Django迁移命令检查迁移状态：
```bash
python manage.py showmigrations app_mgt
python manage.py showmigrations db_mgt
```

### 验证数据初始化脚本
检查`db/`目录下的SQL脚本执行情况，特别是：
- `分支2_x/`下的版本化迁移脚本
- `迭代3_x_x/`中的业务接入脚本
- `schema/`中的表结构定义

### 识别外键约束问题
查询数据库外键约束状态：
```sql
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM
    INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE
    REFERENCED_TABLE_SCHEMA = 'spider';
```

### 修复数据不一致
编写数据修复脚本时遵循以下规范：
1. 使用事务包装所有修改操作
2. 先备份原数据再执行修复
3. 添加详细日志记录
4. 在测试环境验证后再上线

示例修复脚本框架：
```python
from django.db import transaction

@transaction.atomic
def fix_data_consistency():
    # 1. 备份数据
    # 2. 校验数据状态
    # 3. 执行修复逻辑
    # 4. 记录操作日志
    pass
```

**本节来源**
- [0001_initial.py](file://app_mgt/migrations/0001_initial.py#L1-L20)
- [0001_initial.py](file://db_mgt/migrations/0001_initial.py#L1-L20)
- [0001_initial.py](file://ci_cd_mgt/h5/migrations/0001_initial.py#L1-L20)
- [spider迭代3_3_1/01-领域整理-表名调整_for_怀天.sql](file://db/spider迭代3_3_1/01-领域整理-表名调整_for_怀天.sql#L1-L34)
- [分支2_x/2.4.0/02-ops表bak-common_service_cmdbdata.sql](file://db/分支2_x/2.4.0/02-ops表bak-common_service_cmdbdata.sql#L1-L2)

## 结论

本系统通过模块化设计和严格的数据库约束保障数据一致性。在面对数据不一致问题时，应首先检查迁移脚本执行状态，然后验证核心绑定关系的完整性，最后按照安全流程执行数据修复。建议定期审计关键表的数据状态，预防潜在的一致性风险。