# 故障排除

<cite>
**本文档中引用的文件**  
- [log_config.py](file://spider/log_config.py)
- [settings.py](file://spider/settings.py)
- [log.ini](file://spider/log.ini)
- [salt_view.py](file://task_mgt/salt_view.py)
- [http_task.py](file://task_mgt/http_task.py)
- [log_views.py](file://publish/log_views.py)
- [env_mgt_node_log_bind.sql](file://db/分支2_x/2.2.4/05-ptp日志相关表.sql)
- [test_publish_log.sql](file://db/分支2_x/2.7.0/08-清理测试环境一键初始化老表for张弋翔.sql)
</cite>

## 目录
1. [日志查看与分析方法](#日志查看与分析方法)
2. [关键监控指标说明](#关键监控指标说明)
3. [常见错误代码与解决步骤](#常见错误代码与解决步骤)
4. [典型问题排查流程](#典型问题排查流程)
5. [性能问题分析方法](#性能问题分析方法)
6. [系统恢复与数据修复流程](#系统恢复与数据修复流程)
7. [故障报告模板与升级路径](#故障报告模板与升级路径)
8. [预防性维护建议与最佳实践](#预防性维护建议与最佳实践)

## 日志查看与分析方法

系统日志是诊断和排查问题的重要依据。本系统采用Python标准库`logging`模块进行日志管理，配置了多种日志处理器和格式化器，确保日志信息的完整性和可读性。

### 日志级别与格式

系统定义了以下日志级别，按严重程度从低到高排列：
- **DEBUG**：调试信息，用于开发和问题定位
- **INFO**：一般信息，记录系统正常运行状态
- **WARNING**：警告信息，表示潜在问题
- **ERROR**：错误信息，表示发生了错误但系统仍可继续运行
- **CRITICAL**：严重错误，表示系统可能无法继续运行

日志格式统一为：
```
%(asctime)s - [%(pathname)s]--[%(module)s]-%(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s
```
其中包含时间戳、文件路径、模块名、文件名、行号、日志级别和消息内容。

### 日志存储位置

系统日志存储在`/data/logs`目录下，具体文件包括：
- `spider_info.log`：记录INFO级别及以上的日志信息，每天轮转一次，保留最近2天的日志
- `spider_error.log`：记录WARNING级别及以上的日志信息，每天轮转一次，保留最近2天的日志
- `audit_info.log`：审计日志，存储在`/data/logs/spider/audit`目录下，用于记录关键操作

### 日志查看接口

系统提供了REST API接口用于查看远程节点日志，主要涉及以下配置和流程：

1. **Salt API配置**：系统通过SaltStack远程执行命令获取日志内容，相关配置在`settings.py`中定义，包括不同环境套的Salt Master节点地址、用户名和密码。
2. **并发控制**：系统限制同时打开的日志窗口数量，由`QUEUE_LIMIT["slat_log"]`参数控制，防止带宽过载。
3. **访问检查**：当多个用户尝试同时查看同一节点的同一日志文件时，系统会进行冲突检测并返回提示信息。

**Section sources**
- [log_config.py](file://spider/log_config.py#L0-L105)
- [settings.py](file://spider/settings.py#L461-L516)
- [log.ini](file://spider/log.ini#L0-L47)

## 关键监控指标说明

系统运行过程中需要关注以下关键监控指标，这些指标反映了系统的健康状况和性能表现。

### 系统资源指标

- **CPU使用率**：应保持在70%以下，超过80%为警告状态，超过90%为严重状态
- **内存使用率**：应保持在75%以下，超过85%为警告状态，超过95%为严重状态
- **磁盘I/O**：读写延迟应低于50ms，IOPS应根据业务需求保持稳定
- **网络带宽**：使用率应低于80%，避免网络拥塞

### 应用性能指标

- **请求响应时间**：API平均响应时间应低于500ms，P95响应时间应低于1s
- **错误率**：HTTP 5xx错误率应低于0.1%，4xx错误率应低于1%
- **队列长度**：任务队列长度应保持在合理范围内，避免积压

### 业务指标

- **发布成功率**：应保持在99%以上
- **构建耗时**：应保持在历史平均值的±20%范围内
- **数据库连接数**：应低于数据库最大连接数的80%

**Section sources**
- [settings.py](file://spider/settings.py#L461-L516)
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)

## 常见错误代码与解决步骤

### 日志查看相关错误

#### 错误代码：E001
**错误信息**："{节点IP}节点没有维护日志路径，如需查看，请联系SCM添加（env_mgt_node_log_bind表）"
**原因分析**：目标节点的日志路径未在系统中配置
**解决步骤**：
1. 检查`env_mgt_node_log_bind`表中是否存在该节点的记录
2. 如果不存在，联系SCM团队添加相应的日志路径配置
3. 验证配置后重新尝试查看日志

#### 错误代码：E002
**错误信息**："由于带宽限制，系统只允许打开{数量}个日志窗口，目前已经超过，如有疑问请联系冯伟敏。电话：18916598509"
**原因分析**：同时查看日志的用户数量超过了系统限制
**解决步骤**：
1. 关闭不必要的日志查看窗口
2. 等待其他用户关闭日志窗口
3. 如果急需查看，联系系统管理员临时调整限制

#### 错误代码：E003
**错误信息**："{节点IP}节点的{日志路径}日志已经被{用户}打开，请联系{用户}"
**原因分析**：同一节点的同一日志文件正在被其他用户查看
**解决步骤**：
1. 联系正在查看日志的用户，协商查看时间
2. 等待该用户关闭日志查看
3. 如果是紧急情况，联系系统管理员强制关闭

### Salt API相关错误

#### 错误代码：E004
**错误信息**："没有配置{环境套}环境套的salt master 节点信息，请联系冯伟敏！ 电话： 18916598509"
**原因分析**：系统缺少指定环境套的Salt API配置
**解决步骤**：
1. 检查`settings.py`中的`SALT_LOG_API`、`SALT_LOG_API_USER`和`SALT_LOG_API_PASSWORD`配置
2. 确认是否遗漏了新环境套的配置
3. 添加相应的配置并重启服务

**Section sources**
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)
- [task_mgt/http_task.py](file://task_mgt/http_task.py#L205-L225)

## 典型问题排查流程

### 网络连接问题排查

当出现网络连接问题时，按照以下流程进行排查：

1. **确认问题范围**
   - 是单个节点问题还是多个节点问题
   - 是特定环境问题还是所有环境问题
   - 是出站连接问题还是入站连接问题

2. **检查网络连通性**
   ```bash
   ping <目标IP>
   telnet <目标IP> <端口>
   ```

3. **检查防火墙规则**
   - 确认防火墙是否阻止了相关端口
   - 检查安全组规则是否正确配置

4. **检查DNS解析**
   ```bash
   nslookup <域名>
   dig <域名>
   ```

5. **检查路由表**
   ```bash
   route -n
   traceroute <目标IP>
   ```

### 数据库访问问题排查

当出现数据库访问问题时，按照以下流程进行排查：

1. **检查数据库连接参数**
   - 确认数据库IP、端口、用户名、密码是否正确
   - 检查`settings.py`中的数据库配置

2. **测试数据库连通性**
   ```bash
   mysql -h <数据库IP> -P <端口> -u <用户名> -p
   ```

3. **检查数据库服务状态**
   ```bash
   systemctl status mysql
   ps aux | grep mysql
   ```

4. **检查数据库连接数**
   ```sql
   SHOW STATUS LIKE 'Threads_connected';
   SHOW VARIABLES LIKE 'max_connections';
   ```

5. **检查慢查询日志**
   - 查看是否有大量慢查询导致数据库负载过高

### 外部服务集成问题排查

当出现外部服务集成问题时，按照以下流程进行排查：

1. **确认外部服务URL**
   - 检查`settings.py`中的`INTERFACE_URL`配置
   - 确认URL是否正确且可达

2. **检查认证信息**
   - 确认API密钥、Token等认证信息是否正确
   - 检查认证是否过期

3. **测试接口连通性**
   ```bash
   curl -v <外部服务URL>
   ```

4. **检查请求参数**
   - 确认请求参数格式是否符合API文档要求
   - 检查是否有必填参数缺失

5. **查看调用日志**
   - 检查系统日志中相关的调用记录
   - 分析错误信息和响应码

**Section sources**
- [settings.py](file://spider/settings.py#L461-L516)
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)

## 性能问题分析方法

### CPU性能分析

当系统出现CPU性能问题时，采用以下方法进行分析：

1. **监控CPU使用率**
   - 使用`top`或`htop`命令实时监控CPU使用情况
   - 使用`vmstat`命令查看系统整体性能

2. **定位高CPU进程**
   ```bash
   top -H -p <进程ID>
   ```

3. **分析Python进程**
   ```bash
   python -m cProfile -o profile.out your_script.py
   ```

4. **检查GIL影响**
   - 对于多线程Python应用，检查全局解释器锁(GIL)的影响
   - 考虑使用多进程替代多线程

### 内存性能分析

当系统出现内存性能问题时，采用以下方法进行分析：

1. **监控内存使用**
   ```bash
   free -h
   top
   ```

2. **分析Python内存使用**
   ```python
   import tracemalloc
   tracemalloc.start()
   # 执行代码
   snapshot = tracemalloc.take_snapshot()
   top_stats = snapshot.statistics('lineno')
   ```

3. **检查内存泄漏**
   - 使用`objgraph`库分析对象引用关系
   - 检查循环引用和未释放的资源

### 磁盘I/O性能分析

当系统出现磁盘I/O性能问题时，采用以下方法进行分析：

1. **监控磁盘使用**
   ```bash
   iostat -x 1
   df -h
   ```

2. **分析I/O等待**
   - 检查`%util`指标，接近100%表示磁盘饱和
   - 检查`await`指标，表示I/O平均等待时间

3. **优化日志写入**
   - 考虑使用异步日志写入
   - 调整日志轮转策略

### 网络性能分析

当系统出现网络性能问题时，采用以下方法进行分析：

1. **监控网络流量**
   ```bash
   iftop
   nethogs
   ```

2. **分析网络延迟**
   ```bash
   ping -c 100 <目标IP>
   mtr <目标IP>
   ```

3. **检查连接状态**
   ```bash
   netstat -an | grep <端口>
   ss -tuln
   ```

**Section sources**
- [settings.py](file://spider/settings.py#L461-L516)
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)

## 系统恢复与数据修复流程

### 系统恢复流程

当系统出现故障需要恢复时，按照以下流程操作：

1. **故障确认**
   - 确认故障现象和影响范围
   - 记录故障发生时间和相关日志

2. **启动应急预案**
   - 通知相关团队和人员
   - 启动故障处理流程

3. **隔离故障**
   - 将故障节点从服务集群中移除
   - 防止故障扩散

4. **恢复服务**
   - 使用备份恢复数据
   - 重启服务或切换到备用节点

5. **验证恢复**
   - 确认服务恢复正常
   - 进行功能测试和性能测试

6. **故障复盘**
   - 分析故障原因
   - 制定改进措施

### 数据修复流程

当发现数据异常需要修复时，按照以下流程操作：

1. **数据备份**
   - 在修复前先备份当前数据
   - 确保可以回滚到修复前状态

2. **分析数据问题**
   - 确定数据异常的范围和程度
   - 分析数据异常的原因

3. **制定修复方案**
   - 根据数据问题的性质选择合适的修复方法
   - 评估修复方案的风险

4. **执行数据修复**
   - 在测试环境验证修复脚本
   - 在生产环境执行修复操作

5. **验证修复结果**
   - 确认数据已修复且正确
   - 检查相关业务功能是否正常

6. **记录修复过程**
   - 记录数据修复的详细过程
   - 更新相关文档

**Section sources**
- [settings.py](file://spider/settings.py#L461-L516)
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)

## 故障报告模板与升级路径

### 故障报告模板

当发现系统故障时，应按照以下模板提交故障报告：

```markdown
**故障标题**：[简要描述故障现象]

**故障时间**：YYYY-MM-DD HH:MM:SS

**影响范围**：
- 受影响的系统/服务
- 受影响的用户/业务

**故障现象**：
- 详细描述故障表现
- 相关错误信息和截图

**初步分析**：
- 可能的原因分析
- 已进行的排查步骤

**紧急程度**：
- P0：系统完全不可用，影响核心业务
- P1：系统部分不可用，影响重要业务
- P2：系统功能异常，影响一般业务
- P3：系统轻微异常，不影响业务

**联系人**：
- 报告人：姓名，联系方式
- 相关方：姓名，联系方式
```

### 升级路径

当故障无法在规定时间内解决时，按照以下路径进行升级：

1. **一级响应**（30分钟内）
   - 开发团队负责人介入
   - 启动紧急排查

2. **二级响应**（1小时内）
   - 技术主管介入
   - 组织跨团队协作

3. **三级响应**（2小时内）
   - 技术总监介入
   - 升级到公司级应急响应

4. **四级响应**（4小时内）
   - 公司管理层介入
   - 启动重大故障处理流程

**Section sources**
- [settings.py](file://spider/settings.py#L461-L516)
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)

## 预防性维护建议与最佳实践

### 日常维护建议

1. **定期检查日志**
   - 每天检查系统日志，及时发现潜在问题
   - 定期清理过期日志，释放磁盘空间

2. **监控系统性能**
   - 设置性能监控告警
   - 定期分析性能趋势

3. **备份重要数据**
   - 定期备份数据库和配置文件
   - 验证备份的可用性

4. **更新系统补丁**
   - 及时更新操作系统和软件的安全补丁
   - 测试补丁的兼容性

### 最佳实践

1. **配置管理**
   - 所有配置信息集中管理
   - 配置变更需经过评审和测试

2. **变更管理**
   - 所有变更需有详细记录
   - 变更前需进行风险评估

3. **容量规划**
   - 定期评估系统容量需求
   - 提前规划资源扩展

4. **灾难恢复**
   - 定期演练灾难恢复流程
   - 确保恢复流程的有效性

5. **知识共享**
   - 记录常见问题的解决方案
   - 定期组织技术分享

**Section sources**
- [settings.py](file://spider/settings.py#L461-L516)
- [task_mgt/salt_view.py](file://task_mgt/salt_view.py#L21-L155)