<docs>
# 数据模型与数据库设计

<cite>
**本文档引用的文件**
- [models.py](file://be-scripts/app_mgt/models.py)
- [models.py](file://be-scripts/iter_mgt/models.py)
- [models.py](file://be-scripts/test_mgt/models.py)
- [base_model.py](file://be-scripts/dao/base_model.py)
- [iter_info.py](file://be-scripts/dao/get/mysql/iter_info.py)
- [iter_mgt_iter_app_info.py](file://be-scripts/dao/get/mysql/iter_mgt_iter_app_info.py)
- [iter_mgt_publish_application.py](file://be-scripts/dao/get/mysql/iter_mgt_publish_application.py)
- [db_mgt_bind_view.py](file://be-scripts/dao/get/mysql/db_mgt_bind_view.py)
- [settings.py](file://be-scripts/settings.py)
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py) - *数据库迁移脚本变更*
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py) - *SQL迁移业务对象定义*
- [models.py](file://be-scripts/db_mgt/creat_sql_migrate/models.py) - *新增数据库迁移相关模型*
</cite>

## 更新摘要
**变更内容**
- 迭代管理模块的数据模型进行了更新，主要涉及`iter_mgt`模块中的`IterInfo`和`IterAppInfo`实体
- `IterInfo`实体中`br_status`字段被重复定义，可能存在代码冗余或逻辑错误
- `IterAppInfo`实体新增了`git_repos_time`字段，用于记录制品时间
- 在`ci_pipeline`模块中发现了与`iter_mgt`模块重复的`IterAppInfoModel`和`BranchesModel`实体，可能存在模型同步问题
- 新增了`IterWhitelistApp`和`IterWhitelistGroup`等白名单相关实体，用于权限控制和开关管理

**更新部分**
- [核心数据模型分析](#核心数据模型分析)
- [实体关系与数据库模式](#实体关系与数据库模式)
- [关键数据模型详解](#关键数据模型详解)

## 目录
1. [引言](#引言)
2. [核心数据模型分析](#核心数据模型分析)
3. [实体关系与数据库模式](#实体关系与数据库模式)
4. [关键数据模型详解](#关键数据模型详解)
5. [数据访问模式与性能考虑](#数据访问模式与性能考虑)
6. [数据生命周期与管理策略](#数据生命周期与管理策略)
7. [结论](#结论)

## 引言

本文档旨在全面分析项目中的数据模型与数据库设计，重点关注app_mgt、test_mgt和iter_mgt等核心模块。通过深入分析这些模块中的数据实体、关系、约束和访问模式，揭示数据模型如何支持业务逻辑，特别是迭代信息模型如何有效跟踪发布流程。文档将提供详细的数据库模式图，解释数据类型、主键/外键约束，并讨论数据生命周期管理和性能优化策略。

**本文档引用的文件**
- [models.py](file://be-scripts/app_mgt/models.py)
- [models.py](file://be-scripts/iter_mgt/models.py)
- [models.py](file://be-scripts/test_mgt/models.py)
- [base_model.py](file://be-scripts/dao/base_model.py)

## 核心数据模型分析

项目中的数据模型主要围绕应用管理、测试管理和迭代管理三大核心模块构建。这些模块通过定义清晰的实体和关系，支持复杂的业务流程，如应用构建、测试环境管理和迭代发布。数据模型采用Peewee ORM框架实现，与MySQL数据库交互，确保数据的一致性和完整性。

数据模型的设计遵循模块化原则，每个模块包含与其业务功能相关的实体。例如，app_mgt模块管理应用的构建和部署信息，iter_mgt模块跟踪迭代的生命周期和发布流程，而test_mgt模块则负责测试环境和数据库的配置。这些模块通过共享的实体和外键约束相互关联，形成一个完整的数据生态系统。

最近的代码变更对`iter_mgt`模块的数据模型进行了更新，主要涉及`IterInfo`和`IterAppInfo`实体。值得注意的是，`IterInfo`实体中`br_status`字段被重复定义，这可能是一个代码错误或冗余。同时，在`ci_pipeline`模块中发现了与`iter_mgt`模块重复的`IterAppInfoModel`和`BranchesModel`实体，这表明可能存在模型同步或代码复用的问题。

此外，新增了`IterWhitelistApp`和`IterWhitelistGroup`等白名单相关实体，用于实现更精细的权限控制和开关管理。这些实体与`qc_mgt_guard_switch_info`表关联，支持基于组级别和应用级别的白名单配置。

**Section sources**
- [models.py](file://be-scripts/iter_mgt/models.py#L4-L112) - *迭代管理数据模型定义*
- [iter_mgt_ser.py](file://be-scripts/iter_mgt/iter_mgt_ser.py#L0-L237) - *迭代管理服务层实现*
- [iter_models.py](file://be-scripts/ci_pipeline/ci_pipeline_models/iter_models.py#L0-L141) - *CI/CD管道中的迭代模型定义*

## 实体关系与数据库模式

### 核心实体关系图

```mermaid
erDiagram
APP_MGT_APP_BUILD {
int id PK
int app_id
string module_name
string module_code
string module_version
string package_type
string package_name
bool package_full
string build_jdk_version
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
bool need_mock
string mock_build_cmd
string build_cmd
}
APP_MGT_APP_MODULE {
int id PK
int app_id
string module_name
string module_code
int module_status
string module_desc
string module_svn_path
string module_jdk_version
int need_online
int need_check
int app_port
string container_name
string create_path
string lib_repo
string deploy_path
string extend_attr
string create_user
string create_time
datetime update_time
int stamp
int zeus_type
int need_ops
string nacos_namespace
string nacos_conf_name
string share_module_name
int is_agent
int is_component
int batch_publish
int jenkins_batch_publish
int spring_version
}
ITER_MGT_ITER_INFO {
string pipeline_id PK
string br_status
string br_name
string br_style
string duedate
string description
string releaseNotic
string schedule
string file_ccms_config
datetime test_end_date
string update_test_end_date_user
}
ITER_MGT_ITER_APP_INFO {
string pipeline_id PK
string appName
string pom_path
string sys_status
string sys_duedate
string proposer
string simulate_identifier
string config_content
string git_last_version
string jdkVersion
string git_repo_version
string package_type
text git_path
string user_name
bool need_online
string build_cmd
}
ENV_MGT_CONTAINER {
string container_code PK
string container_name
string container_is_active
string container_desc
string k8s_code
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
}
DB_MGT_TABLE_INFO {
string module_name
string suite_code
string db_alias
string table_name
string column_name
string data_type
int data_max_length
string column_comment
datetime create_time
string creator
datetime update_time
string updater
}
DB_MGT_SQL {
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
string module_name
string iteration_id
string git_group
string br_name
string sql_file_path
string sql_file_name
string sql_file_hash
string sql_src_type
string sql_src_path
string sql_ver_name
string sql_ver_db
string sql_ver_group
string sql_ver_tgt
int sql_ver_upload_status
datetime sql_ver_upload_time
string sql_file_desc
string arc_sql_ver_name
}
USER_ACTION_RECORD {
string username
datetime operate_time
string action_item
string action_value
}
BIZ_TEST_APP_NEED {
string module_name
int need_auto_test
string biz_code
string biz_flow_name
int pass_rate_threshold
datetime create_time
datetime update_time
string create_user
string update_user
bigint stamp
}
APP_DEPENDENCY_INFO {
string module_name
string branch
string dependency_name
string dependency_version
int type
string source
datetime create_time
datetime update_time
string create_user
string update_user
bigint stamp
}
SDK_DEPENDENCY_RULE {
string sdk_name
string sdk_branch
string dependency_name
string min_version
string max_version
string whitelist
int type
int is_active
string desc
datetime create_time
datetime update_time
string create_user
string update_user
bigint stamp
}
DB_MGT_ARCHERY_CHECK_TEST_ENV {
string module_name
string check_suite_code
string create_user
datetime create_time
string update_user
datetime update_time
bigint stamp
}
ITER_MGT_SQL_CHECK_DETAIL {
string iteration_id
string module_name
text check_detail
string create_user
datetime create_time
string update_user
datetime update_time
}
ITER_WHITELIST_APP {
int wl_group_id
string wl_type
string wl_name
int wl_pass
int wl_value
string wl_opt_user
datetime wl_opt_time
string wl_desc
string create_user
datetime create_time
string update_user
datetime update_time
bigint stamp
}
ITER_WHITELIST_GROUP {
int wl_group_id PK
string wl_group_name
int wl_switch_id
int wl_group_pass
int wl_group_value
string wl_group_opt_user
datetime wl_group_opt_time
string wl_group_desc
string create_user
datetime create_time
string update_user
datetime update_time
bigint stamp
}
QC_MGT_GUARD_SWITCH_INFO {
int id PK
string guard_name
int guard_switch
}
APP_MGT_APP_BUILD ||--o{ APP_MGT_APP_MODULE : "belongs to"
ITER_MGT_ITER_INFO ||--o{ ITER_MGT_ITER_APP_INFO : "contains"
ITER_MGT_ITER_INFO ||--o{ USER_ACTION_RECORD : "has"
APP_MGT_APP_MODULE ||--o{ APP_DEPENDENCY_INFO : "has"
APP_MGT_APP_MODULE ||--o{ SDK_DEPENDENCY_RULE : "uses"
ENV_MGT_CONTAINER ||--o{ DB_MGT_TABLE_INFO : "hosts"
DB_MGT_TABLE_INFO ||--o{ DB_MGT_SQL : "contains"
DB_MGT_SQL ||--o{ DB_MGT_ARCHERY_CHECK_TEST_ENV : "validated by"
DB_MGT_SQL ||--o{ ITER_MGT_SQL_CHECK_DETAIL : "has check detail"
ITER_WHITELIST_GROUP ||--o{ ITER_WHITELIST_APP : "has"
ITER_WHITELIST_GROUP ||--o{ QC_MGT_GUARD_SWITCH_INFO : "controls"
```

**Diagram sources**
- [models.py](file://be-scripts/app_mgt/models.py)
- [models.py](file://be-scripts/iter_mgt/models.py)
- [models.py](file://be-scripts/test_mgt/models.py)
- [models.py](file://be-scripts/db_mgt/creat_sql_migrate/models.py) - *新增数据库迁移相关模型*
- [iter_models.py](file://be-scripts/ci_pipeline/ci_pipeline_models/iter_models.py) - *CI/CD管道中的迭代模型*

## 关键数据模型详解

### 应用管理模型 (app_mgt)

应用管理模块的数据模型主要由`AppBuildModel`和`AppMgtAppModule`两个核心实体构成，它们共同管理应用的构建和部署信息。

#### AppBuildModel (应用构建模型)

`AppBuildModel`实体存储了应用构建的详细信息，包括构建命令、JDK版本、包类型等。该模型通过`app_id`字段与`AppMgtAppModule`建立关联，形成一对多的关系。

- **关键字段定义**:
  - `app_id`: 整数类型，关联到应用模块的ID
  - `module_name`: 字符串类型，最大长度100，存储模块名称
  - `package_type`: 字符串类型，最大长度100，存储包类型（pom、war、jar、tar）
  - `build_jdk_version`: 字符串类型，最大长度100，存储编译JDK版本
  - `build_cmd`: 字符串类型，最大长度128，存储编译命令
  - `create_time`: 日期时间类型，存储创建时间
  - `stamp`: 整数类型，版本控制字段

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 外键: `app_id` 引用 `AppMgtAppModule` 的 `id`

[SPEC SYMBOL](file://be-scripts/app_mgt/models.py#L4-L38)

#### AppMgtAppModule (应用模块模型)

`AppMgtAppModule`实体是应用管理的核心，存储了应用模块的配置信息，包括部署路径、容器信息、Nacos配置等。

- **关键字段定义**:
  - `module_name`: 字符串类型，最大长度100，存储模块名称
  - `deploy_path`: 字符串类型，最大长度999，存储发布路径
  - `nacos_namespace`: 字符串类型，最大长度255，存储Nacos配置的命名空间
  - `nacos_conf_name`: 字符串类型，最大长度255，存储Nacos配置文件名
  - `spring_version`: 整数类型，存储Spring版本信息
  - `need_online`: 整数类型，标记是否需要上线

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 无直接外键，但通过`module_name`与其他模块关联

[SPEC SYMBOL](file://be-scripts/app_mgt/models.py#L40-L96)

### 迭代管理模型 (iter_mgt)

迭代管理模块的数据模型围绕迭代的生命周期和发布流程构建，核心实体包括`IterInfo`、`IterAppInfo`和`UserActionRecord`。

#### IterInfo (迭代信息模型)

`IterInfo`实体是迭代管理的核心，用于跟踪迭代的整个生命周期，从创建到发布完成。

- **关键字段定义**:
  - `pipeline_id`: 字符串类型，最大长度50，作为主键，标识迭代版本
  - `br_status`: 字符串类型，最大长度20，存储迭代状态（如"open"、"close"），**注意：该字段在代码中被重复定义**
  - `br_name`: 字符串类型，最大长度50，存储分支版本名称
  - `br_style`: 字符串类型，最大长度20，存储分支类型
  - `duedate`: 字符串类型，最大长度50，存储预期上线时间
  - `description`: 字符串类型，最大长度200，存储功能描述
  - `releaseNotic`: 字符串类型，最大长度200，存储注意事项
  - `schedule`: 字符串类型，最大长度200，存储调度信息
  - `file_ccms_config`: 字符串类型，最大长度200，存储文件或ccms配置信息
  - `test_end_date`: 日期时间类型，存储测试结束时间
  - `update_test_end_date_user`: 字符串类型，最大长度50，存储更新测试结束时间的操作人

- **主键/外键约束**:
  - 主键: `pipeline_id`
  - 无外键，但作为其他实体的关联基础

[SPEC SYMBOL](file://be-scripts/iter_mgt/models.py#L4-L20)

#### IterAppInfo (迭代应用信息模型)

`IterAppInfo`实体记录了迭代中每个应用的详细信息，是连接迭代和应用的关键桥梁。

- **关键字段定义**:
  - `pipeline_id`: 字符串类型，最大长度50，作为复合主键的一部分，引用`IterInfo`
  - `appName`: 字符串类型，最大长度100，存储应用名称
  - `pom_path`: 字符串类型，最大长度100，存储pom文件路径
  - `sys_status`: 字符串类型，最大长度20，存储应用状态
  - `sys_duedate`: 字符串类型，最大长度50，存储发布日期
  - `proposer`: 字符串类型，最大长度50，存储申请人
  - `simulate_identifier`: 字符串类型，最大长度20，存储仿真验证人
  - `config_content`: 字符串类型，最大长度100，存储配置文件
  - `git_last_version`: 字符串类型，最大长度50，存储最后一次编译版本
  - `jdkVersion`: 字符串类型，最大长度20，存储指定jdk版本
  - `git_repo_version`: 字符串类型，最大长度50，存储分支制品版本
  - `git_repos_time`: 日期时间类型，存储制品时间，**新增字段**
  - `package_type`: 字符串类型，最大长度10，存储应用类型
  - `git_path`: 文本类型，存储git路径
  - `user_name`: 字符串类型，最大长度100，存储用户名
  - `need_online`: 布尔类型，标记jar是否需要上线
  - `build_cmd`: 字符串类型，最大长度128，存储编译命令

- **主键/外键约束**:
  - 主键: (`pipeline_id`, `appName`)
  - 外键: `pipeline_id` 引用 `IterInfo` 的 `pipeline_id`

[SPEC SYMBOL](file://be-scripts/iter_mgt/models.py#L23-L45)

#### UserActionRecord (用户行为记录模型)

`UserActionRecord`实体用于审计和追踪用户的操作行为，支持系统的可追溯性。

- **关键字段定义**:
  - `username`: 字符串类型，最大长度50，存储用户名
  - `operate_time`: 日期时间类型，存储操作时间
  - `action_item`: 字符串类型，最大长度500，存储操作项
  - `action_value`: 文本类型，存储操作参数

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 无外键

[SPEC SYMBOL](file://be-scripts/iter_mgt/models.py#L48-L59)

#### IterWhitelistApp (迭代白名单应用模型)

`IterWhitelistApp`实体用于管理迭代和应用的白名单配置，支持权限控制和开关管理。

- **关键字段定义**:
  - `wl_group_id`: 整数类型，存储组级别白名单ID
  - `wl_type`: 字符串类型，最大长度20，存储白名单类型（1-应用、2-迭代）
  - `wl_name`: 字符串类型，最大长度100，存储白名主体（迭代名或者应用名）
  - `wl_pass`: 整数类型，存储白名单是否通过
  - `wl_value`: 整数类型，存储白名单阈值
  - `wl_opt_user`: 字符串类型，最大长度20，存储白名单操作人
  - `wl_opt_time`: 日期时间类型，存储白名单操作时间
  - `wl_desc`: 字符串类型，最大长度255，存储白名单描述
  - `create_user`: 字符串类型，最大长度20，存储创建人
  - `create_time`: 日期时间类型，存储创建时间
  - `update_user`: 字符串类型，最大长度20，存储更新人
  - `update_time`: 日期时间类型，存储更新时间
  - `stamp`: 长整数类型，存储版本

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 外键: `wl_group_id` 引用 `IterWhitelistGroup` 的 `wl_group_id`

[SPEC SYMBOL](file://be-scripts/iter_mgt/models.py#L100-L112)

#### IterWhitelistGroup (迭代白名单分组模型)

`IterWhitelistGroup`实体用于管理迭代白名单的分组配置，与`qc_mgt_guard_switch_info`表关联。

- **关键字段定义**:
  - `wl_group_id`: 整数类型，作为主键，存储白名单组ID
  - `wl_group_name`: 字符串类型，最大长度100，存储白名单git分组名称
  - `wl_switch_id`: 整数类型，存储白名单开关ID，引用`qc_mgt_guard_switch_info`的`id`
  - `wl_group_pass`: 整数类型，存储白名单组级别是否放行
  - `wl_group_value`: 整数类型，存储白名单组级别阈值
  - `wl_group_opt_user`: 字符串类型，最大长度20，存储白名单组级别操作人
  - `wl_group_opt_time`: 日期时间类型，存储白名单组级别操作时间
  - `wl_group_desc`: 字符串类型，最大长度255，存储白名单组级别描述
  - `create_user`: 字符串类型，最大长度20，存储创建人
  - `create_time`: 日期时间类型，存储创建时间
  - `update_user`: 字符串类型，最大长度20，存储更新人
  - `update_time`: 日期时间类型，存储更新时间
  - `stamp`: 长整数类型，存储版本

- **主键/外键约束**:
  - 主键: `wl_group_id`
  - 外键: `wl_switch_id` 引用 `qc_mgt_guard_switch_info` 的 `id`

[SPEC SYMBOL](file://be-scripts/iter_mgt/models.py#L114-L126)

### 测试管理模型 (test_mgt)

测试管理模块的数据模型主要关注测试环境和数据库的配置，核心实体包括`EnvMgtContainerModel`、`DbMgtTableInfo`和`DbMgtSql`。

#### EnvMgtContainerModel (容器信息模型)

`EnvMgtContainerModel`实体管理测试环境中的容器信息。

- **关键字段定义**:
  - `container_code`: 字符串类型，最大长度100，作为主键，存储容器编码
  - `container_name`: 字符串类型，最大长度100，存储容器名称
  - `container_is_active`: 字符串类型，最大长度100，存储容器可用性
  - `k8s_code`: 字符串类型，最大长度100，存储所属K8S集群编码

- **主键/外键约束**:
  - 主键: `container_code`
  - 无外键

[SPEC SYMBOL](file://be-scripts/test_mgt/models.py#L4-L27)

#### DbMgtTableInfo (数据库表信息模型)

`DbMgtTableInfo`实体存储了数据库表的元数据信息，用于数据库管理和维护。

- **关键字段定义**:
  - `module_name`: 字符串类型，最大长度100，存储应用名
  - `suite_code`: 字符串类型，最大长度20，存储环境编码
  - `db_alias`: 字符串类型，最大长度50，存储数据库别名
  - `table_name`: 字符串类型，最大长度100，存储表名
  - `column_name`: 字符串类型，最大长度50，存储字段名
  - `data_type`: 字符串类型，最大长度10，存储数据类型
  - `data_max_length`: 整数类型，存储数据最大长度

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 无外键

[SPEC SYMBOL](file://be-scripts/test_mgt/models.py#L29-L52)

#### DbMgtSql (数据库SQL模型)

`DbMgtSql`实体管理迭代中的SQL文件，是数据库变更管理的核心。最近的变更增强了该模型的功能，支持更复杂的SQL制品管理和验证。

- **关键字段定义**:
  - `module_name`: 字符串类型，最大长度100，存储应用名
  - `iteration_id`: 字符串类型，最大长度100，存储迭代名
  - `sql_file_path`: 字符串类型，最大长度999，存储SQL文件路径
  - `sql_file_hash`: 字符串类型，最大长度255，存储SQL文件哈希值（用于变更检测）
  - `sql_ver_name`: 字符串类型，最大长度255，存储SQL制品名
  - `sql_ver_upload_status`: 整数类型，存储SQL制品上传状态
  - `sql_src_type`: 字符串类型，最大长度100，存储SQL文件来源类型（决定解析规则）
  - `arc_sql_ver_name`: 字符串类型，最大长度255，存储归档后的制品名

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 无外键

[SPEC SYMBOL](file://be-scripts/test_mgt/models.py#L54-L90)

### 数据库迁移模型 (db_mgt)

数据库迁移模块是最近新增的核心功能，用于管理SQL制品的生成、版本化和验证。该模块引入了新的业务对象和数据模型，增强了数据库变更管理的能力。

#### SqlMigrateBo (SQL迁移业务对象)

`SqlMigrateBo`是一个业务对象，用于封装SQL迁移过程中的参数和状态。它采用Builder模式创建，确保了参数的完整性和有效性。

- **关键字段定义**:
  - `flag_file_dir`: 字符串类型，存储标志文件目录
  - `iteration_id`: 字符串类型，存储迭代ID
  - `app_name`: 字符串类型，存储应用名称
  - `workspace`: 字符串类型，存储工作空间路径
  - `sid`: 整数类型，存储会话ID
  - `git_group`: 字符串类型，存储Git分组
  - `br_name`: 字符串类型，存储分支名称
  - `vcs_iter_url`: 字符串类型，存储VCS迭代URL
  - `db_vcs_type`: 字符串类型，存储数据库VCS类型
  - `check_suite_code`: 字符串类型，存储SQL检查指定的环境
  - `biz_base_db`: 字符串类型，存储业务基础数据库标识

- **创建模式**:
  - 使用Builder模式创建实例，通过`verify_basic_property`方法验证必要属性
  - 提供了`set_*`方法用于动态设置属性

[SPEC SYMBOL](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py#L0-L147)

#### DbMgtArcheryCheckTestEnv (SQL检查环境模型)

`DbMgtArcheryCheckTestEnv`实体用于指定SQL验证的环境，支持在不同环境中进行SQL检查。

- **关键字段定义**:
  - `module_name`: 字符串类型，最大长度100，存储应用名
  - `check_suite_code`: 字符串类型，最大长度20，存储SQL检查指定的环境
  - `create_user`: 字符串类型，最大长度50，存储创建人
  - `create_time`: 日期时间类型，存储创建时间
  - `update_user`: 字符串类型，最大长度50，存储更新人
  - `update_time`: 日期时间类型，存储更新时间
  - `stamp`: 长整数类型，存储版本

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 无外键

[SPEC SYMBOL](file://be-scripts/db_mgt/creat_sql_migrate/models.py#L10-L24)

#### IterMgtSqlCheckDetail (SQL检查详情模型)

`IterMgtSqlCheckDetail`实体用于存储SQL检查的详细信息，支持问题追溯和审计。

- **关键字段定义**:
  - `iteration_id`: 字符串类型，最大长度100，存储迭代ID
  - `module_name`: 字符串类型，最大长度100，存储应用名
  - `check_detail`: 文本类型，存储SQL检查的详细信息
  - `create_user`: 字符串类型，最大长度100，存储创建人
  - `create_time`: 日期时间类型，存储创建时间
  - `update_user`: 字符串类型，最大长度100，存储更新人
  - `update_time`: 日期时间类型，存储更新时间

- **主键/外键约束**:
  - 主键: `id` (自动生成)
  - 无外键

[SPEC SYMBOL](file://be-scripts/db_mgt/creat_sql_migrate/models.py#L43-L57)

## 数据访问模式与性能考虑

### 数据访问模式

项目中的数据访问模式主要通过DAO（数据访问对象）层实现，遵循分层架构原则。DAO层封装了对数据库的直接操作，提供清晰的接口供上层业务逻辑调用。

#### 查询模式

查询操作主要通过`get`目录下的模块实现，如`iter_info.py`和`iter_mgt_iter_app_info.py`。这些模块定义了针对特定实体的查询方法，使用原生SQL或ORM查询构建复杂的查询逻辑。

- **迭代关系查询**: `get_iter_relations`方法通过JOIN操作查询迭代依赖的公共仓库，支持复杂的依赖管理。
- **应用信息查询**: `get_iter_app_info_for_archive`方法通过GROUP BY和聚合函数查询归档所需的应用信息，优化了批量数据获取。
- **SQL文件查询**: 新增了`get_sql_file_by_module_and_iter`方法，基于应用对应库的SQL进行查询，提高了查询效率。

[SPEC SYMBOL](file://be-scripts/dao/get/mysql/iter_info.py#L4-L416)
[SPEC SYMBOL](file://be-scripts/dao/get/mysql/iter_mgt_iter_app_info.py#L4-L216)
[SPEC SYMBOL](file://be-scripts/dao/get/mysql/db_mgt_info.py#L300-L350)

#### 更新模式

更新操作主要通过`insert`和`update`目录下的模块实现，确保数据的一致性和完整性。

- **事务管理**: 使用`with DBConnectionManager() as db:`语法确保数据库操作的原子性。
- **批量操作**: 通过`executemany`等方法支持批量插入和更新，提高数据处理效率。
- **SQL文件管理**: 新增了基于MD5哈希值的SQL文件变更检测机制，通过`__ins_or_upd_sql_file_by_md5`方法实现，避免了不必要的重复处理。

[SPEC SYMBOL](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py#L300-L400)

### 性能考虑

#### 索引策略

虽然代码中未直接定义索引，但根据字段的使用模式，可以推断出以下索引策略：

- **主键索引**: 所有实体的主键字段自动创建索引。
- **外键索引**: `pipeline_id`、`module_name`等频繁用于JOIN操作的字段应创建索引。
- **查询条件索引**: `br_status`、`sys_status`等用于WHERE条件的字段应创建索引。
- **新增索引**: `sql_file_hash`字段应创建索引，以支持快速的SQL文件变更检测。

#### 查询优化

- **避免N+1查询**: 通过JOIN操作一次性获取关联数据，减少数据库往返次数。
- **分页查询**: 对于大量数据的查询，使用LIMIT和OFFSET进行分页。
- **缓存策略**: 对于频繁读取但不常变更的数据，考虑使用缓存机制。
- **SQL解析优化**: 通过`get_db_group_by_iteration_id`等方法优化SQL解析过程，减少不必要的数据库查询。

[SPEC SYMBOL](file://be-scripts/dao/get/mysql/db_mgt_bind_view.py#L4-L156)

## 数据生命周期与管理策略

### 数据生命周期

项目中的数据遵循明确的生命周期管理策略，从创建到归档，确保数据的完整性和可追溯性。

#### 迭代数据生命周期

迭代数据的生命周期始于`IterInfo`的创建，经历开发、测试、发布等阶段，最终在发布完成后归档。

- **创建阶段**: 当新迭代创建时，`IterInfo`记录被插入，状态为"open"。
- **开发阶段**: 开发人员提交代码，`IterAppInfo`记录被更新，反映应用的最新状态。
- **发布阶段**: 发布申请通过后，`iter_mgt_publish_application`记录被创建，跟踪发布流程。
- **归档阶段**: 发布完成后，`br_status`更新为"close"，相关数据被标记为已归档。

#### 应用数据生命周期

应用数据的生命周期与迭代紧密相关，但具有更长的持续时间。

- **构建阶段**: `AppBuildModel`记录应用的构建信息，包括编译命令和JDK版本。
- **部署阶段**: `AppMgtAppModule`记录应用的部署配置，如发布路径和容器信息。
- **维护阶段**: 应用在生产环境中运行，其状态通过`need_online`等字段进行管理。
- **退役阶段**: 不再使用的应用可以通过`need_online=0`标记为退役。

#### SQL制品生命周期

SQL制品的生命周期是最近新增的重要部分，通过`DbMgtSql`实体和相关流程进行管理。

- **拉取阶段**: 从VCS（SVN/Git/GitLab）拉取SQL文件，通过`__pull_sql`方法实现。
- **版本化阶段**: 为SQL文件生成版本号，通过`__make_sql`方法实现。
- **检查阶段**: 在指定环境中进行SQL检查，通过`__check_sql`方法实现。
- **上传阶段**: 将SQL制品上传到目标仓库，通过`__push_sql`方法实现。
- **归档阶段**: 发布完成后，SQL制品被标记为已归档，相关信息存储在`arc_sql_ver_name`字段中。

[SPEC SYMBOL](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py#L200-L800)

### 管理策略

#### 数据一致性

- **外键约束**: 通过外键约束确保数据的引用完整性。
- **事务管理**: 使用数据库事务确保相关操作的原子性。
- **版本控制**: `stamp`字段用于乐观锁，防止并发更新冲突。
- **变更检测**: 通过`sql_file_hash`字段检测SQL文件的变更，确保数据的一致性。

#### 数据安全

- **敏感信息加密**: 密码等敏感信息应在存储前进行加密。
- **访问控制**: 通过应用层权限控制限制对敏感数据的访问。
- **审计日志**: `UserActionRecord`实体记录所有用户操作，支持安全审计。
- **SQL检查**: 通过`IterMgtSqlCheckDetail`实体记录SQL检查的详细信息，支持问题追溯。

#### 数据备份与恢复

- **定期备份**: 定期备份数据库，防止数据丢失。
- **归档策略**: 对历史数据进行归档，减少生产库的负担。
- **恢复测试**: 定期测试数据恢复流程，确保备份的有效性。
- **SQL制品管理**: 通过`DbMgtExecSqlFileHistory`实体记录SQL脚本的执行历史，支持恢复操作。

[SPEC SYMBOL](file://be-scripts/dao/get/mysql/iter_mgt_publish_application.py#L4-L163)

## 结论

本文档全面分析了项目中的数据模型与数据库设计，重点介绍了app_mgt、test_mgt和iter_mgt等核心模块的数据实体、关系和约束。通过详细的实体关系图和字段定义，揭示了数据模型如何支持复杂的业务逻辑，特别是迭代信息模型如何有效跟踪发布流程。

数据模型的设计体现了模块化和可扩展性的原则，通过清晰的实体划分和关系定义，支持了应用管理、测试管理和迭代管理等核心功能。DAO层的实现提供了高效的数据访问模式，而合理的性能考虑和数据生命周期管理策略确保了系统的稳定性和可靠性。

最近的变更对`iter_mgt`模块的数据模型进行了更新，主要涉及`IterInfo`和`IterAppInfo`实体。值得注意的是，`IterInfo`实体中`br_status`字段被重复定义，这可能是一个需要修复的代码问题。同时，在`ci_pipeline`模块中发现了与`iter_mgt`模块重复的`IterAppInfoModel`和`BranchesModel`实体，这表明可能存在模型同步或代码复用的问题，需要进一步评估和解决。

此外，新增了`IterWhitelistApp`和`IterWhitelistGroup`等白名单相关实体，用于实现更精细的权限控制和开关管理。这些实体与`qc_mgt_guard_switch_info`表关联，支持基于组级别和应用级别的白名单配置，增强了系统的灵活性和安全性。

未来，可以进一步优化索引策略，引入更高级的缓存机制，并加强数据安全措施，以应对不断增长的数据量和业务复杂性。同时，建议