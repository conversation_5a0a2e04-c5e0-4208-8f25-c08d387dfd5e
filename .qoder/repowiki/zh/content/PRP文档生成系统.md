# PRP文档生成系统

<cite>
**本文档引用文件**  
- [prp_base.md](file://PRPs/templates/prp_base.md)
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md)
- [version-management.md](file://PRPs/version-management.md)
- [businesscode-root-paths-interface.md](file://PRPs/businesscode-root-paths-interface.md)
- [metadata.json](file://PRPs/1.18.2/metadata.json)
- [metadata.json](file://PRPs/current/metadata.json)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)
10. [附录](#附录)（如有必要）

## 简介
PRP文档生成系统是一个为AI智能体优化的上下文工程框架，旨在通过富上下文和自我验证循环实现可工作的代码。该系统为功能实现和业务知识管理提供了标准化的模板和工作流程，确保开发过程的可追溯性和一致性。系统支持渐进式成功模式，从简单开始，验证，然后增强，同时遵循全局规则以确保与CLAUDE.md中定义的所有规则保持一致。

## 项目结构
PRP文档生成系统采用标准化的目录结构设计，为每个Git分支/迭代建立独立的上下文工程文件管理结构。该结构确保了需求文档、PRP文件和知识图谱文件的版本一致性和可追溯性。

```mermaid
graph TD
PRPs[PRPs/]
--> templates[templates/]
--> prp_base["prp_base.md<br/>基础PRP模板"]
--> kg_prp_base["kg_prp_base.md<br/>知识图谱PRP模板"]
PRPs --> version1["{branch-version}/"]
--> requirements["requirements/"]
--> INITIAL["INITIAL.md<br/>功能需求文档"]
--> KG_INITIAL["KG-INITIAL.md<br/>知识图谱需求文档"]
version1 --> prps["prps/"]
--> feature_prp["{feature-name}.md<br/>功能实现PRP"]
--> kg_prp["KG-{feature-name}.md<br/>知识图谱PRP"]
version1 --> metadata["metadata.json<br/>版本元数据信息"]
PRPs --> current["current -> {latest-version}<br/>当前版本符号链接"]
```

**图示来源**  
- [version-management.md](file://PRPs/version-management.md#L20-L49)

**本节来源**  
- [version-management.md](file://PRPs/version-management.md#L0-L49)
- [prp_base.md](file://PRPs/templates/prp_base.md#L0-L54)
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md#L0-L31)

## 核心组件
PRP文档生成系统的核心组件包括基础PRP模板、业务知识图谱PRP模板和版本管理规范。基础PRP模板为AI智能体提供了实现功能所需的上下文和验证循环，而业务知识图谱PRP模板则专注于业务领域知识的管理和同步。版本管理规范确保了每个迭代的文件独立管理，避免版本间文件混淆，并支持并行开发分支。

**本节来源**  
- [prp_base.md](file://PRPs/templates/prp_base.md#L0-L54)
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md#L0-L31)
- [version-management.md](file://PRPs/version-management.md#L0-L49)

## 架构概述
PRP文档生成系统的架构基于上下文工程版本管理规范，为每个Git分支/迭代建立标准化的文件管理结构。系统通过`/generate-prp`和`/generate-kg-prp`命令自动生成PRP文件，这些命令会自动检测当前Git分支，创建对应的版本目录结构，并将需求文档复制到相应位置。生成的PRP文件存储在`prps/`目录下，同时更新`metadata.json`文件以记录版本元数据。

```mermaid
graph LR
subgraph "输入"
A[INITIAL.md] --> C[/generate-prp/]
B[KG-INITIAL.md] --> D[/generate-kg-prp/]
end
subgraph "处理"
C --> E[创建版本目录]
D --> E
E --> F[复制需求文档]
F --> G[生成PRP文件]
G --> H[更新metadata.json]
end
subgraph "输出"
H --> I[PRPs/{branch-version}/prps/{feature-name}.md]
H --> J[PRPs/{branch-version}/prps/KG-{feature-name}.md]
end
```

**图示来源**  
- [version-management.md](file://PRPs/version-management.md#L50-L98)

**本节来源**  
- [version-management.md](file://PRPs/version-management.md#L50-L98)
- [version-management.md](file://PRPs/version-management.md#L99-L172)

## 详细组件分析

### 基础PRP模板分析
基础PRP模板为AI智能体优化，用于实现具有足够上下文和自我验证能力的功能。该模板遵循"上下文为王"的原则，包含所有必要的文档、示例和注意事项，并提供AI可以运行和修复的可执行测试/检查。

#### 核心原则
```mermaid
graph TD
A[核心原则] --> B[上下文为王]
A --> C[验证循环]
A --> D[信息密集]
A --> E[渐进式成功]
A --> F[全局规则]
B --> B1["包含所有必要的文档、示例和注意事项"]
C --> C1["提供AI可以运行和修复的可执行测试/检查"]
D --> D1["使用代码库中的关键字和模式"]
E --> E1["从简单开始，验证，然后增强"]
F --> F1["确保遵循CLAUDE.md中的所有规则"]
```

**图示来源**  
- [prp_base.md](file://PRPs/templates/prp_base.md#L4-L9)

**本节来源**  
- [prp_base.md](file://PRPs/templates/prp_base.md#L0-L54)

### 业务知识图谱PRP模板分析
业务知识图谱PRP模板专为业务领域知识管理优化，用于分析、抽取和管理业务知识，确保知识图谱准确反映业务现状和规则。

#### 核心原则
```mermaid
graph TD
A[核心原则] --> B[业务导向]
A --> C[领域专家友好]
A --> D[知识完整性]
A --> E[渐进式构建]
A --> F[可视化管理]
B --> B1["以业务概念和流程为中心，而非技术实现"]
C --> C1["使用业务术语，便于非技术人员理解和参与"]
D --> D1["确保业务知识的完整性和关联关系的准确性"]
E --> E1["支持业务知识的逐步完善和演进"]
F --> F1["提供直观的知识图谱查看和管理界面"]
```

**图示来源**  
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md#L4-L9)

**本节来源**  
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md#L0-L31)

### 版本管理规范分析
版本管理规范为每个Git分支/迭代建立标准化的上下文工程文件管理结构，确保需求文档、PRP文件和知识图谱文件的版本一致性和可追溯性。

#### 工作流程
```mermaid
flowchart TD
Start([开始]) --> CreateDir["创建新版本目录"]
CreateDir --> CreateMetadata["创建版本元数据"]
CreateMetadata --> CopyRequirements["复制需求文档"]
CopyRequirements --> GeneratePRP["生成PRP文件"]
GeneratePRP --> UpdateMetadata["更新metadata.json"]
UpdateMetadata --> End([完成])
style Start fill:#f9f,stroke:#333
style End fill:#bbf,stroke:#333
```

**图示来源**  
- [version-management.md](file://PRPs/version-management.md#L50-L98)

**本节来源**  
- [version-management.md](file://PRPs/version-management.md#L50-L98)
- [version-management.md](file://PRPs/version-management.md#L99-L172)

## 依赖分析
PRP文档生成系统依赖于一系列文件和目录结构来实现其功能。系统依赖于`PRPs/templates/`目录中的模板文件，以及`PRPs/{branch-version}/`目录结构来组织版本特定的文件。此外，系统依赖于`metadata.json`文件来存储版本元数据信息。

```mermaid
graph TD
A[PRP文档生成系统] --> B[PRPs/templates/]
B --> C[prp_base.md]
B --> D[kg_prp_base.md]
A --> E[PRPs/{branch-version}/]
E --> F[requirements/]
F --> G[INITIAL.md]
F --> H[KG-INITIAL.md]
E --> I[prps/]
I --> J[{feature-name}.md]
I --> K[KG-{feature-name}.md]
E --> L[metadata.json]
```

**图示来源**  
- [version-management.md](file://PRPs/version-management.md#L20-L49)

**本节来源**  
- [version-management.md](file://PRPs/version-management.md#L0-L49)
- [prp_base.md](file://PRPs/templates/prp_base.md#L0-L54)
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md#L0-L31)

## 性能考虑
PRP文档生成系统的性能主要取决于文件操作的效率和命令执行的速度。由于系统主要涉及文件读写和目录创建操作，其性能通常不会成为瓶颈。然而，在处理大量并发请求时，可能需要考虑文件锁机制以防止冲突。此外，系统应优化`/generate-prp`和`/generate-kg-prp`命令的执行效率，以减少生成PRP文件所需的时间。

[本节不分析特定文件，因此无需添加来源]

## 故障排除指南
当PRP文档生成系统出现问题时，可以按照以下步骤进行故障排除：

1. **检查目录结构**：确保`PRPs/`目录及其子目录结构正确无误。
2. **验证模板文件**：检查`PRPs/templates/`目录中的`prp_base.md`和`kg_prp_base.md`文件是否存在且内容正确。
3. **检查版本元数据**：验证`metadata.json`文件的格式和内容是否符合规范。
4. **测试生成命令**：手动执行`/generate-prp`和`/generate-kg-prp`命令，检查输出结果和错误信息。
5. **查看日志**：检查系统日志以获取更多调试信息。

**本节来源**  
- [version-management.md](file://PRPs/version-management.md#L99-L172)
- [prp_base.md](file://PRPs/templates/prp_base.md#L0-L54)
- [kg_prp_base.md](file://PRPs/templates/kg_prp_base.md#L0-L31)

## 结论
PRP文档生成系统通过提供标准化的模板和工作流程，为AI智能体实现功能和管理业务知识提供了强大的支持。系统采用清晰的目录结构和版本管理规范，确保了开发过程的可追溯性和一致性。通过遵循核心原则，如"上下文为王"和"验证循环"，系统能够帮助开发人员逐步构建可工作的代码。未来，可以考虑进一步优化系统性能，增加更多的自动化功能，以及提供更丰富的可视化工具来增强用户体验。

[本节不分析特定文件，因此无需添加来源]

## 附录
### 示例：创建新迭代
```bash
# 1. 开始新迭代（假设当前分支是feature/optimization）
git checkout -b feature/optimization

# 2. 创建需求文档
echo "# 文件服务性能优化需求" > INITIAL.md
echo "# 文件服务知识图谱更新" > KG-INITIAL.md

# 3. 生成PRP文件（命令会自动创建版本目录）
/generate-prp INITIAL.md
# 输出: PRPs/feature-optimization/prps/file-service-optimization.md

/generate-kg-prp KG-INITIAL.md
# 输出: PRPs/feature-optimization/prps/KG-file-service-optimization.md

# 4. 查看版本结构
tree PRPs/feature-optimization/
```

**本节来源**  
- [version-management.md](file://PRPs/version-management.md#L217-L238)