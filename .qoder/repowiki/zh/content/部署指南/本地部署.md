# 本地部署

<cite>
**本文档引用的文件**  
- [requirements.txt](file://requirements.txt)
- [spider/settings.py](file://spider/settings.py)
- [gunicorn.conf.py](file://gunicorn.conf.py)
- [start.py](file://start.py)
- [spider/settings.ini](file://spider/settings.ini)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [Python虚拟环境与依赖安装](#python虚拟环境与依赖安装)
4. [数据库初始化与迁移](#数据库初始化与迁移)
5. [核心配置项说明](#核心配置项说明)
6. [开发服务器启动](#开发服务器启动)
7. [生产级WSGI服务器配置](#生产级wsgi服务器配置)
8. [环境变量与敏感信息管理](#环境变量与敏感信息管理)
9. [常见问题排查](#常见问题排查)
10. [附录](#附录)

## 简介
本文档提供详细的本地部署指南，涵盖开发环境和测试环境的安装与配置。详细说明Python虚拟环境的创建和依赖安装，包括Django、MySQL客户端、Jenkins库等。提供数据库初始化和迁移命令的完整流程。解释settings.py中的关键配置项，如DEBUG模式、数据库连接、静态文件处理等。描述使用start.py启动开发服务器的步骤，以及使用gunicorn.conf.py配置生产级WSGI服务器的方法。提供环境变量的配置建议，包括数据库密码、API密钥等敏感信息的管理。包含常见问题排查，如端口冲突、依赖版本不兼容、数据库连接失败等解决方案。

## 项目结构
本项目采用Django框架构建，主要目录结构如下：
- `app_mgt`, `biz_mgt`, `ci_cd_mgt`等：业务模块
- `db`：数据库SQL脚本
- `spider`：Django核心配置
- `public`, `user`, `pipeline`等：功能组件
- 根目录包含启动脚本和配置文件

```mermaid
graph TD
A[项目根目录] --> B[业务模块]
A --> C[数据库脚本]
A --> D[Django核心]
A --> E[功能组件]
A --> F[启动脚本]
B --> B1[app_mgt]
B --> B2[biz_mgt]
B --> B3[ci_cd_mgt]
C --> C1[hm-1.0.0]
C --> C2[spider迭代3_3_7]
D --> D1[settings.py]
D --> D2[urls.py]
D --> D3[wsgi.py]
F --> F1[start.py]
F --> F2[gunicorn.conf.py]
F --> F3[manage.py]
```

**Diagram sources**
- [spider/settings.py](file://spider/settings.py#L1-L50)
- [gunicorn.conf.py](file://gunicorn.conf.py#L1-L10)
- [start.py](file://start.py#L1-L10)

**Section sources**
- [spider/settings.py](file://spider/settings.py#L1-L50)
- [gunicorn.conf.py](file://gunicorn.conf.py#L1-L10)
- [start.py](file://start.py#L1-L10)

## Python虚拟环境与依赖安装

### 创建Python虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
```

### 安装依赖包
根据requirements.txt安装所有依赖：
```bash
pip install -r requirements.txt
```

关键依赖包括：
- Django==3.2：Web框架
- mysqlclient==2.1.0：MySQL数据库客户端
- python-jenkins==1.8.0：Jenkins库
- gunicorn==20.1.0：WSGI HTTP服务器
- djangorestframework==3.13.1：REST框架

**Section sources**
- [requirements.txt](file://requirements.txt#L1-L63)

## 数据库初始化与迁移

### 数据库配置
数据库连接信息在spider/settings.ini中配置，通过spider/settings.py读取。

### 执行数据库迁移
```bash
# 生成迁移文件
python manage.py makemigrations

# 应用迁移到数据库
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser
```

### 数据库测试配置
在settings.py中配置了测试数据库：
```python
'TEST': {
    'NAME': "test_" + local_settings.get('MYSQL', 'DB'),
    'CHARSET': 'utf8mb4',
}
```

**Section sources**
- [spider/settings.py](file://spider/settings.py#L150-L180)
- [manage.py](file://manage.py#L1-L22)

## 核心配置项说明

### DEBUG模式
```python
DEBUG = True  # 开发环境启用，生产环境应设为False
```
控制调试信息的显示，生产环境必须关闭。

### 数据库连接
通过settings.ini配置数据库连接参数，在settings.py中读取：
```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': local_settings.get('MYSQL', 'DB'),
        'USER': local_settings.get('MYSQL', 'USER'),
        'PASSWORD': local_settings.get('MYSQL', 'PASSWORD'),
        'HOST': local_settings.get('MYSQL', 'IP'),
        'PORT': local_settings.get('MYSQL', 'PORT'),
    }
}
```

### 静态文件处理
```python
STATIC_ROOT = '/data/app/admin-static'
STATIC_URL = '/spider/static/'
```
静态文件收集到STATIC_ROOT目录，通过STATIC_URL访问。

### 跨域配置
```python
CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_CREDENTIALS = True
```
允许所有域名访问，支持携带凭证。

**Section sources**
- [spider/settings.py](file://spider/settings.py#L50-L200)

## 开发服务器启动

### 使用start.py启动
start.py脚本提供了启动、停止、重启功能：
```python
def start(self):
    os.system("nohup python3.x /home/<USER>/spider/manage.py runserver 0.0.0.0:9011 > {} 2>&1 &".format(log_path))

def stop(self):
    os.system("pkill -9 -f spider/manage.py")

def restart(self):
    self.stop()
    self.start()
```

### 手动启动开发服务器
```bash
python manage.py runserver 0.0.0.0:9011
```

### 日志配置
日志文件路径：`/data/logs/spider/web.log`

**Section sources**
- [start.py](file://start.py#L1-L34)
- [spider/settings.py](file://spider/settings.py#L30-L40)

## 生产级WSGI服务器配置

### gunicorn配置
gunicorn.conf.py配置了生产级服务器参数：
```python
bind = "0.0.0.0:9011"
worker_class = 'gevent'
workers = 3
threads = 512
loglevel = 'info'
accesslog = os.path.join(BASE_DIR, "logs", "spider-access-log.log")
errorlog = os.path.join(BASE_DIR, "logs", "spider-error-log.log")
```

### 启动gunicorn服务器
```bash
gunicorn spider.wsgi -c gunicorn.conf.py
```

### 配置说明
- 使用gevent工作模式提高并发性能
- 3个工作进程，每个进程512个线程
- 访问日志和错误日志分别记录
- 绑定所有IP的9011端口

**Section sources**
- [gunicorn.conf.py](file://gunicorn.conf.py#L1-L19)
- [spider/wsgi.py](file://spider/wsgi.py)

## 环境变量与敏感信息管理

### 配置文件分离
敏感信息存储在spider/settings.ini中，不在代码中硬编码。

### 关键配置项
- 数据库连接信息
- Jenkins认证信息
- Salt API凭证
- 邮件服务器配置
- API密钥

### 安全建议
1. settings.ini文件不应提交到版本控制系统
2. 生产环境使用不同的配置文件
3. 敏感信息使用环境变量或密钥管理服务
4. 定期轮换密码和密钥

**Section sources**
- [spider/settings.py](file://spider/settings.py#L80-L100)
- [spider/settings.ini](file://spider/settings.ini)

## 常见问题排查

### 端口冲突
**问题**：9011端口已被占用  
**解决方案**：
```bash
# 查看占用端口的进程
lsof -i :9011
# 或
netstat -tlnp | grep 9011

# 终止占用进程
kill -9 <PID>
```

### 依赖版本不兼容
**问题**：安装依赖时出现版本冲突  
**解决方案**：
```bash
# 清理缓存
pip cache purge

# 创建干净的虚拟环境
python -m venv new_venv
source new_venv/bin/activate

# 逐个安装关键依赖
pip install Django==3.2
pip install mysqlclient==2.1.0
```

### 数据库连接失败
**问题**：无法连接MySQL数据库  
**检查步骤**：
1. 确认settings.ini中数据库配置正确
2. 检查MySQL服务是否运行
3. 验证数据库用户权限
4. 检查网络连接和防火墙设置

### Django命令无法识别
**问题**：执行manage.py命令时报错  
**解决方案**：
```bash
# 确保虚拟环境已激活
source venv/bin/activate

# 检查Django是否安装
python -c "import django; print(django.get_version())"

# 检查PYTHONPATH
export PYTHONPATH="${PYTHONPATH}:/path/to/project"
```

**Section sources**
- [spider/settings.py](file://spider/settings.py)
- [requirements.txt](file://requirements.txt)
- [start.py](file://start.py)

## 附录

### 环境配置示例
spider/settings.ini示例：
```ini
[MYSQL]
DB=spider_db
USER=spider_user
PASSWORD=spider_password
IP=127.0.0.1
PORT=3306

[JENKINS_INFO]
USER=jenkins_user
PASSWORD=jenkins_password
URL=http://jenkins.example.com
```

### 启动脚本参数说明
- start.py：开发环境启动脚本
- gunicorn.conf.py：生产环境配置文件
- manage.py：Django管理命令

### 目录权限要求
确保以下目录有适当的读写权限：
- `/data/logs/spider/`：日志目录
- `/data/app/admin-static/`：静态文件目录
- `~/logs/spider/`：临时日志目录