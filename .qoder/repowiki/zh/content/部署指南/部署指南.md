# 部署指南

<cite>
**本文档引用的文件**  
- [Dockerfile](file://Dockerfile)
- [docker/Dockerfile](file://docker/Dockerfile)
- [spider/settings.py](file://spider/settings.py)
- [spider/settings.ini](file://spider/settings.ini)
- [requirements.txt](file://requirements.txt)
- [说明/README.md](file://说明/README.md)
- [说明/开发运行条件.md](file://说明/开发运行条件.md)
- [说明/software_structure.md](file://说明/software_structure.md)
- [main.py](file://main.py)
- [start.py](file://start.py)
- [start9011.py](file://start9011.py)
- [start9031.py](file://start9031.py)
- [gunicorn.conf.py](file://gunicorn.conf.py)
- [manage.py](file://manage.py)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [系统依赖安装与配置](#系统依赖安装与配置)
4. [Python环境配置](#python环境配置)
5. [数据库配置](#数据库配置)
6. [中间件与外部服务配置](#中间件与外部服务配置)
7. [开发、测试、生产环境部署方案](#开发测试生产环境部署方案)
8. [Docker部署指南](#docker部署指南)
9. [系统服务启动、停止与监控](#系统服务启动停止与监控)
10. [高可用部署与负载均衡](#高可用部署与负载均衡)
11. [性能调优建议](#性能调优建议)
12. [日志收集、监控与告警](#日志收集监控与告警)
13. [常见部署问题解决方案](#常见部署问题解决方案)

## 简介
本部署指南旨在为系统提供完整的安装、配置和启动流程说明。涵盖开发、测试、生产等不同环境的部署方案，详细描述系统依赖的安装与配置，包括Python环境、数据库、中间件等。同时提供Docker部署的完整指南，解释系统服务的启动、停止和监控方式，并包含高可用部署方案、性能调优建议以及日志监控配置。

## 项目结构
系统采用模块化设计，主要由多个业务包和基础包组成，各模块职责明确，便于维护和扩展。

```mermaid
graph TD
subgraph "核心业务模块"
A[app_mgt 应用管理]
B[env_mgt 环境管理]
C[iter_mgt 迭代管理]
D[pipeline 流水线管理]
E[publish 发布管理]
F[user 用户管理]
end
subgraph "支持服务模块"
G[task_mgt 任务管理]
H[ci_cd_mgt CI/CD管理]
I[db_mgt 数据库管理]
J[es_mgt Elasticsearch管理]
end
subgraph "系统核心"
K[spider 系统基础功能]
L[public 公共工具]
end
K --> A
K --> B
K --> C
K --> D
K --> E
K --> F
K --> G
K --> H
K --> I
K --> J
L --> G
L --> H
```

**图示来源**
- [说明/README.md](file://说明/README.md)
- [说明/software_structure.md](file://说明/software_structure.md)

## 系统依赖安装与配置
系统依赖主要包括操作系统级工具、Python包依赖和外部服务依赖。

### 操作系统依赖
根据Dockerfile配置，系统需要安装以下基础依赖：
- 编译工具：gcc, gcc-c++, make
- Python开发库：python3-devel
- 数据库客户端库：mariadb-devel
- 其他工具：tree, zip, unzip, wget, sshpass

### Python依赖
系统通过requirements.txt文件管理Python依赖包，主要依赖包括：
- Django 3.2 框架
- Django REST framework
- MySQL数据库驱动
- Nacos SDK
- Jenkins Python客户端
- SaltStack API支持

**章节来源**
- [requirements.txt](file://requirements.txt)
- [Dockerfile](file://Dockerfile)

## Python环境配置
系统使用Python 3.9环境运行，推荐使用虚拟环境进行依赖管理。

### 环境准备
```bash
# 设置内部PyPI源
pip3 config set global.index-url http://pypi.howbuy.pa/simple
pip3 config set install.trusted-host pypi.howbuy.pa

# 安装依赖
pip install -r requirements.txt
```

### 配置文件
系统使用`spider/settings.ini`作为主要配置文件，通过`spider/settings.py`读取并转换为Django配置。配置文件采用INI格式，包含数据库、Jenkins、Salt API、邮件等各类服务的连接信息。

```mermaid
graph LR
A[settings.ini] --> B[配置解析]
B --> C[数据库配置]
B --> D[Jenkins配置]
B --> E[Salt API配置]
B --> F[邮件配置]
B --> G[外部接口配置]
C --> H[Django DATABASES]
D --> I[JENKINS_INFO]
E --> J[SALT_API]
F --> K[EMAIL_BASES]
G --> L[INTERFACE_URL]
```

**章节来源**
- [spider/settings.py](file://spider/settings.py)
- [spider/settings.ini](file://spider/settings.ini)
- [说明/开发运行条件.md](file://说明/开发运行条件.md)

## 数据库配置
系统使用MySQL作为主要数据库，配置信息存储在`settings.ini`文件中。

### 数据库连接配置
```ini
[MYSQL]
charset = utf8
db = spider
port = 3306
password = 123456
ip = spiderdb-test.inner.howbuy.com
user = scm
```

### Django数据库配置
在`settings.py`中，系统将INI配置转换为Django的DATABASES配置：
- 使用MySQL后端
- 设置UTF8MB4字符集
- 配置测试数据库
- 设置SQL模式为严格模式

### 数据库初始化
系统包含多个SQL脚本目录，用于不同迭代版本的数据库结构更新：
- `db/schema`：基础数据库结构
- `db/迭代*`：各迭代版本的增量更新脚本
- `db/分支*`：分支版本的数据库脚本

**章节来源**
- [spider/settings.py](file://spider/settings.py#L150-L180)
- [spider/settings.ini](file://spider/settings.ini#L100-L110)
- [db](file://db)

## 中间件与外部服务配置
系统依赖多个中间件和外部服务，通过配置文件进行统一管理。

### Salt API配置
系统使用SaltStack进行远程命令执行和配置管理，支持多个环境的Salt Master：
- 生产环境：外高桥、唐镇、香港等
- 测试环境：UAT、Beta等
- 灾备环境：移动端灾备

```python
SALT_API = {
    'prod': 'https://192.168.222.236:8000/',
    'zb': 'https://192.168.222.236:8000/',
    'bs-prod': 'https://192.168.222.236:8000/',
    'beta': 'https://192.168.222.236:8000/',
}
```

### Jenkins配置
系统与Jenkins集成，用于构建和发布任务：
```python
JENKINS_INFO = {
    "USER": "howbuyscm",
    "PASSWORD": "Howbuy!@#",
    "URL": "http://jenkinstest-master.howbuy.pa/jenkins",
    "HM_URL": "http://jenkinstest-master.howbuy.pa/jenkins/",
    "SSH_USER": "tomcat",
    "SSH_PASSWORD": "howbuy2015",
}
```

### 其他外部服务
- **GitLab**：代码仓库管理
- **Nacos**：配置中心
- **RocketMQ**：消息队列
- **CMDB**：配置管理数据库
- **Zeus**：调度系统

**章节来源**
- [spider/settings.py](file://spider/settings.py#L200-L350)
- [spider/settings.ini](file://spider/settings.ini#L120-L200)

## 开发测试生产环境部署方案
系统支持多种环境的部署，通过配置文件区分不同环境的参数。

### 环境配置策略
系统采用配置驱动的方式管理不同环境，主要通过`settings.ini`文件的不同配置实现环境隔离。

#### 开发环境
- 数据库：本地或开发测试数据库
- 调试模式：DEBUG = True
- 日志级别：INFO
- 外部服务：使用测试环境地址

#### 测试环境
- 数据库：独立的测试数据库
- 调试模式：DEBUG = True
- 外部服务：使用UAT环境地址
- 特殊配置：启用测试专用的Jenkins任务

#### 生产环境
- 数据库：生产数据库
- 调试模式：DEBUG = False
- 安全配置：限制ALLOWED_HOSTS
- 性能优化：启用数据库连接池
- 外部服务：使用生产环境地址

### 配置管理
系统通过环境变量和配置文件的组合方式管理配置：
1. 基础配置：`settings.ini`文件
2. 环境覆盖：通过环境变量覆盖特定配置
3. 敏感信息：密码等敏感信息通过配置文件管理

**章节来源**
- [spider/settings.py](file://spider/settings.py)
- [spider/settings.ini](file://spider/settings.ini)

## Docker部署指南
系统提供完整的Docker部署支持，包含基础镜像和应用镜像。

### 基础镜像构建
```dockerfile
FROM harbor-test.inner.howbuy.com/pa/centos:7.9-base

# 安装系统依赖
RUN yum install gcc gcc-c++ make python3-devel mariadb-devel -y

# 设置工作目录
WORKDIR /data/app

# 复制依赖文件
COPY pyproject.toml /data/app/
COPY .python-version /data/app/
COPY uv.lock /data/app/
```

### 应用镜像构建
```dockerfile
FROM zt-py39:1.0.0

# 设置工作目录
WORKDIR /data/app/spider/

# 安装额外工具
RUN yum install tree zip unzip wget sshpass -y

# 复制应用代码
COPY spider/.python-version /data/app/
COPY spider/pyproject.toml /data/app/
COPY spider/uv.lock /data/app/

# 同步Python依赖
RUN uv sync

# 启动命令
ENTRYPOINT ["/bin/bash", "-c", "/data/app/.venv/bin/python3 manage.py runserver 0.0.0.0:9000 --noreload > /data/logs/spider/spider-web.log 2>&1"]
```

### 容器编排
推荐使用Docker Compose或Kubernetes进行容器编排：

```yaml
version: '3'
services:
  spider-app:
    build: .
    ports:
      - "9000:9000"
    volumes:
      - ./spider:/data/app/spider/
      - ./logs:/data/logs/spider/
    environment:
      - MYSQL_IP=spiderdb.inner.howbuy.com
      - MYSQL_USER=spider
      - MYSQL_PASSWORD=secure_password
```

### 网络配置
- 应用容器需要访问数据库、Jenkins、Salt Master等外部服务
- 建议使用内部DNS或服务发现机制
- 生产环境应配置网络策略限制不必要的访问

**章节来源**
- [Dockerfile](file://Dockerfile)
- [docker/Dockerfile](file://docker/Dockerfile)

## 系统服务启动、停止与监控
系统提供多种方式启动和管理服务。

### 启动方式
#### 开发模式
```bash
python manage.py runserver 0.0.0.0:9000
```

#### 生产模式
```bash
# 使用Gunicorn
gunicorn spider.wsgi:application -c gunicorn.conf.py

# 或使用启动脚本
python start.py
python start9011.py
python start9031.py
```

### Gunicorn配置
`gunicorn.conf.py`文件包含生产环境的Gunicorn配置：
- 工作进程数
- 绑定地址和端口
- 日志配置
- 超时设置

### 服务停止
```bash
# 查找进程
ps aux | grep gunicorn

# 终止进程
kill <pid>

# 或使用kill命令
pkill gunicorn
```

### 监控方式
- **进程监控**：使用ps、top等系统工具
- **日志监控**：监控`/data/logs/spider/`目录下的日志文件
- **健康检查**：通过HTTP接口检查服务状态
- **资源监控**：监控CPU、内存、磁盘使用情况

**章节来源**
- [main.py](file://main.py)
- [start.py](file://start.py)
- [start9011.py](file://start9011.py)
- [start9031.py](file://start9031.py)
- [gunicorn.conf.py](file://gunicorn.conf.py)

## 高可用部署与负载均衡
为确保系统高可用性，建议采用以下部署架构。

### 高可用架构
```mermaid
graph LR
A[客户端] --> B[负载均衡器]
B --> C[应用实例1]
B --> D[应用实例2]
B --> E[应用实例N]
C --> F[共享数据库]
D --> F
E --> F
C --> G[共享配置]
D --> G
E --> G
C --> H[共享日志存储]
D --> H
E --> H
```

### 部署建议
1. **多实例部署**：至少部署两个应用实例
2. **负载均衡**：使用Nginx或云服务商的负载均衡器
3. **会话管理**：使用数据库存储会话（SESSION_ENGINE = 'django.contrib.sessions.backends.db'）
4. **共享存储**：日志、临时文件等使用共享存储
5. **健康检查**：配置负载均衡器的健康检查机制

### 故障转移
- 监控应用实例的健康状态
- 自动将流量转移到健康实例
- 故障实例恢复后自动重新加入服务池

**章节来源**
- [spider/settings.py](file://spider/settings.py#L70-L80)

## 性能调优建议
为优化系统性能，建议进行以下调优配置。

### 进程与线程配置
- **Gunicorn工作进程数**：建议设置为CPU核心数+1
- **线程数**：根据I/O密集程度调整，建议2-4倍CPU核心数
- **连接池**：配置数据库连接池大小

### 内存分配
- **应用内存**：根据实例大小分配足够内存
- **JVM参数**：如果使用Java相关服务，合理配置JVM参数
- **缓存配置**：合理配置Django缓存

### 数据库优化
- **连接池**：启用并配置数据库连接池
- **查询优化**：优化慢查询，添加必要索引
- **连接超时**：设置合理的连接超时时间

### 缓存策略
- **页面缓存**：对不经常变化的页面进行缓存
- **数据缓存**：缓存频繁访问的数据
- **会话缓存**：考虑使用Redis等外部缓存存储会话

**章节来源**
- [spider/settings.py](file://spider/settings.py)
- [gunicorn.conf.py](file://gunicorn.conf.py)

## 日志收集、监控与告警
系统提供完善的日志记录和监控机制。

### 日志配置
系统使用Python logging模块进行日志管理：
- **应用日志**：记录应用运行日志
- **审计日志**：记录关键操作的审计日志
- **错误日志**：记录错误和异常信息

```python
# 审计日志配置
audit_log_handler = TimedRotatingFileHandler(
    filename=os.path.join("/data/logs/spider/audit", "audit_info.log"),
    backupCount=100
)
```

### 日志收集
- **日志路径**：`/data/logs/spider/`
- **日志轮转**：按时间进行日志轮转
- **日志保留**：保留100个历史日志文件

### 监控指标
- **应用健康**：HTTP健康检查接口
- **性能指标**：响应时间、吞吐量
- **资源使用**：CPU、内存、磁盘
- **错误率**：HTTP错误码统计

### 告警配置
- **错误日志告警**：监控错误日志中的异常
- **性能告警**：响应时间超过阈值
- **可用性告警**：服务不可用
- **资源告警**：资源使用率过高

**章节来源**
- [spider/settings.py](file://spider/settings.py#L30-L60)
- [spider/log_config.py](file://spider/log_config.py)

## 常见部署问题解决方案
### 数据库连接失败
**问题**：应用无法连接数据库
**解决方案**：
1. 检查`settings.ini`中的数据库配置
2. 确认数据库服务是否正常运行
3. 检查网络连通性和防火墙设置
4. 验证数据库用户名和密码

### Salt API调用失败
**问题**：无法通过Salt API执行远程命令
**解决方案**：
1. 检查Salt Master服务状态
2. 验证Salt API的URL、用户名和密码
3. 检查SSL证书配置
4. 确认网络连通性

### 静态文件无法访问
**问题**：前端资源无法加载
**解决方案**：
1. 检查STATIC_ROOT配置
2. 确认静态文件已收集（python manage.py collectstatic）
3. 检查Web服务器对静态文件目录的访问权限

### Jenkins集成失败
**问题**：无法触发Jenkins构建任务
**解决方案**：
1. 检查Jenkins服务状态
2. 验证Jenkins用户名和API Token
3. 确认Jenkins Job名称配置正确
4. 检查网络连通性和防火墙设置

### 环境变量未生效
**问题**：环境变量配置未被应用读取
**解决方案**：
1. 确认环境变量在正确的shell会话中设置
2. 检查Docker容器的环境变量传递
3. 重启应用使环境变量生效
4. 在代码中添加环境变量调试输出

**章节来源**
- [spider/settings.py](file://spider/settings.py)
- [spider/settings.ini](file://spider/settings.ini)