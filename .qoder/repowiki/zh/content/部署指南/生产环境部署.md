# 生产环境部署

<cite>
**本文档引用的文件**   
- [gunicorn.conf.py](file://gunicorn.conf.py)
- [settings.py](file://spider/settings.py)
- [log_config.py](file://spider/log_config.py)
- [settings.ini](file://spider/settings.ini)
- [start.py](file://start.py)
- [start9011.py](file://start9011.py)
- [start9031.py](file://start9031.py)
</cite>

## 目录
1. [高可用架构设计](#高可用架构设计)
2. [Gunicorn生产级配置](#gunicorn生产级配置)
3. [生产环境关键配置](#生产环境关键配置)
4. [日志系统配置](#日志系统配置)
5. [监控与告警系统集成](#监控与告警系统集成)
6. [数据库连接优化](#数据库连接优化)
7. [静态文件处理](#静态文件处理)
8. [安全加固](#安全加固)
9. [部署策略](#部署策略)

## 高可用架构设计

为确保系统在生产环境中的高可用性，建议采用以下架构设计：

1. **负载均衡**：在应用前端部署Nginx或HAProxy作为反向代理和负载均衡器，将流量均匀分配到多个应用实例。通过健康检查机制自动剔除故障节点，确保服务连续性。

2. **服务冗余**：部署多个应用实例，分布在不同的物理服务器或可用区中。通过Docker容器化部署，结合Kubernetes进行容器编排，实现自动扩缩容和故障恢复。

3. **故障转移机制**：配置主备数据库架构，使用MySQL主从复制或PXC集群，实现数据库层面的高可用。应用层通过连接池自动切换到可用的数据库节点。

4. **多区域部署**：在不同地理区域部署应用实例，通过DNS轮询或智能解析将用户请求路由到最近的节点，降低延迟并提高容灾能力。

**Section sources**
- [gunicorn.conf.py](file://gunicorn.conf.py#L1-L18)
- [settings.py](file://spider/settings.py#L1-L832)

## Gunicorn生产级配置

生产环境中的Gunicorn配置应优化性能和稳定性：

```python
import os

BASE_DIR = "/data"
LOG_DIR = os.path.join(BASE_DIR, "logs")
bind = "0.0.0.0:9011"
backlog = 512
chdir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "spider")
worker_class = 'gevent'
workers = 3
threads = 512
loglevel = 'info'
access_log_format = '%(t)s %(p)s %(h)s "%(r)s" %(s)s %(L)s %(b)s %(f)s" "%(a)s"'
accesslog = os.path.join(BASE_DIR, "logs", "spider-access-log.log")
errorlog = os.path.join(BASE_DIR, "logs", "spider-error-log.log")
proc_name = 'spider_api'
```

**关键配置说明**：
- **worker_class**: 使用`gevent`异步模式，提高并发处理能力
- **workers**: 设置为3个进程，可根据CPU核心数调整
- **threads**: 每个进程开启512个线程，充分利用多核CPU
- **backlog**: 监听队列设置为512，防止连接丢失
- **日志配置**: 分别配置访问日志和错误日志，便于问题排查

**Section sources**
- [gunicorn.conf.py](file://gunicorn.conf.py#L1-L18)

## 生产环境关键配置

### DEBUG模式
在生产环境中必须关闭DEBUG模式，防止敏感信息泄露：

```python
DEBUG = False
```

### 安全密钥管理
使用环境变量或配置文件管理安全密钥，避免硬编码：

```python
SECRET_KEY = 'y125d-t30*1fq+*%@(uu@+tx7$_i0*pf#ah^&p6!4-ehldjpy5'
TOKEN_SECRET_KEY = 'insecure-8ie=+7sbfmcl9u1=wlc_napn6_@n6_k!50glol(6_m@!ns1c7a'
```

### 数据库连接池
配置数据库连接池参数，优化连接管理：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': local_settings.get('MYSQL', 'DB'),
        'USER': local_settings.get('MYSQL', 'USER'),
        'PASSWORD': local_settings.get('MYSQL', 'PASSWORD'),
        'HOST': local_settings.get('MYSQL', 'IP'),
        'PORT': local_settings.get('MYSQL', 'PORT'),
        'OPTIONS': {'init_command': "SET sql_mode='STRICT_TRANS_TABLES'", 'charset': 'utf8mb4'},
    }
}
```

### 缓存配置
配置Redis或其他缓存系统，提高数据访问速度：

```python
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}
```

**Section sources**
- [settings.py](file://spider/settings.py#L1-L832)
- [settings.ini](file://spider/settings.ini#L1-L310)

## 日志系统配置

### 日志级别与格式
配置详细的日志格式，包含时间、线程、模块、文件行号等信息：

```python
DictConfig = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'master_format': {
            'format': '%(asctime)s - [%(pathname)s]--[%(module)s]-%(filename)s[line:%(lineno)d] - %(levelname)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': logging.INFO,
            'formatter': 'master_format',
        },
        'timedRotatingFileHandler': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'spider_info.log'),
            'level': logging.INFO,
            'formatter': 'master_format',
            'when': 'd',
            'interval': 1,
            'backupCount': 2
        },
        'timedErrorRotatingFileHandler': {
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': os.path.join(LOG_DIR, 'spider_error.log'),
            'level': logging.WARNING,
            'formatter': 'master_format',
            'when': 'd',
            'interval': 1,
            'backupCount': 2
        },
    },
    'loggers': {
        'rotatingFileLogger': {
            'handlers': ['console', 'timedRotatingFileHandler', 'timedErrorRotatingFileHandler'],
            'level': 'INFO'
        }
    }
}
```

### 日志输出位置
日志文件存储在`/data/logs/spider/`目录下，按天轮转，保留2天历史日志。

**Section sources**
- [log_config.py](file://spider/log_config.py#L1-L106)
- [settings.py](file://spider/settings.py#L1-L832)

## 监控与告警系统集成

### Prometheus指标暴露
配置Prometheus监控指标，暴露关键性能数据：

```python
# 在settings.py中配置监控中间件
MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    # ... 其他中间件
    'common_middle.OpLogs.AuditLogMiddleware'
]
```

### 健康检查端点
提供健康检查API端点，供负载均衡器和监控系统调用：

```python
# 在urls.py中配置健康检查路由
urlpatterns = [
    path('health/', views.health_check, name='health_check'),
    # ... 其他路由
]
```

### 告警规则
配置基于Prometheus的告警规则，监控以下指标：
- 应用响应时间
- 错误率
- CPU和内存使用率
- 数据库连接数
- 队列长度

**Section sources**
- [settings.py](file://spider/settings.py#L1-L832)

## 数据库连接优化

### 连接池配置
优化数据库连接池参数，提高连接复用率：

```python
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': local_settings.get('MYSQL', 'DB'),
        'USER': local_settings.get('MYSQL', 'USER'),
        'PASSWORD': local_settings.get('MYSQL', 'PASSWORD'),
        'HOST': local_settings.get('MYSQL', 'IP'),
        'PORT': local_settings.get('MYSQL', 'PORT'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            'charset': 'utf8mb4',
            'connect_timeout': 28800,
            'read_timeout': 28800,
            'write_timeout': 28800,
        },
        'CONN_MAX_AGE': 600,  # 连接最大生命周期10分钟
        'POOL_OPTIONS': {
            'POOL_SIZE': 20,
            'MAX_OVERFLOW': 10,
            'RECYCLE': 3600,
        }
    }
}
```

### 查询优化
- 使用数据库索引优化查询性能
- 避免N+1查询问题
- 合理使用缓存减少数据库访问

**Section sources**
- [settings.py](file://spider/settings.py#L1-L832)
- [settings.ini](file://spider/settings.ini#L1-L310)

## 静态文件处理

### CDN集成
将静态文件（CSS、JavaScript、图片等）托管到CDN，提高加载速度：

```python
STATIC_ROOT = '/data/app/admin-static'
STATIC_URL = '/spider/static/'
```

### Nginx配置
使用Nginx作为反向代理，直接处理静态文件请求：

```nginx
location /static/ {
    alias /data/app/admin-static/;
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location /media/ {
    alias /data/app/media/;
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

**Section sources**
- [settings.py](file://spider/settings.py#L1-L832)

## 安全加固

### HTTPS配置
强制使用HTTPS，配置SSL证书：

```nginx
server {
    listen 443 ssl;
    server_name example.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
}
```

### CORS策略
配置严格的CORS策略，防止跨站请求伪造：

```python
CORS_ORIGIN_ALLOW_ALL = False
CORS_ALLOWED_ORIGINS = [
    "https://example.com",
    "https://www.example.com",
]
CORS_ALLOW_CREDENTIALS = True
```

### 其他安全措施
- 设置安全的Cookie属性（HttpOnly、Secure）
- 配置CSRF保护
- 限制文件上传类型和大小
- 定期更新依赖库，修复安全漏洞

**Section sources**
- [settings.py](file://spider/settings.py#L1-L832)

## 部署策略

### 蓝绿部署
实施蓝绿部署策略，确保零停机更新：

1. 准备两个完全相同的应用环境（蓝色和绿色）
2. 新版本部署到非生产环境（如绿色）
3. 经过测试验证后，通过负载均衡器切换流量
4. 监控新版本运行情况，如有问题立即回滚

### 滚动更新
对于无法使用蓝绿部署的场景，采用滚动更新：

```bash
# 示例启动脚本
#!/usr/local/bin/python3.x
import os

class Driver:
    log_file_path = '/data/logs/spider'
    log_file_name = 'web.log'

    def start(self):
        if os.path.exists(self.log_file_path):
            os.system('mkdir -p {}'.format(self.log_file_path))
        log_path = os.path.join(self.log_file_path, self.log_file_name)
        if os.path.isfile(log_path):
            os.system("cat /dev/null > {}".format(log_path))
        os.system("nohup gunicorn spider.wsgi -c gunicorn.conf.py > {} 2>&1 &".format(log_path))

    def stop(self):
        os.system("pkill -9 -f spider.wsgi")

    def restart(self):
        self.stop()
        self.start()

if __name__ == "__main__":
    dr = Driver()
    dr.restart()
```

### 回滚机制
建立快速回滚机制，确保出现问题时能迅速恢复：

1. 保留前几个版本的部署包
2. 自动化回滚脚本
3. 数据库迁移脚本的可逆性设计

**Section sources**
- [start.py](file://start.py#L1-L33)
- [start9011.py](file://start9011.py#L1-L33)
- [start9031.py](file://start9031.py#L1-L33)