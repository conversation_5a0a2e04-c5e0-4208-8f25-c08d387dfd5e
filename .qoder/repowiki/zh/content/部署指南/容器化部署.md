# 容器化部署

<cite>
**本文档引用的文件**  
- [Dockerfile](file://Dockerfile)
- [gunicorn.conf.py](file://gunicorn.conf.py)
- [requirements.txt](file://requirements.txt)
- [start.py](file://start.py)
- [start9011.py](file://start9011.py)
- [start9031.py](file://start9031.py)
- [spider/settings.py](file://spider/settings.py)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [Docker镜像构建详解](#docker镜像构建详解)
4. [多阶段构建与优化](#多阶段构建与优化)
5. [容器运行与编排配置](#容器运行与编排配置)
6. [Gunicorn服务器配置优化](#gunicorn服务器配置优化)
7. [环境变量与配置分离](#环境变量与配置分离)
8. [健康检查与性能调优](#健康检查与性能调优)
9. [安全最佳实践](#安全最佳实践)
10. [附录](#附录)

## 简介
本文档旨在为Spider项目提供完整的容器化部署指南，涵盖Docker镜像构建、运行、服务编排、性能优化和安全实践等关键环节。通过详细分析Dockerfile、gunicorn配置和启动脚本，帮助开发和运维团队实现高效、稳定的容器化部署。

## 项目结构
项目根目录包含`docker/`子目录，其中存放了Dockerfile。主应用代码位于`spider/`目录下，配置文件如`gunicorn.conf.py`、`requirements.txt`等位于项目根目录。启动脚本包括`start.py`、`start9011.py`等，用于不同端口的实例启动。

```mermaid
graph TD
A[项目根目录] --> B[docker/]
A --> C[spider/]
A --> D[gunicorn.conf.py]
A --> E[requirements.txt]
A --> F[start.py]
A --> G[start9011.py]
A --> H[start9031.py]
B --> I[Dockerfile]
C --> J[settings.py]
```

**Diagram sources**  
- [Dockerfile](file://Dockerfile)
- [spider/settings.py](file://spider/settings.py)

**Section sources**  
- [Dockerfile](file://Dockerfile)
- [spider/settings.py](file://spider/settings.py)

## Docker镜像构建详解
Dockerfile基于`zt-py39:1.0.0`基础镜像，该镜像是CentOS 7.9.2009系统并预装了uv-3.9.13 Python环境管理工具。镜像构建过程包括系统依赖安装、代码复制和环境同步。

### 基础镜像选择
使用`zt-py39:1.0.0`作为基础镜像，确保了Python 3.9环境的统一性和稳定性。该基础镜像已预配置了必要的开发工具和依赖，减少了构建时间和网络依赖。

### 依赖安装
通过`yum install tree zip unzip wget sshpass -y`命令安装系统级依赖，这些工具在容器运行时可能被用于文件操作、网络传输和远程连接等场景。

### 代码复制与环境同步
Dockerfile通过COPY指令将`.python-version`、`pyproject.toml`和`uv.lock`文件复制到容器中，然后执行`uv sync`命令来安装Python依赖。这种方式利用了uv工具的锁定机制，确保了依赖版本的一致性。

### 入口点配置
使用ENTRYPOINT指令配置容器启动命令：`/data/app/.venv/bin/python3 manage.py runserver 0.0.0.0:9000 --noreload`，并将输出重定向到日志文件`/data/logs/spider/spider-web.log`。

**Section sources**  
- [Dockerfile](file://Dockerfile#L1-L50)

## 多阶段构建与优化
当前Dockerfile未采用多阶段构建模式。建议优化为多阶段构建，将构建阶段和运行阶段分离，以减小最终镜像体积。构建阶段可包含完整的编译工具链，而运行阶段仅包含运行时所需的最小依赖。

```mermaid
graph TD
A[构建阶段] --> B[安装构建依赖]
A --> C[编译Python包]
A --> D[生成依赖环境]
D --> E[运行阶段]
E --> F[复制虚拟环境]
E --> G[设置运行时依赖]
E --> H[启动应用]
```

**Diagram sources**  
- [Dockerfile](file://Dockerfile#L1-L50)

## 容器运行与编排配置
### 镜像构建命令
```bash
docker build -t spider:2.112.1 .
```

### 容器运行参数
```bash
docker run -itd --name zt-spider \
  -p 9000:9000 \
  -v /data/ztws/zt-spider/spider:/data/app/spider/ \
  -v /data/ztws/zt-spider/spider_logs:/data/logs/spider/ \
  spider:2.112.1
```

### Docker Compose配置示例
```yaml
version: '3.8'
services:
  spider:
    build: .
    ports:
      - "9000:9000"
    volumes:
      - ./spider:/data/app/spider
      - ./logs:/data/logs/spider
    environment:
      - DJANGO_SETTINGS_MODULE=spider.settings
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
```

**Section sources**  
- [Dockerfile](file://Dockerfile#L1-L50)

## Gunicorn服务器配置优化
`gunicorn.conf.py`文件配置了Gunicorn服务器的关键参数，用于生产环境的WSGI服务。

### Worker进程配置
- `worker_class = 'gevent'`：使用gevent异步模式，提高并发处理能力
- `workers = 3`：设置3个工作进程，建议根据CPU核心数调整
- `threads = 512`：每个进程开启512个线程，充分利用异步IO优势

### 日志配置
- 访问日志：`/data/logs/spider-access-log.log`
- 错误日志：`/data/logs/spider-error-log.log`
- 日志级别：`info`

### 性能参数
- `backlog = 512`：监听队列长度，平衡连接请求处理能力
- `bind = "0.0.0.0:9011"`：绑定所有网络接口的9011端口

**Section sources**  
- [gunicorn.conf.py](file://gunicorn.conf.py#L1-L19)

## 环境变量与配置分离
项目通过`spider/settings.py`中的配置字典（如`SONAR_SCAN_JOB`、`NGINX_LIB_REPO`等）实现配置管理。建议将这些配置通过环境变量注入，实现配置与镜像的完全分离。

```python
SONAR_SCAN_JOB = {
    'sonar_scan_job_name': os.environ.get('SONAR_SCAN_JOB_NAME', 'default_job'),
}

NGINX_LIB_REPO = {
    'ip': os.environ.get('NGINX_LIB_REPO_IP', 'localhost'),
    'username': os.environ.get('NGINX_LIB_REPO_USER', 'admin'),
}
```

**Section sources**  
- [spider/settings.py](file://spider/settings.py#L687-L715)

## 健康检查与性能调优
### 健康检查配置
建议在Dockerfile或docker-compose.yml中添加健康检查指令：
```dockerfile
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:9000/health || exit 1
```

### 性能调优建议
1. **Worker数量优化**：根据CPU核心数设置workers数量，通常为`2 * CPU核心数 + 1`
2. **内存限制**：为容器设置合理的内存限制，防止内存泄漏导致系统不稳定
3. **连接池优化**：调整数据库连接池大小，匹配应用并发需求
4. **缓存策略**：启用适当的缓存机制，减少重复计算和数据库查询

## 安全最佳实践
1. **最小权限原则**：容器以非root用户运行
2. **镜像来源可信**：基础镜像来自可信 registry
3. **依赖安全扫描**：定期扫描`requirements.txt`中的依赖漏洞
4. **网络隔离**：使用Docker网络策略限制容器间通信
5. **日志审计**：集中收集和分析容器日志，及时发现异常行为

## 附录
### 启动脚本说明
项目提供了多个启动脚本：
- `start.py`：通用启动脚本
- `start9011.py`：监听9011端口的启动脚本
- `start9031.py`：监听9031端口的启动脚本

这些脚本实现了应用的启动、停止和重启功能，并管理日志文件。

**Section sources**  
- [start.py](file://start.py#L1-L34)
- [start9011.py](file://start9011.py#L1-L34)
- [start9031.py](file://start9031.py#L1-L34)