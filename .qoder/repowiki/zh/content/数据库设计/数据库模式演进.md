# 数据库模式演进

<cite>
**本文档引用的文件**  
- [<EMAIL>](file://db/schema/<EMAIL>)
- [2.1.6数据表修改名字.sql](file://db/schema/2.1.6数据表修改名字.sql)
- [22-迁库至spider后相关表名的优化******************](file://db/schema/22-迁库至spider后相关表名的优化******************)
- [24-视图更名及相关优化.sql](file://db/schema/24-视图更名及相关优化.sql)
- [app_mgt/models.py](file://app_mgt/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [初始数据库模式](#初始数据库模式)
3. [核心模式重构决策](#核心模式重构决策)
4. [表名优化与模块化](#表名优化与模块化)
5. [视图重构与优化](#视图重构与优化)
6. [字段调整与数据结构演进](#字段调整与数据结构演进)
7. [关键演进节点模式对比](#关键演进节点模式对比)
8. [向后兼容性处理策略](#向后兼容性处理策略)
9. [数据库迁移脚本执行流程](#数据库迁移脚本执行流程)
10. [风险控制措施](#风险控制措施)
11. [模式版本控制最佳实践](#模式版本控制最佳实践)
12. [结论](#结论)

## 引言
本文档系统梳理了从初始版本到当前版本的数据库模式演进历程。通过分析主要的模式重构决策，包括表名优化、视图重构和字段调整等，揭示了模式变更背后的业务驱动因素和技术考量。文档提供了关键演进节点的模式对比图，说明了向后兼容性处理策略，并记录了数据库迁移脚本的执行流程和风险控制措施。最后，总结了确保开发、测试、生产环境一致性的模式版本控制最佳实践。

## 初始数据库模式
系统初始版本的数据库模式于2020年2月20日建立，以`ztst`为前缀，集中管理应用、团队、代码仓库等核心信息。初始模式包含以下主要数据表：

- `ztst_git_server`：GIT服务器信息表
- `ztst_svn_server`：SVN服务器信息表
- `ztst_git_url`：GIT路径信息表
- `ztst_svn_url`：SVN路径信息表
- `ztst_team`：团队信息表
- `ztst_app`：应用信息表
- `ztst_app_team`：应用与团队关联表
- `ztst_app_user`：应用人员信息表
- `ztst_app_module`：应用模块信息表
- `ztst_app_build`：应用构建信息表

该模式通过外键关联和唯一约束确保数据完整性，为系统初期的持续集成和部署提供了基础支持。

**Section sources**
- [<EMAIL>](file://db/schema/<EMAIL>)

## 核心模式重构决策
随着系统功能的扩展和微服务架构的演进，数据库模式经历了多次重大重构。主要决策包括：

1. **模块化拆分**：将单一数据库中的表按功能模块拆分到不同的应用管理模块中，如`app_mgt`、`tool_mgt`等。
2. **表名规范化**：统一表名前缀，将`ztst`前缀替换为更具业务含义的前缀，如`app_mgt_`、`tool_mgt_`。
3. **视图重构**：重建核心视图，优化查询性能，确保数据一致性。
4. **数据迁移**：在保证数据完整性的前提下，将数据从旧表迁移到新表。

这些决策旨在提高系统的可维护性、可扩展性和性能。

## 表名优化与模块化
2020年4月26日，系统进行了重要的表名优化和模块化重构。主要变更包括：

- 将`ztst_team`表重命名为`tool_mgt_ztst_team`，并最终优化为`tool_mgt_team_info`
- 将`ztst_svn_server`和`ztst_svn_url`表迁移至`tool_mgt`模块
- 将`ztst_git_server`和`ztst_git_url`表迁移至`tool_mgt`模块
- 将`ztst_app`表重命名为`app_mgt_ztst_app`，并最终优化为`app_mgt_app_info`
- 将`ztst_app_module`、`ztst_app_build`、`ztst_app_team`、`ztst_app_user`等表迁移至`app_mgt`模块

这一系列变更实现了数据的模块化管理，提高了代码的可读性和可维护性。

**Section sources**
- [22-迁库至spider后相关表名的优化******************](file://db/schema/22-迁库至spider后相关表名的优化******************)

## 视图重构与优化
视图重构是数据库模式演进的重要组成部分。主要变更包括：

- 将`ztst_app_view`视图重构为`spider_app_view`，优化了查询逻辑和性能
- 将`common_service_artifactinfo`视图进行优化，提高了数据查询效率
- 将`ztst_git_br_view`、`ztst_svn_br_view`、`ztst_br_view`等视图重命名为`zeus_git_br_view`、`zeus_svn_br_view`、`zeus_br_view`，统一了命名规范
- 重构了`ztst_git_master_view`和`ztst_svn_trunk_view`视图，提高了数据一致性

这些视图重构不仅优化了查询性能，还为上层应用提供了更稳定的数据接口。

**Section sources**
- [24-视图更名及相关优化.sql](file://db/schema/24-视图更名及相关优化.sql)

## 字段调整与数据结构演进
在模式演进过程中，对多个表的字段进行了调整和优化：

- 在`app_mgt_app_info`表中增加了`lib_location`字段，用于存储特征团队信息
- 在`app_mgt_app_module`表中增加了`extend_attr`字段，用于存储扩展属性
- 优化了`app_mgt_app_build`表的唯一约束，提高了数据完整性
- 调整了多个表的自动增长起始值，避免了ID冲突

这些字段调整和数据结构优化，更好地支持了业务需求的变化。

## 关键演进节点模式对比
以下是关键演进节点的模式对比：

```mermaid
erDiagram
%% 初始模式
ztst_app ||--o{ ztst_app_module : "包含"
ztst_app ||--o{ ztst_app_build : "构建"
ztst_app ||--o{ ztst_app_team : "关联"
ztst_app ||--o{ ztst_app_user : "管理"
ztst_team ||--o{ ztst_app_team : "拥有"
ztst_git_server ||--o{ ztst_git_url : "提供"
ztst_svn_server ||--o{ ztst_svn_url : "提供"
%% 演进后模式
app_mgt_app_info ||--o{ app_mgt_app_module : "包含"
app_mgt_app_info ||--o{ app_mgt_app_build : "构建"
app_mgt_app_info ||--o{ app_mgt_app_team : "关联"
app_mgt_app_info ||--o{ app_mgt_app_user : "管理"
tool_mgt_team_info ||--o{ app_mgt_app_team : "拥有"
tool_mgt_git_server ||--o{ tool_mgt_git_url : "提供"
tool_mgt_svn_server ||--o{ tool_mgt_svn_url : "提供"
%% 视图对比
spider_app_view }o-- app_mgt_app_module : "查询"
spider_app_view }o-- app_mgt_app_info : "查询"
spider_app_view }o-- app_mgt_app_build : "查询"
zeus_br_view }o-- app_mgt_app_module : "查询"
zeus_br_view }o-- app_mgt_app_info : "查询"
```

**Diagram sources**
- [<EMAIL>](file://db/schema/<EMAIL>)
- [22-迁库至spider后相关表名的优化******************](file://db/schema/22-迁库至spider后相关表名的优化******************)
- [24-视图更名及相关优化.sql](file://db/schema/24-视图更名及相关优化.sql)

## 向后兼容性处理策略
为确保系统平稳过渡，采用了以下向后兼容性处理策略：

1. **逐步迁移**：采用逐步迁移策略，先迁移非核心数据，再迁移核心数据。
2. **双写机制**：在迁移期间，同时向新旧表写入数据，确保数据一致性。
3. **视图兼容**：通过创建兼容视图，确保旧代码能够正常访问新表数据。
4. **数据校验**：在迁移完成后，进行全面的数据校验，确保数据完整性。

这些策略有效降低了迁移风险，保证了系统的稳定运行。

## 数据库迁移脚本执行流程
数据库迁移脚本的执行流程如下：

1. **准备阶段**：备份当前数据库，检查迁移脚本的完整性和正确性。
2. **预检查**：执行预检查脚本，验证环境和数据状态。
3. **执行迁移**：按顺序执行表结构变更、数据迁移、视图重建等脚本。
4. **数据校验**：执行数据校验脚本，验证迁移结果。
5. **清理阶段**：清理临时数据和旧表，优化数据库性能。

该流程确保了迁移过程的可控性和可追溯性。

## 风险控制措施
为降低数据库迁移风险，采取了以下控制措施：

1. **备份策略**：在迁移前进行全量备份，确保数据可恢复。
2. **灰度发布**：先在测试环境验证，再在生产环境分批次发布。
3. **监控告警**：在迁移期间加强系统监控，设置关键指标告警。
4. **回滚预案**：制定详细的回滚预案，确保在出现问题时能够快速恢复。

这些措施有效保障了迁移过程的安全性和稳定性。

## 模式版本控制最佳实践
为确保开发、测试、生产环境的一致性，采用了以下模式版本控制最佳实践：

1. **版本化脚本**：将所有数据库变更脚本版本化管理，与代码库同步。
2. **自动化执行**：通过CI/CD流水线自动化执行数据库迁移脚本。
3. **环境隔离**：严格隔离开发、测试、生产环境，避免相互影响。
4. **变更审计**：记录所有数据库变更，确保变更可追溯。

这些实践有效提高了数据库管理的规范性和可靠性。

## 结论
数据库模式的演进是一个持续优化的过程。通过表名优化、视图重构、字段调整等一系列重构决策，系统实现了更好的模块化、可维护性和性能。向后兼容性处理策略、迁移脚本执行流程和风险控制措施，确保了模式变更的平稳过渡。模式版本控制最佳实践，则为多环境一致性提供了有力保障。未来，将继续关注业务需求变化，持续优化数据库模式，为系统发展提供坚实的数据基础。