# 应用管理数据模型

<cite>
**本文档引用文件**   
- [models.py](file://app_mgt/models.py)
- [0002_appbuild_appinfo_appmd5info_appmodule_appteam_appuser_jenkinsautomaticarchiveinfo_miniversionupgrade.py](file://app_mgt/migrations/0002_appbuild_appinfo_appmd5info_appmodule_appteam_appuser_jenkinsautomaticarchiveinfo_miniversionupgrade.py)
- [app_register.py](file://app_mgt/app_register.py)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [admin.py](file://app_mgt/admin.py)
- [app_info客户端数据.sql](file://db/hm-1.03.0/app_info客户端数据.sql)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体模型定义](#核心实体模型定义)
   - [AppInfo 应用信息](#appinfo-应用信息)
   - [AppModule 应用模块](#appmodule-应用模块)
   - [AppBuild 应用构建](#appbuild-应用构建)
   - [AppTeam 应用团队](#appteam-应用团队)
3. [实体关系与ER图](#实体关系与er图)
4. [应用注册流程与数据流转](#应用注册流程与数据流转)
5. [状态机与业务规则](#状态机与业务规则)
6. [变更日志与审计策略](#变更日志与审计策略)
7. [数据库迁移版本控制](#数据库迁移版本控制)
8. [数据查询最佳实践](#数据查询最佳实践)
9. [结论](#结论)

## 引言

本文档旨在全面描述应用管理域的数据模型，涵盖核心实体（AppInfo、AppModule、AppBuild、AppTeam等）的字段定义、数据类型、约束条件和业务规则。文档详细解释了应用与构建、模块、团队之间的关系，包括一对多和多对多关联，并通过ER图直观展示数据结构。同时，文档说明了应用注册流程中的数据流转和状态机设计，记录了应用信息变更的日志机制和审计策略，解释了数据库迁移版本控制流程，并为应用数据查询提供了最佳实践建议。

**本文档引用文件**   
- [models.py](file://app_mgt/models.py)
- [app_register.py](file://app_mgt/app_register.py)

## 核心实体模型定义

### AppInfo 应用信息

`AppInfo` 模型是应用管理的核心，代表一个独立的应用实体。

**字段定义与约束：**
- `app_name` (CharField, max_length=100): 应用英文名，唯一标识符。
- `app_cname` (CharField, max_length=100): 应用中文名，用于展示。
- `app_status` (BooleanField): 应用状态，`1` 表示使用中，`0` 表示已废弃。
- `git_url` (CharField, max_length=999): Git仓库的域名或地址。
- `git_path` (CharField, max_length=999): Git仓库在域名下的路径。
- `svn_url` (CharField, max_length=999): SVN仓库的域名或地址。
- `svn_path` (CharField, max_length=999): SVN仓库在域名下的路径（注意：代码中`svn_path`被错误地注释为“svn编码”，应为“svn路径”）。
- `app_jdk_version` (CharField, max_length=100): 应用整体默认的JDK版本。
- `app_desc` (CharField, max_length=255): 应用说明或描述。
- `create_user` (CharField, max_length=20): 记录创建该应用的用户。
- `create_time` (DateTimeField): 记录应用创建的时间戳。
- `update_user` (CharField, max_length=20): 记录最后修改该应用的用户。
- `update_time` (DateTimeField): 记录应用最后修改的时间戳。
- `stamp` (IntegerField): 版本戳，用于乐观锁或并发控制。
- `lib_location` (CharField, max_length=10): 团队名，标识应用所属的团队。
- `platform_type` (BooleanField): 标识应用是否已接入平台。
- `platform_time` (DateTimeField): 记录应用接入平台的时间。

**业务规则：**
- 一个应用必须关联一个团队（通过`lib_location`字段）。
- 应用的`app_name`必须是唯一的。
- 当`app_status`为`0`时，表示该应用已被废弃，不应再被用于新的构建或部署。

**Section sources**
- [models.py](file://app_mgt/models.py#L5-L44)

### AppModule 应用模块

`AppModule` 模型代表一个应用内的模块，一个应用可以包含多个模块。

**字段定义与约束：**
- `app_id` (IntegerField): 外键，关联到`AppInfo`的ID，表示该模块属于哪个应用。
- `module_name` (CharField, max_length=100): 模块名，通常与应用名一致或为其子集。
- `module_code` (CharField, max_length=100): 模块编码，用于系统内部标识。
- `module_status` (BooleanField): 模块状态，`1` 表示使用中，`0` 表示已废弃。
- `module_desc` (CharField, max_length=255): 模块说明。
- `module_svn_path` (CharField, max_length=999): 模块在SVN中的具体路径。
- `module_jdk_version` (CharField, max_length=100): 模块特定的JDK版本，优先级高于`AppInfo`中的`app_jdk_version`。
- `need_online` (BooleanField): 标识该模块的JAR包是否需要上线部署。
- `need_check` (BooleanField): 标识该模块是否需要维护。
- `app_port` (IntegerField): 应用端口。
- `container_name` (CharField, max_length=255): 容器名。
- `create_path` (CharField, max_length=100): 打包路径。
- `lib_repo` (CharField, max_length=100): 制品库路径。
- `deploy_path` (CharField, max_length=999): 发布路径。
- `extend_attr` (CharField, max_length=999): 扩展属性，如Dubbo端口等。
- `zeus_type` (BooleanField): 标识是否接入宙斯（Zeus）服务。
- `need_ops` (BooleanField): 标识是否是mock应用。
- `is_component` (IntegerField): 标识是否是组件。

**业务规则：**
- 一个`AppModule`必须关联一个`AppInfo`（通过`app_id`）。
- 一个应用可以有多个模块，形成一对多关系。
- `module_name`在系统中通常需要保持唯一性。

**Section sources**
- [models.py](file://app_mgt/models.py#L46-L85)

### AppBuild 应用构建

`AppBuild` 模型存储了应用模块的构建配置信息。

**字段定义与约束：**
- `app_id` (IntegerField): 外键，关联到`AppInfo`的ID。
- `module_name` (CharField, max_length=100): 模块名，与`AppModule`中的`module_name`关联。
- `module_code` (CharField, max_length=100): 模块编码。
- `module_version` (CharField, max_length=100): 模块版本。
- `package_type` (CharField, max_length=100): 包类型，如`pom`、`war`、`jar`、`tar`。
- `package_name` (CharField, max_length=255): 包名。
- `package_full` (BooleanField): 标识是否为完整包。
- `build_jdk_version` (CharField, max_length=100): 编译时使用的JDK版本。
- `need_mock` (BooleanField): 标识是否存在mock类型。
- `mock_build_cmd` (CharField, max_length=100): mock编译命令。
- `build_cmd` (CharField, max_length=128): 编译命令。

**业务规则：**
- `AppBuild`通过`app_id`和`module_name`与`AppInfo`和`AppModule`建立关联。
- 构建配置决定了如何编译和打包一个模块。

**Section sources**
- [models.py](file://app_mgt/models.py#L87-L115)

### AppTeam 应用团队

`AppTeam` 模型定义了应用与团队之间的关联关系。

**字段定义与约束：**
- `app_id` (IntegerField): 外键，关联到`AppInfo`的ID。
- `team_id` (IntegerField): 外键，关联到团队信息表的ID。
- `create_user`, `create_time`, `update_user`, `update_time`, `stamp`: 标准的审计和版本控制字段。

**业务规则：**
- 该模型实现了`AppInfo`和`TeamInfo`之间的多对多关系。
- 一个应用可以属于多个团队，一个团队也可以管理多个应用。

**Section sources**
- [models.py](file://app_mgt/models.py#L117-L128)

## 实体关系与ER图

应用管理域的核心实体通过外键和关联表建立了紧密的联系。

```mermaid
erDiagram
APPINFO {
string app_name PK
string app_cname
boolean app_status
string git_url
string git_path
string svn_url
string svn_path
string app_jdk_version
string app_desc
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
string lib_location
boolean platform_type
datetime platform_time
}
APPMODULE {
int id PK
int app_id FK
string module_name
string module_code
boolean module_status
string module_desc
string module_svn_path
string module_jdk_version
boolean need_online
boolean need_check
int app_port
string container_name
string create_path
string lib_repo
string deploy_path
string extend_attr
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
boolean zeus_type
boolean need_ops
int is_component
}
APPBUILD {
int id PK
int app_id FK
string module_name
string module_code
string module_version
string package_type
string package_name
boolean package_full
string build_jdk_version
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
boolean need_mock
string mock_build_cmd
string build_cmd
}
APPTeamBind {
int id PK
int app_id FK
int team_id FK
string bind_desc
string create_user
datetime create_time
string update_user
datetime update_time
int stamp
}
APPINFO ||--o{ APPMODULE : "1:N"
APPINFO ||--o{ APPBUILD : "1:N"
APPINFO ||--o{ APPTeamBind : "1:N"
```

**Diagram sources**
- [models.py](file://app_mgt/models.py#L5-L128)

## 应用注册流程与数据流转

应用注册流程通过`app_register.py`中的`AppRegisterApi`类实现，该流程会同时创建`AppInfo`、`AppModule`、`AppTeam`和`AppBuild`记录。

**流程步骤：**
1.  **输入验证**：API接收应用信息（如`app_name`, `app_type`, `team`, `trunk_path`等）。
2.  **存在性检查**：检查`app_name`是否已存在，防止重复注册。
3.  **仓库类型判断**：根据`trunk_path`判断是Git还是SVN仓库。
4.  **数据创建**：
    -   **AppInfo**：创建应用基本信息记录。
    -   **AppModule**：创建默认的应用模块记录，`app_id`指向新创建的`AppInfo`。
    -   **AppTeam**：创建应用与团队的绑定关系，`app_id`指向新创建的`AppInfo`，`team_id`通过`team_short_name`从`TeamInfo`表查询得到。
    -   **AppBuild**：创建应用的构建配置记录，`app_id`指向新创建的`AppInfo`。
5.  **返回结果**：成功则返回成功信息，失败则返回错误原因。

此流程确保了在注册一个新应用时，其核心的元数据、模块、团队归属和构建配置能够被原子性地创建，保证了数据的一致性。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L45-L200)

## 状态机与业务规则

应用管理域中存在多个基于布尔字段的状态机。

**AppInfo状态机 (`app_status`):**
- **状态**：`1` (使用中), `0` (已废弃)
- **转换规则**：
    -   新应用注册时，`app_status`默认为`1`。
    -   当应用被废弃时，`app_status`被更新为`0`。系统其他模块（如部署、构建）在操作前会检查此状态，避免对已废弃的应用进行操作。

**AppModule状态机 (`module_status`):**
- **状态**：`1` (使用中), `0` (已废弃)
- **转换规则**：与`app_status`类似，用于控制单个模块的生命周期。

**业务规则：**
- **平台接入**：`platform_type`字段为`True`时，表示该应用已接入平台，可能触发特定的自动化流程。
- **Mock应用**：`need_ops`字段标识应用是否为mock类型，影响其部署和监控策略。
- **上线需求**：`need_online`字段决定该模块的产物是否需要发布到生产环境。

**Section sources**
- [models.py](file://app_mgt/models.py#L10-L11)
- [models.py](file://app_mgt/models.py#L52-L53)

## 变更日志与审计策略

系统通过多种机制实现数据变更的审计。

**审计字段：**
每个核心模型都包含标准的审计字段：
- `create_user` / `create_time`: 记录首次创建的用户和时间。
- `update_user` / `update_time`: 记录最后一次修改的用户和时间。
- `stamp`: 版本戳，用于检测并发更新。

**日志记录：**
- **操作日志**：`common_middle/OpLogs.py` 文件的存在表明系统有独立的操作日志模块，用于记录关键操作。
- **接口变更审计**：`app_mgt_interface.py` 中的 `audit_app_branch_api` 方法实现了对API信息变更的稽核，通过比较不同数据源（如`AppMgtInterfaceInfo`和`AppMgtApidocInfo`）来发现不一致，并可能触发告警或自动修正。

**Section sources**
- [models.py](file://app_mgt/models.py#L17-L18)
- [models.py](file://app_mgt/models.py#L58-L59)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L370-L450)

## 数据库迁移版本控制

数据库的结构演进通过Django的迁移文件进行管理。

**初始迁移 (`0001_initial.py`):**
- 定义了`AppInfo`, `AppModule`, `AppBuild`, `AppTeam`等核心模型的初始数据库表结构。

**后续迁移 (`0002_...py`):**
- 该文件（`0002_appbuild_appinfo_appmd5info_appmodule_appteam_appuser_jenkinsautomaticarchiveinfo_miniversionupgrade.py`）展示了如何通过迁移来扩展模型。
- **关键变更**：此迁移文件创建了一个名为 `MiniVersionUpgradeModule` 的新模型。
    ```python
    operations = [
        migrations.CreateModel(
            name='MiniVersionUpgradeModule',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('module_name', models.CharField(max_length=255, verbose_name='标准统一模块名')),
            ],
            options={
                'verbose_name': '需要小版本更新的模块',
                'db_table': 'app_mgt_mini_version_upgrade_module',
            },
        )
    ]
    ```
- **意义**：这表明系统需要跟踪哪些模块需要进行小版本升级，这是一个新的业务需求，通过数据库迁移安全地添加了新的数据表。

**最佳实践：**
- 所有数据库结构的变更都应通过生成和应用迁移文件来完成，而不是直接修改数据库。
- 迁移文件是版本控制的一部分，确保了开发、测试和生产环境数据库结构的一致性。

**Section sources**
- [0002_appbuild_appinfo_appmd5info_appmodule_appteam_appuser_jenkinsautomaticarchiveinfo_miniversionupgrade.py](file://app_mgt/migrations/0002_appbuild_appinfo_appmd5info_appmodule_appteam_appuser_jenkinsautomaticarchiveinfo_miniversionupgrade.py#L1-L24)

## 数据查询最佳实践

为了高效地查询应用数据，应遵循以下最佳实践。

**索引使用：**
- **主键索引**：所有表的`id`字段都有主键索引。
- **外键索引**：`app_id`, `team_id`, `module_name`等外键和频繁用于连接的字段应建立索引。
- **查询条件索引**：在`WHERE`子句中频繁使用的字段，如`app_name`, `app_status`, `module_name`, `package_type`等，应建立索引以加速查询。

**查询优化：**
- **使用`select_related`**：在Django ORM中，当需要访问外键关联的对象时，使用`select_related`进行SQL的JOIN操作，避免N+1查询问题。例如，查询`AppModule`时同时获取其`AppInfo`信息。
- **使用`prefetch_related`**：对于多对多或反向外键关系，使用`prefetch_related`一次性获取所有相关对象。
- **避免`SELECT ***：只查询需要的字段，减少数据传输量。
- **合理使用分页**：对于可能返回大量结果的查询，使用分页（如`LIMIT`和`OFFSET`）来控制结果集大小。

**性能监控：**
- **慢查询日志**：启用数据库的慢查询日志，定期分析执行时间过长的SQL语句。
- **监控工具**：使用如Prometheus、Grafana等工具监控数据库的CPU、内存、I/O和连接数等关键指标。
- **代码审查**：在代码审查中重点关注数据库查询的效率，避免在循环中执行数据库查询。

**Section sources**
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L10-L200)

## 结论

本文档全面阐述了应用管理系统的数据模型。核心实体`AppInfo`、`AppModule`、`AppBuild`和`AppTeam`通过清晰的字段定义和关系约束，构建了一个稳定可靠的数据基础。应用注册流程确保了数据的原子性创建，而状态机和业务规则则定义了数据的生命周期和行为。通过审计字段和日志机制，系统具备了完善的数据变更追踪能力。数据库迁移机制保证了数据结构的可演进性。最后，通过遵循索引、查询优化和性能监控的最佳实践，可以确保系统在高负载下依然保持高性能和高可用性。