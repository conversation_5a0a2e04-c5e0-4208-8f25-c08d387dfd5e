# 迭代管理数据模型

<cite>
**本文档引用的文件**
- [models.py](file://iter_mgt/models.py)
- [plan_ser.py](file://iter_mgt/plan_ser.py)
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py)
- [archive_view.py](file://iter_mgt/archive_view.py)
- [iter_lock_mgt.py](file://iter_mgt/iter_lock_mgt.py)
- [publish/models.py](file://publish/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [核心实体字段定义](#核心实体字段定义)
3. [迭代状态机与业务规则](#迭代状态机与业务规则)
4. [实体关联关系](#实体关联关系)
5. [迭代生命周期ER图](#迭代生命周期er图)
6. [数据变更流程](#数据变更流程)
7. [迭代锁机制](#迭代锁机制)
8. [性能优化方案](#性能优化方案)
9. [数据归档与清理](#数据归档与清理)
10. [结论](#结论)

## 引言
本文档全面描述了迭代管理系统中的核心数据模型，包括Iteration（迭代）、Branch（分支）、Archive（归档）、PublishPlan（发布计划）等实体的详细定义。文档深入分析了各实体的字段、状态转换、业务规则以及它们之间的关联关系。通过ER图展示了迭代生命周期的整体数据结构，并详细说明了在迭代创建、归档和回滚过程中的数据变更流程。此外，文档还记录了迭代锁的实现原理、数据库约束、查询性能优化策略以及数据归档和清理的详细方案。

## 核心实体字段定义

### 迭代 (Iteration)
迭代实体（`Branches`）是系统的核心，代表一个完整的开发周期。

**字段定义:**
- `pipeline_id`: 迭代版本，主键，格式为`组名_分支名`。
- `br_name`: 分支版本，如`2.1.0`。
- `project_group`: 迭代分组，即GitLab项目组名。
- `br_style`: 分支类型，如`bugfix`、`feature`。
- `br_start_date`: 迭代创建时间。
- `br_end_date`: 迭代完成时间。
- `duedate`: 预期上线时间。
- `br_status`: 分支状态，`open`（开启）或`close`（关闭/归档）。
- `is_new`: 是否为新开迭代。
- `description`: 功能描述。
- `releaseNotic`: 注意事项。
- `schedule`: 调度信息。
- `tapd_id`: 关联的TAPD需求ID。
- `archive_msg`: 归档信息，记录归档过程中的日志。
- `create_date`: 创建时间。
- `test_end_date`: 测试结束时间。
- `update_test_end_date_user`: 更新测试结束时间的操作人。

**Section sources**
- [models.py](file://iter_mgt/models.py#L20-L49)

### 分支应用 (BranchIncludeSys)
该实体记录了迭代中包含的具体应用及其状态。

**字段定义:**
- `pipeline_id`: 所属迭代ID。
- `appName`: 应用名。
- `sys_status`: 应用状态，包含`测试中`、`已推送制品库`、`发布私服`、`计划上线`、`仿真通过`、`已上线`、`已归档`等。
- `sys_duedate`: 发布日期。
- `proposer`: 申请人。
- `simulate_identifier`: 仿真验证人。
- `git_last_version`: 最后一次编译版本。
- `git_repo_version`: 分支制品版本。
- `git_repos_time`: 制品时间。
- `package_type`: 应用类型。
- `git_path`: Git路径。
- `user_name`: 用户名。
- `need_online`: 是否需要上线。
- `build_cmd`: 编译命令。
- `zeus_type`: 分支配置类型。

**Section sources**
- [models.py](file://iter_mgt/models.py#L51-L104)

### 发布计划 (PublishPlan)
发布计划实体（`IterMgtPublishPlan`）定义了迭代的发布流程。

**字段定义:**
- `id`: 主键。
- `pipeline_id`: 关联的迭代ID。
- `plan_name`: 计划名称。
- `plan_status`: 计划状态，如`待执行`、`执行中`、`已完成`。
- `create_user`: 创建人。
- `create_time`: 创建时间。
- `update_user`: 更新人。
- `update_time`: 更新时间。

**Section sources**
- [iter_mgt/publish_plan/model/iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py)

### 归档 (Archive)
归档实体（`IterMgtArchiveLog`）记录了每次归档操作的详细日志。

**字段定义:**
- `iteration_id`: 迭代版本。
- `step_order`: 步骤顺序。
- `step_name`: 步骤名称。
- `step_desc`: 步骤描述。
- `step_status`: 步骤结果。
- `request_params`: 请求参数（JSON格式）。
- `response_result`: 返回结果（JSON格式）。
- `opt_time`: 操作时间。
- `opt_user`: 操作人。

**Section sources**
- [models.py](file://iter_mgt/models.py#L386-L398)

### 发布申请 (PublishApplication)
该实体（`PublishOrder`）记录了向生产环境发布应用的申请。

**字段定义:**
- `team`: 组。
- `br_name`: 分支版本。
- `appName`: 应用名称。
- `applicant`: 申请人。
- `apply_at`: 申请时间。
- `pipeline_description`: 流水线说明。
- `sql_address`: SQL地址。
- `description`: 描述。
- `pipeline_id`: 迭代ID。
- `env`: 发布环境。
- `git_last_version`: 最新版本ID。
- `git_repo_version`: 制品库版本。
- `status`: 发布申请状态。

**Section sources**
- [models.py](file://iter_mgt/models.py#L106-L128)

## 迭代状态机与业务规则

### 迭代状态机
迭代（`Branches`）的状态由`br_status`字段表示，其状态机如下：

```mermaid
stateDiagram-v2
[*] --> open
open --> close : 归档完成
close --> open : 回滚操作
```

**Diagram sources**
- [models.py](file://iter_mgt/models.py#L20-L49)

### 应用状态机
应用（`BranchIncludeSys`）在迭代中的状态由`sys_status`字段表示，其状态机是一个线性流程：

```mermaid
stateDiagram-v2
[*] --> 测试中
测试中 --> 已推送制品库 : 编译成功
已推送制品库 --> 发布私服 : 推送成功
发布私服 --> 计划上线 : 发布计划确认
计划上线 --> 仿真通过 : 仿真验证通过
仿真通过 --> 已上线 : 生产发布成功
已上线 --> 已归档 : 迭代归档
已归档 --> [*]
测试中 --> 测试中-回滚中 : 回滚中
已归档 --> 已归档-回滚中 : 回滚中
```

**Diagram sources**
- [models.py](file://iter_mgt/models.py#L51-L104)

### 核心业务规则
1.  **迭代创建规则**: 创建新迭代时，必须指定`project_group`、`br_name`、`br_style`、`deadline`（截止日期）和`desc`（描述）。系统会检查该组下是否已存在同名分支。
2.  **应用加入规则**: 应用加入迭代时，会检查该应用是否已在其他未归档的迭代中，以防止冲突。
3.  **发布申请规则**: 只有状态为`仿真通过`或`已上线`的应用才能提交生产发布申请。申请时需提供SQL变更说明。
4.  **归档规则**: 迭代归档前，所有应用必须达到`已上线`或`已归档`状态。归档操作会将`br_status`从`open`改为`close`，并记录`br_end_date`。
5.  **回滚规则**: 回滚操作会创建一个新的`open`状态的迭代，其基础是已归档的迭代。

**Section sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L10-L799)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L10-L257)

## 实体关联关系

### 主要关联
- **迭代 (Iteration)** 与 **分支应用 (BranchIncludeSys)** 是一对多关系。一个迭代包含多个应用。
- **迭代 (Iteration)** 与 **发布计划 (PublishPlan)** 是一对多关系。一个迭代可以有多个发布计划。
- **迭代 (Iteration)** 与 **归档日志 (IterMgtArchiveLog)** 是一对多关系。一次归档操作会产生多条日志。
- **迭代 (Iteration)** 与 **发布申请 (PublishOrder)** 是一对多关系。一个迭代中的多个应用可以发起多次发布申请。

### 关联ER图
```mermaid
erDiagram
Iteration ||--o{ BranchIncludeSys : "包含"
Iteration ||--o{ PublishPlan : "拥有"
Iteration ||--o{ ArchiveLog : "产生"
Iteration ||--o{ PublishOrder : "触发"
class Iteration {
+pipeline_id PK
+br_name
+project_group
+br_status
+br_start_date
+br_end_date
+duedate
+description
}
class BranchIncludeSys {
+pipeline_id FK
+appName PK
+sys_status
+git_repo_version
+proposer
}
class PublishPlan {
+id PK
+pipeline_id FK
+plan_status
+create_user
}
class ArchiveLog {
+id PK
+iteration_id FK
+step_name
+step_status
+opt_time
}
class PublishOrder {
+id PK
+pipeline_id FK
+appName
+env
+status
+apply_at
}
```

**Diagram sources**
- [models.py](file://iter_mgt/models.py)

## 迭代生命周期ER图
以下ER图展示了迭代从创建到归档的完整生命周期中涉及的主要实体及其关系。

```mermaid
erDiagram
USER ||--o{ Iteration : "创建"
Iteration ||--o{ BranchIncludeSys : "包含"
Iteration ||--o{ PublishPlan : "生成"
Iteration ||--o{ PublishOrder : "触发"
Iteration ||--o{ IterMgtArchiveLog : "产生"
Iteration ||--o{ IterMgtLock : "持有"
BranchIncludeSys ||--o{ Artifactinfo : "基于"
PublishPlan ||--o{ IterMgtPublishPlanNode : "由节点组成"
IterMgtPublishPlanNode ||--o{ IterMgtPublishPlanNodeDefaultParam : "有默认参数"
IterMgtLock ||--o{ USER : "被锁定"
class USER {
+username PK
+email
}
class Iteration {
+pipeline_id PK
+br_name
+project_group
+br_status
+create_date
}
class BranchIncludeSys {
+pipeline_id FK
+appName PK
+sys_status
}
class PublishPlan {
+id PK
+pipeline_id FK
+plan_status
}
class PublishOrder {
+id PK
+pipeline_id FK
+appName
+status
}
class IterMgtArchiveLog {
+id PK
+iteration_id FK
+step_name
+opt_time
}
class IterMgtLock {
+id PK
+iteration_id FK
+lock_type
+lock_status
+lock_user
}
class Artifactinfo {
+appName PK
+package_type
+need_online
}
class IterMgtPublishPlanNode {
+id PK
+publish_plan_id FK
+node_name
}
class IterMgtPublishPlanNodeDefaultParam {
+id PK
+node_id FK
+param_key
+param_value
}
```

**Diagram sources**
- [models.py](file://iter_mgt/models.py)
- [publish/models.py](file://publish/models.py)

## 数据变更流程

### 迭代创建流程
当创建一个新迭代时，系统执行以下数据变更：
1.  在`iter_mgt_iter_info`表中插入一条新记录，`br_status`为`open`。
2.  将用户选择的应用列表插入`iter_mgt_iter_app_info`表，`sys_status`初始化为`测试中`。
3.  如果是新开迭代，可能还会在`iter_whitelist_app`表中创建白名单记录。

```mermaid
flowchart TD
Start([用户发起创建请求]) --> ValidateInput["验证输入参数<br/>project_group, br_name等"]
ValidateInput --> CheckExist["检查迭代是否已存在"]
CheckExist --> |存在| ReturnError["返回错误"]
CheckExist --> |不存在| InsertIteration["插入iter_mgt_iter_info记录"]
InsertIteration --> InsertApps["批量插入iter_mgt_iter_app_info记录"]
InsertApps --> CreateWhitelist["创建迭代白名单(可选)"]
CreateWhitelist --> End([迭代创建成功])
ReturnError --> End
```

**Diagram sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L10-L799)
- [models.py](file://iter_mgt/models.py)

### 迭代归档流程
归档是迭代生命周期的结束，其数据变更流程如下：
1.  调用`archive_view.py`中的API，检查归档条件（所有应用状态）。
2.  触发Jenkins归档任务。
3.  任务执行过程中，向`iter_mgt_archive_log`表写入每一步的详细日志。
4.  归档成功后，更新`iter_mgt_iter_info`表，将`br_status`设为`close`，并设置`br_end_date`。

```mermaid
flowchart TD
Start([用户发起归档请求]) --> ValidateStatus["验证应用状态<br/>必须为'已上线'"]
ValidateStatus --> |验证失败| ReturnError["返回错误"]
ValidateStatus --> |验证通过| TriggerJenkins["触发Jenkins归档任务"]
TriggerJenkins --> WriteLog["任务执行中，持续写入<br/>iter_mgt_archive_log"]
WriteLog --> UpdateStatus["归档成功，更新<br/>iter_mgt_iter_info<br/>br_status='close'"]
UpdateStatus --> End([归档完成])
ReturnError --> End
```

**Diagram sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L10-L446)
- [models.py](file://iter_mgt/models.py)

### 迭代回滚流程
回滚操作会创建一个新的迭代，其数据变更流程为：
1.  选择一个已归档的迭代作为回滚基础。
2.  创建一个新的`pipeline_id`，其`br_status`为`open`。
3.  将原迭代中的应用及其制品版本信息复制到新的`iter_mgt_iter_app_info`记录中。
4.  新迭代的应用状态从`已归档`恢复为`测试中`或`已上线`。

**Section sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L10-L799)
- [archive_view.py](file://iter_mgt/archive_view.py#L10-L446)

## 迭代锁机制

### 实现原理
迭代锁（`IterMgtLock`）用于防止在关键操作（如编译、部署）时对同一迭代进行并发修改。

**字段定义:**
- `iteration_id`: 被锁定的迭代ID。
- `lock_type`: 锁类型，`compile`（编译锁）或`deploy`（部署锁）。
- `lock_status`: 锁状态，`1`表示锁定，`0`表示解锁。
- `lock_user`: 锁定人。
- `lock_desc`: 锁描述。
- `create_time/update_time`: 时间戳。

**Section sources**
- [models.py](file://iter_mgt/models.py#L371-L384)

### API接口
`iter_lock_mgt.py`提供了RESTful API来管理锁。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "IterLockApi"
participant DB as "数据库"
Client->>API : GET /lock?iteration_id=X&lock_type=Y
API->>DB : 查询IterMgtLock记录
DB-->>API : 返回锁状态
API-->>Client : 返回锁状态
Client->>API : POST /lock {iteration_id, lock_type, lock_status}
API->>DB : 查询当前锁持有者
DB-->>API : 返回锁信息
alt 有锁且非锁定人请求解锁
API-->>Client : 返回错误"需锁定人操作"
else 无锁或锁定人操作
API->>DB : 更新或创建锁记录
DB-->>API : 操作成功
API-->>Client : 返回"操作成功"
end
```

**Diagram sources**
- [iter_lock_mgt.py](file://iter_mgt/iter_lock_mgt.py#L1-L53)
- [models.py](file://iter_mgt/models.py)

### 数据库约束
- `iteration_id`和`lock_type`共同构成业务上的唯一性约束，确保同一迭代的同类型锁只有一个。
- 使用数据库的`UPDATE`语句的原子性来保证锁的获取和释放是线程安全的。

**Section sources**
- [iter_lock_mgt.py](file://iter_mgt/iter_lock_mgt.py#L1-L53)

## 性能优化方案

### 时间序列数据索引策略
系统中存在大量按时间查询的数据，如归档日志、发布记录等。为此，采用了以下索引策略：

1.  **复合索引**: 在`iter_mgt_archive_log`表上，为`iteration_id`和`opt_time`创建复合索引，以支持按迭代ID和时间范围快速查询日志。
    ```sql
    CREATE INDEX idx_archive_iter_time ON iter_mgt_archive_log(iteration_id, opt_time);
    ```
2.  **时间字段索引**: 在`iter_mgt_iter_info`表上，为`br_start_date`和`br_end_date`创建单独的索引，以加速按创建或结束时间查询迭代列表。
    ```sql
    CREATE INDEX idx_iter_start_date ON iter_mgt_iter_info(br_start_date);
    CREATE INDEX idx_iter_end_date ON iter_mgt_iter_info(br_end_date);
    ```
3.  **状态字段索引**: 在`iter_mgt_iter_info`表上，为`br_status`创建索引，以快速筛选出`open`状态的迭代。
    ```sql
    CREATE INDEX idx_iter_status ON iter_mgt_iter_info(br_status);
    ```

**Section sources**
- [models.py](file://iter_mgt/models.py)

### 查询优化
- **避免N+1查询**: 在`iter_mgt_ser.py`中，使用原生SQL查询（`cursor.execute`）来一次性获取迭代、应用、构建类型等关联信息，而不是在Django ORM中进行多次查询。
- **分页查询**: 对于列表查询（如`get_iter_search_info`），实现了分页逻辑，避免一次性加载过多数据。
- **只查询必要字段**: 在序列化器（Serializer）中，使用`fields`参数精确控制返回的字段，减少网络传输量。

**Section sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L10-L799)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L10-L257)

## 数据归档与清理

### 归档策略
1.  **自动归档**: 当迭代状态变为`close`时，相关的归档日志（`iter_mgt_archive_log`）和发布申请（`iter_mgt_publish_application`）被视为已归档数据。
2.  **冷存储**: 历史归档数据可以定期从主数据库导出到数据仓库或冷存储系统，以减轻主库压力。

### 清理策略
1.  **日志清理**: `iter_mgt_archive_log`表中的日志是操作审计的关键，通常保留较长时间（如1-2年）。可以编写定时任务，删除超过保留期限的日志。
2.  **临时数据清理**: 与Jenkins任务相关的临时记录（如`JenkinsMgtTestDataDevJob`）在任务完成后，其状态为`success`或`failure`超过一定时间（如30天）后，可以被清理。
3.  **清理流程**:
    -  **识别**: 通过`create_time`和状态筛选出可清理的记录。
    -  **备份**: 在删除前，将数据备份到归档表或文件系统。
    -  **删除**: 执行`DELETE`语句或`TRUNCATE`操作。

**Section sources**
- [models.py](file://iter_mgt/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)

## 结论
本文档详细阐述了迭代管理系统的数据模型，涵盖了从实体定义、状态机、关联关系到具体实现和优化的各个方面。该模型设计清晰，通过`pipeline_id`作为核心关联键，有效地组织了迭代、应用、发布和归档等核心业务数据。锁机制和状态机确保了数据的一致性和业务流程的正确性。通过合理的索引策略和查询优化，保证了系统在处理大量时间序列数据时的性能。整体数据模型为系统的稳定运行和高效管理提供了坚实的基础。