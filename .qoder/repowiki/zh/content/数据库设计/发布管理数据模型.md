# 发布管理数据模型

<cite>
**本文档引用的文件**   
- [models.py](file://publish/models.py)
- [publish_ser.py](file://publish/publish_ser.py)
- [publish_record.py](file://publish/utils/publish_record.py)
- [group_publish_record.py](file://publish/utils/group_publish_record.py)
- [resource_ser.py](file://publish/resource_ser.py)
</cite>

## 目录
1. [核心实体模型](#核心实体模型)
2. [发布流程与关联关系](#发布流程与关联关系)
3. [发布策略与审批流程](#发布策略与审批流程)
4. [发布回滚机制](#发布回滚机制)
5. [资源分配与事务管理](#资源分配与事务管理)
6. [发布历史查询与性能优化](#发布历史查询与性能优化)
7. [数据保留与归档策略](#数据保留与归档策略)

## 核心实体模型

发布管理数据模型的核心实体包括 `PublishRecord`、`GroupPublish` 和 `Resource`，这些实体在代码中通过 Django ORM 模型类进行定义，主要位于 `publish/models.py` 文件中。

### PublishRecord (发布记录)

`PublishRecord` 实体在代码中由多个模型类共同体现，其中 `ProductInfo` 模型是其核心组成部分，用于存储制品信息。

**字段定义与数据类型：**
- **module_name**: 应用名，`CharField`，最大长度 24。
- **lib_repo_url**: 制品库地址，`CharField`，最大长度 256，可为空。
- **lib_repo_branch**: 制品分支，`CharField`，最大长度 64，可为空。
- **lib_repo_version**: git 提交 md5 信息，`CharField`，最大长度 64，可为空。
- **lib_repo_size**: 制品大小，`CharField`，最大长度 96，可为空。
- **create_time**: 创建时间，`DateTimeField`，自动添加。
- **iteration_id**: 迭代号，`CharField`，最大长度 64，可为空。
- **lib_repo_version_log**: git 提交日志，`CharField`，最大长度 128，可为空。
- **suite_code**: 环境套，`CharField`，最大长度 24，可为空。
- **package_stamp**: 制品的时间戳，`CharField`，最大长度 50。

**约束条件：**
- 该模型对应数据库表 `product_mgt_product_info`。
- `create_time` 字段在创建记录时自动填充当前时间。

### GroupPublish (分组发布)

`GroupPublish` 实体在代码中通过 `IterMgtJenkinsPublishPipelineInfo` 和 `IterMgtJenkinsPublishPipelineDetail` 模型来实现，用于管理分组发布的流水线信息和详情。

**字段定义与数据类型：**
- **action_id**: action_id，`IntegerField`。
- **job_name**: job_name，`CharField`，最大长度 255。
- **build_id**: job 构建 id，`IntegerField`。
- **exec_id**: 随机 id，防重放，`CharField`，最大长度 200。
- **status**: 状态：running,aborted,failure,success，`CharField`，最大长度 20。
- **lock_status**: 锁状态:lock,unlock，`CharField`，最大长度 20。
- **lock_time**: 锁时间，`DateTimeField`。
- **create_time**: 创建时间，`DateTimeField`。
- **create_user**: 创建人，`CharField`，最大长度 100。
- **update_time**: 更新时间，`DateTimeField`。
- **update_user**: 更新人，`CharField`，最大长度 100。
- **execute_status**: 执行状态，`CharField`，最大长度 20，有预定义的选择项（检查中、检查通过、检查不通过、执行中、执行成功、执行失败、已终止）。

**约束条件：**
- `IterMgtJenkinsPublishPipelineInfo` 模型对应数据库表 `iter_mgt_jenkins_publish_pipeline_info`。
- `IterMgtJenkinsPublishPipelineDetail` 模型对应数据库表 `iter_mgt_jenkins_publish_pipeline_detail`。
- `execute_status` 字段的值必须是预定义选项之一。

### Resource (资源)

`Resource` 实体在代码中由 `DeployInfo` 模型表示，用于存储应用的部署信息。

**字段定义与数据类型：**
- **module_name**: 模块名，`CharField`，最大长度 100。
- **container_name**: 容器名，`CharField`，最大长度 255，可为空。
- **deploy_path**: 部署路径，`CharField`，最大长度 999，可为空。
- **config_path**: 配置路径，`CharField`，最大长度 999，可为空。
- **lib_repo_config_path**: 配置制品路径，`CharField`，最大长度 999，可为空。
- **script_path**: 脚本路径，`CharField`，最大长度 999，可为空。
- **script_name**: 脚本名称，`CharField`，最大长度 50，可为空。
- **start_level**: 启动级别，`IntegerField`，可为空。
- **lib_push_dir**: 制品推送目录，`CharField`，最大长度 999，可为空。

**约束条件：**
- 该模型对应数据库表 `publish_deploy_info`。

**Section sources**
- [models.py](file://publish/models.py#L145-L184)

## 发布流程与关联关系

发布记录与应用、环境、迭代之间存在复杂的关联关系。这些关系通过数据库表之间的外键和业务逻辑代码来维护。

### 关联关系说明

- **发布记录与应用 (PublishRecord - App)**: `ProductInfo` 模型中的 `module_name` 字段直接关联到应用名，建立了发布记录与应用的直接联系。
- **发布记录与环境 (PublishRecord - Environment)**: `ProductInfo` 模型中的 `suite_code` 字段关联到环境套，表明该制品是为哪个环境准备的。
- **发布记录与迭代 (PublishRecord - Iteration)**: `ProductInfo` 模型中的 `iteration_id` 字段直接关联到具体的迭代号，将发布记录绑定到特定的迭代周期。

```mermaid
erDiagram
PRODUCT_INFO {
string module_name PK
string lib_repo_url
string lib_repo_branch
string lib_repo_version
string lib_repo_size
datetime create_time
string iteration_id FK
string lib_repo_version_log
string suite_code FK
string package_stamp
}
ITER_MGT_JENKINS_PUBLISH_PIPELINE_INFO {
int action_id
string job_name
int build_id
string exec_id PK
string status
string lock_status
datetime lock_time
datetime create_time
string create_user
datetime update_time
string update_user
string execute_status
}
ITER_MGT_JENKINS_PUBLISH_PIPELINE_DETAIL {
int id PK
int publish_id FK
string suite_code
string module_name
datetime create_time
string create_user
datetime update_time
string update_user
}
DEPLOY_INFO {
string module_name PK
string container_name
string deploy_path
string config_path
string lib_repo_config_path
string script_path
string script_name
int start_level
string lib_push_dir
}
PRODUCT_INFO ||--o{ ITER_MGT_JENKINS_PUBLISH_PIPELINE_DETAIL : "发布"
ITER_MGT_JENKINS_PUBLISH_PIPELINE_INFO ||--o{ ITER_MGT_JENKINS_PUBLISH_PIPELINE_DETAIL : "包含"
PRODUCT_INFO }|--|| DEPLOY_INFO : "部署配置"
```

**Diagram sources **
- [models.py](file://publish/models.py#L145-L184)
- [models.py](file://publish/models.py#L305-L334)
- [models.py](file://publish/models.py#L336-L364)
- [models.py](file://publish/models.py#L105-L143)

## 发布策略与审批流程

发布策略和执行状态通过 `IterMgtJenkinsPublishPipelineInfo` 模型进行管理。该模型中的 `execute_status` 字段定义了发布流程的状态机。

### 发布流程状态机

```mermaid
stateDiagram-v2
[*] --> CHECK
CHECK --> CHECK_SUCCESS : 检查通过
CHECK --> CHECK_FAILURE : 检查不通过
CHECK_SUCCESS --> RUNNING : 开始执行
RUNNING --> SUCCESS : 执行成功
RUNNING --> FAILURE : 执行失败
RUNNING --> ABORTED : 已终止
SUCCESS --> [*]
FAILURE --> [*]
ABORTED --> [*]
```

**Diagram sources **
- [models.py](file://publish/models.py#L318-L320)

### 审批流程

代码中未直接体现一个独立的“审批”流程模型，但 `execute_status` 的 `CHECK` 状态可以视为审批流程的开始。当发布请求被创建后，系统会进入 `CHECK` 状态，只有通过检查（变为 `CHECK_SUCCESS`）后，才能进入 `RUNNING` 状态开始执行发布。

**Section sources**
- [models.py](file://publish/models.py#L305-L334)

## 发布回滚机制

发布回滚机制的数据实现主要通过 `PublishMgtRollbackInfo` 模型来完成。

### 回滚信息模型

**字段定义与数据类型：**
- **create_user**: 创建人，`CharField`，最大长度 50。
- **create_time**: 创建时间，`DateTimeField`。
- **update_user**: 更新人，`CharField`，最大长度 50。
- **update_time**: 更新时间，`DateTimeField`。
- **stamp**: 版本，`BigIntegerField`。
- **id**: id，`BigIntegerField`，主键。
- **module_name**: 应用名，`CharField`，最大长度 100。
- **rollback_date**: 回滚时间，`DateTimeField`。
- **rollback_status**: 回滚状态，`CharField`，最大长度 10，选择项为 `RollbackStatus` 枚举（running, end）。
- **branch_name**: 回滚分支名称，`CharField`，最大长度 100。
- **lib_repo_version**: 回滚制品commit id，`CharField`，最大长度 100。

**约束条件：**
- 该模型对应数据库表 `publish_mgt_rollback_info`。
- `rollback_status` 字段的值必须是 `RollbackStatus` 枚举中的 `RUNNING` 或 `END`。

**Section sources**
- [models.py](file://publish/models.py#L372-L383)

## 资源分配与事务管理

发布过程中的资源分配和释放通过 `RepoInfoRecorder` 类及其子类来管理，主要涉及制品信息和配置信息的记录。

### 资源分配逻辑

`RepoInfoRecorder` 类负责在发布过程中更新 `env_mgt_node_bind` 表中的 `lib_repo_info_id` 和 `node_lib_repo_update_time` 字段，从而将特定的制品版本与具体的环境节点绑定起来。

```mermaid
flowchart TD
Start([开始发布]) --> GetRepoId["获取制品ID (lib_repo_id)"]
GetRepoId --> GetNodeBindId["获取节点绑定ID (node_bind_id_list)"]
GetNodeBindId --> UpdateBind["更新节点绑定表"]
UpdateBind --> End([发布完成])
```

**Diagram sources **
- [publish_ser.py](file://publish/publish_ser.py#L100-L140)

### 事务管理

代码中通过 `connection.cursor()` 执行 SQL 语句，并在关键操作后调用 `connection.commit()` 来提交事务，确保数据的一致性。例如，在 `update_publish_lock` 函数中，执行了两次 `UPDATE` 操作，并分别调用了 `connection.commit()`。

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L450-L470)

## 发布历史查询与性能优化

发布历史查询的性能优化策略主要体现在索引设计和分页查询上。

### 性能优化策略

- **索引设计**: 虽然代码中没有直接定义索引，但从 SQL 查询语句可以推断出，`product_mgt_product_info` 表的 `module_name`、`iteration_id` 和 `create_time` 字段，以及 `env_mgt_node_bind` 表的 `module_name`、`node_ip` 和 `suite_code` 字段上应该存在索引，以支持高效的查询。
- **分页查询**: 在 `get_last_can_rollback_info` 函数中，使用了 `LIMIT 1 OFFSET N` 的 SQL 语法来实现分页，以获取上 N 个可回滚的日期对应的版本，这避免了全表扫描，提高了查询效率。

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L750-L799)

## 数据保留与归档策略

文档中未明确提及发布数据的保留策略和归档机制的具体实现。相关逻辑可能存在于未提供的代码或数据库脚本中。

**Section sources**
- 无直接相关文件分析