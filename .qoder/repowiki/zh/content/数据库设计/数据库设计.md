# 数据库设计

<cite>
**本文档引用的文件**
- [<EMAIL>](file://db/schema/<EMAIL>)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://app_mgt/models.py)
- [models.py](file://db_mgt/models.py)
- [models.py](file://publish/models.py)
- [models.py](file://iter_mgt/models.py)
- [models.py](file://env_mgt/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)
10. [附录](#附录)（如有必要）

## 引言
本文档旨在全面介绍系统的核心数据结构和关系，重点阐述主要实体的字段定义、数据类型、约束条件和业务规则。文档详细解释了表之间的关系，包括一对一、一对多和多对多关联，并通过ER图直观展示数据库结构。同时，文档还涵盖了索引设计策略、数据生命周期管理、数据库迁移机制以及数据访问层的最佳实践。

## 项目结构
本项目采用模块化设计，数据库相关文件主要分布在`db/schema`目录下，包含多个SQL脚本文件，用于定义和维护数据库结构。核心数据模型分布在`app_mgt`、`env_mgt`、`publish`等多个模块的`models.py`文件中，通过Django ORM进行管理。

```mermaid
erDiagram
APP_INFO {
bigint id PK
varchar app_name UK
varchar app_cname
tinyint app_status
varchar git_url
varchar git_path
varchar svn_url
varchar svn_path
varchar app_jdk_version
varchar app_desc
varchar create_user
datetime create_time
varchar update_user
datetime update_time
int stamp
boolean platform_type
datetime platform_time
}
APP_MODULE {
bigint id PK
int app_id FK
varchar module_name UK
varchar module_code
tinyint module_status
varchar module_desc
varchar module_svn_path
varchar module_jdk_version
boolean need_online
boolean need_check
int app_port
varchar container_name
varchar create_path
varchar lib_repo
varchar deploy_path
varchar extend_attr
varchar create_user
datetime create_time
varchar update_user
datetime update_time
int stamp
boolean zeus_type
boolean need_ops
int is_component
}
APP_BUILD {
bigint id PK
int app_id FK
varchar module_name UK
varchar module_code
varchar module_version
varchar package_type
varchar package_name
boolean package_full
varchar build_jdk_version
varchar create_user
datetime create_time
varchar update_user
datetime update_time
int stamp
boolean need_mock
varchar mock_build_cmd
varchar build_cmd
}
ENV_MGT_REGION {
bigint id PK
varchar addr_name
varchar addr_short_name
varchar type_name
varchar type_short_name
varchar region_name UK
varchar region_desc
tinyint region_is_active
varchar create_user
datetime create_time
varchar update_user
datetime update_time
bigint stamp
}
ENV_MGT_SUITE {
bigint id PK
bigint region_id FK
varchar suite_code UK
varchar suite_name
varchar suite_desc
tinyint suite_is_active
tinyint support_docker
varchar test_group
varchar create_user
datetime create_time
varchar update_user
datetime update_time
bigint stamp
}
ENV_MGT_NODE {
bigint id PK
varchar node_name
varchar node_ip
varchar minion_id
bigint region_id FK
varchar node_os
tinyint node_status
datetime node_recyled
varchar node_desc
varchar create_user
datetime create_time
varchar update_user
datetime update_time
bigint stamp
}
ENV_MGT_NODE_BIND {
bigint id PK
varchar module_name FK
bigint suite_id FK
bigint node_id FK
bigint node_port
varchar node_docker
varchar node_bind_desc
bigint deploy_group
tinyint deploy_type
varchar deploy_path
varchar health_check_url
varchar create_user
datetime create_time
varchar update_user
datetime update_time
bigint stamp
}
PUBLISH_RECORD {
bigint id PK
varchar module_name FK
varchar publish_version
varchar publish_user
datetime publish_time
varchar publish_status
varchar publish_desc
varchar create_user
datetime create_time
varchar update_user
datetime update_time
int stamp
}
DB_MGT_PIPELINE {
bigint id PK
varchar pipeline_name
varchar pipeline_desc
varchar create_user
datetime create_time
varchar update_user
datetime update_time
int stamp
}
APP_INFO ||--o{ APP_MODULE : "包含"
APP_INFO ||--o{ APP_BUILD : "构建"
APP_MODULE ||--o{ APP_BUILD : "构建"
ENV_MGT_REGION ||--o{ ENV_MGT_SUITE : "包含"
ENV_MGT_REGION ||--o{ ENV_MGT_NODE : "包含"
ENV_MGT_SUITE ||--o{ ENV_MGT_NODE_BIND : "绑定"
ENV_MGT_NODE ||--o{ ENV_MGT_NODE_BIND : "绑定"
APP_MODULE ||--o{ ENV_MGT_NODE_BIND : "部署"
APP_MODULE ||--o{ PUBLISH_RECORD : "发布"
DB_MGT_PIPELINE ||--o{ PUBLISH_RECORD : "执行"
```

**图示来源**
- [<EMAIL>](file://db/schema/<EMAIL>#L1-L218)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L1-L134)
- [models.py](file://app_mgt/models.py#L1-L479)

**本节来源**
- [db/schema](file://db/schema)
- [env_mgt/db/schema](file://env_mgt/db/schema)

## 核心组件
系统的核心数据结构主要围绕应用管理、环境管理和发布管理三大模块展开。应用管理模块定义了应用、模块和构建的核心实体；环境管理模块负责管理可用域、环境套、节点及其绑定关系；发布管理模块则记录了发布历史和相关状态。

**本节来源**
- [models.py](file://app_mgt/models.py#L1-L479)
- [models.py](file://env_mgt/models.py)
- [models.py](file://publish/models.py)

## 架构概述
系统采用分层架构，数据库层通过Django ORM与应用层交互。数据模型设计遵循规范化原则，同时考虑了查询性能和业务需求。核心实体之间的关系通过外键约束维护，确保数据完整性。

```mermaid
graph TB
subgraph "数据库层"
DB[(数据库)]
end
subgraph "应用层"
AM[应用管理]
EM[环境管理]
PM[发布管理]
DM[数据库管理]
end
AM --> DB
EM --> DB
PM --> DB
DM --> DB
```

**图示来源**
- [models.py](file://app_mgt/models.py#L1-L479)
- [models.py](file://env_mgt/models.py)
- [models.py](file://publish/models.py)
- [models.py](file://db_mgt/models.py)

## 详细组件分析

### 应用管理模块分析
应用管理模块是系统的核心，负责管理所有应用的元数据信息。

#### 类图
```mermaid
classDiagram
class AppInfo {
+string app_name
+string app_cname
+bool app_status
+string git_url
+string git_path
+string svn_url
+string svn_path
+string app_jdk_version
+string app_desc
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
+bool platform_type
+datetime platform_time
+Meta
}
class AppModule {
+int app_id
+string module_name
+string module_code
+bool module_status
+string module_desc
+string module_svn_path
+string module_jdk_version
+bool need_online
+bool need_check
+int app_port
+string container_name
+string create_path
+string lib_repo
+string deploy_path
+string extend_attr
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
+bool zeus_type
+bool need_ops
+int is_component
+Meta
}
class AppBuild {
+int app_id
+string module_name
+string module_code
+string module_version
+string package_type
+string package_name
+bool package_full
+string build_jdk_version
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
+bool need_mock
+string mock_build_cmd
+string build_cmd
+Meta
}
AppInfo --> AppModule : "1对多"
AppInfo --> AppBuild : "1对多"
AppModule --> AppBuild : "1对多"
```

**图示来源**
- [models.py](file://app_mgt/models.py#L1-L479)

**本节来源**
- [models.py](file://app_mgt/models.py#L1-L479)

### 环境管理模块分析
环境管理模块负责管理系统的物理和逻辑环境结构。

#### 类图
```mermaid
classDiagram
class EnvMgtRegion {
+string addr_name
+string addr_short_name
+string type_name
+string type_short_name
+string region_name
+string region_desc
+bool region_is_active
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+bigint stamp
+Meta
}
class EnvMgtSuite {
+bigint region_id
+string suite_code
+string suite_name
+string suite_desc
+bool suite_is_active
+bool support_docker
+string test_group
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+bigint stamp
+Meta
}
class EnvMgtNode {
+string node_name
+string node_ip
+string minion_id
+bigint region_id
+string node_os
+tinyint node_status
+datetime node_recyled
+string node_desc
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+bigint stamp
+Meta
}
class EnvMgtNodeBind {
+string module_name
+bigint suite_id
+bigint node_id
+bigint node_port
+string node_docker
+string node_bind_desc
+bigint deploy_group
+tinyint deploy_type
+string deploy_path
+string health_check_url
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+bigint stamp
+Meta
}
EnvMgtRegion --> EnvMgtSuite : "1对多"
EnvMgtRegion --> EnvMgtNode : "1对多"
EnvMgtSuite --> EnvMgtNodeBind : "1对多"
EnvMgtNode --> EnvMgtNodeBind : "1对多"
```

**图示来源**
- [models.py](file://env_mgt/models.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L1-L134)

**本节来源**
- [models.py](file://env_mgt/models.py)
- [env_mgt/db/schema](file://env_mgt/db/schema)

### 发布管理模块分析
发布管理模块负责跟踪和管理所有发布活动。

#### 类图
```mermaid
classDiagram
class PublishRecord {
+string module_name
+string publish_version
+string publish_user
+datetime publish_time
+string publish_status
+string publish_desc
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
+Meta
}
class PublishPlan {
+string plan_name
+string plan_desc
+datetime start_time
+datetime end_time
+string plan_status
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
+Meta
}
class PublishReason {
+string reason_code
+string reason_desc
+string create_user
+datetime create_time
+string update_user
+datetime update_time
+int stamp
+Meta
}
PublishPlan --> PublishRecord : "1对多"
PublishReason --> PublishRecord : "1对多"
```

**图示来源**
- [models.py](file://publish/models.py)
- [models.py](file://iter_mgt/models.py)

**本节来源**
- [models.py](file://publish/models.py)
- [models.py](file://iter_mgt/models.py)

## 依赖分析
系统各模块之间存在明确的依赖关系。应用管理模块为环境管理和发布管理提供基础数据支持。环境管理模块的节点绑定信息依赖于应用模块信息。发布管理模块则依赖于应用模块和环境绑定信息。

```mermaid
graph TD
AM[应用管理] --> EM[环境管理]
AM --> PM[发布管理]
EM --> PM
DM[数据库管理] --> PM
```

**图示来源**
- [models.py](file://app_mgt/models.py#L1-L479)
- [models.py](file://env_mgt/models.py)
- [models.py](file://publish/models.py)

**本节来源**
- [models.py](file://app_mgt/models.py#L1-L479)
- [models.py](file://env_mgt/models.py)
- [models.py](file://publish/models.py)

## 性能考虑
数据库设计中考虑了多个性能优化策略。所有核心表的主键字段均设置为自增BIGINT类型，确保高效的索引查找。关键查询字段如`app_name`、`module_name`、`region_name`等均设置了唯一约束或索引，以加速查询操作。对于频繁查询的组合条件，建议创建复合索引以进一步提升性能。

## 故障排除指南
当遇到数据库相关问题时，建议按照以下步骤进行排查：
1. 检查数据库连接配置是否正确
2. 验证相关表结构是否与最新迁移文件一致
3. 查看数据库日志以获取详细的错误信息
4. 检查外键约束是否满足
5. 验证数据完整性约束

**本节来源**
- [models.py](file://app_mgt/models.py#L1-L479)
- [models.py](file://env_mgt/models.py)
- [models.py](file://publish/models.py)

## 结论
本文档全面介绍了系统的数据库设计，涵盖了核心数据结构、关系模型、索引策略和最佳实践。通过规范化的数据模型设计和合理的索引策略，系统能够高效地支持各种业务场景。建议在后续开发中严格遵守数据模型规范，确保系统的稳定性和可维护性。