# 环境管理数据模型

<cite>
**本文档引用的文件**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://env_mgt/models.py)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py)
</cite>

## 目录
1. [项目结构](#项目结构)
2. [核心数据模型](#核心数据模型)
3. [环境配置继承与覆盖规则](#环境配置继承与覆盖规则)
4. [环境拓扑结构ER图](#环境拓扑结构er图)
5. [环境初始化与节点分配流程](#环境初始化与节点分配流程)
6. [环境变更审计日志机制](#环境变更审计日志机制)
7. [节点绑定关系查询性能优化](#节点绑定关系查询性能优化)
8. [环境数据迁移与清理脚本](#环境数据迁移与清理脚本)

## 项目结构

环境管理模块（env_mgt）位于项目根目录下，主要包含数据库模式定义、业务逻辑服务和数据模型定义。该模块负责管理环境、节点、可用区等核心资源的生命周期。

```mermaid
graph TD
A[env_mgt] --> B[db/schema]
A --> C[models.py]
A --> D[env_mgt_ser.py]
A --> E[env_node_mgt_ser.py]
B --> F[01-env_mgt_schema.sql]
```

**图示来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://env_mgt/models.py)

**本节来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://env_mgt/models.py)

## 核心数据模型

### EnvInfo（环境信息）

EnvInfo 对应数据库中的 `env_mgt_suite` 表，表示环境套的概念。每个环境套属于一个可用域（region），并具有唯一的编码和名称。

**字段定义：**
- `id`: BIGINT(11)，主键，自增
- `region_id`: BIGINT(11)，外键关联 `env_mgt_region.id`，表示所属可用域
- `suite_code`: VARCHAR(100)，唯一约束，环境套编码
- `suite_name`: VARCHAR(100)，环境套名称
- `suite_desc`: VARCHAR(255)，环境套说明
- `suite_is_active`: TINYINT(1)，是否可用
- `support_docker`: TINYINT(1)，是否支持Docker部署
- `create_user`: VARCHAR(20)，创建人
- `create_time`: DATETIME(0)，创建时间
- `update_user`: VARCHAR(20)，修改人
- `update_time`: DATETIME(0)，修改时间
- `stamp`: BIGINT(20)，版本戳

**约束条件：**
- `suite_code` 字段具有唯一性约束
- `region_id` 为外键，关联 `env_mgt_region` 表
- 表注释为 "环境套表"

**本节来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L37-L59)
- [models.py](file://env_mgt/models.py#L24-L46)

### EnvNode（节点信息）

EnvNode 对应数据库中的 `env_mgt_node` 表，表示物理或虚拟节点的信息。节点数据来源于CMDB系统。

**字段定义：**
- `id`: BIGINT(11)，主键，自增
- `node_name`: VARCHAR(100)，节点名称
- `node_ip`: VARCHAR(100)，节点IP地址
- `minion_id`: VARCHAR(100)，SaltStack中的唯一标识
- `region_id`: BIGINT(11)，外键关联 `env_mgt_region.id`，表示所属可用域
- `node_os`: VARCHAR(100)，操作系统类型
- `node_status`: TINYINT(1)，节点状态（0:使用中, 1:待回收, 2:回收中, 3:已回收）
- `node_recyled`: DATETIME(0)，回收时间
- `node_desc`: VARCHAR(255)，节点说明
- `create_user`: VARCHAR(20)，创建人
- `create_time`: DATETIME(0)，创建时间
- `update_user`: VARCHAR(20)，修改人
- `update_time`: DATETIME(0)，修改时间
- `stamp`: BIGINT(20)，版本戳
- `tomcat_password`: VARCHAR(255)，Tomcat用户密码
- `apply_order_code`: VARCHAR(100)，申请单单号

**约束条件：**
- `region_id` 为外键，关联 `env_mgt_region` 表
- 表注释为 "节点信息"
- 节点状态采用整数编码表示不同生命周期状态

**本节来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L100-L122)
- [models.py](file://env_mgt/models.py#L97-L114)

### NodeBind（节点绑定）

NodeBind 对应数据库中的 `env_mgt_node_bind` 表，是环境管理的核心表，表示模块在特定环境套中的节点绑定关系。

**字段定义：**
- `id`: BIGINT(11)，主键，自增
- `module_name`: VARCHAR(100)，模块名称
- `suite_id`: BIGINT(11)，外键关联 `env_mgt_suite.id`，表示所属环境套
- `node_id`: BIGINT(11)，外键关联 `env_mgt_node.id`，表示绑定的节点
- `node_port`: BIGINT(11)，服务端口号
- `node_docker`: VARCHAR(100)，Docker描述信息
- `node_bind_desc`: VARCHAR(255)，绑定说明
- `deploy_group`: BIGINT(11)，外键关联 `env_mgt_deploy_group.id`，部署分组ID
- `deploy_type`: TINYINT(1)，部署类型（1:虚拟机, 2:K8S, 3:物理机, 4:其它）
- `deploy_path`: VARCHAR(255)，部署路径
- `health_check_url`: VARCHAR(255)，健康检查URL
- `create_user`: VARCHAR(20)，创建人
- `create_time`: DATETIME(0)，创建时间
- `update_user`: VARCHAR(20)，修改人
- `update_time`: DATETIME(0)，修改时间
- `stamp`: BIGINT(20)，版本戳
- `lib_repo_info_id`: BIGINT(11)，制品表ID
- `node_lib_repo_update_time`: DATETIME(0)，制品更新时间
- `config_repo_info_id`: BIGINT(11)，配置制品ID
- `node_config_repo_update_time`: DATETIME(0)，配置制品更新时间

**约束条件：**
- `suite_id`、`node_id`、`deploy_group` 均为外键
- 表注释为 "节点绑定"
- 一个模块可以在多个环境套中绑定到不同的节点
- 支持Docker和传统虚拟机/物理机部署

**本节来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L125-L152)
- [models.py](file://env_mgt/models.py#L115-L141)

### 层级关系与绑定机制

环境、节点、可用区之间存在明确的层级关系：

1. **可用域（Region）** 是最高层级，代表物理机房或云区域
2. **环境套（Suite）** 隶属于某个可用域，代表一组具有相同特性的环境
3. **节点（Node）** 隶属于某个可用域，是具体的计算资源
4. **节点绑定（NodeBind）** 建立了模块、环境套和节点之间的关联关系

绑定机制通过 `env_mgt_node_bind` 表实现，该表作为关联表连接了模块、环境套和节点三个实体。这种设计支持多对多的灵活绑定关系，允许同一模块在不同环境套中绑定到不同的节点集合。

**本节来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://env_mgt/models.py)

## 环境配置继承与覆盖规则

环境配置的继承与覆盖规则主要体现在部署配置的传递机制中。虽然在提供的数据模型中没有直接体现继承关系的字段，但从 `NodeBind` 表的设计可以看出配置管理的思路：

1. **基础配置继承**：环境套（Suite）级别的配置作为基础配置，被该环境套下的所有节点绑定继承
2. **节点级别覆盖**：在 `NodeBind` 表中，可以通过 `deploy_path`、`health_check_url` 等字段对特定节点的配置进行覆盖
3. **部署分组继承**：`deploy_group` 字段关联的部署分组可能包含默认的部署配置，这些配置可以被具体绑定关系继承
4. **制品版本管理**：通过 `lib_repo_info_id` 和 `config_repo_info_id` 字段，实现了制品和配置的版本化管理，支持配置的回滚和历史追溯

这种设计模式遵循了"约定优于配置"的原则，既保证了配置的一致性，又保留了必要的灵活性。

**本节来源**  
- [models.py](file://env_mgt/models.py#L115-L141)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)

## 环境拓扑结构ER图

```mermaid
erDiagram
env_mgt_region {
BIGINT id PK
VARCHAR addr_name
VARCHAR addr_short_name
VARCHAR type_name
VARCHAR type_short_name
VARCHAR region_name UK
VARCHAR region_desc
TINYINT region_is_active
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
}
env_mgt_suite {
BIGINT id PK
BIGINT region_id FK
VARCHAR suite_code UK
VARCHAR suite_name
VARCHAR suite_desc
TINYINT suite_is_active
TINYINT support_docker
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
INTEGER node_bind_level
VARCHAR suite_group
}
env_mgt_node {
BIGINT id PK
VARCHAR node_name
VARCHAR node_ip
VARCHAR minion_id
BIGINT region_id FK
VARCHAR node_os
INTEGER node_status
DATETIME node_recyled
VARCHAR node_desc
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
VARCHAR tomcat_password
VARCHAR apply_order_code
}
env_mgt_node_bind {
BIGINT id PK
VARCHAR module_name
BIGINT suite_id FK
BIGINT node_id FK
BIGINT node_port
VARCHAR node_docker
VARCHAR node_bind_desc
BIGINT deploy_group FK
INTEGER deploy_type
VARCHAR deploy_path
VARCHAR health_check_url
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
BIGINT lib_repo_info_id
DATETIME node_lib_repo_update_time
BIGINT config_repo_info_id
DATETIME node_config_repo_update_time
}
env_mgt_deploy_group {
BIGINT id PK
VARCHAR module_name
VARCHAR module_code
VARCHAR deploy_group_name
VARCHAR deploy_group_code
VARCHAR deploy_group_desc
VARCHAR create_user
DATETIME create_time
VARCHAR update_user
DATETIME update_time
BIGINT stamp
}
env_mgt_region ||--o{ env_mgt_suite : "包含"
env_mgt_region ||--o{ env_mgt_node : "包含"
env_mgt_suite ||--o{ env_mgt_node_bind : "包含"
env_mgt_node ||--o{ env_mgt_node_bind : "被绑定"
env_mgt_deploy_group ||--o{ env_mgt_node_bind : "关联"
```

**图示来源**  
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [models.py](file://env_mgt/models.py)

## 环境初始化与节点分配流程

环境初始化和节点分配的数据流程主要通过一系列服务方法实现，核心流程如下：

1. **环境套创建**：首先创建可用域（Region），然后在该可用域下创建环境套（Suite）
2. **节点导入**：从CMDB系统同步节点信息到 `env_mgt_node` 表
3. **部署分组创建**：创建应用部署分组，定义模块的部署策略
4. **节点绑定**：通过 `env_mgt_node_bind` 表建立模块、环境套和节点之间的绑定关系

关键服务方法包括：
- `get_region_info`：获取可用域信息
- `get_suite_info`：获取环境套信息
- `get_node_list`：获取可用节点列表
- `get_node_info`：获取节点详细信息，包括绑定关系

这些方法在 `env_mgt_ser.py` 文件中实现，通过原生SQL查询获取数据，支持分页和条件过滤。

**本节来源**  
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
- [models.py](file://env_mgt/models.py)

## 环境变更审计日志机制

环境变更的审计日志机制通过独立的日志系统实现。虽然环境管理模块本身不直接处理审计日志，但系统整体架构中包含了完善的审计功能。

审计日志的主要特点：
1. **独立日志文件**：审计日志写入专门的 `audit_info.log` 文件，与其他应用日志分离
2. **详细记录**：记录请求用户、远程地址、请求方法、请求路径、响应状态码、请求参数、响应内容、处理耗时等信息
3. **时间戳**：每条日志记录都包含精确的时间戳
4. **JSON格式**：日志以JSON格式存储，便于后续分析和处理

审计日志的配置在 `spider/settings.py` 中定义，使用 `TimedRotatingFileHandler` 实现按时间轮转的日志管理，保留最近100个日志文件。

**本节来源**  
- [spider/settings.py](file://spider/settings.py#L45-L83)
- [common_middle/OpLogs.py](file://common_middle/OpLogs.py#L44-L71)

## 节点绑定关系查询性能优化

针对节点绑定关系的快速检索，系统采用了多种性能优化策略：

### 查询优化

1. **复合查询SQL**：在 `get_node_info` 方法中，使用LEFT JOIN一次性关联多个表，减少数据库往返次数
```sql
SELECT m.node_name, m.node_ip, m.minion_id, m.node_status, 
       b.module_name, b.node_port, b.node_docker, b.deploy_type, 
       b.id AS bind_id, g.deploy_group_code, g.deploy_group_name,
       s.suite_code, s.suite_name, r.region_name, c.container_is_active,
       IFNULL(m.node_ip,b.node_docker) AS node_ip, m.id
FROM env_mgt_node_bind b
LEFT JOIN env_mgt_node m ON m.id = b.node_id
LEFT JOIN env_mgt_container c ON c.container_code = b.node_docker
LEFT JOIN env_mgt_deploy_group g ON b.deploy_group = g.id
LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
LEFT JOIN env_mgt_region r ON s.region_id = r.id
WHERE 1=1
```

2. **条件索引优化**：虽然未在DDL中明确指定，但根据查询模式，建议在以下字段上创建索引：
   - `env_mgt_node_bind.module_name`
   - `env_mgt_node_bind.suite_id`
   - `env_mgt_node_bind.node_id`
   - `env_mgt_node.node_ip`
   - `env_mgt_node.node_status`

3. **分页查询**：支持分页查询，避免一次性返回大量数据，提高响应速度

### 服务层优化

1. **缓存机制**：虽然代码中未直接体现，但可以通过Django的缓存框架对频繁查询的结果进行缓存
2. **连接复用**：使用Django的数据库连接池，避免频繁创建和销毁数据库连接
3. **批量操作**：在节点状态更新等场景中，使用批量更新语句提高效率

**本节来源**  
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L143-L243)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)

## 环境数据迁移与清理脚本

环境数据迁移和清理主要通过SQL脚本实现，存放在 `db` 目录下的各个迭代子目录中。

### 数据迁移脚本

1. **可用域和环境套初始化**：在 `01-env_mgt_schema.sql` 中定义了所有核心表的创建语句
2. **节点数据同步**：通过定期从CMDB同步节点信息，更新 `env_mgt_node` 表
3. **绑定关系初始化**：在系统上线或环境变更时，执行相应的SQL脚本初始化绑定关系

### 数据清理脚本

1. **节点回收**：通过更新节点状态实现数据清理
```sql
UPDATE spider.env_mgt_node 
SET node_status = 1 
WHERE node_ip = '**************';
```

2. **回收订单管理**：使用 `env_mgt_recycle_order` 和 `env_mgt_recycle_node` 表跟踪回收过程
3. **批量操作**：支持批量启用/禁用节点，提高运维效率
```sql
UPDATE env_mgt_node 
SET node_status = 0, 
    update_user = 'huaitian.zhang', 
    update_time = '2023-12-26 10:11:12', 
    node_desc = '(启用）「wgq-zb」多个节点_for_韩春生（zt@2023-12-26）' 
WHERE id = 5090 AND node_ip = '***********' AND node_status = -1;
```

这些脚本按照迭代版本组织，确保了数据变更的可追溯性和可重复性。

**本节来源**  
- [db/分支2_x/2.2.0/05-回收单相关表设计.sql](file://db/分支2_x/2.2.0/05-回收单相关表设计.sql#L44-L56)
- [db/迭代3.0.2/04-基础信息-外高桥灾备-启用记录-20231215.sql](file://db/迭代3.0.2/04-基础信息-外高桥灾备-启用记录-20231215.sql#L30-L35)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)