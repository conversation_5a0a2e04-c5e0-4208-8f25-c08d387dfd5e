# 测试策略

<cite>
**本文档引用的文件**
- [test_allure_study.py](file://be-scripts/be_test/test_allure_study.py)
- [test_elasticsearch.py](file://be-scripts/be_test/test_elasticsearch.py)
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py)
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py)
- [test_suite_init.groovy](file://Jenkinsfile/test_suite_init.groovy)
- [ES-2.115.0-3-流水线-DLD.md](file://ES-2.115.0-3-流水线-DLD.md)
- [test_data_init.groovy](file://Jenkinsfile/test_data_init.groovy) - *新增于最近提交*
- [test_data_init.py](file://be-scripts/job/jenkins/test_data_dev/test_data_init.py) - *更新于最近提交*
- [test_info_collect.py](file://be-scripts/test_mgt/test_info_collect.py) - *更新于最近提交*
- [test_suite_init_impl_db.py](file://be-scripts/test_publish_aio/test_suite_init_impl_db.py) - *更新于最近提交*
</cite>

## 更新摘要
**变更内容**
- 更新了单元测试与集成测试覆盖范围部分，新增了测试数据初始化流程相关内容
- 在复杂自动化脚本测试编写部分增加了测试数据初始化流水线的执行流程
- 更新了外部依赖模拟指南，增加了对测试信息收集模块的说明
- 新增了测试数据初始化模块的详细说明
- 更新了开发者测试编写指南，增加了新的测试模块参考

### 目录
1. [引言](#引言)
2. [测试框架与方法](#测试框架与方法)
3. [单元测试与集成测试覆盖范围](#单元测试与集成测试覆盖范围)
4. [复杂自动化脚本的测试编写](#复杂自动化脚本的测试编写)
5. [外部依赖模拟指南](#外部依赖模拟指南)
6. [开发者测试编写指南](#开发者测试编写指南)
7. [测试数据初始化模块](#测试数据初始化模块)
8. [结论](#结论)

## 引言
本项目采用基于Pytest的测试框架，结合Allure报告生成工具，构建了完整的自动化测试体系。测试策略覆盖了从单元测试到集成测试的多个层面，重点针对Jenkins、GitLab API等外部依赖进行模拟和集成验证。测试用例主要集中在`be-scripts/be_test/`目录下，通过模拟真实环境行为确保核心业务逻辑的正确性。近期代码更新增加了测试数据初始化和测试信息收集功能，进一步完善了测试体系。

## 测试框架与方法

项目采用Pytest作为核心测试框架，结合Allure生成可视化测试报告。测试用例设计遵循标准的单元测试模式，包含成功、失败、跳过和异常四种基本场景。

```mermaid
flowchart TD
Start([测试开始]) --> Setup["设置测试环境"]
Setup --> Execute["执行测试逻辑"]
Execute --> Assert["断言结果"]
Assert --> |通过| Pass["标记为成功"]
Assert --> |失败| Fail["标记为失败"]
Fail --> Exception{"是否预期异常?"}
Exception --> |是| Skip["标记为跳过"]
Exception --> |否| Broken["标记为异常中断"]
Pass --> End([测试结束])
Skip --> End
Broken --> End
```

**图示来源**
- [test_allure_study.py](file://be-scripts/be_test/test_allure_study.py#L1-L24)

**本节来源**
- [test_allure_study.py](file://be-scripts/be_test/test_allure_study.py#L1-L24)

## 单元测试与集成测试覆盖范围

### 单元测试覆盖
单元测试主要验证独立函数和类的行为，如`test_allure_study.py`中定义的四种基本测试类型：
- `test_success`: 验证正常通过的测试用例
- `test_failure`: 验证预期失败的测试用例
- `test_skip`: 验证条件性跳过的测试用例
- `test_broken`: 验证异常中断的测试用例

### 集成测试覆盖
集成测试重点验证系统组件间的交互，特别是与外部系统的集成：
- Elasticsearch操作集成测试
- Jenkins作业管理集成
- GitLab仓库操作集成
- SaltStack配置管理集成
- 测试数据初始化流程集成
- 测试信息收集模块集成

集成测试通过调用实际API或模拟API响应来验证端到端流程的正确性。

```mermaid
graph TB
subgraph "测试类型"
UT[单元测试]
IT[集成测试]
end
subgraph "验证目标"
UT --> Function["独立函数逻辑"]
UT --> Class["类方法行为"]
IT --> API["API接口调用"]
IT --> External["外部系统集成"]
end
External --> Jenkins["Jenkins集成"]
External --> GitLab["GitLab集成"]
External --> ES["Elasticsearch集成"]
External --> SaltStack["SaltStack集成"]
External --> TestDataInit["测试数据初始化"]
External --> TestInfoCollect["测试信息收集"]
```

**图示来源**
- [test_elasticsearch.py](file://be-scripts/be_test/test_elasticsearch.py#L0-L221)
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py#L0-L19)
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py#L0-L45)
- [test_data_init.py](file://be-scripts/job/jenkins/test_data_dev/test_data_init.py#L0-L947)
- [test_info_collect.py](file://be-scripts/test_mgt/test_info_collect.py#L0-L183)

**本节来源**
- [test_allure_study.py](file://be-scripts/be_test/test_allure_study.py#L1-L24)
- [test_elasticsearch.py](file://be-scripts/be_test/test_elasticsearch.py#L0-L221)
- [test_data_init.py](file://be-scripts/job/jenkins/test_data_dev/test_data_init.py#L0-L947)
- [test_info_collect.py](file://be-scripts/test_mgt/test_info_collect.py#L0-L183)

## 复杂自动化脚本的测试编写

复杂自动化脚本的测试需要考虑多阶段执行、条件分支和错误处理。以Elasticsearch脚本执行为例，测试需要覆盖以下关键路径：

```mermaid
flowchart TD
Start([脚本执行开始]) --> Clone["克隆脚本仓库"]
Clone --> |成功| Scan["扫描脚本文件"]
Clone --> |失败| Error1["记录错误并抛出"]
Scan --> |发现文件| Execute["执行脚本文件"]
Scan --> |无文件| Skip["跳过执行"]
Execute --> Parse["解析脚本内容"]
Parse --> Run["执行ES命令"]
Run --> |成功| Next["处理下一个文件"]
Run --> |失败| Error2["记录错误并抛出"]
Next --> More{"还有更多文件?"}
More --> |是| Execute
More --> |否| Complete["完成所有执行"]
Complete --> End([执行结束])
Error1 --> End
Error2 --> End
```

测试用例应覆盖：
1. 仓库克隆成功与失败场景
2. 脚本文件存在与不存在场景
3. 脚本解析成功与失败场景
4. ES命令执行成功与失败场景
5. 多文件顺序执行场景

**图示来源**
- [ES-2.115.0-3-流水线-DLD.md](file://ES-2.115.0-3-流水线-DLD.md#L705-L922)
- [test_suite_init.groovy](file://Jenkinsfile/test_suite_init.groovy#L348-L379)

**本节来源**
- [ES-2.115.0-3-流水线-DLD.md](file://ES-2.115.0-3-流水线-DLD.md#L705-L922)
- [test_elasticsearch.py](file://be-scripts/be_test/test_elasticsearch.py#L0-L221)

## 外部依赖模拟指南

### Jenkins API 模拟
Jenkins API通过`jenkins` Python包实现，采用单例模式管理连接。测试时应模拟以下行为：

```mermaid
sequenceDiagram
participant Test as "测试用例"
participant Client as "JenkinsApi客户端"
participant Mock as "Mock服务器"
Test->>Client : 调用delete_job(job_name)
Client->>Mock : 发送DELETE请求到Jenkins API
Mock-->>Client : 返回200成功响应
Client-->>Test : 返回成功
```

模拟要点：
- 使用`unittest.mock`或`pytest-mock`替换`jenkins.Jenkins`实例
- 预定义API响应状态码和返回数据
- 验证API调用参数的正确性

### GitLab API 模拟
GitLab API使用`python-gitlab`库，测试时需要模拟：
- 仓库获取操作
- 分支信息查询
- 提交信息获取
- 合并请求操作

```mermaid
sequenceDiagram
participant Test as "测试用例"
participant Client as "GitLabApi客户端"
participant Mock as "Mock服务器"
Test->>Client : 调用get_one_git_repos_last_version(br_name, git_repos)
Client->>Mock : 发送GET请求查询分支信息
Mock-->>Client : 返回分支提交ID
Client-->>Test : 返回提交信息
```

模拟要点：
- 模拟404（仓库不存在）、403（权限不足）、401（认证失败）等错误场景
- 验证URL路径和查询参数的正确性
- 模拟分页数据获取

### 测试信息收集模块模拟
测试信息收集模块通过`test_info_collect.py`实现，主要功能包括：
- 服务IP和Dubbo端口收集
- 主机信息收集
- 环境节点绑定信息更新

```mermaid
sequenceDiagram
participant Test as "测试用例"
participant Collector as "TestInfoCollectForm"
participant K8s as "K8sOperation"
Test->>Collector : 调用test_info_collect_from(request)
Collector->>K8s : 获取服务IP和端口
K8s-->>Collector : 返回节点服务信息
Collector->>Collector : 执行telnet检测Dubbo端口
Collector->>Collector : 更新环境节点绑定动态表
Collector-->>Test : 返回采集成功
```

**图示来源**
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py#L0-L19)
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py#L34-L45)
- [test_info_collect.py](file://be-scripts/test_mgt/test_info_collect.py#L0-L183)

**本节来源**
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py#L0-L19)
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py#L0-L45)
- [test_info_collect.py](file://be-scripts/test_mgt/test_info_collect.py#L0-L183)

## 开发者测试编写指南

### 测试用例命名规范
- 使用`test_`前缀
- 描述性名称，如`test_success`、`test_failure`
- 包含测试场景的关键信息

### 测试结构
```python
def test_example():
    """测试描述"""
    # Arrange: 设置测试数据
    # Act: 执行测试操作
    # Assert: 验证预期结果
```

### 外部依赖处理
1. **使用依赖注入**：将外部服务作为参数传入
2. **使用mock.patch**：临时替换模块或方法
3. **创建测试专用客户端**：隔离测试环境

### Allure报告集成
- 使用`@allure.step`装饰器标记关键步骤
- 使用`@allure.severity`标记严重级别
- 添加截图和附件增强报告可读性

### 测试执行
```bash
# 运行所有测试
pytest

# 生成Allure报告
pytest --alluredir=./allure-results

# 查看报告
allure serve ./allure-results
```

**本节来源**
- [test_allure_study.py](file://be-scripts/be_test/test_allure_study.py#L1-L24)
- [test_elasticsearch.py](file://be-scripts/be_test/test_elasticsearch.py#L0-L221)
- [test_info_collect.py](file://be-scripts/test_mgt/test_info_collect.py#L0-L183)

## 测试数据初始化模块

### 模块概述
测试数据初始化模块通过`test_data_init.groovy`流水线和`test_data_init.py`脚本实现，为数据开发环境提供完整的数据初始化解决方案。

### 核心功能
- SQL制品准备
- SQL分拣
- 数据库恢复
- SQL执行
- 数据导出与导入
- 数据库清理

### 执行流程
```mermaid
flowchart TD
Start([数据记录]) --> PrepareSQL["准备sql制品"]
PrepareSQL --> SortSQL["分拣sql"]
SortSQL --> Restore1["dev-dump恢复①"]
Restore1 --> ExecuteSQL["执行sql"]
ExecuteSQL --> |成功| Success["标记为成功"]
ExecuteSQL --> |失败| Export["dev数据导出"]
Export --> Clean["数据库清空"]
Clean --> PullSQL["SQL制品准备"]
PullSQL --> ResortSQL["SQL分拣"]
ResortSQL --> ReExecute["SQL执行"]
ReExecute --> |成功| Import["dev数据灌入"]
Import --> |成功| Success
Import --> |失败| Restore3["dev-dump恢复③"]
Restore3 --> Notify["执行通知"]
Notify --> Failure["标记为失败"]
```

### 关键方法说明
- `pull_sql_repo`: 准备SQL制品，从指定分支拉取SQL仓库
- `sort_sql`: 分拣SQL，根据可用的开发分支进行SQL分拣
- `restore_dump`: 数据库恢复，从dump文件恢复Oracle和MySQL数据库
- `execute_sql`: 执行SQL，使用Flyway执行DDL和DML脚本
- `test_data_export`: 数据导出，导出Oracle和MySQL数据库数据
- `test_data_import`: 数据导入，将导出的数据重新导入数据库
- `db_clean`: 数据库清理，使用Flyway clean或空dump方式清理数据库

**本节来源**
- [test_data_init.groovy](file://Jenkinsfile/test_data_init.groovy#L0-L188)
- [test_data_init.py](file://be-scripts/job/jenkins/test_data_dev/test_data_init.py#L0-L947)
- [test_suite_init_impl_db.py](file://be-scripts/test_publish_aio/test_suite_init_impl_db.py#L0-L1433)

## 结论
本项目的测试策略建立了从单元测试到集成测试的完整体系，特别强调对外部依赖的模拟和验证。通过Pytest框架和Allure报告工具，实现了测试的自动化和可视化。近期更新增加了测试数据初始化和测试信息收集功能，进一步完善了测试体系。建议开发者在编写新测试时遵循既定的模式，重点关注外部API的模拟，确保测试的可靠性和可维护性。