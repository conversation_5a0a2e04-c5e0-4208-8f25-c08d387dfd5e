# 技术栈与依赖

<cite>
**本文档中引用的文件**  
- [pyproject.toml](file://pyproject.toml) - *引入uv作为包管理器，替代requirements.txt*
- [uv.lock](file://uv.lock) - *uv生成的依赖锁定文件*
- [.python-version](file://.python-version) - *指定Python版本为3.9.13*
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py)
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py)
- [saltapi.py](file://be-scripts/common/call_api/saltstack/saltapi.py)
- [base_model.py](file://be-scripts/dao/base_model.py)
- [nacos_file_rules.py](file://be-scripts/utils/config/nacos_file_rules.py)
- [Hello_parameters.groovy](file://Jenkinsfile/Hello_parameters.groovy)
- [jenkins_composition_mgt.groovy](file://Jenkinsfile/jenkins_composition_mgt.groovy)
- [settings.py](file://be-scripts/settings.py)
- [py_image_template.py](file://be-scripts/job/jenkins/py_image_template.py) - *使用uv构建Docker镜像的模板*
- [requirements.txt](file://be-scripts/requirements.txt) - *旧版依赖管理文件，已更新*
</cite>

## 更新摘要
**变更内容**   
- 将依赖管理从`requirements.txt`迁移至`pyproject.toml`和`uv.lock`，采用uv作为新的Python包管理工具
- 更新Python版本要求为3.9.13，并在`.python-version`文件中明确指定
- 在Docker镜像构建模板中引入uv进行依赖安装，替代原有的pip方式
- 同步更新依赖库版本信息，确保与`uv.lock`文件一致
- 移除对已废弃的`requirements.txt`文件的引用和说明，并确认其内容已同步至`pyproject.toml`

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心技术栈](#核心技术栈)
4. [依赖库详解](#依赖库详解)
5. [外部服务集成](#外部服务集成)
6. [数据库支持](#数据库支持)
7. [技术协同工作机制](#技术协同工作机制)
8. [开发与运行环境配置](#开发与运行环境配置)
9. [结论](#结论)

## 引言
本项目是一个面向DevOps自动化的综合脚本系统，旨在通过集成多种技术栈和外部服务，实现从代码构建、测试到部署的全流程自动化。系统主要采用Python和Groovy作为开发语言，结合Jenkins、GitLab、SaltStack等关键工具，构建了一个高效、可靠的自动化流水线。本文档将全面介绍项目的技术栈和依赖关系，为开发者提供清晰的技术全景图，帮助理解系统的架构设计和运行机制。近期，项目已将依赖管理工具从传统的`requirements.txt`升级为现代化的`uv`工具链，显著提升了依赖解析和安装效率。

## 项目结构
项目采用模块化设计，主要分为Jenkins流水线脚本、Python自动化脚本和配置文件三大类。Jenkinsfile目录包含各类Groovy编写的流水线模板，覆盖从环境初始化到应用发布的完整流程。be-scripts目录是Python脚本的核心，包含应用管理、持续集成、数据库管理、外部API调用等多个功能模块。整个项目结构清晰，职责分明，便于维护和扩展。

```mermaid
graph TD
A[项目根目录] --> B[Jenkinsfile]
A --> C[be-scripts]
A --> D[git-hooks]
A --> E[文档]
B --> B1[PA工具]
B --> B2[devops_publish]
B --> B3[通用流水线]
C --> C1[app_mgt]
C --> C2[ci_pipeline]
C --> C3[common]
C --> C4[dao]
C --> C5[jenkins_mgt]
C --> C6[publish_tool]
C --> C7[test_publish_aio]
C1 --> C11[API文档管理]
C1 --> C12[分库分表]
C3 --> C31[调用API]
C3 --> C32[扩展命令]
C3 --> C33[日志管理]
C7 --> C71[测试发布]
C7 --> C72[测试套件初始化]
```

**Diagram sources**
- [Jenkinsfile](file://Jenkinsfile)
- [be-scripts](file://be-scripts)

## 核心技术栈
项目采用Python 3作为主要编程语言，配合Groovy脚本在Jenkins环境中执行流水线任务。Python负责复杂的业务逻辑处理、数据持久化和外部服务调用，而Groovy则专注于定义CI/CD流程的执行步骤和条件判断。这种语言分工充分发挥了各自的优势：Python在数据处理和系统集成方面的强大能力，以及Groovy在Jenkins生态中的原生支持和流畅语法。项目已将Python版本锁定为3.9.13，并通过`.python-version`文件进行版本管理。

**Section sources**
- [pyproject.toml](file://pyproject.toml)
- [Hello_parameters.groovy](file://Jenkinsfile/Hello_parameters.groovy)
- [.python-version](file://.python-version)

## 依赖库详解
项目依赖库在`pyproject.toml`文件中明确定义，并通过`uv.lock`文件锁定具体版本，确保了依赖的可重现性。核心依赖包括：
- **peewee==3.14.9**: 作为轻量级的ORM框架，用于操作MySQL和Oracle数据库，简化了数据模型的定义和CRUD操作。
- **paramiko==2.7.2**: 提供SSHv2协议的实现，用于安全地连接和管理远程服务器，执行命令和文件传输。
- **requests==2.28.1**: 用于发起HTTP/HTTPS请求，与Jenkins、GitLab等RESTful API进行交互。
- **python-jenkins==1.8.0**: Jenkins官方Python客户端，封装了Jenkins API，便于在Python脚本中创建、触发和监控Jenkins任务。
- **python-gitlab==2.5.0**: GitLab API的Python封装，用于管理项目、分支、合并请求等。
- **nacos-sdk-python==0.1.6**: 用于与Nacos配置中心交互，实现配置的动态获取和管理。
- **cx-Oracle==8.3.0** 和 **oracledb==1.3.1**: 提供Python与Oracle数据库的连接能力。
- **mysqlclient==2.1.0** 和 **mysql-connector-python==8.0.33**: 提供Python与MySQL数据库的连接能力。
- **uv**: 作为新的Python包管理器，替代了原有的pip和requirements.txt方式，提供了更快的依赖解析和安装速度。

```mermaid
graph LR
A[Python脚本] --> B[peewee ORM]
A --> C[paramiko]
A --> D[requests]
A --> E[python-jenkins]
A --> F[python-gitlab]
A --> G[nacos-sdk-python]
A --> H[uv]
B --> I[MySQL]
B --> J[Oracle]
C --> K[远程服务器]
D --> L[Jenkins API]
D --> M[GitLab API]
D --> N[SaltStack API]
E --> L
F --> M
G --> O[Nacos]
H --> P[依赖管理]
```

**Diagram sources**
- [pyproject.toml](file://pyproject.toml)
- [uv.lock](file://uv.lock)
- [base_model.py](file://be-scripts/dao/base_model.py)
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py)
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py)
- [saltapi.py](file://be-scripts/common/call_api/saltstack/saltapi.py)

## 外部服务集成
项目深度集成了多个外部服务，构建了一个完整的DevOps工具链。

### Jenkins API集成
通过`python-jenkins`库和`requests`库，项目实现了对Jenkins的全面控制。`common/call_api/be_jenkins/jenkins_api.py`模块封装了Jenkins API的调用，采用单例模式管理连接，确保了连接的复用和效率。Python脚本可以创建、删除、触发Jenkins任务，并获取构建状态，实现了与Jenkins流水线的双向通信。

**Section sources**
- [jenkins_api.py](file://be-scripts/common/call_api/be_jenkins/jenkins_api.py)
- [CLAUDE.md](file://CLAUDE.md)

### GitLab API集成
`common/call_api/gitlab/`目录下的`gitlab_api.py`和`merge.py`模块负责与GitLab交互。系统可以查询仓库的最新提交、获取分支信息、创建合并请求等。`gitlab_code`目录下的脚本则用于从GitLab拉取代码、分析文件内容，为自动化流程提供数据支持。

**Section sources**
- [gitlab_api.py](file://be-scripts/common/call_api/gitlab/gitlab_api.py)
- [job/jenkins/gitlab_code](file://be-scripts/job/jenkins/gitlab_code)

### SaltStack集成
SaltStack作为配置管理工具，被用于执行远程服务器上的命令。`common/call_api/saltstack/saltapi.py`模块封装了Salt API的调用，处理认证、Token管理和异步任务执行。在Jenkins流水线中（如`docker_publish-dsm.groovy`），也直接通过curl调用Salt API，实现了从流水线到服务器操作的无缝衔接。

**Section sources**
- [saltapi.py](file://be-scripts/common/call_api/saltstack/saltapi.py)
- [docker_publish-dsm.groovy](file://Jenkinsfile/devops_publish/docker_publish-dsm.groovy)
- [call_salt.py](file://be-scripts/publish_tool/utils/call_salt.py)

### Nacos与Zeus集成
Nacos作为配置中心，用于管理应用的动态配置。`utils/config/nacos_file_rules.py`等模块负责与Nacos交互，获取和更新配置。Zeus则作为权限和环境管理平台，`job/zeus/`目录下的脚本用于同步环境配置和ACL规则，确保发布过程符合安全策略。

**Section sources**
- [utils/config/nacos_file_rules.py](file://be-scripts/utils/config/nacos_file_rules.py)
- [job/zeus](file://be-scripts/job/zeus)

## 数据库支持
项目支持MySQL和Oracle两种数据库。通过peewee ORM，定义了统一的数据模型（如`dao/base_model.py`中的`BaseModel`），并针对不同数据库创建了相应的连接模块（`dao/connect/mysql.py`和`dao/connect/oracle.py`）。这种设计使得业务逻辑可以独立于具体的数据库实现，提高了代码的可移植性。项目中的DAO层负责所有数据库操作，包括数据的增删改查和复杂查询。

**Section sources**
- [base_model.py](file://be-scripts/dao/base_model.py)
- [connect](file://be-scripts/dao/connect)

## 技术协同工作机制
各项技术通过清晰的职责划分和接口定义协同工作。Python脚本作为“大脑”，协调整个自动化流程。它首先通过`requests`或专用SDK调用Jenkins API触发流水线，然后在流水线的Groovy脚本中，根据需要调用Python脚本进行复杂的数据处理或决策。Python脚本通过`paramiko`连接目标服务器执行命令，或通过`requests`调用SaltStack API进行批量操作。数据持久化则通过peewee ORM完成，确保了操作记录和状态的可靠存储。整个流程形成了一个闭环，实现了从触发、执行到记录的全自动化。

```mermaid
sequenceDiagram
participant User as 用户
participant Jenkins as Jenkins
participant Python as Python脚本
participant DB as 数据库
participant SaltStack as SaltStack
participant Remote as 远程服务器
User->>Jenkins : 触发流水线
Jenkins->>Python : 调用Python脚本
Python->>DB : 读取配置和状态
Python->>Python : 处理业务逻辑
Python->>SaltStack : 调用API执行命令
SaltStack->>Remote : 在目标服务器执行
Remote-->>SaltStack : 返回结果
SaltStack-->>Python : 返回执行状态
Python->>DB : 记录操作日志
Python-->>Jenkins : 返回结果
Jenkins-->>User : 完成流水线
```

**Diagram sources**
- [jenkins_composition_mgt.groovy](file://Jenkinsfile/jenkins_composition_mgt.groovy)
- [test_suite_init_main.py](file://be-scripts/test_publish_aio/test_suite_init_main.py)
- [call_salt.py](file://be-scripts/publish_tool/utils/call_salt.py)
- [base_model.py](file://be-scripts/dao/base_model.py)

## 开发与运行环境配置
开发者需要配置Python 3.9.13环境，并通过`uv sync`命令安装所有依赖。关键的配置信息（如数据库连接、Jenkins URL、GitLab Token等）存储在`settings.py`文件中。运行时，Python脚本会读取这些配置来连接相应的服务。对于Jenkins流水线，需要在Jenkins服务器上配置好Python环境和必要的库，并确保网络可以访问GitLab、SaltStack等外部服务。Docker镜像构建已采用`uv`作为依赖安装工具，通过`py_image_template.py`中的模板实现高效构建。

**Section sources**
- [pyproject.toml](file://pyproject.toml)
- [settings.py](file://be-scripts/settings.py)
- [py_image_template.py](file://be-scripts/job/jenkins/py_image_template.py)

## 结论
本项目构建了一个以Python为核心，Groovy为辅助，深度集成Jenkins、GitLab、SaltStack、Nacos等多种工具的DevOps自动化平台。通过合理的技术选型和模块化设计，实现了高效、可靠的自动化流程。近期，项目已将依赖管理工具从`requirements.txt`升级为`uv`，显著提升了依赖解析和安装效率。理解这些技术栈和依赖关系，是进行二次开发和问题排查的基础。未来可以考虑进一步优化容器化部署流程，提升系统的可维护性和可移植性。