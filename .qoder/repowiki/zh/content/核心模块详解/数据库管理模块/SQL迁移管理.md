# SQL迁移管理

<cite>
**本文档引用的文件**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py) - *在最近的提交中进行了更新*
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py) - *在最近的提交中进行了更新*
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py) - *在最近的提交中进行了更新*
- [settings.py](file://be-scripts/settings.py) - *在最近的提交中进行了更新*
</cite>

## 更新摘要
**变更内容**   
- 更新了拉取SQL、版本化SQL、检查SQL和推送SQL流程的实现逻辑
- 优化了基于MD5哈希值的SQL文件对比机制
- 增强了归档时间检查和SQL重建逻辑
- 更新了业务对象构建器的验证逻辑

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档旨在深入解析SQL迁移管理系统的实现逻辑，重点介绍从版本控制系统自动创建和管理SQL迁移脚本的机制。系统通过`create_sql_from_vcs.py`脚本实现自动化流程，结合`sql_migrate_bo.py`业务对象进行数据处理，确保SQL脚本的安全、高效迁移。文档将详细阐述系统的架构设计、数据处理流程、版本控制策略及实际使用案例。

## 项目结构
SQL迁移管理功能主要位于`be-scripts/db_mgt/creat_sql_migrate/`目录下，核心文件包括`create_sql_from_vcs.py`和`zt_create_sql_from_vcs.py`。业务对象定义在`be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py`中，相关数据库模型定义在`models.py`文件中。系统通过调用`dao/get/mysql/db_mgt_info.py`中的数据访问方法与数据库交互。

```mermaid
graph TD
A[create_sql_from_vcs.py] --> B[sql_migrate_bo.py]
A --> C[db_mgt_info.py]
A --> D[models.py]
B --> E[Builder模式]
C --> F[数据库查询]
D --> G[数据模型]
```

**图示来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py)
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py)
- [models.py](file://be-scripts/db_mgt/creat_sql_migrate/models.py)

**章节来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py)

## 核心组件
系统的核心组件包括`SqlMigrateBo`业务对象和`SqlMigrateBusiness`类。`SqlMigrateBo`采用Builder模式构建迁移任务的上下文信息，确保对象创建的灵活性和安全性。`SqlMigrateBusiness`类封装了SQL迁移的完整业务流程，包括拉取、版本化、检查和推送等关键步骤。

**章节来源**  
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py)
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)

## 架构概述
系统采用分层架构设计，上层为业务逻辑层，中层为服务协调层，底层为数据访问层。业务逻辑层由`SqlMigrateBusiness`类实现，负责协调整个迁移流程。服务协调层通过`SqlMigrateBo`对象传递上下文信息。数据访问层通过`db_mgt_info.py`中的函数与数据库交互，实现数据的持久化操作。

```mermaid
graph TD
A[业务逻辑层] --> B[服务协调层]
B --> C[数据访问层]
C --> D[数据库]
A --> |调用| B
B --> |传递| C
C --> |查询/更新| D
```

**图示来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py)

## 详细组件分析

### SqlMigrateBo 分析
`SqlMigrateBo`类是SQL迁移任务的核心数据载体，采用Builder模式构建。该模式允许逐步设置对象属性，并在最后一步进行验证和创建，确保了对象的完整性和一致性。

```mermaid
classDiagram
class SqlMigrateBo {
+__flag_file_dir : str
+__iteration_id : str
+__app_name : str
+__workspace : str
+__sid : int
+__sql_update : bool
+__git_group : str
+__br_name : str
+__vcs_iter_url : str
+__db_vcs_type : str
+__check_suite_code : str
+__biz_base_db : str
+set_app_name(app_name : str) : SqlMigrateBo
+flag_file_dir : str
+iteration_id : str
+module_name : str
+check_suite_code : str
+app_name : str
+workspace : str
+sid : int
+sql_update : bool
+git_group : str
+br_name : str
+biz_base_db : str
+vcs_iter_url : str
+set_vcs_iter_url(vcs_iter_url : str) : SqlMigrateBo
+db_vcs_type : str
+set_db_vcs_type(db_vcs_type : str) : SqlMigrateBo
}
class Builder {
+flag_file_dir : str
+iteration_id : str
+app_name : str
+workspace : str
+sid : int
+sql_update : bool
+git_group : str
+br_name : str
+vcs_iter_url : str
+db_vcs_type : str
+check_suite_code : str
+biz_base_db : str
+set_flag_file_dir(flag_file_dir : str) : Builder
+set_iteration_id(iteration_id : str) : Builder
+set_app_name(app_name : str) : Builder
+set_workspace(workspace : str) : Builder
+set_sid(sid : int) : Builder
+set_git_group(git_group : int) : Builder
+set_br_name(br_name : int) : Builder
+set_biz_base_db(biz_base_db : str) : Builder
+set_check_suite_code(check_suite_code : str) : Builder
+verify_basic_property() : void
+build_bo() : SqlMigrateBo
}
Builder --> SqlMigrateBo : "构建"
```

**图示来源**  
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py)

**章节来源**  
- [sql_migrate_bo.py](file://be-scripts/db_mgt/sql_create_bo/sql_migrate_bo.py)

### 迁移流程分析
SQL迁移流程分为四个主要步骤：拉取SQL、版本化SQL、检查SQL和推送SQL。每个步骤都封装在`SqlMigrateBusiness`类的静态方法中，通过装饰器`recode_sql_check_detail`实现检查结果的记录。

#### 拉取SQL流程
```mermaid
sequenceDiagram
participant Client as "客户端"
participant Business as "SqlMigrateBusiness"
participant DAO as "db_mgt_info"
participant VCS as "版本控制系统"
Client->>Business : __pull_sql(sql_migrate_bo)
Business->>DAO : get_vcs_info_by_module_name()
DAO-->>Business : vcs_info_list
alt 存在VCS信息
Business->>Business : __get_devops_sql()
Business->>VCS : 拉取SQL文件
VCS-->>Business : sql_file_dict
Business->>Business : __get_db_name_list()
Business->>Business : __ins_or_upd_sql_file_by_md5()
Business-->>Client : 拉取成功
else 无VCS信息
Business-->>Client : 警告：未配置SQL制品信息
end
```

**图示来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py)

#### 版本化SQL流程
```mermaid
flowchart TD
Start([开始]) --> GetSQL["获取数据库中的SQL文件"]
GetSQL --> CheckExistence{"SQL文件存在?"}
CheckExistence --> |是| SortSQL["按文件路径排序"]
SortSQL --> ParseDB["解析库和分组名"]
ParseDB --> Versioning["生成版本化文件名"]
Versioning --> UpdateDB["更新数据库记录"]
UpdateDB --> End([结束])
CheckExistence --> |否| LogInfo["记录无新增SQL"]
LogInfo --> End
```

**图示来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)

#### SQL检查流程
```mermaid
sequenceDiagram
participant Business as "SqlMigrateBusiness"
participant Archery as "Archery平台"
participant DB as "数据库"
Business->>Business : __start_check_sql()
Business->>DB : get_db_type_by_module_name()
DB-->>Business : 数据库类型
alt 非MySQL数据库
Business-->>Business : 跳过检测
else MySQL数据库
Business->>Business : __get_archery_info()
Business->>Archery : 发送SQL检查请求
Archery-->>Business : 检查结果
alt 检查通过
Business-->>Business : 记录成功
else 检查失败
Business->>Business : 记录错误信息
alt 强制检测开启
Business-->>Business : 抛出异常
else 强制检测关闭
Business-->>Business : 记录警告
end
end
end
```

**图示来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py)

**章节来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)

## 依赖分析
系统依赖于多个外部组件和内部模块。主要依赖包括数据库连接、版本控制系统访问、Archery平台集成以及各种工具类。这些依赖通过清晰的接口定义和分层架构进行管理，确保了系统的可维护性和可扩展性。

```mermaid
graph TD
A[create_sql_from_vcs.py] --> B[sql_migrate_bo.py]
A --> C[db_mgt_info.py]
A --> D[settings.py]
A --> E[common_tool.py]
A --> F[PipelineRecorder]
C --> G[DBConnectionManager]
C --> H[db_mgt_bind_view.py]
A --> I[sqlparse]
A --> J[asyncio]
A --> K[logging]
```

**图示来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py)
- [settings.py](file://be-scripts/settings.py)

**章节来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)
- [db_mgt_info.py](file://be-scripts/dao/get/mysql/db_mgt_info.py)
- [settings.py](file://be-scripts/settings.py)

## 性能考虑
系统在设计时考虑了多项性能优化措施。首先，通过MD5哈希值对比避免重复处理未更改的SQL文件。其次，批量数据库操作减少了数据库交互次数。此外，系统实现了归档时间检查机制，避免不必要的全量重建。对于大规模SQL脚本执行，建议分批处理并监控执行时间。

## 故障排除指南
常见问题包括VCS配置错误、数据库连接失败、Archery检查超时等。系统通过详细的日志记录帮助定位问题。对于SQL检查失败，系统会记录详细的错误信息，包括错误级别和具体错误消息。建议定期检查日志文件`/data/ztst_logs/create_sql_from_vcs/create_sql_from_vcs.log`以监控系统运行状态。

**章节来源**  
- [create_sql_from_vcs.py](file://be-scripts/db_mgt/creat_sql_migrate/create_sql_from_vcs.py)

## 结论
SQL迁移管理系统通过自动化流程显著提高了数据库变更的安全性和效率。系统设计合理，模块职责清晰，具备良好的可维护性和扩展性。通过严格的版本控制和检查机制，有效防止了错误SQL的执行。建议在生产环境中使用前进行充分测试，并根据实际需求调整配置参数。