# 发布计划管理

<cite>
**本文档引用的文件**   
- [publish_plan_service.py](file://be-scripts/iter_mgt/publish_plan/dao/service/publish_plan_service.py)
- [executor_factory.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_factory.py)
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py)
- [publish_apply_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/phase_publish_pre/publish_apply_executor.py)
- [executor_imports.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_imports.py)
</cite>

## 目录
1. [介绍](#介绍)
2. [发布计划架构设计](#发布计划架构设计)
3. [核心执行流程](#核心执行流程)
4. [发布计划服务类分析](#发布计划服务类分析)
5. [执行器工厂机制](#执行器工厂机制)
6. [基础执行器抽象类](#基础执行器抽象类)
7. [预发布阶段执行器](#预发布阶段执行器)
8. [发布流程状态监控](#发布流程状态监控)
9. [异常处理与问题排查](#异常处理与问题排查)
10. [配置策略调整](#配置策略调整)

## 介绍
本文档深入解析发布计划模块的架构设计和工作流程，重点介绍发布计划服务如何协调整个发布生命周期。详细说明发布计划的四个核心阶段：预发布阶段、发布决策阶段、发布执行阶段和归档阶段的工作机制。

## 发布计划架构设计

```mermaid
graph TD
A[发布计划服务] --> B[执行器工厂]
B --> C[基础执行器]
C --> D[预发布执行器]
C --> E[发布决策执行器]
C --> F[发布执行器]
C --> G[归档执行器]
A --> H[数据库持久层]
H --> I[发布计划节点表]
H --> J[发布计划主表]
```

**图示来源**
- [publish_plan_service.py](file://be-scripts/iter_mgt/publish_plan/dao/service/publish_plan_service.py)
- [executor_factory.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_factory.py)
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py)

**本节来源**
- [publish_plan_service.py](file://be-scripts/iter_mgt/publish_plan/dao/service/publish_plan_service.py#L1-L56)
- [executor_factory.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_factory.py#L1-L26)

## 核心执行流程

```mermaid
sequenceDiagram
participant 服务 as 发布计划服务
participant 工厂 as 执行器工厂
participant 执行器 as 具体执行器
participant 数据库 as 数据库
服务->>工厂 : 创建执行器实例
工厂->>工厂 : 扫描所有执行器类
工厂->>服务 : 返回匹配的执行器
服务->>执行器 : 调用execute方法
执行器->>数据库 : 获取锁并更新状态
数据库-->>执行器 : 返回查询结果
执行器->>执行器 : 调用invoke_node
执行器->>执行器 : 调用query_execute_result
执行器->>数据库 : 更新节点结果
数据库-->>服务 : 完成发布流程
```

**图示来源**
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py#L50-L368)
- [executor_factory.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_factory.py#L1-L26)

**本节来源**
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py#L50-L368)

## 发布计划服务类分析
发布计划服务类提供了核心的查询功能，包括查询服务结果、查询任务结果和获取迭代中的应用数量。这些服务方法为发布流程的状态监控提供了基础支持。

**本节来源**
- [publish_plan_service.py](file://be-scripts/iter_mgt/publish_plan/dao/service/publish_plan_service.py#L1-L56)

## 执行器工厂机制
执行器工厂负责根据配置动态选择和创建相应的执行器实例。通过扫描所有对象并检查类继承关系，确保返回正确的执行器实现。

```mermaid
classDiagram
class ExecutorFactory {
+create_executor(exec_node_name, exec_bo)
}
class BaseExecutor {
+execute()
+invoke_node()
+query_execute_result()
}
ExecutorFactory --> BaseExecutor : "创建"
BaseExecutor <|-- PublishApplyExecutor : "实现"
```

**图示来源**
- [executor_factory.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_factory.py#L1-L26)
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py#L1-L368)

**本节来源**
- [executor_factory.py](file://be-scripts/iter_mgt/publish_plan/executor/executor_factory.py#L1-L26)

## 基础执行器抽象类
BaseExecutor定义了统一的执行接口，包括execute、invoke_node和query_execute_result三个核心方法。该抽象类还实现了状态管理、事务控制和并发安全等通用功能。

**本节来源**
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py#L1-L368)

## 预发布阶段执行器
预发布阶段执行器(PublishApplyExecutor)负责处理发布申请和环境准备工作，主要包含两个核心操作：发布申请检查和正式发布申请。

```mermaid
flowchart TD
Start([开始]) --> CheckApply["调用产线申请检查"]
CheckApply --> CheckResult{"检查成功?"}
CheckResult --> |否| ReturnFail1["返回失败结果"]
CheckResult --> |是| QueryResult["查询申请结果"]
QueryResult --> QuerySuccess{"查询成功?"}
QuerySuccess --> |否| ReturnFail2["返回失败结果"]
QuerySuccess --> |是| DoApply["调用产线申请接口"]
DoApply --> ApplySuccess{"申请成功?"}
ApplySuccess --> |否| ReturnFail3["返回失败结果"]
ApplySuccess --> |是| ReturnRunning["返回运行中状态"]
ReturnFail1 --> End([结束])
ReturnFail2 --> End
ReturnFail3 --> End
ReturnRunning --> End
```

**图示来源**
- [publish_apply_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/phase_publish_pre/publish_apply_executor.py#L1-L139)

**本节来源**
- [publish_apply_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/phase_publish_pre/publish_apply_executor.py#L1-L139)

## 发布流程状态监控
发布流程通过定时查询机制监控执行状态，设置了合理的超时时间（5分钟和10分钟）来避免无限等待。状态监控包括服务结果查询和任务结果查询两种方式。

**本节来源**
- [publish_plan_service.py](file://be-scripts/iter_mgt/publish_plan/dao/service/publish_plan_service.py#L1-L30)

## 异常处理与问题排查
系统实现了完善的异常处理机制，包括：
- 发布流程卡顿：检查数据库锁状态和执行器是否正常运行
- 状态不一致：验证数据库事务完整性和状态更新逻辑
- 超时问题：调整sleep_max参数和重试间隔

**本节来源**
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py#L50-L368)
- [publish_plan_service.py](file://be-scripts/iter_mgt/publish_plan/dao/service/publish_plan_service.py#L1-L56)

## 配置策略调整
通过配置文件可以调整发布策略，包括：
- 超时时间设置
- 重试间隔配置
- 并行执行策略
- 状态检查频率

**本节来源**
- [settings.py](file://be-scripts/settings.py)
- [base_executor.py](file://be-scripts/iter_mgt/publish_plan/executor/base_executor.py#L50-L368)