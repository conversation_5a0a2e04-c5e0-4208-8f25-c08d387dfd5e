# 发布检查API

<cite>
**本文档引用的文件**
- [publish_check.py](file://publish/publish_check.py)
- [external_service.py](file://task_mgt/external_service.py)
- [models.py](file://task_mgt/models.py)
- [external_service_config.py](file://task_mgt/config/external_service_config.py)
- [urls.py](file://spider/urls.py)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 简介
本文档详细描述了发布检查API的技术实现，重点说明发布前自动化检查的接口规范和验证逻辑。文档涵盖代码质量门禁、安全扫描、依赖版本合规性、环境健康度等检查项的实现机制，解释检查结果的返回格式、严重级别划分（阻断/警告），以及如何通过API获取详细的检查报告。同时说明检查项的可配置性，包括检查项开关、阈值设置、白名单机制等，并提供检查失败时的错误码定义、修复建议及重新触发检查的流程。

## 项目结构
发布检查功能主要位于`publish`模块中，通过Django REST框架提供API接口。核心检查逻辑由`publish_check.py`实现，通过调用外部服务执行具体的检查任务。系统采用模块化设计，各功能组件职责清晰，通过API网关进行统一调度。

```mermaid
graph TD
subgraph "前端"
UI[用户界面]
end
subgraph "API网关"
API[发布检查API]
end
subgraph "后端服务"
ExternalService[外部服务调度]
ScriptExecutor[脚本执行器]
Database[(数据库)]
end
UI --> API
API --> ExternalService
ExternalService --> ScriptExecutor
ScriptExecutor --> Database
Database --> ScriptExecutor
ScriptExecutor --> ExternalService
ExternalService --> API
API --> UI
```

**图示来源**
- [publish_check.py](file://publish/publish_check.py#L1-L222)
- [external_service.py](file://task_mgt/external_service.py#L1-L86)

**本节来源**
- [publish_check.py](file://publish/publish_check.py#L1-L222)
- [spider/urls.py](file://spider/urls.py#L310-L334)

## 核心组件
发布检查API的核心组件包括`AppPublishCheckApi`、`AppListPublishCheckApi`、`IterConfirmStatusApi`和`H5IterConfirmStatusApi`四个视图集。这些组件负责接收检查请求、验证参数、调用外部检查服务并返回执行结果。系统通过`ExternalService`类实现与后端检查脚本的交互，确保检查任务的异步执行和结果追踪。

**本节来源**
- [publish_check.py](file://publish/publish_check.py#L1-L222)
- [external_service.py](file://task_mgt/external_service.py#L1-L86)

## 架构概述
发布检查系统采用分层架构设计，前端通过API接口发起检查请求，中间层API服务负责请求验证和路由，后端服务执行具体的检查逻辑。系统通过服务结果表记录每次检查的执行状态和详细信息，支持后续的结果查询和审计。

```mermaid
graph TB
subgraph "表现层"
API[API接口]
end
subgraph "业务逻辑层"
CheckService[检查服务]
ExternalService[外部服务]
end
subgraph "数据层"
ServiceResults[服务结果表]
Config[配置管理]
end
API --> CheckService
CheckService --> ExternalService
ExternalService --> ServiceResults
CheckService --> Config
Config --> CheckService
```

**图示来源**
- [publish_check.py](file://publish/publish_check.py#L1-L222)
- [task_mgt/models.py](file://task_mgt/models.py#L1-L199)

## 详细组件分析

### 发布检查API分析
`AppPublishCheckApi`和`AppListPublishCheckApi`是发布检查的核心接口，负责启动单个或多个应用的发布前检查流程。接口接收迭代ID、应用名称列表和环境参数，通过`ExternalService`调用后端检查服务。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "发布检查API"
participant ExternalService as "外部服务"
participant Script as "检查脚本"
Client->>API : GET /publish_check?iteration_id=...&app_name=...&env=...
API->>API : 验证请求参数
API->>ExternalService : 初始化服务调用
ExternalService->>Script : 异步执行检查脚本
Script-->>ExternalService : 返回执行ID
ExternalService-->>API : 返回成功响应
API-->>Client : {success : true, data : {sid : "执行ID"}}
```

**图示来源**
- [publish_check.py](file://publish/publish_check.py#L15-L45)
- [external_service.py](file://task_mgt/external_service.py#L1-L86)

### 迭代确认状态分析
`IterConfirmStatusApi`和`H5IterConfirmStatusApi`负责验证迭代发布的确认状态，确保发布操作经过必要的审批流程。这些API检查确认人是否在当天同意了迭代上线申请，并验证是否有应用处于"上线中"状态。

```mermaid
flowchart TD
Start([开始]) --> GetIterationID["获取迭代ID"]
GetIterationID --> QueryConfirmation["查询发布确认记录"]
QueryConfirmation --> HasConfirmation{"存在确认记录?"}
HasConfirmation --> |否| ReturnNotConfirmed["返回未确认"]
HasConfirmation --> |是| CheckStatus["检查状态是否为success"]
CheckStatus --> IsSuccess{"状态为success?"}
IsSuccess --> |否| ReturnNotConfirmed
IsSuccess --> |是| CheckDate["检查确认时间是否为今天"]
CheckDate --> IsToday{"确认时间为今天?"}
IsToday --> |否| ReturnExpired["返回确认过期"]
IsToday --> |是| QueryApplication["查询上线申请"]
QueryApplication --> HasApplication{"有应用上线中?"}
HasApplication --> |否| ReturnNoApplication["返回无上线申请"]
HasApplication --> |是| ReturnSuccess["返回校验通过"]
ReturnNotConfirmed --> End([结束])
ReturnExpired --> End
ReturnNoApplication --> End
ReturnSuccess --> End
```

**图示来源**
- [publish_check.py](file://publish/publish_check.py#L47-L222)
- [task_mgt/models.py](file://task_mgt/models.py#L1-L199)

**本节来源**
- [publish_check.py](file://publish/publish_check.py#L47-L222)
- [task_mgt/models.py](file://task_mgt/models.py#L1-L199)

## 依赖分析
发布检查API依赖多个核心模块和配置文件。系统通过`external_service.py`实现与后端脚本的交互，依赖`external_service_config.py`中的配置定义脚本路径和日志目录。检查结果存储在`ServiceResults`模型对应的数据库表中，通过Django ORM进行数据访问。

```mermaid
graph TD
publish_check[publish_check.py] --> external_service[external_service.py]
publish_check --> models[models.py]
external_service --> external_service_config[external_service_config.py]
external_service --> ServiceResults[ServiceResults模型]
external_service --> SSHConnection[SSH连接管理]
publish_check --> urls[urls.py]
style publish_check fill:#f9f,stroke:#333
style external_service fill:#bbf,stroke:#333
style models fill:#f96,stroke:#333
```

**图示来源**
- [publish_check.py](file://publish/publish_check.py#L1-L222)
- [external_service.py](file://task_mgt/external_service.py#L1-L86)
- [external_service_config.py](file://task_mgt/config/external_service_config.py#L1-L34)
- [models.py](file://task_mgt/models.py#L1-L199)

**本节来源**
- [publish_check.py](file://publish/publish_check.py#L1-L222)
- [external_service.py](file://task_mgt/external_service.py#L1-L86)
- [external_service_config.py](file://task_mgt/config/external_service_config.py#L1-L34)
- [models.py](file://task_mgt/models.py#L1-L199)

## 性能考虑
发布检查API采用异步执行模式，确保接口响应快速。检查任务通过`ExternalService`提交到后端执行，API立即返回执行ID，避免长时间等待。系统通过合理的日志记录和错误处理机制，确保检查过程的可追踪性和可靠性。建议在高并发场景下对API进行压力测试，确保系统稳定。

## 故障排除指南
当发布检查失败时，可通过执行ID查询详细的检查结果。系统提供`ExternalServiceAPI`接口用于查询服务执行状态和详细信息。常见问题包括参数验证失败、外部服务调用异常、数据库查询错误等。建议检查请求参数是否正确、确认人是否已同意发布、以及相关应用是否有上线申请。

**本节来源**
- [external_service_view.py](file://task_mgt/external_service_view.py#L1-L32)
- [publish_check.py](file://publish/publish_check.py#L1-L222)

## 结论
发布检查API为发布流程提供了重要的质量保障机制。通过自动化检查和审批验证，确保了发布的安全性和合规性。系统设计合理，接口清晰，具备良好的可扩展性和可维护性。建议进一步完善检查项的可配置性，增加更多的检查维度，提升系统的智能化水平。