# 发布策略API

<cite>
**本文档引用文件**   
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py)
- [models.py](file://publish/models.py)
- [publish_ser.py](file://publish/publish_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [发布策略配置接口](#发布策略配置接口)
4. [后端处理逻辑与状态机](#后端处理逻辑与状态机)
5. [灰度发布与全量发布接口对比](#灰度发布与全量发布接口对比)
6. [策略动态调整机制](#策略动态调整机制)
7. [关键操作请求示例](#关键操作请求示例)
8. [响应数据结构](#响应数据结构)

## 简介
本文档详细描述了发布策略API的实现，重点涵盖灰度发布、分批发布和全量发布等不同发布策略的接口设计与实现机制。文档深入解析了`group_publish_strategy_view.py`中各类发布策略的配置接口，包括分组规则、流量比例、批次间隔等参数设置，并解释了不同发布策略对应的后端处理逻辑及状态机转换机制。

## 核心组件

本系统的核心组件包括发布策略视图、发布策略模型以及发布服务工具。这些组件共同实现了灵活的发布策略管理功能。

**Section sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L1-L73)
- [models.py](file://publish/models.py#L339-L352)
- [publish_ser.py](file://publish/publish_ser.py#L717-L732)

## 发布策略配置接口

### 分组发布全局策略视图
`GroupPublishGlobalStrategyView`类提供了分组发布策略的配置接口，支持查询和创建操作。

#### 查询接口 (list)
通过`git_group_name`参数查询指定Git分组的发布策略列表。接口返回应用名称和步骤编号的映射关系。

```mermaid
flowchart TD
A[客户端请求] --> B{验证用户权限}
B --> C[从数据库查询现有策略]
C --> D[获取Git分组下的所有应用]
D --> E{应用是否已存在策略}
E --> |否| F[添加默认策略]
E --> |是| G[保留现有策略]
F --> H[合并策略列表]
G --> H
H --> I[返回策略列表]
```

**Diagram sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L35)

#### 创建接口 (create)
用于保存或更新指定Git分组的发布策略。接口接收`git_group_name`和`app_step_dict_list`参数，通过事务处理确保数据一致性。

```mermaid
flowchart TD
A[接收请求数据] --> B{参数验证}
B --> C[开启数据库事务]
C --> D[删除旧策略]
D --> E[构建新策略对象列表]
E --> F[批量创建策略]
F --> G{操作成功?}
G --> |是| H[返回成功响应]
G --> |否| I[事务回滚]
I --> J[返回错误信息]
H --> K[结束]
J --> K
```

**Diagram sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L37-L73)

**Section sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L73)

## 后端处理逻辑与状态机

### 数据模型设计
`IterMgtGroupPublishGlobalStrategy`模型定义了分组发布策略的核心数据结构，包含以下字段：

| 字段名 | 类型 | 描述 |
|--------|------|------|
| git_group | 字符串 | Git分组名 |
| app_name | 字符串 | 应用名 |
| step_num | 整数 | 顺序号 |
| publish_mode | 整数 | 节点发布模式 (0: 串行, 2: 均分) |
| create_time | 时间戳 | 创建时间 |
| update_time | 时间戳 | 更新时间 |

**Section sources**
- [models.py](file://publish/models.py#L339-L352)

### 应用列表获取逻辑
`get_app_step_dict_list`函数通过SQL查询获取指定Git分组下需要发布的应用列表，过滤条件包括：
- 需要上线 (need_online = 1)
- 需要检查 (need_check = 1)
- 支持Jenkins批量发布 (jenkins_batch_publish = 1)

```mermaid
flowchart TD
A[输入Git分组名] --> B[构建SQL查询]
B --> C[执行数据库查询]
C --> D{获取结果集}
D --> E[遍历结果]
E --> F[构建应用策略字典]
F --> G{是否还有记录}
G --> |是| E
G --> |否| H[返回应用策略列表]
```

**Diagram sources**
- [publish_ser.py](file://publish/publish_ser.py#L717-L732)

**Section sources**
- [publish_ser.py](file://publish/publish_ser.py#L717-L732)

## 灰度发布与全量发布接口对比

| 特性 | 灰度发布 | 全量发布 |
|------|----------|----------|
| 发布范围 | 指定子集节点 | 所有生产节点 |
| 流量控制 | 可配置流量比例 | 100%流量 |
| 风险等级 | 低 | 高 |
| 回滚速度 | 快速 | 相对较慢 |
| 适用场景 | 新功能验证、重大变更 | 日常更新、紧急修复 |

灰度发布通过`step_num`字段控制发布顺序，逐步扩大发布范围；全量发布则一次性部署到所有目标节点。

**Section sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L73)
- [models.py](file://publish/models.py#L339-L352)

## 策略动态调整机制

系统支持通过API动态调整发布策略，主要流程如下：

1. **策略预览**：调用`list`接口获取当前策略配置
2. **策略修改**：在客户端修改应用的`step_num`和`publish_mode`参数
3. **策略验证**：前端进行基本验证（如步骤编号唯一性）
4. **策略生效**：调用`create`接口提交修改后的策略

```mermaid
stateDiagram-v2
[*] --> 策略预览
策略预览 --> 策略修改 : 用户操作
策略修改 --> 策略验证 : 提交前
策略验证 --> 策略生效 : 验证通过
策略验证 --> 策略修改 : 验证失败
策略生效 --> [*] : 完成
```

**Diagram sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L73)

**Section sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L73)

## 关键操作请求示例

### 查询发布策略
```http
GET /api/publish/strategy?git_group_name=example-group
Authorization: Bearer <token>
```

### 创建/更新发布策略
```json
POST /api/publish/strategy
Content-Type: application/json
Authorization: Bearer <token>

{
  "git_group_name": "example-group",
  "app_step_dict_list": [
    {
      "app_name": "service-a",
      "step_num": 1
    },
    {
      "app_name": "service-b",
      "step_num": 2
    }
  ]
}
```

**Section sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L73)

## 响应数据结构

### 成功响应
```json
{
  "code": 200,
  "message": "保存组发布全局策略成功！",
  "data": null,
  "success": true
}
```

### 查询响应
```json
{
  "code": 200,
  "message": "",
  "data": [
    {
      "app_name": "service-a",
      "step_num": 1
    },
    {
      "app_name": "service-b",
      "step_num": 2
    }
  ],
  "success": true
}
```

### 错误响应
```json
{
  "code": 500,
  "message": "数据库操作失败",
  "data": null,
  "success": false
}
```

**Section sources**
- [group_publish_strategy_view.py](file://publish/group_publish_strategy_view.py#L20-L73)
- [spider/settings.py](file://spider/settings.py#LNewApiResult)