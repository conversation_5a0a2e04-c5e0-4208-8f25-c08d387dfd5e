# 性能测试集成API

<cite>
**本文档引用文件**  
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py)
- [models.py](file://test_mgt/models.py)
- [pipeline_task_ser.py](file://task_mgt/pipeline_task_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [性能测试执行接口参数配置](#性能测试执行接口参数配置)
3. [性能测试结果模型设计](#性能测试结果模型设计)
4. [性能测试任务调度实现](#性能测试任务调度实现)
5. [性能测试报告查询与基线对比](#性能测试报告查询与基线对比)
6. [CI/CD集成方式](#cicd集成方式)
7. [结论](#结论)

## 简介
本文档详细描述了性能测试集成API的设计与实现，涵盖性能测试任务的自动化触发、执行参数配置、结果存储、任务调度及与CI/CD流程的集成。重点分析了性能测试专属接口的参数设计、数据模型结构以及任务调度逻辑，为系统性能验证提供完整的自动化支持。

## 性能测试执行接口参数配置

在 `suite_info_view.py` 文件中定义了性能测试执行接口，用于获取迭代环境下的应用套件信息。该接口支持通过 `iteration_id` 查询测试环境配置，为性能测试任务提供目标环境上下文。

接口通过 `AppSuiteCodeApi` 和 `PlatformSuiteCodeApi` 两个视图类分别获取应用级和平台级的套件信息。核心参数包括：
- **iteration_id**: 迭代ID，用于定位特定版本的测试环境
- **suite_code**: 环境编码，标识测试执行的具体环境（如 pd-test）
- **platform_suite**: 平台级套件信息，支持基金和存钱罐等多平台测试

该接口虽未直接暴露性能测试专属参数（如并发用户数、测试时长等），但为后续性能测试任务提供了必要的环境配置基础。实际性能测试参数应在调用性能测试引擎时通过任务参数传递。

**节来源**
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py#L1-L28)

## 性能测试结果模型设计

在 `test_mgt/models.py` 文件中，虽然未直接定义 `PerformanceTestResult` 模型，但存在多个与测试管理相关的数据模型，为性能测试结果存储提供了设计参考。

基于系统架构和测试管理需求，合理的 `PerformanceTestResult` 模型应包含以下关键性能指标字段：
- **响应时间**: 包括平均响应时间、最大响应时间、95%响应时间等
- **吞吐量**: 每秒事务数（TPS）、请求速率等
- **错误率**: 请求失败比例、错误类型分布
- **资源利用率**: CPU使用率、内存占用、网络I/O等
- **测试元数据**: 测试ID、应用名、迭代ID、环境编码、测试开始/结束时间

该模型应继承 Django 的 `models.Model`，并映射到专用的数据库表（如 `test_mgt_performance_result`），通过外键关联到 `IntegrationTestList` 等测试批次管理模型，实现测试结果的可追溯性。

**节来源**
- [models.py](file://test_mgt/models.py#L1-L82)

## 性能测试任务调度实现

在 `pipeline_task_ser.py` 文件中实现了任务调度的核心逻辑，提供了两个关键函数用于支持性能测试任务的调度：

1. **get_iter_app_id**: 根据分支名（br_name）和代码路径（git_code_path）查询迭代应用ID列表。该函数通过多表关联查询（iter_mgt_iter_info、iter_mgt_iter_app_info、app_mgt_app_module、app_mgt_app_info）定位具体应用，为性能测试任务绑定目标应用提供数据支持。

2. **get_iter_app_module_name**: 获取指定分支和代码路径下的应用模块名称映射。该函数返回以流水线ID为键、应用模块名为值的字典，支持批量性能测试任务的模块识别与分组。

这些函数通过原生SQL查询实现高效的数据检索，为性能测试任务的自动化调度提供了底层支持。性能测试任务可在CI/CD流水线的特定阶段调用这些接口，动态获取待测应用信息并触发性能测试。

**节来源**
- [pipeline_task_ser.py](file://task_mgt/pipeline_task_ser.py#L1-L46)

## 性能测试报告查询与基线对比

性能测试报告查询接口应返回结构化的性能指标数据，建议响应格式如下：

```json
{
  "test_id": "string",
  "app_name": "string",
  "iteration_id": "string",
  "metrics": {
    "tps": "number",
    "avg_response_time": "number",
    "p95_response_time": "number",
    "p99_response_time": "number",
    "error_rate": "number",
    "cpu_usage": "number",
    "memory_usage": "number"
  },
  "baseline_comparison": {
    "baseline_test_id": "string",
    "regression_threshold": "number",
    "is_regression": "boolean",
    "metric_deltas": {
      "tps": "number",
      "avg_response_time": "number"
    }
  }
}
```

性能基线对比功能通过将当前测试结果与历史基线版本（如上一版本或生产版本）进行对比，识别性能回归。系统应支持配置性能衰减阈值（如TPS下降超过10%即判定为回归），并在CI/CD流程中自动阻断性能不达标的版本发布。

## CI/CD集成方式

性能测试应作为CI/CD流水线的独立阶段集成，典型流程如下：
1. 代码构建与单元测试通过后
2. 部署到性能测试环境（通过 `suite_info_view.py` 获取环境配置）
3. 调用 `pipeline_task_ser.py` 接口识别待测应用
4. 触发性能测试任务（JMeter/Gatling等）
5. 收集性能测试结果并存储到 `PerformanceTestResult` 模型
6. 执行基线对比分析
7. 根据性能达标情况决定是否继续后续发布流程

该集成方式确保每次代码变更都经过性能验证，防止性能退化版本流入生产环境。

## 结论
本文档详细阐述了性能测试集成API的关键组件，包括执行接口参数配置、结果模型设计、任务调度实现及CI/CD集成方案。通过合理利用现有接口并扩展性能测试专用模型与逻辑，可构建完整的自动化性能测试体系，为系统稳定性提供有力保障。