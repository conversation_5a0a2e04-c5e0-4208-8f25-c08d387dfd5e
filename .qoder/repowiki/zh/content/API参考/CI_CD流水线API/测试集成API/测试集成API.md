# 测试集成API

<cite>
**本文档引用的文件**
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py)
- [models.py](file://test_mgt/models.py)
- [jacoco_ser.py](file://task_mgt/jacoco_ser.py)
- [biz_flow_run_record_view.py](file://external_interaction/mantis_mgt/biz_flow_run_record_view.py)
</cite>

## 目录
1. [简介](#简介)
2. [测试套件执行接口参数配置](#测试套件执行接口参数配置)
3. [测试任务数据模型设计](#测试任务数据模型设计)
4. [代码覆盖率数据收集接口实现](#代码覆盖率数据收集接口实现)
5. [与Mantis系统的集成方式](#与mantis系统的集成方式)
6. [测试结果查询接口响应格式](#测试结果查询接口响应格式)
7. [轮询建议](#轮询建议)

## 简介
本文档详细描述了自动化测试任务的触发、执行和结果上报全流程。重点说明了测试套件执行接口的参数配置、测试任务数据模型的设计、代码覆盖率数据收集接口的实现细节，以及与Mantis系统的集成方式。通过`biz_flow_run_record_view.py`将测试结果同步到项目管理平台，并提供测试结果查询接口的响应格式和轮询建议。

## 测试套件执行接口参数配置

### 接口说明
`suite_info_view.py` 文件中定义了两个视图类：`AppSuiteCodeApi` 和 `PlatformSuiteCodeApi`，用于获取应用和平台的测试套件信息。

### 参数配置
- **test_suite_id**: 测试套件ID，用于唯一标识一个测试套件。
- **target_build**: 目标构建版本，指定要测试的具体构建版本。
- **test_env**: 测试环境，如 "pd-test"，表示测试环境的编码。

### 示例请求
```http
GET /api/suite_info?iteration_id=12345
```

**Section sources**
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py#L1-L28)

## 测试任务数据模型设计

### 数据模型概述
`test_mgt/models.py` 文件中定义了多个数据模型，涵盖单元测试、集成测试等类型。

### 主要模型
- **AppTest**: 应用测试管理模型，包含模块名、是否需要接口统计、是否需要单元测试等字段。
- **IntegrationTestList**: 集成测试执行清单模型，记录创建时间和测试集ID。
- **IntegrationTestListDetail**: 集成测试详细执行清单模型，包含应用名、分支名、创建时间、创建用户等字段。

### 字段说明
- **module_name**: 模块名，最大长度100字符。
- **need_interface_agent**: 是否需要进行接口统计，布尔值，默认为1。
- **need_unit_test**: 是否需要进行单元测试，布尔值，默认为1。
- **test_set_id**: 测试集ID，整数类型。
- **app_name**: 应用名，最大长度100字符。
- **br_name**: 分支名，最大长度100字符。
- **create_time**: 创建时间，日期时间类型。
- **create_user**: 创建用户，最大长度100字符。

**Section sources**
- [models.py](file://test_mgt/models.py#L1-L82)

## 代码覆盖率数据收集接口实现

### 接口功能
`jacoco_ser.py` 文件中的 `get_app_env_jacoco_info` 函数用于获取应用和环境的Jacoco部署信息。

### 实现细节
- **输入参数**:
  - `app_name`: 应用名。
  - `suite_code`: 环境编码。
- **逻辑流程**:
  1. 查询 `env_mgt_node_bind` 表，获取部署类型。
  2. 根据部署类型（1为普通部署，其他为Docker部署），执行不同的SQL查询。
  3. 返回节点IP或Docker IP及Jacoco端口。

### 示例代码
```python
def get_app_env_jacoco_info(app_name, suite_code):
    cursor = connection.cursor()
    sql = """select t.deploy_type from env_mgt_node_bind t 
            left join env_mgt_suite ems on t.suite_id = ems.id
            where 
            t.module_name = '{}'
            and 
            ems.suite_code = '{}'""".format(app_name, suite_code)
    cursor.execute(sql)
    for row in cursor.fetchall():
        deploy_type = row[0]

    if deploy_type == 1:
        sql = """select emn.node_ip, t.jacoco_port from env_mgt_node_bind t 
                left join env_mgt_suite ems on t.suite_id = ems.id
                left join env_mgt_node emn on t.node_id = emn.id
                where 
                t.module_name = '{}'
                and 
                ems.suite_code = '{}'
        """.format(app_name, suite_code)
    else:
        sql = """select t.node_docker_ip, t.jacoco_port from env_mgt_node_bind t 
                left join env_mgt_suite ems on t.suite_id = ems.id
                where 
                t.module_name = '{}'
                and 
                ems.suite_code = '{}'
                """.format(app_name, suite_code)
    cursor.execute(sql)
    return cursor.fetchall()
```

**Section sources**
- [jacoco_ser.py](file://task_mgt/jacoco_ser.py#L1-L44)

## 与Mantis系统的集成方式

### 集成接口
`biz_flow_run_record_view.py` 文件中的 `BizFlowRunRecord` 类提供了与Mantis系统的集成接口。

### 接口方法
- **get_test_flow_run_record**: 获取测试流程运行记录。
  - **请求方式**: GET
  - **URL路径**: `/get_test_flow_run_record`
  - **参数**: `start_time`，查询起始时间。

### 示例请求
```http
GET /api/get_test_flow_run_record?start_time=2023-01-01T00:00:00Z
```

### 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "test_flow_run_record": [
      {
        "id": 1,
        "start_time": "2023-01-01T00:00:00Z",
        "end_time": "2023-01-01T01:00:00Z",
        "status": "success"
      }
    ]
  }
}
```

**Section sources**
- [biz_flow_run_record_view.py](file://external_interaction/mantis_mgt/biz_flow_run_record_view.py#L1-L23)

## 测试结果查询接口响应格式

### 响应结构
- **code**: 状态码，0表示成功。
- **msg**: 消息，描述请求结果。
- **data**: 数据部分，包含具体的测试结果信息。

### 数据字段
- **test_flow_run_record**: 测试流程运行记录列表。
  - **id**: 记录ID。
  - **start_time**: 开始时间。
  - **end_time**: 结束时间。
  - **status**: 状态，如 "success" 或 "failed"。
  - **pass_rate**: 通过率，百分比形式。
  - **failed_cases**: 失败用例列表。
  - **coverage**: 覆盖率指标。

### 示例响应
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "test_flow_run_record": [
      {
        "id": 1,
        "start_time": "2023-01-01T00:00:00Z",
        "end_time": "2023-01-01T01:00:00Z",
        "status": "success",
        "pass_rate": 95,
        "failed_cases": [
          "test_case_001",
          "test_case_002"
        ],
        "coverage": 85
      }
    ]
  }
}
```

## 轮询建议
为了确保及时获取最新的测试结果，建议客户端采用以下轮询策略：
- **初始轮询间隔**: 10秒。
- **最大轮询间隔**: 60秒。
- **重试次数**: 最多重试10次。
- **成功后停止轮询**: 当收到状态为 "success" 或 "failed" 的响应时，停止轮询。