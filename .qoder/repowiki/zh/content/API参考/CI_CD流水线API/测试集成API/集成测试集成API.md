# 集成测试集成API

<cite>
**本文档引用文件**  
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py)
- [biz_flow_run_record_view.py](file://external_interaction/mantis_mgt/biz_flow_run_record_view.py)
- [env_apply_view.py](file://test_env_mgt/env_apply_view.py)
- [publish_info_ser.py](file://ci_cd_mgt/publish_info_ser.py)
- [test_env_mgt.py](file://test_env_mgt/test_env_mgt.py)
- [spider/urls.py](file://spider/urls.py)
</cite>

## 目录
1. [集成测试套件执行接口](#集成测试套件执行接口)
2. [测试结果与Mantis系统同步机制](#测试结果与mantis系统同步机制)
3. [测试环境申请与集成测试协同流程](#测试环境申请与集成测试协同流程)
4. [集成测试结果查询接口](#集成测试结果查询接口)

## 集成测试套件执行接口

`ci_cd_mgt/h5/suite_info_view.py` 文件中定义了集成测试套件执行的核心接口，用于获取特定迭代下的测试环境信息。该模块提供了两个主要API端点：

- `AppSuiteCodeApi`：通过 `iteration_id` 查询发布申请信息，返回与 `pd-test` 环境相关的应用套件信息。
- `PlatformSuiteCodeApi`：查询 `dist` 平台的环境信息，返回基金平台和小猪平台的测试套件配置。

关键参数包括：
- `iteration_id`：指定迭代ID，用于关联测试套件与特定开发迭代。
- `target_build`：通过 `get_iter_suite` 和 `get_iter_platform_suite` 服务函数间接获取构建信息，确保测试环境与目标构建版本一致。
- `test_env`：在服务层通过 `["pd-test"]` 固定指定测试环境，支持环境隔离与专用测试套件管理。

该接口通过 `ApiResult.success_dict` 返回结构化数据，包含 `app_suite_info`、`fund_platform_suite` 和 `piggy_platform_suite` 等字段，支持前端展示和自动化调用。

**本节来源**  
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py#L1-L28)
- [publish_info_ser.py](file://ci_cd_mgt/publish_info_ser.py#L1084-L1114)

## 测试结果与Mantis系统同步机制

`external_interaction/mantis_mgt/biz_flow_run_record_view.py` 实现了测试执行记录与Mantis项目管理系统的同步机制。`BizFlowRunRecord` 视图类提供 `get_test_flow_run_record` 接口，用于获取测试流程运行记录。

同步机制工作流程如下：
1. **请求触发**：客户端通过 `GET` 请求调用 `/get_test_flow_run_record` 接口，携带 `start_time` 参数指定查询起始时间。
2. **数据获取**：调用 `get_test_flow_run_record_info(query_start_time)` 服务函数，从数据库或外部系统获取测试流程执行记录。
3. **状态更新**：测试执行记录包含执行状态（如“成功”、“失败”、“进行中”），通过 `ApiResult.success_dict` 返回结构化数据，供Mantis系统更新任务状态。
4. **缺陷关联**：测试失败时，系统可自动创建缺陷工单，并将测试日志、截图等证据关联至Mantis缺陷条目，实现测试与缺陷管理的闭环。

该接口目前未启用JWT认证和权限控制（`authentication_classes` 和 `permission_classes` 为空列表），适用于内部系统间集成调用。

**本节来源**  
- [biz_flow_run_record_view.py](file://external_interaction/mantis_mgt/biz_flow_run_record_view.py#L1-L23)
- [iter_mgt/iter_mgt_view.py](file://iter_mgt/iter_mgt_view.py#L1269-L1295)

## 测试环境申请与集成测试协同流程

`test_env_mgt/env_apply_view.py` 模块实现了测试环境申请与集成测试的完整协同工作流程，涵盖环境预置、测试执行和环境释放三个阶段。

### 环境申请与预置

- **环境申请**：通过 `GetAllTestEnvApplyInfo` 接口查询所有测试环境申请情况，支持分页和筛选。
- **模板管理**：`TemplateInfoApi` 提供测试模板列表，`UpdateTemplateInfo` 支持模板的创建与更新，包含初始化数据库、清理缓存、测试集ID等配置。
- **应用绑定**：`GetAllAppByTemplate` 根据模板ID查询关联的应用列表，`GetSuiteBindApp` 获取指定环境套中已绑定的应用信息。

### 测试执行

- **Jenkins调用**：`CallJenkins` 和 `CallUpdateJenkins` 类负责调用Jenkins执行测试任务。通过 `ZtstTpAio` 类封装请求参数，发送POST请求触发测试流水线。
- **参数配置**：关键参数包括 `suite_id`（环境ID）、`suite_code`（环境编码）、`app_name_list`（应用列表）、`db_env`（数据库环境）、`is_clear_cache`（是否清理缓存）、`test_set`（测试集ID）等。
- **Mock服务**：支持通过 `need_mock_app_list` 参数指定需要启用Mock代理的应用，实现服务隔离与依赖模拟。

### 环境释放

- **环境回收**：`RecycleTestEnv` 接口用于回收测试环境，将 `TestEnvApplyInfo` 表中的 `apply_user`、`apply_reason`、`invalid_at` 等字段置空，并清除 `jenkins_url`。
- **延期与编辑**：`DelayTestEnv` 和 `UpdateTestEnvApplyInfo` 支持修改环境使用期限和申请信息。

该流程通过 `spider/urls.py` 中的路由注册对外暴露，如 `/spider/test_env_mgt/recycle_test_env`、`/spider/test_env_mgt/call_jenkins` 等。

```mermaid
flowchart TD
A[开始] --> B[申请测试环境]
B --> C[选择测试模板]
C --> D[绑定应用]
D --> E[调用Jenkins执行测试]
E --> F{测试是否完成?}
F --> |是| G[回收测试环境]
F --> |否| H[等待]
H --> F
G --> I[结束]
```

**图表来源**  
- [env_apply_view.py](file://test_env_mgt/env_apply_view.py#L0-L1133)
- [spider/urls.py](file://spider/urls.py#L208-L275)

**本节来源**  
- [env_apply_view.py](file://test_env_mgt/env_apply_view.py#L0-L1133)
- [test_env_mgt.py](file://test_env_mgt/test_env_mgt.py#L0-L38)

## 集成测试结果查询接口

集成测试结果查询接口通过 `TestSuiteInitLog` 模型记录测试执行的完整信息，支持对测试结果的详细查询与分析。

### 响应格式

响应数据包含以下关键字段：
- `job_http_suite_code`：测试环境编码。
- `job_http_dict`：HTTP请求参数，包含 `OPT_TYPE`、`APP_NAME_LIST`、`DB_ENV` 等。
- `job_result`：测试执行结果（`True`/`False`）。
- `job_build_id`：Jenkins构建ID，用于追溯构建日志。
- `job_desc`：执行描述，包含并发检查提示等信息。
- `create_user` 和 `update_user`：操作用户信息。

### 重试策略

系统内置并发控制机制，防止同一环境的重复初始化：
1. **并发检查**：在创建 `TestSuiteInitLog` 记录前，查询 `TestSuiteInitLog` 表中是否存在相同 `job_http_suite_code` 且 `job_result` 为 `None` 的记录。
2. **阻塞提示**：若存在未完成的执行记录，返回提示信息：“『环境编码』正在由用户『用户名』初始化中，同一环境并发执行可能会出现无法预料的错误！”
3. **幂等性保证**：通过 `job_business_id`（即 `TestSuiteInitLog` 的主键ID）作为Jenkins任务的业务ID，确保每次请求生成唯一的执行记录。

该机制确保了测试执行的稳定性和可追溯性，避免了因并发调用导致的环境冲突和数据不一致。

**本节来源**  
- [env_apply_view.py](file://test_env_mgt/env_apply_view.py#L700-L800)
- [biz_mgt/biz_mgt_dao.py](file://biz_mgt/biz_mgt_dao.py#L675-L701)