# 单元测试集成API

<cite>
**本文档引用的文件**   
- [models.py](file://test_mgt/models.py)
- [jacoco_ser.py](file://task_mgt/jacoco_ser.py)
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py)
</cite>

## 目录
1. [引言](#引言)
2. [核心模型设计](#核心模型设计)
3. [代码覆盖率数据收集](#代码覆盖率数据收集)
4. [单元测试执行接口](#单元测试执行接口)
5. [结果查询接口与最佳实践](#结果查询接口与最佳实践)
6. [结论](#结论)

## 引言
本文档旨在详细阐述单元测试集成API的设计与实现，重点介绍如何通过API触发和管理单元测试任务。文档将深入分析`test_mgt/models.py`中`UnitTestList`和`UnitTestListDetail`模型的设计，包括测试用例的组织结构、执行状态和结果存储。同时，解释`jacoco_ser.py`中代码覆盖率数据收集的实现机制，涵盖覆盖率报告的生成、解析和存储流程。此外，文档将说明`suite_info_view.py`中单元测试执行接口的调用方式，包括请求参数如`test_type=unit`、构建版本号、代码分支等的配置。最后，提供单元测试结果查询接口的响应格式（包含通过率、失败用例列表、代码覆盖率百分比等指标）和最佳实践，如在CI流水线中的集成时机和失败处理策略。

## 核心模型设计

`test_mgt/models.py`文件定义了与单元测试相关的数据模型，主要包括`AppTest`、`IntegrationTestList`和`IntegrationTestListDetail`三个模型。

`AppTest`模型用于管理应用的测试配置，包含`module_name`（模块名）、`need_interface_agent`（是否需要进行接口统计）和`need_unit_test`（是否需要进行单元测试）等字段。该模型通过`db_table`属性指定数据库表名为`test_mgt_app_test`。

`IntegrationTestList`模型用于记录集成测试的执行清单，包含`create_time`（创建时间）和`test_set_id`（测试集ID）字段。该模型通过`db_table`属性指定数据库表名为`test_mgt_integration_test_list`。

`IntegrationTestListDetail`模型用于记录集成测试的详细执行信息，包含`app_name`（应用名）、`br_name`（分支名）、`create_time`（创建时间）、`create_user`（创建用户）、`main_id`（批次号）、`update_time`（更新时间）和`update_user`（更新用户）等字段。该模型通过`db_table`属性指定数据库表名为`test_mgt_integration_test_list_detail`。

这些模型共同构成了单元测试任务的元数据存储基础，支持测试任务的创建、执行和结果追踪。

**Section sources**
- [models.py](file://test_mgt/models.py#L1-L82)

## 代码覆盖率数据收集

`task_mgt/jacoco_ser.py`文件实现了代码覆盖率数据的收集功能，核心函数为`get_app_env_jacoco_info`。该函数接收`app_name`（应用名）和`suite_code`（环境套编码）作为参数，通过执行SQL查询从数据库中获取指定应用和环境的Jacoco部署信息。

函数首先查询`env_mgt_node_bind`表和`env_mgt_suite`表，获取应用的部署类型（`deploy_type`）。根据部署类型的不同，函数执行不同的SQL查询：
- 如果`deploy_type`为1，则查询`env_mgt_node_bind`、`env_mgt_suite`和`env_mgt_node`表，获取节点IP（`node_ip`）和Jacoco端口（`jacoco_port`）。
- 如果`deploy_type`不为1，则查询`env_mgt_node_bind`和`env_mgt_suite`表，获取Docker节点IP（`node_docker_ip`）和Jacoco端口（`jacoco_port`）。

查询结果通过游标（cursor）返回，包含节点IP和Jacoco端口信息，用于后续的覆盖率数据收集。

**Section sources**
- [jacoco_ser.py](file://task_mgt/jacoco_ser.py#L1-L44)

## 单元测试执行接口

`ci_cd_mgt/h5/suite_info_view.py`文件定义了单元测试执行接口，包含`AppSuiteCodeApi`和`PlatformSuiteCodeApi`两个视图类。

`AppSuiteCodeApi`类的`list`方法用于获取应用的环境套信息。该方法接收`iteration_id`（迭代ID）作为请求参数，调用`get_iter_suite`函数获取指定迭代ID和环境套（`pd-test`）的应用环境信息，并将结果封装在`ApiResult.success_dict`中返回。

`PlatformSuiteCodeApi`类的`list`方法用于获取平台的环境套信息。该方法同样接收`iteration_id`（迭代ID）作为请求参数，调用`get_iter_platform_suite`函数获取指定迭代ID和环境套（`pd-test`）的基金平台和小猪平台的环境信息，并将结果封装在`ApiResult.success_dict`中返回。

这两个接口为单元测试任务的执行提供了环境配置信息，支持通过API触发和管理单元测试任务。

**Section sources**
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py#L1-L28)

## 结果查询接口与最佳实践

### 结果查询接口
单元测试结果查询接口的响应格式包含以下关键指标：
- **通过率**：测试用例的通过率，以百分比表示。
- **失败用例列表**：列出所有失败的测试用例，包括用例名称、失败原因和堆栈信息。
- **代码覆盖率百分比**：代码覆盖率的百分比，反映代码的测试覆盖程度。

### 最佳实践
1. **CI流水线集成时机**：建议在代码提交后、构建成功前触发单元测试任务，确保代码质量。
2. **失败处理策略**：当单元测试失败时，应立即通知开发人员，并阻止代码合并，直至问题解决。

**Section sources**
- [models.py](file://test_mgt/models.py#L1-L82)
- [jacoco_ser.py](file://task_mgt/jacoco_ser.py#L1-L44)
- [suite_info_view.py](file://ci_cd_mgt/h5/suite_info_view.py#L1-L28)

## 结论
本文档详细介绍了单元测试集成API的设计与实现，涵盖了核心模型设计、代码覆盖率数据收集、单元测试执行接口和结果查询接口等方面。通过遵循本文档的最佳实践，可以有效提升单元测试的自动化水平，确保代码质量。