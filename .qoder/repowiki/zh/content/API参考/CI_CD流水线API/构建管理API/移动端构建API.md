# 移动端构建API

<cite>
**本文档引用文件**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py)
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py)
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py)
- [pipeline_view.py](file://pipeline/pipeline_view.py)
- [urls.py](file://ci_cd_mgt/mobile/urls.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心组件](#核心组件)
3. [移动端构建接口](#移动端构建接口)
4. [快速打包服务集成](#快速打包服务集成)
5. [通用流水线系统集成](#通用流水线系统集成)
6. [构建参数校验规则](#构建参数校验规则)
7. [错误码说明](#错误码说明)
8. [客户端调用示例](#客户端调用示例)

## 简介
本文档详细说明了移动端构建API的实现机制，重点分析了移动端应用构建接口的实现逻辑。文档涵盖了POST /api/mobile/build/接口的HTTP方法、URL路径和认证机制，深入解析了请求体参数结构，包括应用ID、平台类型、构建类型、发布渠道、版本号等移动端特有参数。同时描述了与快速打包服务和通用流水线系统的集成方式，提供了构建参数校验规则和错误码说明，并给出了Python客户端和curl命令调用示例。

## 核心组件

**Section sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py#L1-L19)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

## 移动端构建接口

```mermaid
sequenceDiagram
participant 客户端 as 客户端
participant AppView as app_view.py
participant PipelineView as pipeline_view.py
客户端->>AppView : POST /api/mobile/build/
AppView->>AppView : 参数校验
AppView->>PipelineView : 调用Jenkins流水线
PipelineView->>Jenkins : 触发构建任务
Jenkins-->>PipelineView : 返回构建状态
PipelineView-->>AppView : 返回任务ID和URL
AppView-->>客户端 : 返回构建结果
```

**Diagram sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

**Section sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [urls.py](file://ci_cd_mgt/mobile/urls.py#L1-L16)

### HTTP方法与URL路径
移动端构建接口采用POST方法，通过`/api/mobile/build/`路径触发构建任务。该接口基于Django REST framework实现，支持JSON格式的请求体数据。

### 认证机制
接口采用API Key认证机制，用户需要在请求头中提供有效的API Key进行身份验证，确保只有授权用户才能触发构建任务。

### 请求体参数结构
移动端构建接口的请求体包含以下关键参数：

| 参数 | 类型 | 描述 |
|------|------|------|
| app_id | 字符串 | 应用ID，唯一标识一个移动应用 |
| platform | 字符串 | 平台类型，支持iOS和Android |
| build_type | 字符串 | 构建类型，支持debug和release |
| channel | 字符串 | 发布渠道，如应用商店、内测等 |
| version_code | 整数 | 版本号，用于版本控制 |

## 快速打包服务集成

```mermaid
sequenceDiagram
participant 客户端 as 客户端
participant ExpressView as express_service_view.py
participant ExpressSer as express_service_ser.py
客户端->>ExpressView : GET /api/mobile/express/
ExpressView->>ExpressSer : 获取应用在线版本
ExpressSer->>数据库 : 查询最新版本信息
数据库-->>ExpressSer : 返回版本数据
ExpressSer-->>ExpressView : 返回应用版本列表
ExpressView-->>客户端 : 返回快速打包结果
```

**Diagram sources**
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py#L1-L19)
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L55)

**Section sources**
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py#L1-L19)
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L55)

### 快速打包服务逻辑
`express_service_view.py`中的`AppExpressBuildService`类实现了快速打包服务的集成逻辑。该服务通过`list`方法处理GET请求，接收H5平台代码列表，调用`AppExpressBuildServiceSer`服务类获取应用在线版本信息。

### 多渠道包构建
系统通过`h5_platform_combinations`映射表管理H5平台与移动应用的对应关系，支持`fund-h5-res`对应`fund-ios`和`fund-android`，`piggy-h5-res`对应`piggy-ios`和`piggy-android`。这种设计使得一个H5平台可以快速生成多个渠道的移动应用包。

## 通用流水线系统集成

```mermaid
flowchart TD
A[构建请求] --> B{参数校验}
B --> |通过| C[生成操作ID]
C --> D[插入状态数据]
D --> E[调用Jenkins流水线]
E --> F[异步执行构建]
F --> G[更新构建状态]
G --> H[返回构建结果]
B --> |失败| I[返回错误信息]
```

**Diagram sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

**Section sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

### 异步处理机制
构建任务采用异步处理机制，通过`TaskQueue`类实现任务队列管理。`async_run()`方法确保构建任务在后台执行，不阻塞主线程，提高系统响应速度。

### 构建结果回调
系统通过`get_jenkins_last_status`方法定期查询Jenkins构建状态，实现构建结果的回调处理。该方法获取最后一次构建的时间、状态、操作者和任务ID，为用户提供实时的构建进度反馈。

## 构建参数校验规则

### 版本号递增验证
系统在构建前会检查版本号是否符合递增规则，确保新版本的`version_code`大于当前线上版本，避免版本回退导致的问题。

### 渠道包签名配置检查
对于Android应用，系统会检查渠道包的签名配置是否完整，包括keystore文件、别名、密码等信息，确保构建出的APK包可以正常安装和更新。

**Section sources**
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L55)
- [task_mgt/async_exec_scripts/script_param_bo.py](file://task_mgt/async_exec_scripts/script_param_bo.py#L143-L155)

## 错误码说明

| 错误码 | 描述 | 可能原因 |
|--------|------|----------|
| 400 | 渠道配置错误 | 渠道参数缺失或格式不正确 |
| 404 | 证书不存在 | 签名证书文件未找到或路径错误 |
| 500 | 打包服务器异常 | Jenkins服务器故障或资源不足 |

**Section sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

## 客户端调用示例

### Python客户端调用
```python
import requests

url = "http://api.example.com/api/mobile/build/"
headers = {
    "Authorization": "APIKey your-api-key",
    "Content-Type": "application/json"
}
data = {
    "app_id": "12345",
    "platform": "iOS",
    "build_type": "release",
    "channel": "appstore",
    "version_code": 100
}

response = requests.post(url, json=data, headers=headers)
print(response.json())
```

### curl命令示例
```bash
# 触发iOS应用构建
curl -X POST \
  http://api.example.com/api/mobile/build/ \
  -H 'Authorization: APIKey your-api-key' \
  -H 'Content-Type: application/json' \
  -d '{
    "app_id": "12345",
    "platform": "iOS",
    "build_type": "release",
    "channel": "appstore",
    "version_code": 100
}'

# 触发Android应用构建
curl -X POST \
  http://api.example.com/api/mobile/build/ \
  -H 'Authorization: APIKey your-api-key' \
  -H 'Content-Type: application/json' \
  -d '{
    "app_id": "12345",
    "platform": "Android",
    "build_type": "debug",
    "channel": "internal",
    "version_code": 50
}'
```

**Section sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [urls.py](file://ci_cd_mgt/mobile/urls.py#L1-L16)