# 构建管理API

<cite>
**本文档引用的文件**   
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)
- [mobile/app_view.py](file://ci_cd_mgt/mobile/app_view.py)
- [pipeline/pipeline_view.py](file://pipeline/pipeline_view.py)
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py)
- [task_queue.py](file://task_mgt/task_queue.py)
- [view.py](file://ci_cd_mgt/h5/view.py)
- [models.py](file://ci_cd_mgt/h5/models.py)
- [enums.py](file://ci_cd_mgt/h5/enums.py)
</cite>

## 目录
1. [H5应用构建触发接口](#h5应用构建触发接口)
2. [移动端构建接口](#移动端构建接口)
3. [通用流水线触发器设计模式](#通用流水线触发器设计模式)
4. [构建参数校验与错误处理](#构建参数校验与错误处理)
5. [Jenkins集成与构建ID回传](#jenkins集成与构建id回传)
6. [客户端调用示例](#客户端调用示例)

## H5应用构建触发接口

H5应用的构建触发接口实现在`ci_cd_mgt/h5/app_ci_pipeline.py`文件中的`AppCIPipeline`类。该接口通过继承`H5CIPipelineApi`基类，实现了H5应用的CI/CD流程控制。

接口支持多种应用类型，通过`business_name_dict`字典映射不同包类型到对应的Jenkins业务名称：
- `ios` → `app_ios_publish`
- `android` → `app_android_publish`
- `ios-com` 和 `android-com` → `app_component_publish`
- `android-global` → `app_android_publish`
- `ios-global` → `app_ios_publish`

构建请求的HTTP方法为POST，URL路径通过Django REST framework的ViewSet机制自动生成。接口通过`run_publish_apply_stage`方法处理发布申请阶段的执行逻辑，该方法会：
1. 创建任务队列`AppTaskQueue`
2. 执行责任链检查任务
3. 调用Jenkins job进行发布
4. 发送电子邮件通知
5. 异步执行所有任务

请求体参数结构包含以下关键字段：
- `iterationID`: 迭代ID
- `appEnv`: 应用环境
- `appNameList`: 应用名称列表
- `packageType`: 包类型
- `cc`: 抄送邮箱列表

**Section sources**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)

## 移动端构建接口

移动端构建接口实现在`ci_cd_mgt/mobile/app_view.py`文件中的`H5TestPublishApi`类。该接口同样继承自`H5CIPipelineApi`基类，专门处理移动端应用的构建请求。

接口支持的移动端应用类型通过`business_name_dict`字典定义：
- `ios` → `app_ios_publish`
- `android` → `app_android_publish`

构建请求的HTTP方法为POST，处理逻辑在`run_stage`方法中实现。该方法会：
1. 创建`TaskQueue`任务队列
2. 执行校验阶段的检查任务
3. 准备Jenkins调用参数
4. 将Jenkins任务加入队列
5. 异步执行所有任务

请求体参数结构与H5接口类似，包含`action_id`、`request_data`等字段，其中`request_data`包含具体的构建参数。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Api as "H5TestPublishApi"
participant Queue as "TaskQueue"
participant Jenkins as "JenkinsCaller"
Client->>Api : POST /api/mobile/build
Api->>Queue : 创建任务队列
Api->>Api : 执行校验阶段
Api->>Queue : 添加Jenkins任务
Queue->>Jenkins : 异步执行构建
Jenkins-->>Queue : 返回构建结果
Queue-->>Api : 汇总执行状态
Api-->>Client : 返回响应
```

**Diagram sources**
- [mobile/app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [task_queue.py](file://task_mgt/task_queue.py#L1-L1009)

**Section sources**
- [mobile/app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)

## 通用流水线触发器设计模式

通用流水线触发器的设计模式在`pipeline/pipeline_view.py`文件中的`PipelineViewSet`类中实现。该设计采用了责任链模式和任务队列模式的组合，实现了灵活的流水线控制机制。

核心设计特点包括：

### 异步任务队列处理机制
系统使用`TaskQueue`类实现异步任务处理，支持多种任务类型：
- 脚本执行 (`script`)
- 接口调用 (`interface`)
- Jenkins任务 (`jenkins`)
- Salt命令 (`salt`)
- 电子邮件 (`email`)

任务通过`enter_queue`方法加入队列，然后通过`async_run`方法异步执行。这种设计实现了任务的解耦和并行处理能力。

### 任务状态轮询接口
系统提供了任务状态轮询机制，通过`get_jenkins_last_status`方法获取Jenkins任务的最新状态。该方法会：
1. 连接到Jenkins服务器
2. 获取任务的构建信息
3. 查询数据库中的执行日志
4. 返回任务状态、最后执行时间和操作者

```mermaid
classDiagram
class TaskQueue {
+task_queue : QueueObject[]
+enter_queue(call_type, business_name, action_id, params)
+async_run()
+run_script(q_obj)
+call_interface(q_obj)
+run_jenkins(q_obj)
+run_salt(q_obj)
+run_email(q_obj)
}
class QueueObject {
+call_type : str
+business_name : str
+action_id : int
+params : dict
+end_mark : bool
}
class TaskTypeObject {
+script : str
+interface : str
+jenkins : str
+salt : str
+email : str
}
TaskQueue --> QueueObject : 包含
TaskQueue --> TaskTypeObject : 使用
```

**Diagram sources**
- [pipeline/pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [task_queue.py](file://task_mgt/task_queue.py#L1-L1009)

**Section sources**
- [pipeline/pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [task_queue.py](file://task_mgt/task_queue.py#L1-L1009)

## 构建参数校验与错误处理

系统实现了完善的构建参数校验机制和错误处理策略，确保构建过程的可靠性和可追溯性。

### 参数校验规则
参数校验在多个层次进行：
1. **基础校验**：检查必填字段是否存在
2. **业务校验**：验证应用与环境的绑定关系
3. **状态校验**：检查并发申请情况

在`PipelineViewSet`类的`__check_app_and_env_bind`方法中，实现了应用与环境绑定关系的校验：
```python
def __check_app_and_env_bind(self, app_name, env_list):
    if not app_name:
        raise Exception('应用名称不能为空')
    not_bind_env_list = []
    for env in env_list:
        obj = get_app_and_env_bind(app_name, env)
        if not obj:
            not_bind_env_list.append(env)
    if not_bind_env_list:
        not_bind_env_str = ','.join(not_bind_env_list)
        return False, not_bind_env_str
    return True, ''
```

### 默认值设置
系统为多个参数设置了合理的默认值：
- `db_exec_type`默认为`increment`
- `is_junit`默认为`False`
- `is_mock_agent`默认为`false`

### 错误码说明
系统定义了标准的错误响应格式，包含以下HTTP状态码：
- `400`: 参数错误，当请求参数不符合要求时返回
- `404`: 应用不存在，当指定的应用未找到时返回
- `500`: 构建系统异常，当内部处理发生错误时返回

错误响应体包含详细的错误信息，便于客户端进行问题诊断。

**Section sources**
- [pipeline/pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)

## Jenkins集成与构建ID回传

系统与Jenkins的集成实现在`pipeline/pipeline_view.py`文件中，通过`JenkinsJobMgt`类和`call_jenkins_job`方法实现。

### job_name映射逻辑
Jenkins job名称的映射遵循以下规则：
- H5应用：`{iterationID}_{appName}`
- 移动端应用：`{iterationID}_{appName}`
- 发布流水线：`{appName}_publish_pipeline`

在`compile_deploy_app`方法中，系统根据迭代ID和应用名称动态生成job名称：
```python
job_name = iteration_id + '_' + app_name
```

### 构建ID回传机制
系统实现了完整的构建ID回传机制，确保构建过程的可追溯性：
1. 调用Jenkins API触发构建，获取`nextBuildNumber`
2. 将构建ID存储在`H5DeployResult`模型中
3. 通过轮询机制监控构建状态
4. 将最终状态更新回数据库

`call_jenkins_job`方法负责Jenkins任务的调用和状态监控：
```python
def call_jenkins_job(self, job_name, is_junit, is_mock_agent, dump_bis_code, db_exec_type=None):
    try:
        jenkins_server = self.__jenkins_job_mgt.get_jenkins_server_by_job_name(job_name).server
        next_build_number = jenkins_server.get_job_info(job_name)['nextBuildNumber']
        # ... 构建调用逻辑
    except Exception as err:
        logger.error(err)
        return 'failed', '未找到jenkins流水线job', '', ''
```

构建完成后，系统会将构建结果（包括构建号、构建URL等）返回给客户端，便于后续的构建状态查询和日志查看。

**Section sources**
- [pipeline/pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [task_queue.py](file://task_mgt/task_queue.py#L1-L1009)

## 客户端调用示例

### Python客户端调用示例

```python
import requests
import json

# H5应用构建请求
def trigger_h5_build():
    url = "http://your-api-server/ci-cd/h5/build"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "Bearer your-jwt-token"
    }
    data = {
        "iterationID": "iter_20231201",
        "appEnv": "test",
        "appNameList": ["h5-app-1", "h5-app-2"],
        "packageType": "dist",
        "cc": ["<EMAIL>"]
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print(response.json())

# 移动端构建请求
def trigger_mobile_build():
    url = "http://your-api-server/ci-cd/mobile/build"
    headers = {
        "Content-Type": "application/json",
        "Authorization": "API-Key your-api-key"
    }
    data = {
        "action_id": 12345,
        "request_data": [
            {
                "iteration_id": "iter_20231201",
                "app_name": "mobile-app",
                "suite_code": "beta",
                "package_type": "android",
                "br_name": "release-1.0"
            }
        ]
    }
    response = requests.post(url, headers=headers, data=json.dumps(data))
    print(response.json())

# 执行构建
trigger_h5_build()
trigger_mobile_build()
```

### curl命令示例

```bash
# 触发H5应用构建
curl -X POST \
  http://your-api-server/ci-cd/h5/build \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer your-jwt-token' \
  -d '{
    "iterationID": "iter_20231201",
    "appEnv": "test",
    "appNameList": ["h5-app-1", "h5-app-2"],
    "packageType": "dist",
    "cc": ["<EMAIL>"]
}'

# 触发移动端构建
curl -X POST \
  http://your-api-server/ci-cd/mobile/build \
  -H 'Content-Type: application/json' \
  -H 'Authorization: API-Key your-api-key' \
  -d '{
    "action_id": 12345,
    "request_data": [
      {
        "iteration_id": "iter_20231201",
        "app_name": "mobile-app",
        "suite_code": "beta",
        "package_type": "android",
        "br_name": "release-1.0"
      }
    ]
  }'

# 查询构建状态
curl -X GET \
  http://your-api-server/ci-cd/status?iteration_id=iter_20231201 \
  -H 'Authorization: Bearer your-jwt-token'
```

**Section sources**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)
- [mobile/app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [pipeline/pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)