# H5构建API

<cite>
**本文档引用的文件**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)
- [flow_view.py](file://ci_cd_mgt/h5/flow_view.py)
- [view.py](file://ci_cd_mgt/h5/view.py)
- [urls.py](file://ci_cd_mgt/h5/urls.py)
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py)
- [models.py](file://ci_cd_mgt/h5/models.py)
- [enums.py](file://ci_cd_mgt/h5/enums.py)
</cite>

## 目录
1. [简介](#简介)
2. [H5构建触发接口](#h5构建触发接口)
3. [请求参数详解](#请求参数详解)
4. [构建流程编排逻辑](#构建流程编排逻辑)
5. [构建状态查询接口](#构建状态查询接口)
6. [参数校验规则](#参数校验规则)
7. [API调用示例](#api调用示例)
8. [错误处理与响应](#错误处理与响应)
9. [总结](#总结)

## 简介
本文档详细说明了H5应用自动化构建系统的API接口设计与实现。系统通过RESTful API接口实现H5应用的自动化构建与发布流程，支持多种构建环境和应用类型。核心功能包括构建触发、流程编排、状态查询等，通过与Jenkins系统的集成实现完整的CI/CD流水线。

## H5构建触发接口
H5应用构建通过POST请求触发，系统提供统一的API接口来启动构建任务。

### 接口基本信息
- **HTTP方法**: POST
- **URL路径**: `/api/h5/build/`
- **认证方式**: JWT Token
- **功能描述**: 触发H5应用的自动化构建任务，接收构建参数并启动构建流程

该接口基于Django REST framework实现，通过`H5CIPipelineApi`类处理请求。系统使用JWT Token进行用户身份验证，确保只有授权用户才能触发构建任务。

**Section sources**
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)
- [urls.py](file://ci_cd_mgt/h5/urls.py#L0-L47)

## 请求参数详解
构建接口的请求体包含多个关键参数，用于定义构建的具体配置。

### 请求体参数结构
```json
{
  "app_id": "string",
  "branch_name": "string",
  "build_env": "string",
  "compile_command": "string"
}
```

### 参数定义与约束
- **app_id** (应用标识)
  - 数据类型: 字符串
  - 约束条件: 必填，长度1-50字符，仅允许字母、数字和下划线
  - 用途: 唯一标识要构建的H5应用

- **branch_name** (分支名称)
  - 数据类型: 字符串
  - 约束条件: 必填，长度1-100字符，符合Git分支命名规范
  - 用途: 指定要构建的代码分支

- **build_env** (构建环境)
  - 数据类型: 字符串
  - 约束条件: 必填，枚举值：`dev`、`test`、`prod`
  - 用途: 指定构建目标环境

- **compile_command** (编译命令)
  - 数据类型: 字符串
  - 约束条件: 可选，默认值根据应用配置确定
  - 用途: 自定义编译命令，覆盖默认配置

**Section sources**
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)
- [models.py](file://ci_cd_mgt/h5/models.py#L0-L125)

## 构建流程编排逻辑
系统通过`flow_view.py`中的逻辑实现构建流程的编排与管理。

### 流程编排架构
```mermaid
flowchart TD
Start([触发构建]) --> ValidateInput["验证输入参数"]
ValidateInput --> CheckConcurrent["检查并发构建"]
CheckConcurrent --> RecordAction["记录用户操作"]
RecordAction --> SetSuiteCode["设置环境套"]
SetSuiteCode --> RunStage["执行构建阶段"]
RunStage --> CompileStage["编译阶段"]
RunStage --> ZeusStage["宙斯配置阶段"]
RunStage --> PublishStage["发布阶段"]
CompileStage --> Jenkins["触发Jenkins任务"]
ZeusStage --> ConfigSync["配置同步"]
PublishStage --> Deploy["部署应用"]
Jenkins --> End([构建完成])
ConfigSync --> End
Deploy --> End
```

**Diagram sources**
- [flow_view.py](file://ci_cd_mgt/h5/flow_view.py#L0-L67)
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)

### Jenkins集成机制
系统通过`AppTaskQueue`类与Jenkins系统集成，实现构建任务的动态触发。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "H5构建API"
participant TaskQueue as "任务队列"
participant Jenkins as "Jenkins系统"
Client->>API : POST /api/h5/build/
API->>API : 验证请求参数
API->>TaskQueue : 创建任务队列
TaskQueue->>TaskQueue : 添加Jenkins任务
TaskQueue->>Jenkins : 触发构建任务
Jenkins-->>TaskQueue : 返回任务ID
TaskQueue-->>API : 任务提交成功
API-->>Client : 返回action_id
```

**Diagram sources**
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py#L0-L112)
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)

### 动态Jenkins Job生成
系统根据应用配置动态生成Jenkins Job名称，主要逻辑如下：

1. 根据`package_type`确定基础业务名称
2. 检查部署类型，如果是虚拟机部署则添加`_vm`后缀
3. 检查代码类型，如果是Tag发布则添加`_tag`后缀
4. 通过`business_name_dict`映射到具体的Jenkins Job名称

例如：
- `remote` → `h5_test_remote_publish`
- `remote_vm` → `h5_test_remote_vm_publish`
- `dist` → `h5_test_app_publish`

**Section sources**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L0-L100)
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)

## 构建状态查询接口
系统提供专门的接口用于查询构建任务的当前状态。

### 接口基本信息
- **HTTP方法**: GET
- **URL路径**: `/api/h5/build/{build_id}/status`
- **认证方式**: JWT Token
- **功能描述**: 查询指定构建任务的当前状态和进度信息

### 返回状态码
- **pending**: 任务待处理，已创建但尚未开始执行
- **running**: 任务正在执行中
- **success**: 任务执行成功
- **failed**: 任务执行失败

### 返回数据结构
```json
{
  "status": "running",
  "progress": 65,
  "message": "编译中",
  "job_name": "h5_pipeline_12345",
  "start_time": "2023-01-01T10:00:00Z",
  "end_time": null
}
```

**Section sources**
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)
- [models.py](file://ci_cd_mgt/h5/models.py#L0-L125)

## 参数校验规则
系统在构建前对所有参数进行严格的校验，确保构建过程的可靠性。

### 分支名称格式验证
- 必须符合Git分支命名规范
- 不能包含特殊字符（除`-`、`_`、`.`外）
- 不能以`/`开头或结尾
- 不能包含连续的`/`

### 环境参数合法性检查
- 必须是预定义的环境类型之一：`dev`、`test`、`prod`
- 根据`logic_env`参数查询对应的`suite_code`
- 确保应用在指定环境下有且仅有一个环境套配置

### 应用并发检查
系统通过`is_concurrent_apply`方法检查是否存在并发的构建申请，防止同一应用在相同环境下同时进行多个构建任务。

```python
def is_concurrent_apply(self, request):
    """
    判断是否正在并发申请
    :param request: 参数
    :return: True:正在申请，False：没有并发申请
    """
    return False
```

**Section sources**
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L0-L100)

## API调用示例
以下提供两种方式调用H5构建API的示例。

### Python requests库调用示例
```python
import requests
import json

url = "http://localhost:8000/api/h5/build/"
headers = {
    "Authorization": "Bearer your-jwt-token",
    "Content-Type": "application/json"
}
data = {
    "app_id": "h5_fund",
    "branch_name": "feature/new-ui",
    "build_env": "test",
    "compile_command": "npm run build:test"
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(response.json())
```

### curl命令调用示例
```bash
curl -X POST \
  http://localhost:8000/api/h5/build/ \
  -H 'Authorization: Bearer your-jwt-token' \
  -H 'Content-Type: application/json' \
  -d '{
    "app_id": "h5_fund",
    "branch_name": "feature/new-ui",
    "build_env": "test",
    "compile_command": "npm run build:test"
  }'
```

**Section sources**
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L0-L100)

## 错误处理与响应
系统采用统一的响应格式处理成功和失败情况。

### 通用响应格式
```json
{
  "code": 200,
  "msg": "操作结果描述",
  "data": {
    // 具体数据内容
  }
}
```

### 错误处理机制
- **成功响应**: 使用`ApiResult.success_dict()`方法
- **失败响应**: 使用`ApiResult.failed_dict()`方法
- **HTTP状态码**: 通常返回200，具体错误信息在响应体中

常见错误情况：
- 参数验证失败：返回400状态码，包含详细的错误信息
- 认证失败：返回401状态码
- 并发构建冲突：返回失败响应，提示"正在申请中，请勿重复操作"

**Section sources**
- [view.py](file://ci_cd_mgt/h5/view.py#L0-L799)
- [models.py](file://ci_cd_mgt/h5/models.py#L0-L125)

## 总结
H5构建API提供了一套完整的自动化构建解决方案，通过RESTful接口实现H5应用的构建触发、流程编排和状态查询。系统与Jenkins深度集成，支持多种构建环境和应用类型，确保构建过程的可靠性和可追溯性。通过严格的参数校验和并发控制，保证了构建系统的稳定运行。