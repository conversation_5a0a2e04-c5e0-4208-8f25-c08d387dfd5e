# CI/CD流水线API

<cite>
**本文档引用的文件**  
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)
- [pipeline_view.py](file://pipeline/pipeline_view.py)
- [view.py](file://ci_cd_mgt/h5/view.py)
- [enums.py](file://ci_cd_mgt/h5/enums.py)
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py)
- [hm_jenkins_info.py](file://ci_cd_mgt/h5/hm_jenkins_info.py)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概览](#架构概览)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排查指南](#故障排查指南)
9. [结论](#结论)

## 简介
本文档旨在为CI/CD流水线系统提供全面的API文档，涵盖构建触发、流水线配置、部署执行等关键接口。重点说明`app_ci_pipeline`和`pipeline_view`中构建触发器的配置参数，包括代码分支、构建环境、触发条件等。详细描述流水线状态查询接口的响应格式和轮询机制，提供构建日志获取和实时流式输出的实现方式。特别说明移动端和H5平台特有的构建参数配置，如编译命令、打包选项等。解释与Jenkins系统的集成机制和任务ID映射关系，并包含对自定义流水线脚本的支持说明和安全沙箱限制。

## 项目结构
CI/CD管理系统主要由H5和移动端两个平台构成，分别位于`ci_cd_mgt/h5`和`ci_cd_mgt/mobile`目录下。核心功能包括构建触发、状态查询、日志获取和部署执行。系统通过`pipeline`模块与Jenkins集成，实现自动化构建和部署。`task_mgt`模块负责任务队列管理和异步执行。

```mermaid
graph TB
subgraph "CI/CD管理"
H5[h5模块]
Mobile[mobile模块]
end
subgraph "核心服务"
Pipeline[pipeline模块]
TaskMgt[task_mgt模块]
JenkinsMgt[jenkins_mgt模块]
end
H5 --> Pipeline
Mobile --> Pipeline
Pipeline --> TaskMgt
Pipeline --> JenkinsMgt
```

**图示来源**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)
- [pipeline_view.py](file://pipeline/pipeline_view.py)
- [jenkins_job_mgt.py](file://jenkins_mgt/jenkins_job_mgt.py)

**本节来源**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py)
- [pipeline_view.py](file://pipeline/pipeline_view.py)

## 核心组件
系统核心组件包括构建触发器、状态查询服务、日志获取接口和部署执行器。`AppCIPipeline`类负责处理H5应用的CI/CD流程，`PipelineViewSet`提供通用的流水线操作接口。`H5CIPipelineApi`基类定义了H5平台的公共API方法，而`H5CompileApi`和`H5MiniCompileApi`分别处理普通编译和小程序编译。

**本节来源**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [view.py](file://ci_cd_mgt/h5/view.py#L1-L799)

## 架构概览
系统采用分层架构，前端通过REST API调用后端服务，后端服务通过任务队列异步执行Jenkins构建任务。状态查询接口定期轮询Jenkins服务器获取构建状态，并通过数据库记录构建结果。日志获取接口从Jenkins服务器流式读取构建日志并实时返回给客户端。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "API服务"
participant TaskQueue as "任务队列"
participant Jenkins as "Jenkins服务器"
Client->>API : 触发构建请求
API->>TaskQueue : 创建构建任务
TaskQueue->>Jenkins : 调用Jenkins Job
Jenkins-->>TaskQueue : 返回构建ID
TaskQueue-->>API : 确认任务创建
API-->>Client : 返回任务ID
loop 轮询状态
Client->>API : 查询构建状态
API->>Jenkins : 获取Jenkins构建状态
Jenkins-->>API : 返回状态信息
API-->>Client : 返回状态
end
loop 获取日志
Client->>API : 请求构建日志
API->>Jenkins : 流式读取日志
Jenkins-->>API : 返回日志片段
API-->>Client : 流式返回日志
end
```

**图示来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [view.py](file://ci_cd_mgt/h5/view.py#L1-L799)

## 详细组件分析

### 构建触发器分析
构建触发器负责接收构建请求，验证参数，并将构建任务加入队列。`AppCIPipeline`类的`run_stage`方法处理测试发布阶段的执行，`run_publish_apply_stage`方法处理发布申请阶段的执行。

```mermaid
flowchart TD
Start([接收构建请求]) --> ValidateParams["验证请求参数"]
ValidateParams --> CheckStage["执行检查任务"]
CheckStage --> PrepareJob["准备Jenkins Job参数"]
PrepareJob --> QueueJob["将任务加入队列"]
QueueJob --> RunAsync["异步执行任务"]
RunAsync --> End([返回任务ID])
```

**图示来源**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)
- [view.py](file://ci_cd_mgt/h5/view.py#L1-L799)

**本节来源**
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)
- [view.py](file://ci_cd_mgt/h5/view.py#L1-L799)

### 状态查询接口分析
状态查询接口提供流水线执行状态的实时查询功能。`PipelineViewSet`的`get_jenkins_last_status`方法从Jenkins服务器获取最新的构建状态，并结合本地数据库记录返回完整状态信息。

```mermaid
classDiagram
class PipelineStatus {
+string success
+string failure
+string running
+string aborted
+string unstable
}
class PipelineViewSet {
+get_jenkins_last_status(job_name)
+call_jenkins_job(job_name, params)
+create_test_skip_file(job_name)
}
class JenkinsJobMgt {
+get_jenkins_server_by_job_name(job_name)
+get_job_config(job_name)
+reconfig_job(job_name, config)
}
PipelineViewSet --> JenkinsJobMgt : "使用"
PipelineViewSet --> PipelineStatus : "返回"
```

**图示来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [jenkins_job_mgt.py](file://jenkins_mgt/jenkins_job_mgt.py)

**本节来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

### 日志获取机制分析
日志获取机制支持实时流式输出构建日志。系统通过SSH连接到Jenkins服务器，实时读取构建日志文件并流式返回给客户端，实现构建过程的实时监控。

```mermaid
flowchart TD
ClientRequest["客户端请求日志"] --> CheckStatus["检查构建状态"]
CheckStatus --> IsRunning{"构建是否运行?"}
IsRunning --> |是| StreamLog["流式读取日志文件"]
IsRunning --> |否| ReadFinalLog["读取完整日志"]
StreamLog --> FormatLog["格式化日志输出"]
ReadFinalLog --> FormatLog
FormatLog --> SendToClient["发送日志到客户端"]
SendToClient --> End([完成])
```

**图示来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [ssh_cnt.py](file://public/ssh_cnt.py)

**本节来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)

## 依赖分析
系统依赖于Jenkins作为构建引擎，通过`jenkins_mgt`模块与Jenkins服务器交互。`task_mgt`模块提供任务队列管理，确保构建任务的有序执行。`public`模块提供通用工具，如SSH连接、邮件发送等。

```mermaid
graph LR
A[CI/CD API] --> B[jenkins_mgt]
A --> C[task_mgt]
A --> D[public]
B --> E[Jenkins服务器]
C --> F[任务队列]
D --> G[SSH连接]
D --> H[邮件服务]
```

**图示来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [task_queue.py](file://task_mgt/task_queue.py)
- [ssh_cnt.py](file://public/ssh_cnt.py)

**本节来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [task_queue.py](file://task_mgt/task_queue.py)

## 性能考虑
系统采用异步任务队列机制，避免阻塞API请求。状态查询接口采用轮询机制，建议客户端合理设置轮询间隔，避免对服务器造成过大压力。日志流式输出采用分块传输，确保大日志文件的高效传输。

## 故障排查指南
常见问题包括构建任务未触发、状态查询失败、日志获取超时等。排查时应首先检查Jenkins服务器连接状态，然后验证任务参数是否正确，最后检查数据库记录是否完整。

**本节来源**
- [pipeline_view.py](file://pipeline/pipeline_view.py#L1-L403)
- [app_ci_pipeline.py](file://ci_cd_mgt/h5/app_ci_pipeline.py#L1-L100)
- [view.py](file://ci_cd_mgt/h5/view.py#L1-L799)

## 结论
本文档详细介绍了CI/CD流水线系统的API设计和实现机制。系统通过模块化设计，实现了构建触发、状态查询、日志获取和部署执行的完整流程。通过与Jenkins的深度集成，提供了稳定可靠的自动化构建服务。移动端和H5平台的特殊需求通过专门的API接口得到满足，确保了不同平台的构建需求都能被有效支持。