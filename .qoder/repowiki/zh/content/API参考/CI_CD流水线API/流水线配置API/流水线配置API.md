# 流水线配置API

<cite>
**本文档引用文件**  
- [models.py](file://pipeline/models.py)
- [pipeline_ser.py](file://pipeline/pipeline_ser.py)
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [流水线配置数据模型设计](#流水线配置数据模型设计)
3. [流水线配置序列化器](#流水线配置序列化器)
4. [与迭代管理模块的集成](#与迭代管理模块的集成)
5. [流水线模板管理接口](#流水线模板管理接口)
6. [环境变量配置接口](#环境变量配置接口)
7. [触发条件配置](#触发条件配置)
8. [配置JSON Schema示例](#配置json-schema示例)
9. [最佳实践建议](#最佳实践建议)
10. [附录](#附录)

## 简介
本文档全面介绍流水线配置API的设计与实现，重点说明流水线配置数据模型、序列化器、与迭代管理模块的集成方式，以及相关接口的使用方法。文档涵盖构建阶段、测试阶段、部署阶段的配置参数，提供配置JSON Schema示例和最佳实践建议，帮助开发者更好地理解和使用流水线配置系统。

## 流水线配置数据模型设计

流水线配置数据模型主要定义了流水线执行过程中的各种实体和关系，包括环境绑定、操作日志、执行主日志和步骤日志等。这些模型为流水线的执行、监控和审计提供了数据基础。

### 环境绑定模型
`EnvBind` 模型用于记录流水线与应用、环境之间的绑定关系，是流水线执行的基础配置。

**模型字段：**
- `pipeline_id`: 迭代版本，最大长度48字符
- `app_name`: 应用名，最大长度255字符
- `env`: 环境名，最大长度255字符
- `operator`: 操作者，最大长度18字符

该模型对应数据库表 `pipeline_env_bind`，用于存储流水线环境绑定信息。

### 操作日志模型
`LogOpt` 模型用于记录流水线的操作日志，包括操作者、操作类型、操作时间等信息。

**模型字段：**
- `sid`: 主键，自增
- `opt_user`: 操作者，最大长度50字符
- `opt_type`: 操作类型，最大长度50字符
- `job_name`: job名称，最大长度50字符
- `opt_time`: 操作时间
- `pipeline_id`: 流水线ID，最大长度200字符
- `opt_info_str`: 操作日志，最大长度200字符

该模型对应数据库表 `pipeline_log_opt`，用于存储流水线操作日志。

### 执行主日志模型
`LogMain` 模型用于记录流水线执行的主日志，包括执行参数、执行状态、开始和结束时间等。

**模型字段：**
- `sid`: 脚本执行ID，主键，自增
- `exec_jenkins`: 执行jenkins，IP地址类型
- `exec_parameter`: 执行参数，文本类型
- `start_at`: 执行开始时间，可为空
- `iteration_id`: 迭代号，最大长度50字符
- `app_name`: 应用名称，最大长度50字符
- `end_at`: 执行结束时间，可为空
- `status`: 执行状态，枚举类型，包括"未执行"、"执行中"、"执行成功"、"执行失败"
- `suite_name`: 环境套，最大长度50字符

该模型对应数据库表 `pipeline_log_main`，用于存储流水线主日志。

### 步骤日志模型
`LogMinor` 模型用于记录流水线执行的步骤日志，是 `LogMain` 模型的子表。

**模型字段：**
- `sid`: 外键，关联 `LogMain` 模型的 `sid` 字段，级联删除
- `step`: 执行步骤名称，最大长度50字符
- `status`: 执行状态，枚举类型，包括"执行中"、"执行成功"、"执行失败"
- `log`: 详细日志，文本类型，可为空
- `start_at`: 执行开始时间，可为空
- `end_at`: 执行结束时间，可为空
- `module_name`: 模块名，最大长度50字符

该模型对应数据库表 `pipeline_log_minor`，用于存储流水线步骤日志。

```mermaid
erDiagram
pipeline_env_bind {
string pipeline_id PK
string app_name
string env
string operator
}
pipeline_log_opt {
int sid PK
string opt_user
string opt_type
string job_name
datetime opt_time
string pipeline_id
string opt_info_str
}
pipeline_log_main {
int sid PK
string exec_jenkins
string exec_parameter
datetime start_at
string iteration_id
string app_name
datetime end_at
string status
string suite_name
}
pipeline_log_minor {
int sid FK
string step
string status
text log
datetime start_at
datetime end_at
string module_name
}
pipeline_env_bind ||--o{ pipeline_log_main : "通过pipeline_id关联"
pipeline_log_main ||--o{ pipeline_log_minor : "一对多关系"
pipeline_log_main ||--o{ pipeline_log_opt : "通过pipeline_id关联"
```

**Diagram sources**
- [models.py](file://pipeline/models.py#L1-L130)

**Section sources**
- [models.py](file://pipeline/models.py#L1-L130)

## 流水线配置序列化器

`pipeline_ser.py` 文件中的序列化器和工具函数负责处理流水线配置的验证、默认值设置和数据转换。这些函数确保了流水线配置的正确性和一致性。

### 字段验证规则
流水线配置序列化器通过一系列工具函数实现字段验证，确保输入数据的完整性和正确性。

**主要验证规则：**
- `pipeline_id` 必须存在且不为空
- `app_name` 必须存在且不为空
- `br_name` 必须存在且不为空
- `node_list` 必须存在且不为空

### 默认值设置
序列化器在处理流水线配置时会自动设置一些默认值，简化用户的配置工作。

**主要默认值：**
- `exec_parameter`: 从输入参数中提取并格式化执行参数
- `status`: 默认为"未执行"
- `start_at`: 默认为当前时间
- `end_at`: 默认为空

### 核心序列化函数
`pipeline_ser.py` 提供了多个核心函数用于处理流水线配置。

**主要函数：**
- `test_publish_node_list`: 处理测试发布节点列表，启动SSH连接执行发布任务
- `get_opt_his_info`: 获取操作历史信息
- `get_mypipeline_mock_info`: 获取我的流水线mock信息
- `get_pipeline_env_info`: 获取流水线页面可选用环境
- `send_to_jenkins_for_ptp`: 发送任务到Jenkins执行
- `get_app_and_env_bind`: 获取应用和环境绑定信息
- `get_app_suite_bind`: 获取应用和环境套绑定信息

这些函数通过数据库查询和业务逻辑处理，为流水线配置提供了丰富的数据支持。

```mermaid
flowchart TD
Start([开始]) --> ValidateInput["验证输入参数"]
ValidateInput --> InputValid{"参数有效?"}
InputValid --> |否| ReturnError["返回错误信息"]
InputValid --> |是| ProcessData["处理流水线配置数据"]
ProcessData --> SetDefaults["设置默认值"]
SetDefaults --> ValidateFields["验证字段"]
ValidateFields --> FieldsValid{"字段验证通过?"}
FieldsValid --> |否| ReturnError
FieldsValid --> |是| TransformData["转换数据格式"]
TransformData --> SaveConfig["保存配置"]
SaveConfig --> End([结束])
ReturnError --> End
```

**Diagram sources**
- [pipeline_ser.py](file://pipeline/pipeline_ser.py#L0-L534)

**Section sources**
- [pipeline_ser.py](file://pipeline/pipeline_ser.py#L0-L534)

## 与迭代管理模块的集成

流水线配置系统与迭代管理模块深度集成，能够自动获取当前迭代信息并填充流水线参数，实现了迭代与流水线的无缝衔接。

### 获取当前迭代信息
通过 `iter_mgt_ser.py` 中的 `get_iter_info` 函数，流水线系统可以获取当前用户的迭代信息。

**函数参数：**
- `user`: 用户名
- `project_type`: 项目类型（server、h5、py）
- `br_status`: 迭代状态（open、close）

**返回数据：**
- `pipeline_id`: 迭代号
- `br_style`: 迭代风格
- `project_group`: 项目组
- `br_start_date`: 开始日期
- `duedate`: 截止日期
- `description`: 描述
- `tapd_id`: TAPD ID
- `br_name`: 分支名称

### 自动填充流水线参数
流水线系统利用迭代信息自动填充流水线参数，减少用户手动配置的工作量。

**自动填充逻辑：**
1. 根据用户和项目类型获取当前迭代列表
2. 选择状态为"open"的最新迭代
3. 从迭代信息中提取 `pipeline_id`、`br_name` 等参数
4. 将这些参数作为流水线配置的默认值

### 集成实现方式
集成主要通过数据库查询和API调用实现，确保数据的一致性和实时性。

**主要集成点：**
- `get_iter_info`: 获取迭代信息
- `get_repos_info`: 获取迭代下的仓库信息
- `get_iter_repos_info`: 获取迭代下的应用信息
- `get_product_info`: 获取产品信息

这些函数通过复杂的SQL查询，从多个表中关联获取所需信息，为流水线配置提供数据支持。

```mermaid
sequenceDiagram
participant User as "用户"
participant Pipeline as "流水线系统"
participant IterMgt as "迭代管理系统"
participant DB as "数据库"
User->>Pipeline : 请求创建流水线
Pipeline->>IterMgt : 调用get_iter_info获取迭代信息
IterMgt->>DB : 执行SQL查询
DB-->>IterMgt : 返回迭代数据
IterMgt-->>Pipeline : 返回迭代信息
Pipeline->>Pipeline : 自动填充流水线参数
Pipeline-->>User : 显示预填充的流水线配置
```

**Diagram sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L0-L799)

**Section sources**
- [iter_mgt_ser.py](file://iter_mgt/iter_mgt_ser.py#L0-L799)

## 流水线模板管理接口

流水线模板管理接口提供了创建、更新、删除和查询流水线模板的功能，支持流水线配置的复用和标准化。

### 创建流水线模板
创建流水线模板接口用于定义新的流水线配置模板。

**请求参数：**
- `template_name`: 模板名称
- `description`: 模板描述
- `config`: 模板配置，JSON格式
- `creator`: 创建者

**响应数据：**
- `template_id`: 模板ID
- `template_name`: 模板名称
- `description`: 模板描述
- `config`: 模板配置
- `creator`: 创建者
- `create_time`: 创建时间

### 更新流水线模板
更新流水线模板接口用于修改现有模板的配置。

**请求参数：**
- `template_id`: 模板ID
- `description`: 新的描述
- `config`: 新的配置
- `updater`: 更新者

**响应数据：**
- `success`: 更新是否成功
- `message`: 更新结果信息

### 删除流水线模板
删除流水线模板接口用于移除不再使用的模板。

**请求参数：**
- `template_id`: 模板ID
- `deleter`: 删除者

**响应数据：**
- `success`: 删除是否成功
- `message`: 删除结果信息

### 查询流水线模板
查询流水线模板接口用于获取模板列表或特定模板的详细信息。

**请求参数：**
- `template_id`: 可选，模板ID
- `template_name`: 可选，模板名称
- `page_num`: 页码
- `page_size`: 每页大小

**响应数据：**
- `total`: 总数
- `page_num`: 页码
- `page_size`: 每页大小
- `data`: 模板列表

## 环境变量配置接口

环境变量配置接口用于管理流水线执行过程中的环境变量，支持不同环境的差异化配置。

### 配置环境变量
配置环境变量接口用于设置流水线的环境变量。

**请求参数：**
- `pipeline_id`: 流水线ID
- `env`: 环境名称
- `variables`: 环境变量，键值对格式
- `updater`: 更新者

**响应数据：**
- `success`: 配置是否成功
- `message`: 配置结果信息

### 获取环境变量
获取环境变量接口用于查询特定流水线在特定环境下的环境变量。

**请求参数：**
- `pipeline_id`: 流水线ID
- `env`: 环境名称

**响应数据：**
- `variables`: 环境变量
- `update_time`: 更新时间

### 环境变量作用域
环境变量具有不同的作用域，决定了其生效范围。

**作用域类型：**
- 全局环境变量：对所有流水线生效
- 项目环境变量：对特定项目的所有流水线生效
- 流水线环境变量：仅对特定流水线生效
- 阶段环境变量：仅对特定阶段生效

## 触发条件配置

触发条件配置允许用户定义流水线的自动触发规则，支持多种触发方式。

### 定时触发
定时触发使用cron表达式定义触发时间。

**配置参数：**
- `cron_expression`: cron表达式
- `timezone`: 时区
- `enabled`: 是否启用

**示例：**
```
0 0 2 * * ?  # 每天凌晨2点触发
```

### 代码提交触发
代码提交触发在代码仓库有新提交时自动触发流水线。

**配置参数：**
- `repo_url`: 仓库URL
- `branch`: 分支名称
- `events`: 触发事件（push、pull_request等）
- `enabled`: 是否启用

### 手动触发
手动触发允许用户通过界面或API手动启动流水线。

**配置参数：**
- `allowed_users`: 允许触发的用户列表
- `require_approval`: 是否需要审批
- `approval_users`: 审批用户列表

### 触发条件优先级
当多个触发条件同时满足时，系统按照预定义的优先级执行。

**优先级顺序：**
1. 手动触发
2. 代码提交触发
3. 定时触发

## 配置JSON Schema示例

以下是一个完整的流水线配置JSON Schema示例，展示了所有配置项的结构和类型。

```json
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "title": "Pipeline Configuration",
  "type": "object",
  "properties": {
    "pipeline_id": {
      "type": "string",
      "description": "流水线ID"
    },
    "app_name": {
      "type": "string",
      "description": "应用名称"
    },
    "br_name": {
      "type": "string",
      "description": "分支名称"
    },
    "env": {
      "type": "string",
      "description": "环境名称"
    },
    "build": {
      "type": "object",
      "properties": {
        "enabled": {
          "type": "boolean",
          "description": "是否启用构建"
        },
        "command": {
          "type": "string",
          "description": "构建命令"
        },
        "timeout": {
          "type": "integer",
          "description": "超时时间（秒）"
        }
      },
      "required": ["enabled"]
    },
    "test": {
      "type": "object",
      "properties": {
        "enabled": {
          "type": "boolean",
          "description": "是否启用测试"
        },
        "suite": {
          "type": "string",
          "description": "测试套件"
        },
        "coverage_threshold": {
          "type": "number",
          "description": "代码覆盖率阈值"
        }
      },
      "required": ["enabled"]
    },
    "deploy": {
      "type": "object",
      "properties": {
        "enabled": {
          "type": "boolean",
          "description": "是否启用部署"
        },
        "target_env": {
          "type": "string",
          "description": "目标环境"
        },
        "rollback_enabled": {
          "type": "boolean",
          "description": "是否启用回滚"
        }
      },
      "required": ["enabled"]
    },
    "triggers": {
      "type": "array",
      "items": {
        "type": "object",
        "properties": {
          "type": {
            "type": "string",
            "enum": ["cron", "git", "manual"]
          },
          "config": {
            "type": "object"
          }
        }
      }
    },
    "environment_variables": {
      "type": "object",
      "additionalProperties": {
        "type": "string"
      }
    }
  },
  "required": ["pipeline_id", "app_name", "br_name", "env", "build", "test", "deploy"]
}
```

## 最佳实践建议

### 确保敏感信息加密存储
敏感信息如密码、密钥等应加密存储，避免明文暴露。

**实现方式：**
- 使用加密服务对敏感字段进行加密
- 在数据库中存储加密后的值
- 在需要使用时进行解密

### 实施配置版本控制
流水线配置应纳入版本控制系统，便于追踪变更和回滚。

**实现方式：**
- 将流水线配置文件存储在Git仓库中
- 为每次配置变更创建新的提交
- 使用分支管理不同环境的配置
- 通过Pull Request进行配置变更评审

### 使用模板提高一致性
通过模板管理流水线配置，确保团队内配置的一致性。

**实现方式：**
- 创建标准化的流水线模板
- 根据项目类型选择合适的模板
- 允许基于模板进行个性化调整
- 定期评审和更新模板

### 监控和告警
建立完善的监控和告警机制，及时发现和处理流水线问题。

**实现方式：**
- 监控流水线执行状态和耗时
- 设置关键指标的告警阈值
- 集成通知系统，及时通知相关人员
- 定期生成流水线执行报告

## 附录

### 术语表
- **流水线(Pipeline)**: 一组自动化的构建、测试和部署步骤
- **迭代(Iteration)**: 软件开发的一个周期，通常包含需求、开发、测试和发布
- **环境(Environment)**: 应用运行的上下文，如开发、测试、生产环境
- **模板(Template)**: 可复用的配置模式，用于快速创建相似的配置

### 参考资料
- [Jenkins Pipeline文档](https://www.jenkins.io/doc/book/pipeline/)
- [JSON Schema规范](https://json-schema.org/)
- [REST API设计指南](https://restfulapi.net/)