# 移动端部署API

<cite>
**本文档引用文件**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py)
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py)
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py)
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py)
- [h5/view.py](file://ci_cd_mgt/h5/view.py)
</cite>

## 目录
1. [简介](#简介)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)
10. [附录](#附录)（如有必要）

## 简介
本文档详细说明了移动端部署API的设计与实现，重点解析iOS和Android应用部署的接口逻辑。文档涵盖移动端特有的部署参数、CI信息处理服务、构建集成机制以及完整的部署流程状态机。通过分析app_view.py和ci_info_ser.py等核心文件，阐述了移动端部署的关键环节，包括包名校验、版本递增规则、渠道包生成和签名验证等。

## 项目结构
移动端部署相关功能主要位于ci_cd_mgt/mobile目录下，包含视图层、服务层和CI信息处理模块。该结构实现了与Jenkins构建系统的集成，支持移动端特有的部署需求。

```mermaid
graph TD
subgraph "移动端部署模块"
app_view["app_view.py<br/>部署接口视图"]
ci_info_ser["ci_info_ser.py<br/>CI信息处理服务"]
express_service_ser["express_service_ser.py<br/>快速构建服务"]
express_service_view["express_service_view.py<br/>服务视图"]
end
subgraph "基础依赖"
h5_view["h5/view.py<br/>H5 CI管道基类"]
task_queue["task_mgt/task_queue.py<br/>任务队列管理"]
DeployResult["models.py<br/>部署结果模型"]
end
app_view --> h5_view
app_view --> task_queue
ci_info_ser --> DeployResult
express_service_ser --> DeployResult
express_service_view --> express_service_ser
```

**图示来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py#L1-L27)
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L56)
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py#L1-L20)
- [h5/view.py](file://ci_cd_mgt/h5/view.py)

**本节来源**  
- [ci_cd_mgt/mobile](file://ci_cd_mgt/mobile)

## 核心组件
移动端部署系统的核心组件包括H5TestPublishApi类，负责处理iOS和Android应用的测试发布流程。该组件继承自H5CIPipelineApi，复用其CI管道功能，同时针对移动端特性进行定制化扩展。关键业务逻辑包括校验阶段、发布阶段和Jenkins任务调用。

**本节来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [h5/view.py](file://ci_cd_mgt/h5/view.py)

## 架构概述
移动端部署架构采用分层设计模式，上层为视图层处理HTTP请求，中层为服务层实现业务逻辑，底层为数据访问层与数据库交互。系统通过TaskQueue与Jenkins集成，实现异步构建任务调度。

```mermaid
graph TB
Client[客户端] --> API[移动端部署API]
API --> Service[业务服务层]
Service --> CI[CI/CD系统]
CI --> Jenkins[Jenkins构建系统]
CI --> Database[(数据库)]
Database --> Service
Service --> API
API --> Client
subgraph "核心流程"
Validation[校验阶段] --> Build[构建阶段]
Build --> Publish[发布阶段]
Publish --> Callback[结果回调]
end
```

**图示来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py#L1-L27)

## 详细组件分析

### H5TestPublishApi分析
H5TestPublishApi是移动端部署的核心控制器，负责协调整个发布流程。该类通过action_item和business_name_dict属性定义了移动端特有的业务标识和名称映射关系。

#### 类图
```mermaid
classDiagram
class H5TestPublishApi {
+string action_item
+dict business_name_dict
+run_stage(user, action_id, request_data) void
+check_stage(action_id, request_data, task_queue) void
+ci_publish_info(request_data, action_id) dict
}
class H5CIPipelineApi {
+run_stage(user, action_id, request_data) void
+check_stage(action_id, request_data, task_queue) void
}
H5TestPublishApi --> H5CIPipelineApi : "继承"
H5TestPublishApi --> TaskQueue : "使用"
H5TestPublishApi --> DeployResult : "使用"
```

**图示来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [h5/view.py](file://ci_cd_mgt/h5/view.py)

**本节来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)

### CI信息处理服务分析
ci_info_ser.py模块负责处理移动端CI信息，通过SQL查询获取构建结果和部署状态。该服务直接与数据库交互，为上层应用提供构建历史和状态查询功能。

#### 数据流图
```mermaid
flowchart TD
Start([开始]) --> Query["构建结果查询"]
Query --> Join["RIGHT JOIN task_mgt_deploy_result"]
Join --> Join2["RIGHT JOIN task_mgt_jenkins_results"]
Join2 --> Subquery["子查询: 获取最新构建记录"]
Subquery --> Filter["过滤条件: pipeline_id和package_type"]
Filter --> Group["GROUP BY app_name"]
Group --> Result["返回结果"]
Result --> End([结束])
```

**图示来源**  
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py#L1-L27)

**本节来源**  
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py#L1-L27)

### 快速构建服务分析
AppExpressBuildServiceSer服务提供移动端快速构建功能，支持根据H5平台代码查询关联的iOS和Android应用在线版本信息。

#### 序列图
```mermaid
sequenceDiagram
participant Client as "客户端"
participant View as "ExpressServiceView"
participant Service as "ExpressServiceSer"
participant DB as "数据库"
Client->>View : GET /app_express_build
View->>Service : get_app_online_version()
Service->>DB : 执行SQL查询
DB-->>Service : 返回构建记录
Service-->>View : 返回应用版本信息
View-->>Client : 返回API响应
```

**图示来源**  
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L56)
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py#L1-L20)

**本节来源**  
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L56)
- [express_service_view.py](file://ci_cd_mgt/mobile/express_service_view.py#L1-L20)

## 依赖分析
移动端部署模块依赖于多个核心组件，形成完整的CI/CD链条。主要依赖关系包括与H5 CI管道基类的继承关系、与任务队列系统的集成、以及与数据库的直接交互。

```mermaid
graph TD
app_view --> h5_view : "继承"
app_view --> task_queue : "使用"
ci_info_ser --> database : "直接查询"
express_service_ser --> database : "直接查询"
express_service_view --> express_service_ser : "依赖"
task_queue --> jenkins : "调用"
style app_view fill:#f9f,stroke:#333
style ci_info_ser fill:#f9f,stroke:#333
style express_service_ser fill:#f9f,stroke:#333
```

**图示来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py#L1-L27)
- [express_service_ser.py](file://ci_cd_mgt/mobile/express_service_ser.py#L1-L56)

**本节来源**  
- [ci_cd_mgt/mobile](file://ci_cd_mgt/mobile)

## 性能考虑
移动端部署系统在设计时考虑了性能优化，采用异步任务处理机制（async_run）避免阻塞主线程。SQL查询经过优化，使用索引和适当的数据过滤条件。对于频繁查询的构建信息，建议增加缓存层以提高响应速度。

## 故障排除指南
常见问题包括构建任务无法启动、版本信息查询失败等。排查时应首先检查Jenkins连接状态，然后验证数据库查询语句的正确性，最后确认任务队列配置是否正确。日志记录在spider.settings.logger中，可用于追踪问题根源。

**本节来源**  
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L24)
- [ci_info_ser.py](file://ci_cd_mgt/mobile/ci_info_ser.py#L1-L27)

## 结论
移动端部署API通过模块化设计实现了iOS和Android应用的自动化部署。系统充分利用现有H5 CI基础设施，同时针对移动端特性进行扩展。未来可考虑增加更多移动端特有的功能，如热更新支持、灰度发布控制等。

## 附录
### 移动端部署参数说明
- **bundle_id**: iOS应用包标识符
- **package_name**: Android应用包名
- **signing_config**: 签名配置
- **channel**: 渠道标识
- **package_type**: 包类型（ios-global, android-global等）

### 典型部署场景
1. 常规版本发布
2. 热更新部署
3. 渠道包批量生成
4. 内测分发
5. 应用商店发布