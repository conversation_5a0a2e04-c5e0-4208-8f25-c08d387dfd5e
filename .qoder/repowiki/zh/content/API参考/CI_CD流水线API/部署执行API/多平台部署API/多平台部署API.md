# 多平台部署API

<cite>
**本文档引用文件**  
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py)
- [app_publish_views.py](file://publish/app_publish_views.py)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档旨在提供一个多平台部署API的技术说明，涵盖H5、移动端和服务器端三种部署场景的接口设计与实现。重点解析各平台特有的部署参数、环境配置要求、部署包格式规范，以及平台间共用的核心参数如deploy_id、version_tag、operator等。

## 项目结构
本项目采用模块化设计，主要分为以下几个部分：CI/CD管理、发布管理、环境管理、任务队列等。每个模块负责不同的功能，确保部署流程的高效与稳定。

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py#L1-L103)
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L799)

## 核心组件
### H5部署结果记录
`deploy_result.py` 文件中的 `DeployResult` 类负责H5部署结果的记录与状态流转。通过 `insert_status_data` 和 `update_deploy_result_data_by_id` 方法，实现了部署状态的插入与更新。

### 移动端应用部署
`app_view.py` 文件中的 `H5TestPublishApi` 类处理移动端应用的部署请求。通过 `run_stage` 方法，实现了校验阶段和发布阶段的分离，确保部署过程的安全性。

### 服务器端部署
`servers_view.py` 文件中的 `ServersPublishApplyApi` 类负责服务器端的部署申请。通过 `zeus_stage` 方法，实现了灾备步骤的添加，确保服务端部署的高可用性。

### 跨平台通用发布接口
`app_publish_views.py` 文件中的 `PublishInfoApi` 和 `PublishOperate` 类提供了跨平台的发布操作接口。通过 `create` 方法，支持发布、重启、停止、回滚等多种操作。

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py#L1-L103)
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L799)

## 架构概述
系统架构采用微服务设计，各模块通过API进行通信。CI/CD管理模块负责部署流程的控制，发布管理模块负责具体的发布操作，环境管理模块负责环境信息的维护，任务队列模块负责异步任务的调度。

```mermaid
graph TD
A[客户端] --> B[API网关]
B --> C[CI/CD管理]
B --> D[发布管理]
B --> E[环境管理]
B --> F[任务队列]
C --> G[数据库]
D --> G
E --> G
F --> G
```

**Diagram sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py#L1-L103)
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L799)

## 详细组件分析
### H5部署结果记录接口
`DeployResult` 类中的 `op_type_to_status` 字典定义了不同操作类型对应的状态码。`insert_status_data` 方法用于插入新的部署状态，`update_deploy_result_data_by_id` 方法用于更新指定ID的部署结果。

#### 类图
```mermaid
classDiagram
class DeployResult {
+dict op_type_to_status
+static insert_status_data(user, request_data)
+static insert_result_data(request_data)
+static update_deploy_result_data_by_id(deploy_id, status, msg, job_name)
+static update_deploy_result_data_by_ids(deploy_ids, status, msg, job_name)
+static check_deploy_status(app_name, iteration_id, suite_name, op_type)
+static check_app_component_publish(param)
+static update_result_by_filter(filter_dict, param)
+static update_result_by_sql_where(sql_where, status, msg)
}
```

**Diagram sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)

### 移动端应用部署参数校验
`H5TestPublishApi` 类中的 `run_stage` 方法首先调用 `check_stage` 进行参数校验，然后通过 `ci_publish_info` 获取Jenkins任务参数，最后将任务加入队列并异步执行。

#### 序列图
```mermaid
sequenceDiagram
participant Client as "客户端"
participant H5TestPublishApi as "H5TestPublishApi"
participant TaskQueue as "TaskQueue"
Client->>H5TestPublishApi : 发送部署请求
H5TestPublishApi->>H5TestPublishApi : check_stage(校验参数)
H5TestPublishApi->>H5TestPublishApi : ci_publish_info(获取Jenkins参数)
H5TestPublishApi->>TaskQueue : enter_queue(加入任务队列)
TaskQueue->>TaskQueue : async_run(异步执行)
TaskQueue-->>Client : 返回成功响应
```

**Diagram sources**
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)

### 服务器端资源调度
`ServersPublishApplyApi` 类中的 `zeus_stage` 方法通过 `add_zeus_queue` 添加宙斯配置回合，确保服务端部署的配置一致性。`run_stage` 方法中还包含了灾备步骤的处理逻辑。

#### 流程图
```mermaid
flowchart TD
Start([开始]) --> CheckStage["校验阶段"]
CheckStage --> |产线环境| CheckBusiness["检查业务名称"]
CheckStage --> |非产线环境| PublishConfig["发布配置阶段"]
CheckBusiness --> PublishConfig
PublishConfig --> ZeusStage["宙斯配置阶段"]
ZeusStage --> ScriptPublish["脚本发布阶段"]
ScriptPublish --> End([结束])
```

**Diagram sources**
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py#L1-L103)

### 跨平台通用发布接口
`PublishOperate` 类中的 `create` 方法支持多种操作类型，包括发布、重启、停止、回滚等。通过 `log_deploy_status` 记录部署状态，并使用 `TaskQueue` 异步执行任务。

#### 状态机对比图
```mermaid
stateDiagram-v2
[*] --> 预检
预检 --> 分发 : H5静态资源部署
预检 --> 分发 : 移动端热更新
预检 --> 分发 : 服务器端蓝绿部署
分发 --> 启动
启动 --> 验证
验证 --> [*]
```

**Diagram sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L799)

## 依赖分析
系统依赖于多个外部服务，包括Jenkins用于持续集成，SaltStack用于远程命令执行，CMDB用于环境信息管理。各模块之间通过API进行通信，确保系统的松耦合。

```mermaid
graph TD
A[CI/CD管理] --> B[Jenkins]
A --> C[SaltStack]
A --> D[CMDB]
B --> E[构建服务器]
C --> F[目标服务器]
D --> G[数据库]
```

**Diagram sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py#L1-L103)
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L799)

## 性能考虑
为了提高部署效率，系统采用了异步任务队列机制，避免阻塞主线程。同时，通过缓存常用数据和优化数据库查询，减少了响应时间。

## 故障排除指南
常见问题包括部署失败、状态不一致等。建议首先检查日志文件，确认错误信息。对于部署失败的情况，可以尝试重新提交部署请求；对于状态不一致的情况，可以通过手动更新数据库记录来修复。

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [app_view.py](file://ci_cd_mgt/mobile/app_view.py#L1-L23)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py#L1-L103)
- [app_publish_views.py](file://publish/app_publish_views.py#L1-L799)

## 结论
本文档详细介绍了多平台部署API的设计与实现，涵盖了H5、移动端和服务器端三种部署场景。通过合理的架构设计和模块划分，确保了系统的高效与稳定。未来可以进一步优化异步任务处理机制，提升整体性能。