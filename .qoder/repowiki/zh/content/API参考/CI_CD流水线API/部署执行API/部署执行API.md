# 部署执行API

<cite>
**本文档引用文件**  
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py)
- [app_publish_views.py](file://publish/app_publish_views.py)
- [salt_ser.py](file://task_mgt/salt_ser.py)
- [zeus_ser.py](file://task_mgt/zeus_ser.py)
- [models.py](file://task_mgt/models.py)
- [publish_ser.py](file://publish/publish_ser.py)
</cite>

## 目录
1. [引言](#引言)
2. [部署结果记录接口](#部署结果记录接口)
3. [发布申请接口](#发布申请接口)
4. [SaltStack与宙斯系统调用机制](#saltstack与宙斯系统调用机制)
5. [部署状态机与查询接口](#部署状态机与查询接口)
6. [部署失败分析与重试策略](#部署失败分析与重试策略)
7. [结论](#结论)

## 引言

本技术文档旨在全面阐述部署执行API的全流程，涵盖从发布单创建到最终部署完成的各个环节。文档详细描述了`deploy_result.py`中部署结果记录接口的数据模型、`app_publish_views.py`中发布申请接口的参数结构、`salt_ser.py`和`zeus_ser.py`中SaltStack与宙斯系统的调用实现机制。同时，文档还定义了部署状态机的转换逻辑、查询接口的响应格式，并提供了部署失败的常见原因分析及重试策略，为开发、运维和测试人员提供完整的部署执行参考。

## 部署结果记录接口

`deploy_result.py`文件定义了`DeployResult`类，该类负责处理部署结果的记录与查询。其核心数据模型基于`H5DeployResult`数据库模型，主要字段包括：

- **deploy_id**: 对应数据库中的`id`字段，是部署记录的唯一标识。
- **status**: 对应`status`字段，表示当前部署状态。状态值包括`running`（运行中）、`success`（成功）、`failure`（失败）等，具体状态由`op_type`决定。
- **start_time**: 对应`op_time`字段，表示部署操作的开始时间。
- **end_time**: 该字段在`H5DeployResult`模型中未直接体现，但可通过`start_time`和后续的状态更新来推算或通过其他日志表获取。

`DeployResult`类提供了多个静态方法来操作部署结果：
- `insert_status_data`: 插入一条新的运行中状态记录。
- `update_deploy_result_data_by_id`: 根据`deploy_id`更新状态、消息和任务名称。
- `check_deploy_status`: 检查指定应用在特定环境下的部署状态是否正在运行中。

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [models.py](file://task_mgt/models.py#L150-L199)

## 发布申请接口

`app_publish_views.py`文件中的`PublishInfoApi`类定义了发布申请接口。该接口的参数结构主要通过`request.data`传递，关键参数包括：

- **target_env**: 在代码中体现为`suite_name`，表示目标部署环境，如`prod`（生产）、`beta`（测试）等。
- **version_package**: 在代码中体现为`begin_ver`和`end_ver`，分别表示打包的起始版本和结束版本。
- **rollback_flag**: 在代码中未直接体现为一个布尔标志，但通过`op_type`为`rollback`来触发回滚操作。

`PublishOperate`类的`create`方法是处理发布请求的核心。它首先验证请求参数，然后通过`TaskQueue`将任务分发到不同的执行队列中。该方法还负责记录部署状态，通过调用`log_deploy_status`方法向`H5DeployResult`表中插入初始的运行中状态。

**Section sources**
- [app_publish_views.py](file://publish/app_publish_views.py#L200-L400)
- [publish_ser.py](file://publish/publish_ser.py#L300-L400)

## SaltStack与宙斯系统调用机制

### SaltStack调用机制

`salt_ser.py`文件中的`get_salt_cmd_info`函数负责获取执行Salt命令所需的信息。该函数通过SQL查询`publish_exec_salt_cmd`表，根据应用名、操作类型和节点IP来获取具体的`exec_cmd`（执行命令）和`salt_func`（Salt函数）。

当没有配置自定义命令时，系统会使用`default_salt_cmd`和`default_salt_func`函数生成默认命令。例如，对于`deploy`操作，会生成类似`su -c "/home/<USER>/shell/app_name.sh auto" tomcat`的命令。

整个调用流程如下：
1.  `PublishOperate.create`方法接收到发布请求。
2.  通过`get_salt_cmd_by_app_name`（在`publish_ser.py`中）获取具体命令。
3.  将命令和参数封装成`task_params`，并加入`TaskQueue`。
4.  `TaskQueue`异步执行，调用Salt API执行命令，并将结果记录在`SaltResults`表中。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant PublishOperate as "发布操作"
participant TaskQueue as "任务队列"
participant SaltAPI as "Salt API"
participant TargetNode as "目标节点"
Client->>PublishOperate : 发送发布请求
PublishOperate->>PublishOperate : 验证参数，获取Salt命令
PublishOperate->>TaskQueue : 创建任务并入队
TaskQueue->>SaltAPI : 异步执行Salt命令
SaltAPI->>TargetNode : 在目标节点执行命令
TargetNode-->>SaltAPI : 返回执行结果
SaltAPI-->>TaskQueue : 记录结果
TaskQueue-->>PublishOperate : 更新任务状态
PublishOperate-->>Client : 返回处理中
```

**Diagram sources**
- [salt_ser.py](file://task_mgt/salt_ser.py#L15-L60)
- [publish_ser.py](file://publish/publish_ser.py#L250-L260)

### 宙斯系统调用机制

`zeus_ser.py`文件定义了与宙斯系统交互的逻辑。`MultiNodeOperateApi`类中的`add_zeus_queue`方法负责将配置同步、一致性检查和配置发布等任务添加到执行队列中。

调用流程如下：
1.  当`op_type`为`update_and_deploy`等需要配置更新的操作时，`exec_publish`方法被调用。
2.  如果`zeus_type`为1，表示该应用接入了宙斯系统，则调用`add_zeus_queue`。
3.  `add_zeus_queue`方法将`sync_config`、`check_config_consistent`和`publish_config`等任务加入`TaskQueue`。
4.  `TaskQueue`会调用相应的接口（如`SyncConfigApi`、`PublishConfigBranchApi`）与宙斯系统进行交互。

```mermaid
sequenceDiagram
participant PublishOperate as "发布操作"
participant TaskQueue as "任务队列"
participant ZeusAPI as "宙斯API"
participant ZeusSystem as "宙斯系统"
PublishOperate->>TaskQueue : 添加配置同步任务
TaskQueue->>ZeusAPI : 调用sync_config接口
ZeusAPI->>ZeusSystem : 同步配置
ZeusSystem-->>ZeusAPI : 返回结果
ZeusAPI-->>TaskQueue : 记录结果
TaskQueue->>ZeusAPI : 调用check_config_consistent接口
ZeusAPI->>ZeusSystem : 检查配置一致性
ZeusSystem-->>ZeusAPI : 返回结果
ZeusAPI-->>TaskQueue : 记录结果
TaskQueue->>ZeusAPI : 调用publish_config接口
ZeusAPI->>ZeusSystem : 发布配置
ZeusSystem-->>ZeusAPI : 返回结果
ZeusAPI-->>TaskQueue : 记录结果
```

**Diagram sources**
- [zeus_ser.py](file://task_mgt/zeus_ser.py#L100-L150)
- [app_publish_views.py](file://publish/app_publish_views.py#L500-L550)

## 部署状态机与查询接口

### 部署状态机

系统定义了清晰的部署状态机，其状态转换逻辑如下：

```mermaid
stateDiagram-v2
[*] --> 待部署
待部署 --> 部署中 : 接收发布请求
部署中 --> 成功 : 所有节点部署成功
部署中 --> 失败 : 任一节点部署失败
部署中 --> 已回滚 : 手动或自动触发回滚
成功 --> [*]
失败 --> [*]
已回滚 --> [*]
```

状态值在代码中通过`H5DeployResult.STATUS_CHOICE`定义，包括`running`、`success`、`failure`等。`check_deploy_status`方法用于防止重复部署，它会查询数据库中是否存在状态为`running`的记录。

### 查询接口

`SpiderPublishStatus`类提供了查询部署状态的接口。`list`方法接收`iteration_id`和`app_name`作为参数，调用`SpiderPublishController.get_latest_publish_status_info`来获取最新的发布状态信息，并以JSON格式返回。

响应格式示例如下：
```json
{
  "msg": "操作信息查询成功",
  "data": [
    {
      "action_id": 123,
      "app_name": "example-app",
      "ip": "*************",
      "suite_code": "prod",
      "status": "success",
      "op_time": "2023-10-01 12:00:00",
      "op_user": "admin",
      "op_type": "deploy"
    }
  ],
  "code": 0
}
```

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L50-L70)
- [app_publish_views.py](file://publish/app_publish_views.py#L800-L850)

## 部署失败分析与重试策略

### 常见失败原因

1.  **Salt minion离线**: 当目标节点的Salt minion服务未运行或网络不通时，Salt命令无法执行，导致部署失败。
2.  **配置文件错误**: 在调用宙斯系统时，如果配置文件存在语法错误或逻辑错误，`check_config_consistent`或`publish_config`步骤会失败。
3.  **命令执行失败**: 自定义的`exec_cmd`脚本在执行过程中报错，例如权限不足、路径错误等。
4.  **资源冲突**: 在交易时间（非允许时段）尝试发布，会被`WebsitePublishRestrictions`拦截。

### 重试策略

系统本身不提供自动重试机制，但提供了以下支持：
- **状态查询**: 用户可以通过查询接口确认失败原因。
- **手动重试**: 用户可以根据失败原因修正问题后，重新发起发布请求。
- **幂等性设计**: `insert_status_data`和`update_deploy_result_data_by_id`等方法使用了`update_or_create`，保证了操作的幂等性，允许安全地重试。

**Section sources**
- [salt_ser.py](file://task_mgt/salt_ser.py#L15-L60)
- [zeus_ser.py](file://task_mgt/zeus_ser.py#L100-L150)
- [app_publish_views.py](file://publish/app_publish_views.py#L600-L650)

## 结论

本文档详细阐述了部署执行API的核心组件和工作流程。通过`deploy_result.py`记录部署状态，`app_publish_views.py`接收和处理发布请求，`salt_ser.py`和`zeus_ser.py`分别与SaltStack和宙斯系统进行交互，实现了从发布申请到最终部署的完整闭环。系统具备清晰的状态机和查询接口，能够有效处理部署过程中的各种情况。对于部署失败，虽然没有自动重试，但其幂等性设计和详细的日志记录为手动重试和问题排查提供了坚实的基础。