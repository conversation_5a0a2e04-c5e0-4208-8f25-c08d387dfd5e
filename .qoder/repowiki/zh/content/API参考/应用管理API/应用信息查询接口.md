# 应用信息查询接口

<cite>
**本文档引用的文件**   
- [app_mgt.py](file://app_mgt/app_mgt.py)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
- [models.py](file://app_mgt/models.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心端点](#核心端点)
3. [查询参数](#查询参数)
4. [分页参数](#分页参数)
5. [响应数据结构](#响应数据结构)
6. [响应示例](#响应示例)
7. [高级查询功能](#高级查询功能)
8. [性能优化](#性能优化)

## 简介
本文档详细描述了应用信息查询接口，该接口用于获取系统中应用的详细信息。接口设计旨在支持单个应用查询和批量查询，提供灵活的过滤和分页功能，以满足不同场景下的数据获取需求。

## 核心端点
应用信息查询接口提供了两个核心端点，分别用于获取单个应用的详细信息和批量查询应用列表。

### 单个应用查询
此端点用于根据应用ID获取单个应用的详细信息。

**端点**: `GET /api/app/info`

**功能**: 根据提供的应用ID，返回该应用的完整信息，包括主表信息、关联的模块信息和构建信息。

### 批量应用查询
此端点用于根据多种条件批量查询应用列表。

**端点**: `GET /api/app/list`

**功能**: 支持通过应用ID、应用名称、团队、状态等多种条件进行过滤，返回符合条件的应用列表，并支持分页。

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L47-L69)

## 查询参数
查询接口支持丰富的查询参数，允许用户根据不同的条件精确地筛选所需的应用信息。

### 基础查询参数
| 参数 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `app_id` | 字符串 | 否 | 应用的唯一标识ID，用于精确查询单个应用。 |
| `app_name` | 字符串 | 否 | 应用的名称，支持模糊搜索。 |
| `team` | 字符串 | 否 | 应用所属的团队名称，用于按团队过滤应用。 |
| `status` | 字符串 | 否 | 应用的状态，如“active”（激活）或“inactive”（停用）。 |

### 高级查询参数
| 参数 | 类型 | 必填 | 描述 |
| :--- | :--- | :--- | :--- |
| `module_name` | 字符串 | 否 | 关联的应用模块名称，用于查询特定模块的应用。 |
| `build_info` | 字符串 | 否 | 构建相关信息，如构建版本或构建状态。 |

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L103-L159)
- [models.py](file://app_mgt/models.py#L1-L479)

## 分页参数
为了处理大量数据，接口支持分页功能，通过以下两个参数控制返回结果的范围。

| 参数 | 类型 | 默认值 | 描述 |
| :--- | :--- | :--- | :--- |
| `page` | 整数 | 1 | 请求的页码，从1开始计数。 |
| `page_size` | 整数 | 10 | 每页返回的应用数量，最大值为100。 |

**示例**: `GET /api/app/list?page=2&page_size=20` 将返回第二页，每页20条记录。

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L47-L69)

## 响应数据结构
接口返回的响应数据采用统一的JSON格式，包含元信息和实际数据。

### 响应格式
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "page": {
      "num": 1,
      "size": 10,
      "total": 100
    },
    "data_list": [
      {
        "app_name": "example-app",
        "app_cname": "示例应用",
        "app_status": true,
        "git_url": "https://git.example.com/example-app",
        "app_jdk_version": "1.8",
        "app_desc": "这是一个示例应用。",
        "team_name": "开发团队",
        "module_info": [
          {
            "module_name": "example-module",
            "module_status": true,
            "module_desc": "示例模块",
            "app_port": 8080
          }
        ],
        "build_info": {
          "package_type": "jar",
          "package_name": "example-app-1.0.0.jar",
          "build_jdk_version": "1.8"
        }
      }
    ]
  }
}
```

### 字段说明
- **`code`**: 响应状态码，200表示成功。
- **`msg`**: 响应消息，描述请求结果。
- **`data.page`**: 分页信息，包含当前页码、每页大小和总记录数。
- **`data.data_list`**: 应用信息列表，每个元素包含：
  - **`AppInfo`**: 应用主表信息，如名称、状态、代码仓库URL等。
  - **`AppModule`**: 关联的应用模块信息，包括模块名、端口等。
  - **`AppBuild`**: 构建信息，如包类型、包名等。

**Section sources**
- [models.py](file://app_mgt/models.py#L1-L479)

## 响应示例
### 正常情况
当请求成功时，接口返回200状态码和包含应用信息的JSON数据。

**请求**: `GET /api/app/list?app_name=example&page=1&page_size=5`

**响应**:
```json
{
  "code": 200,
  "msg": "获取应用列表成功",
  "data": {
    "page": {
      "num": 1,
      "size": 5,
      "total": 15
    },
    "data_list": [
      {
        "app_name": "example-app-1",
        "app_cname": "示例应用一",
        "app_status": true,
        "git_url": "https://git.example.com/example-app-1",
        "app_jdk_version": "1.8",
        "app_desc": "第一个示例应用。",
        "team_name": "开发团队A",
        "module_info": [
          {
            "module_name": "module-a",
            "module_status": true,
            "module_desc": "模块A",
            "app_port": 8081
          }
        ],
        "build_info": {
          "package_type": "war",
          "package_name": "example-app-1-1.0.0.war",
          "build_jdk_version": "1.8"
        }
      }
    ]
  }
}
```

### 应用不存在 (404)
当查询的应用不存在时，接口返回404状态码。

**请求**: `GET /api/app/info?app_id=nonexistent-app`

**响应**:
```json
{
  "code": 404,
  "msg": "未找到指定的应用",
  "data": {}
}
```

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L47-L69)

## 高级查询功能
接口支持高级查询功能，以满足更复杂的业务需求。

### 模糊搜索
通过`app_name`参数支持模糊搜索。例如，`app_name=exam`可以匹配`example-app`和`exam-system`。

### 多条件组合查询
可以同时使用多个查询参数进行组合查询。例如，`GET /api/app/list?team=开发团队A&status=active&page=1&page_size=10` 将返回“开发团队A”下所有激活状态的应用。

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L103-L159)

## 性能优化
为了确保接口在高并发和大数据量下的性能，系统实施了以下优化措施。

### 缓存策略
系统对频繁查询的应用信息进行了缓存。当应用信息发生变更时，缓存会自动失效并重新加载最新数据，从而显著减少数据库查询次数。

### 数据库索引
在数据库的关键字段上建立了索引，包括`app_name`、`team_id`和`app_status`。这些索引极大地提高了查询效率，尤其是在处理大量数据时。

**Section sources**
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L0-L799)
- [models.py](file://app_mgt/models.py#L1-L479)