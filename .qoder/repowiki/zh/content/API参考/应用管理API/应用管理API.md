# 应用管理API

<cite>
**本文档引用的文件**   
- [app_register.py](file://app_mgt/app_register.py)
- [app_compare.py](file://app_mgt/app_compare.py)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
- [models.py](file://app_mgt/models.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心数据模型](#核心数据模型)
3. [应用注册接口](#应用注册接口)
4. [应用信息查询接口](#应用信息查询接口)
5. [应用对比接口](#应用对比接口)
6. [模块管理接口](#模块管理接口)
7. [API调用顺序与状态机](#api调用顺序与状态机)
8. [批量操作与分页过滤](#批量操作与分页过滤)
9. [错误码与异常处理](#错误码与异常处理)
10. [客户端调用示例](#客户端调用示例)

## 简介
本API文档详细描述了应用管理模块的核心功能，包括应用注册、信息查询、应用对比和模块管理等接口。所有接口均采用RESTful风格设计，使用JSON格式进行数据交换，并通过JWT进行身份认证。文档重点说明了`app_register`、`app_compare`、`app_info`等核心接口的使用方法，以及应用状态机的流转逻辑。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L1-L677)
- [app_compare.py](file://app_mgt/app_compare.py#L1-L497)
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L1-L740)

## 核心数据模型

### AppInfo 应用信息模型
该模型定义了应用的基本信息，与数据库表`app_mgt_app_info`对应。

| 字段名 | 类型 | 说明 | ORM字段 |
| :--- | :--- | :--- | :--- |
| app_name | string | 应用英文名 | app_name |
| app_cname | string | 应用中文名 | app_cname |
| app_status | boolean | 应用状态：0-已废弃，1-使用中 | app_status |
| git_url | string | Git仓库地址 | git_url |
| git_path | string | Git路径 | git_path |
| svn_url | string | SVN仓库地址 | svn_url |
| svn_path | string | SVN路径 | svn_path |
| app_jdk_version | string | JDK默认版本 | app_jdk_version |
| lib_location | string | 所属团队 | lib_location |
| platform_type | boolean | 是否接入平台 | platform_type |

**Section sources**
- [models.py](file://app_mgt/models.py#L4-L35)

### AppModule 应用模块模型
该模型定义了应用的模块信息，与数据库表`app_mgt_app_module`对应。

| 字段名 | 类型 | 说明 | ORM字段 |
| :--- | :--- | :--- | :--- |
| app_id | integer | 关联的应用ID | app_id |
| module_name | string | 模块名 | module_name |
| module_status | boolean | 模块状态：0-已废弃，1-使用中 | module_status |
| need_online | boolean | jar包是否需要上线 | need_online |
| need_check | boolean | 是否需要维护 | need_check |
| app_port | integer | 应用端口 | app_port |
| container_name | string | 容器名 | container_name |
| lib_repo | string | 制品库路径 | lib_repo |
| deploy_path | string | 发布路径 | deploy_path |
| zeus_type | boolean | 是否接入宙斯 | zeus_type |
| need_ops | boolean | 是否为mock应用 | need_ops |

**Section sources**
- [models.py](file://app_mgt/models.py#L37-L74)

## 应用注册接口

### POST /api/v1/app/register
注册一个新应用。

**认证方式**: JWT

**请求参数**:
- **Header**:
  - `Authorization: Bearer <token>` (必需)
- **Body** (JSON):
```json
{
  "app_name": "string",
  "app_cname": "string",
  "app_type": "jar|war|pom",
  "jdk_version": "string",
  "team": "string",
  "trunk_path": "string",
  "repo_path": "string"
}
```

**响应**:
- **200 OK**: 注册成功
```json
{
  "code": 0,
  "msg": "添加成功",
  "data": null
}
```
- **400 Bad Request**: 应用已存在或参数错误
```json
{
  "code": 1,
  "msg": "应用已存在",
  "data": null
}
```

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L100-L200)

## 应用信息查询接口

### GET /api/v1/app/info
根据应用名查询应用信息。

**认证方式**: JWT

**请求参数**:
- **Query**:
  - `app_name` (string, 可选): 应用名
  - `page_num` (integer, 可选): 页码，默认1
  - `page_size` (integer, 可选): 每页数量，默认10

**响应**:
- **200 OK**: 查询成功
```json
{
  "code": 0,
  "msg": "获取应用列表成功",
  "data": {
    "page": {
      "num": 1,
      "size": 10,
      "total": 100
    },
    "data_list": [
      {
        "module_name": "string",
        "app_jdk_version": "string",
        "url_path": "string",
        "url": "string",
        "path": "string",
        "lib_repo": "string",
        "app_port": 8080,
        "container_name": "string",
        "deploy_path": "string",
        "package_name": "string",
        "app_type": "jar",
        "need_online": true,
        "need_check": true,
        "zeus_type": false,
        "platform_type": true,
        "create_path": "string",
        "team_name": "string"
      }
    ]
  }
}
```

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L50-L100)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L150-L200)

## 应用对比接口

### POST /api/v1/app/compare
对比多个节点上同一应用的部署状态。

**认证方式**: JWT

**请求参数**:
- **Header**:
  - `Authorization: Bearer <token>` (必需)
- **Body** (JSON):
```json
{
  "ip_list": ["*************", "*************"],
  "app_name": "gateway-web"
}
```

**响应**:
- **200 OK**: 对比成功
```json
{
  "code": 0,
  "msg": "调用成功",
  "data": {
    "res_dict": {
      "*************": ["74477708", "625576"],
      "*************": ["74477708", "625576"]
    },
    "diff_num": 0
  }
}
```
- **500 Internal Server Error**: 对比失败
```json
{
  "code": 1,
  "msg": "只支持jar,war类型包MD5对比，gateway-web应用的类型为dist",
  "data": null
}
```

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L100-L200)

## 模块管理接口

### PUT /api/v1/app/module
更新应用模块信息。

**认证方式**: JWT

**请求参数**:
- **Header**:
  - `Authorization: Bearer <token>` (必需)
- **Body** (JSON):
```json
{
  "module_name": "string",
  "need_online": boolean,
  "need_check": boolean,
  "lib_repo": "string",
  "container_name": "string",
  "app_port": integer,
  "create_path": "string",
  "url": "string",
  "path": "string",
  "package_name": "string",
  "team_name": "string",
  "platform_type": 0|1
}
```

**响应**:
- **200 OK**: 更新成功
```json
{
  "code": 0,
  "msg": "编辑应用信息成功",
  "data": {}
}
```

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L150-L200)

## API调用顺序与状态机

```mermaid
stateDiagram-v2
[*] --> 应用注册
应用注册 --> 信息查询 : 成功
应用注册 --> 应用注册 : 失败(应用已存在)
信息查询 --> 模块管理 : 获取应用ID
模块管理 --> 应用对比 : 配置完成
应用对比 --> [*] : 对比完成
```

**Diagram sources**
- [app_register.py](file://app_mgt/app_register.py#L100-L200)
- [app_mgt.py](file://app_mgt/app_mgt.py#L50-L100)
- [app_compare.py](file://app_mgt/app_compare.py#L100-L200)

## 批量操作与分页过滤

### GET /api/v1/app/batch
查询批量发布应用列表，支持分页、过滤和排序。

**请求参数**:
- **Query**:
  - `module_name` (string, 可选): 模块名
  - `suite_code` (string, 可选): 环境套代码
  - `team_name` (string, 可选): 团队名
  - `department_name` (string, 可选): 部门名
  - `page_num` (integer, 可选): 页码
  - `page_size` (integer, 可选): 每页数量

**响应**:
- **200 OK**: 查询成功，返回分页数据
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "page": {
      "total": 50,
      "page_num": 1,
      "page_size": 10
    },
    "app_list": [
      {
        "module_name": "string",
        "suite_code": "string",
        "node_ips": ["*************", "*************"],
        "team_name": "string",
        "department_name": "string",
        "branch_name": "string",
        "status": 0|1
      }
    ]
  }
}
```

**Section sources**
- [app_mgt.py](file://app_mgt/app_mgt.py#L300-L330)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L500-L550)

## 错误码与异常处理

| 错误码 | HTTP状态码 | 场景 | 处理方式 |
| :--- | :--- | :--- | :--- |
| 1 | 400 | 应用已存在 | 检查应用名是否重复 |
| 1 | 400 | 仓库不存在 | 验证仓库地址和权限 |
| 1 | 500 | 插入数据库失败 | 检查数据库连接和字段约束 |
| 1 | 500 | 未匹配到svn组 | 检查SVN组配置 |
| 1 | 500 | minion_id不存在 | 联系管理员维护节点信息 |
| 1 | 500 | 包名称未维护 | 在应用模块中维护包名 |

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L150-L200)
- [app_compare.py](file://app_mgt/app_compare.py#L50-L100)

## 客户端调用示例

### curl 示例
```bash
# 注册应用
curl -X POST https://api.example.com/api/v1/app/register \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "app_name": "myapp",
    "app_cname": "我的应用",
    "app_type": "jar",
    "jdk_version": "1.8",
    "team": "dev",
    "trunk_path": "********************:group/myapp.git",
    "repo_path": "/data/repo/myapp"
  }'

# 查询应用信息
curl -X GET "https://api.example.com/api/v1/app/info?app_name=myapp&page_num=1&page_size=10" \
  -H "Authorization: Bearer your-jwt-token"

# 应用对比
curl -X POST https://api.example.com/api/v1/app/compare \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "ip_list": ["*************", "*************"],
    "app_name": "myapp"
  }'
```

### Python 客户端调用示例
```python
import requests
import json

class AppMgtClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def register_app(self, app_data):
        url = f'{self.base_url}/api/v1/app/register'
        response = requests.post(url, headers=self.headers, json=app_data)
        return response.json()

    def get_app_info(self, app_name=None, page_num=1, page_size=10):
        url = f'{self.base_url}/api/v1/app/info'
        params = {'app_name': app_name, 'page_num': page_num, 'page_size': page_size}
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()

    def compare_app(self, ip_list, app_name):
        url = f'{self.base_url}/api/v1/app/compare'
        data = {'ip_list': ip_list, 'app_name': app_name}
        response = requests.post(url, headers=self.headers, json=data)
        return response.json()

# 使用示例
client = AppMgtClient('https://api.example.com', 'your-jwt-token')

# 注册应用
app_data = {
    'app_name': 'myapp',
    'app_cname': '我的应用',
    'app_type': 'jar',
    'jdk_version': '1.8',
    'team': 'dev',
    'trunk_path': '********************:group/myapp.git',
    'repo_path': '/data/repo/myapp'
}
result = client.register_app(app_data)
print(result)

# 查询应用
info = client.get_app_info('myapp')
print(info)

# 应用对比
compare_result = client.compare_app(['*************', '*************'], 'myapp')
print(compare_result)
```

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L100-L200)
- [app_mgt.py](file://app_mgt/app_mgt.py#L50-L100)
- [app_compare.py](file://app_mgt/app_compare.py#L100-L200)