# 应用对比接口

<cite>
**本文档引用文件**   
- [app_compare.py](file://app_mgt/app_compare.py)
- [app_compare_ser.py](file://app_mgt/app_compare_ser.py)
- [models.py](file://app_mgt/models.py)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心功能](#核心功能)
3. [请求参数说明](#请求参数说明)
4. [对比结果数据结构](#对比结果数据结构)
5. [响应示例](#响应示例)
6. [对比算法实现原理](#对比算法实现原理)
7. [性能特征与适用场景](#性能特征与适用场景)
8. [实际使用示例](#实际使用示例)
9. [关键指标与风险提示](#关键指标与风险提示)
10. [决策支持](#决策支持)

## 简介
应用对比接口提供了一种机制，用于比较不同应用版本之间的差异。该接口通过执行底层系统命令来获取部署路径下的文件大小信息，并基于这些信息判断不同节点间的配置一致性。主要功能包括：版本间依赖库、配置文件和环境变量的对比分析。

## 核心功能
该接口的核心功能是实现应用版本间的差异分析，具体包括：
- 比较指定应用在多个IP地址上的部署状态。
- 支持`jar`和`war`类型包的对比。
- 生成详细的对比结果，包含每个节点的资源使用情况。
- 提供差异数量统计，帮助快速识别不一致的节点。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L1-L497)

## 请求参数说明
POST `/api/app/compare` 端点接受以下请求参数：

- **ip_list**: 字符串数组，表示参与对比的服务器IP列表。
- **app_name**: 字符串，表示待对比的应用名称。

这些参数通过HTTP请求体传递，格式为JSON。例如：
```json
{
  "ip_list": ["*************", "*************"],
  "app_name": "tenpay"
}
```

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L250-L252)

## 对比结果数据结构
对比结果以JSON格式返回，包含以下字段：

- **res_dict**: 对象，键为IP地址，值为该节点上执行对比命令后返回的信息列表。
- **diff_num**: 整数，表示与其他节点存在差异的节点数量。

例如：
```json
{
  "res_dict": {
    "*************": ["74477708", "625576"],
    "*************": ["74477708", "625576"],
    "*************": ["74473612", "625576"]
  },
  "diff_num": 1
}
```

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L275-L278)

## 响应示例
成功响应示例如下：
```json
{
  "code": 0,
  "msg": "调用成功",
  "data": {
    "res_dict": {
      "*************": ["74477708", "625576"],
      "*************": ["74477708", "625576"],
      "*************": ["74477708", "625576"],
      "*************": ["74473612", "625576"]
    },
    "diff_num": 1
  }
}
```

当出现异常时，响应将包含错误信息：
```json
{
  "code": -1,
  "msg": "只支持jar,war类型包MD5对比，howbuy-gateway应用的类型为dist",
  "data": null
}
```

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L273-L279)

## 对比算法实现原理
对比算法的实现分为以下几个步骤：

1. **获取发布信息**：调用 `get_app_publish_info` 函数从数据库中查询指定应用在各IP上的部署路径、包名、包类型等信息。
2. **生成对比命令**：根据包类型（`jar` 或 `war`）生成相应的系统命令。对于 `war` 包，默认对比 `/WEB-INF/lib` 和 `/WEB-INF/classes/com` 目录；对于 `jar` 包，则根据是否为全量包决定对比策略。
3. **执行命令**：通过Salt工具在目标节点上执行生成的命令，获取目录大小信息。
4. **结果编译**：将所有节点的对比结果进行汇总，并计算与基准节点（`ip_list[0]`）不同的节点数量。

该算法依赖于Salt任务系统执行远程命令，并通过正则解析输出结果。

```mermaid
flowchart TD
Start([开始]) --> ValidateInput["验证输入参数"]
ValidateInput --> GetPublishInfo["获取应用发布信息"]
GetPublishInfo --> GenerateCmd["生成对比命令"]
GenerateCmd --> ExecuteCmd["执行Salt命令"]
ExecuteCmd --> CollectResults["收集各节点结果"]
CollectResults --> CompileDiff["计算差异数量"]
CompileDiff --> ReturnResult["返回对比结果"]
ReturnResult --> End([结束])
```

**Diagram sources **
- [app_compare.py](file://app_mgt/app_compare.py#L150-L190)
- [app_mgt_ser.py](file://app_mgt/app_mgt_ser.py#L20-L40)

## 性能特征与适用场景
### 性能特征
- **高并发支持**：由于使用了异步线程处理自动归档任务，接口本身响应迅速，适合集成到自动化流程中。
- **网络依赖性强**：性能受Salt通信延迟影响较大，特别是在跨区域或高延迟网络环境中。
- **资源消耗低**：仅执行轻量级的`du`命令，对目标服务器CPU和内存影响极小。

### 适用场景
- **生产环境与预发布环境对比**：确保上线前配置一致性。
- **灾备环境同步验证**：检查主备节点间是否存在配置漂移。
- **批量部署后验证**：确认多节点部署结果的一致性。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L100-L150)

## 实际使用示例
### 生产环境与预发布环境对比
假设需要对比 `tenpay` 应用在生产环境（`*************`, `*************`）和预发布环境（`*************`）之间的差异，可发送如下请求：

```http
POST /api/app/compare
Content-Type: application/json

{
  "ip_list": ["*************", "*************", "*************"],
  "app_name": "tenpay"
}
```

若返回 `diff_num=1`，说明有一个节点与其他节点存在差异，需进一步排查 `*************` 节点的部署情况。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L250-L280)

## 关键指标与风险提示
### 关键指标
- **差异数量（diff_num）**：直接反映环境一致性程度，理想值为0。
- **节点响应时间**：反映Salt通信效率，可用于评估网络健康状况。
- **部署路径完整性**：确保所有节点均能找到正确的部署路径。

### 风险提示
- **Minion ID缺失**：若某节点未配置Minion ID，将导致无法执行远程命令。
- **包名未维护**：缺少包名信息会导致命令生成失败。
- **发布路径未找到**：可能意味着应用未正确部署或路径配置错误。

发现上述问题时，系统会抛出相应异常并记录日志，便于运维人员及时处理。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L210-L230)

## 决策支持
根据对比结果，可做出以下决策：
- **差异数为0**：环境一致，可继续后续操作（如归档、发布）。
- **差异数大于0**：暂停自动化流程，人工介入检查差异原因。
- **特定节点异常**：针对性地重新部署或修复该节点。

此外，系统还支持通过邮件通知相关人员，实现及时告警和闭环管理。

**Section sources**
- [app_compare.py](file://app_mgt/app_compare.py#L350-L400)