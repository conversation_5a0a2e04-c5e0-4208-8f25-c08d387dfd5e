# 应用注册接口

<cite>
**本文档引用的文件**   
- [app_register.py](file://app_mgt/app_register.py)
- [models.py](file://app_mgt/models.py)
- [cmdb.py](file://public/cmdb.py)
</cite>

## 目录
1. [简介](#简介)
2. [API设计](#api设计)
3. [请求体结构](#请求体结构)
4. [数据验证与唯一性约束](#数据验证与唯一性约束)
5. [CMDB系统集成](#cmdb系统集成)
6. [响应示例](#响应示例)
7. [调用示例](#调用示例)
8. [返回元数据说明](#返回元数据说明)
9. [错误场景](#错误场景)

## 简介
应用注册接口用于在系统中注册新的应用。该接口支持Java和非Java应用的注册，能够处理单个或多个应用的注册请求。注册过程中会进行数据验证、唯一性检查，并与CMDB系统进行集成。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L1-L50)

## API设计
应用注册接口通过POST方法暴露在`/api/app/register`端点上。该接口由`AppRegisterApi`类实现，负责处理应用注册请求。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "AppRegisterApi"
participant DB as "数据库"
Client->>API : POST /api/app/register
API->>API : 验证用户权限
API->>API : 解析请求数据
API->>API : 检查应用是否已存在
API->>API : 验证仓库地址
API->>DB : 创建应用信息记录
DB-->>API : 返回应用ID
API->>DB : 创建模块信息记录
DB-->>API : 确认创建
API->>DB : 创建团队关联记录
DB-->>API : 确认创建
API->>DB : 创建构建配置记录
DB-->>API : 确认创建
API-->>Client : 返回注册结果
```

**Diagram sources**
- [app_register.py](file://app_mgt/app_register.py#L135-L157)
- [models.py](file://app_mgt/models.py#L10-L20)

## 请求体结构
应用注册请求体包含应用的基本信息、类型、负责人、团队信息等字段。

### 必填字段
- **app_name**: 应用英文名
- **app_name_cn**: 应用中文名
- **app_type**: 应用类型（如jar、war、pom等）
- **team**: 所属团队
- **trunk_path**: 代码仓库主干路径

### 可选字段
- **jar_name**: 包名
- **jdk_version**: JDK版本
- **repo_path**: 制品库路径
- **repo_type**: 仓库类型（GIT/SVN）

```mermaid
classDiagram
class AppInfo {
+string app_name
+string app_cname
+int app_status
+string git_url
+string git_path
+string svn_url
+string svn_path
+string app_jdk_version
+string lib_location
}
class AppModule {
+int app_id
+string module_name
+int module_status
+string module_desc
+int need_online
+int need_check
+string lib_repo
}
class AppTeam {
+int app_id
+int team_id
}
class AppBuild {
+int app_id
+string module_name
+string package_type
+string package_name
+int package_full
}
AppInfo --> AppModule : "1对多"
AppInfo --> AppTeam : "1对多"
AppInfo --> AppBuild : "1对多"
```

**Diagram sources**
- [models.py](file://app_mgt/models.py#L10-L100)

## 数据验证与唯一性约束
系统在注册过程中实施严格的数据验证和唯一性约束。

### 数据验证规则
- 应用英文名不能为空且必须唯一
- 应用中文名不能为空
- 团队名称必须存在于系统中
- 仓库路径必须有效且可访问

### 唯一性约束
- `app_name`字段在`AppInfo`表中具有唯一性约束
- 应用注册前会检查同名应用是否存在

```mermaid
flowchart TD
Start([开始注册]) --> ValidateInput["验证输入参数"]
ValidateInput --> AppExists{"应用已存在?"}
AppExists --> |是| ReturnError["返回409冲突"]
AppExists --> |否| CheckRepo["检查仓库有效性"]
CheckRepo --> RepoValid{"仓库有效?"}
RepoValid --> |否| ReturnError
RepoValid --> |是| CreateRecords["创建数据库记录"]
CreateRecords --> Success["返回成功"]
```

**Diagram sources**
- [app_register.py](file://app_mgt/app_register.py#L100-L120)

## CMDB系统集成
应用注册过程与CMDB系统深度集成，确保应用信息在多个系统间保持一致。

### 集成逻辑
- 注册成功后，应用信息会同步到CMDB系统
- 团队信息从CMDB获取并验证
- 应用状态变更会触发CMDB更新

```mermaid
graph TB
subgraph "应用注册系统"
API[应用注册API]
DB[(应用数据库)]
end
subgraph "CMDB系统"
CMDB[CMDB服务]
CMDB_DB[(CMDB数据库)]
end
API --> CMDB: 同步应用信息
CMDB --> DB: 确认同步完成
DB --> API: 返回注册成功
```

**Diagram sources**
- [cmdb.py](file://public/cmdb.py#L100-L150)

## 响应示例
### 成功响应
```json
{
    "code": 200,
    "msg": "添加成功",
    "data": null
}
```

### 失败响应
```json
{
    "code": 400,
    "msg": "参数校验失败",
    "data": null
}
```

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L600-L620)

## 调用示例
### Python调用示例
```python
import requests
import json

url = "http://localhost/api/app/register"
headers = {"Content-Type": "application/json"}
data = {
    "app_name": "test-app",
    "app_name_cn": "测试应用",
    "app_type": "jar",
    "team": "dev-team",
    "trunk_path": "**************:test/test-app.git"
}

response = requests.post(url, headers=headers, data=json.dumps(data))
print(response.json())
```

### curl调用示例
```bash
curl -X POST \
  http://localhost/api/app/register \
  -H 'Content-Type: application/json' \
  -d '{
    "app_name": "test-app",
    "app_name_cn": "测试应用",
    "app_type": "jar",
    "team": "dev-team",
    "trunk_path": "**************:test/test-app.git"
  }'
```

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L550-L580)

## 返回元数据说明
注册成功后，系统会返回应用的元数据信息，包括：

- **应用ID**: 系统生成的唯一标识
- **创建时间**: 应用注册时间戳
- **更新时间**: 最后修改时间戳
- **创建人**: 注册操作的执行者

这些元数据用于后续的应用管理和审计追踪。

**Section sources**
- [models.py](file://app_mgt/models.py#L20-L30)

## 错误场景
### 应用已存在 (409)
当尝试注册一个已存在的应用时，系统返回409状态码。

```json
{
    "code": 409,
    "msg": "应用已存在",
    "data": null
}
```

### 参数校验失败 (400)
当请求参数不符合验证规则时，系统返回400状态码。

```json
{
    "code": 400,
    "msg": "参数校验失败",
    "data": null
}
```

### 仓库不存在
当提供的代码仓库路径无效或无法访问时，系统返回相应的错误信息。

**Section sources**
- [app_register.py](file://app_mgt/app_register.py#L120-L130)