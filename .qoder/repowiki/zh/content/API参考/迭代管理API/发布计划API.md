# 发布计划API

<cite>
**本文档引用的文件**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py)
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py)
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py)
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py)
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py)
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py)
- [publish_plan_view.py](file://iter_mgt/publish_plan/publish_plan_view.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心模型与数据结构](#核心模型与数据结构)
3. [发布计划类型与配置](#发布计划类型与配置)
4. [发布节点状态机与流程](#发布节点状态机与流程)
5. [发布计划状态枚举](#发布计划状态枚举)
6. [RESTful API 接口说明](#restful-api-接口说明)
7. [完整发布流程示例](#完整发布流程示例)
8. [权限校验与冲突处理](#权限校验与冲突处理)

## 简介

本API文档详细描述了`iter_mgt_publish_plan`模型所对应的RESTful接口，旨在为发布计划的创建、查询、更新和删除操作提供全面的技术指导。文档深入解析了不同发布类型（如`normal_publish_plan_config`、`express_publish_plan_config`）的配置差异，阐明了`publish_plan_node_config`中定义的发布节点状态转换逻辑，以及`publish_plan_status_enum`中各状态的含义。同时，文档提供了从创建发布计划、添加应用、提交审批到执行发布的完整调用示例，并涵盖了请求体结构、响应码、权限规则和冲突处理策略。

## 核心模型与数据结构

### 发布计划主模型 (iter_mgt_publish_plan)

该模型定义了发布计划的核心属性。

**Section sources**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L1-L25)

### 发布计划节点模型 (iter_mgt_publish_plan_node)

该模型定义了发布计划中每个执行节点的详细信息，包括参数、状态和执行顺序。

**Section sources**
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py#L1-L25)

## 发布计划类型与配置

系统支持多种发布计划类型，每种类型对应不同的配置流程。

### 普通发布计划 (NormalPublishPlanConfig)

普通发布计划包含验证、上线前、上线中和上线后四个阶段。其配置逻辑由`normal_publish_plan_config.py`定义。

**Section sources**
- [normal_publish_plan_config.py](file://iter_mgt/publish_plan/config/normal_publish_plan_config.py#L1-L34)

### 快速发布计划 (ExpressPublishPlanConfig)

快速发布计划（特急发布）流程更为紧凑，包含构建/部署、自动化测试、产线申请、发布决策、节点发布和归档等环节，确保快速上线。

**Section sources**
- [express_publish_plan_config.py](file://iter_mgt/publish_plan/config/express_publish_plan_config.py#L1-L34)

## 发布节点状态机与流程

发布流程由一系列有序的节点组成，每个节点的状态遵循预定义的状态机进行转换。

```mermaid
flowchart TD
A["等待调用 (ready)"] --> B["正在执行 (running)"]
B --> C["执行成功 (success)"]
B --> D["执行失败 (fail)"]
```

**Diagram sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L20-L30)

### 节点配置与阶段定义

`publish_plan_node_config.py`文件定义了所有可用的发布节点及其参数。例如：
- **构建/部署 (CICD)**: 需要指定应用名和环境。
- **产线申请 (PUBLISH_APPLY)**: 需要选择提交的SQL。
- **发布决策 (PUBLISH_DECISION)**: 需要指定邮件通知列表。
- **节点发布 (PUBLISH)**: 可配置发布时间和发布策略。
- **自动化测试 (AUTO_TEST)**: 需要选择业务分支、环境和编排名称。
- **归档 (ARCHIVE)**: 可选择归档策略。

这些节点被组织成不同的阶段（Phase），如`VERIFY`（验证）、`BEFORE_PUBLISH`（上线前）、`PROCESS_PUBLISH`（上线中）和`AFTER_PUBLISH`（上线后）。

**Section sources**
- [publish_plan_node_config.py](file://iter_mgt/publish_plan/config/publish_plan_node_config.py#L1-L138)

## 发布计划状态枚举

`publish_plan_status_enum.py`文件定义了发布计划和节点的全局状态。

### 发布计划状态 (IterMgtPublishPlanEnum)

- **running**: 正在执行
- **success**: 执行成功
- **fail**: 执行失败
- **stop**: 终止

### 节点状态 (NodeStatusEnum)

- **ready**: 等待调用
- **running**: 正在执行
- **success**: 执行成功
- **fail**: 执行失败

**Section sources**
- [publish_plan_status_enum.py](file://iter_mgt/publish_plan/enums/publish_plan_status_enum.py#L1-L42)

## RESTful API 接口说明

### 创建发布计划

- **端点**: `POST /api/publish_plan/`
- **请求体**: 包含`module_name`, `branch_name`, `iter_id`, `plan_type`等字段的JSON。
- **响应码**:
  - `201 Created`: 创建成功
  - `400 Bad Request`: 请求参数无效
  - `403 Forbidden`: 权限不足

### 查询发布计划

- **端点**: `GET /api/publish_plan/{batch_no}/`
- **响应码**:
  - `200 OK`: 返回计划详情
  - `404 Not Found`: 计划不存在

### 更新发布计划

- **端点**: `PUT /api/publish_plan/{batch_no}/`
- **说明**: 可更新计划的参数，但状态为`running`或`success`的计划不可修改。

### 删除发布计划

- **端点**: `DELETE /api/publish_plan/{batch_no}/`
- **说明**: 仅允许删除状态为`ready`的计划。

**Section sources**
- [publish_plan_view.py](file://iter_mgt/publish_plan/publish_plan_view.py)

## 完整发布流程示例

以下是一个创建特急发布计划并执行的完整流程：

1.  **创建计划**: 调用`POST /api/publish_plan/`，指定`plan_type`为`express`。
2.  **添加应用**: 在创建的计划中，为`CICD`节点配置`module_name`和`build_suite_code`。
3.  **提交审批**: 确保`PUBLISH_APPLY`和`PUBLISH_DECISION`节点的参数已填写，将计划状态推进。
4.  **执行发布**: 系统自动或手动触发`PUBLISH`节点，开始部署。
5.  **完成归档**: 发布成功后，`ARCHIVE`节点自动执行，完成流程。

## 权限校验与冲突处理

### 权限校验

所有API调用均需进行身份认证和权限校验。用户必须拥有对应应用和环境的操作权限才能创建或修改发布计划。

### 处理发布冲突

系统通过`batch_no`和`unique_together`约束（`module_name`, `iter_id`, `batch_no`）来确保计划的唯一性。当检测到同一应用在同一迭代下存在未完成的发布计划时，系统将拒绝新的创建请求，以防止并发冲突。

**Section sources**
- [iter_mgt_publish_plan.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan.py#L25-L28)
- [iter_mgt_publish_plan_node.py](file://iter_mgt/publish_plan/model/iter_mgt_publish_plan_node.py#L40-L42)