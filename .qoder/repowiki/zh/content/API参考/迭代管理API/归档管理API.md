# 归档管理API

<cite>
**本文档引用文件**   
- [archive_step_enum.py](file://iter_mgt/archive_step_enum.py)
- [archive_view.py](file://iter_mgt/archive_view.py)
- [models.py](file://iter_mgt/models.py)
- [urls.py](file://iter_mgt/urls.py)
- [plan_ser.py](file://iter_mgt/plan_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [归档流程与API操作](#归档流程与api操作)
3. [归档申请与参数校验](#归档申请与参数校验)
4. [Jenkins集成机制](#jenkins集成机制)
5. [归档状态回传](#归档状态回传)
6. [错误处理与重试机制](#错误处理与重试机制)
7. [完整归档操作示例](#完整归档操作示例)
8. [归档日志记录](#归档日志记录)

## 简介
归档管理API提供了一套完整的归档申请、审批和执行流程接口，用于管理软件迭代的归档过程。该系统通过与Jenkins集成，实现了自动化归档流程，包括代码比对、构建、部署验证等阶段。API设计遵循RESTful原则，提供了清晰的接口定义和状态管理机制，确保归档过程的可追溯性和可靠性。

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [urls.py](file://iter_mgt/urls.py#L1-L68)

## 归档流程与API操作
归档流程由多个阶段组成，每个阶段对应特定的API操作。`archive_step_enum.py`文件定义了归档流程的各个阶段，包括配置检查、代码合并、数据更新等。

```mermaid
classDiagram
class ArchiveStepEnum {
+CHECK_CONFIG : ('1-1-0', "check_config", '宙斯配置检查')
+ARCHIVE_CONFIG : ('1-1-1', "archive_config", '宙斯配置归档')
+PRE_ITER_ARCHIVE : ('2-1-0', "pre_iter_archive", '迭代预归档')
+COMMON_PRE_ITER_ARCHIVE : ('2-1-1', "common_pre_iter_archive", '依赖包迭代预归档')
+CODE_MERGE : ('2-2-0', "code_merge", '代码合并')
+ITER_DATA_UPDATE : ('2-2-1', "iter_data_update", '迭代数据更新')
+DELETE_JENKINS_PIPELINE : ('2-2-2', "delete_jenkins_pipeline", '删除Jenkins流水线')
+UPDATE_ARCHIVE_MSG : ('2-2-3', "update_archive_msg", '更新归档信息')
+COMMON_CODE_MERGE : ('2-3-1', "common_code_merge", '依赖包代码合并')
+COMMON_ITER_DATA_UPDATE : ('2-3-2', "common_iter_data_update", '依赖包迭代数据更新')
+UPDATE_COMMON_ARCHIVE_MSG : ('2-3-3', "update_common_archive_msg", '更新依赖包归档信息')
+SEND_ARCHIVE_INFO_TO_RMQ : ('2-4-0', "send_archive_info_to_rmq", '发送归档信息到RMQ')
+ITER_SQL_ARCHIVE : ('2-4-1', "iter_sql_archive", '迭代sql归档')
}
```

**Diagram sources **
- [archive_step_enum.py](file://iter_mgt/archive_step_enum.py#L1-L18)

**Section sources**
- [archive_step_enum.py](file://iter_mgt/archive_step_enum.py#L1-L18)

## 归档申请与参数校验
归档申请通过`ArchiveViewSet`的`create`方法实现，需要提供迭代ID作为参数。系统会对申请进行参数校验，包括分支合法性检查和代码差异分析。

```mermaid
sequenceDiagram
participant 客户端
participant ArchiveViewSet
participant SSHConnectionManager
客户端->>ArchiveViewSet : POST /archive/
ArchiveViewSet->>ArchiveViewSet : 验证请求参数
ArchiveViewSet->>ArchiveViewSet : 检查归档是否正在执行
ArchiveViewSet->>SSHConnectionManager : 执行归档脚本
SSHConnectionManager-->>ArchiveViewSet : 返回执行结果
ArchiveViewSet-->>客户端 : 返回API响应
```

**Diagram sources **
- [archive_view.py](file://iter_mgt/archive_view.py#L45-L95)

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L45-L95)
- [plan_ser.py](file://iter_mgt/plan_ser.py#L1-L258)

## Jenkins集成机制
归档系统通过JenkinsJobMgt类与Jenkins集成，触发归档相关的流水线任务。集成机制包括任务调用、状态监控和结果回传。

```mermaid
sequenceDiagram
participant 归档系统
participant JenkinsJobMgt
participant Jenkins
归档系统->>JenkinsJobMgt : 调用call_jenkins_job
JenkinsJobMgt->>Jenkins : 发送Jenkins API请求
Jenkins-->>JenkinsJobMgt : 返回任务ID
JenkinsJobMgt-->>归档系统 : 返回调用结果
loop 状态监控
归档系统->>Jenkins : 查询任务状态
Jenkins-->>归档系统 : 返回当前状态
end
```

**Diagram sources **
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [models.py](file://iter_mgt/models.py#L1-L423)

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [models.py](file://iter_mgt/models.py#L1-L423)

## 归档状态回传
归档状态通过日志文件和数据库记录两种方式回传。系统会定期检查归档日志，更新归档状态。

```mermaid
flowchart TD
A[开始] --> B[执行归档脚本]
B --> C[生成归档日志]
C --> D[更新数据库状态]
D --> E[发送状态通知]
E --> F[结束]
subgraph 状态监控
G[定时检查日志]
H[解析最新日志行]
I[判断是否完成]
G --> H --> I
I --> |未完成| G
I --> |已完成| J[更新最终状态]
end
```

**Diagram sources **
- [archive_view.py](file://iter_mgt/archive_view.py#L15-L44)
- [models.py](file://iter_mgt/models.py#L380-L399)

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L15-L44)
- [models.py](file://iter_mgt/models.py#L380-L399)

## 错误处理与重试机制
系统实现了完善的错误处理机制，包括归档冲突、构建失败等场景的处理。对于可重试的错误，系统会自动进行重试。

```mermaid
flowchart TD
A[归档请求] --> B{检查归档是否正在执行}
B --> |是| C[返回错误: 归档正在执行]
B --> |否| D[执行归档]
D --> E{执行成功?}
E --> |是| F[返回成功]
E --> |否| G{错误类型}
G --> |可重试错误| H[记录重试次数]
H --> I{达到最大重试次数?}
I --> |否| J[延迟后重试]
J --> D
I --> |是| K[返回最终错误]
G --> |不可重试错误| K
```

**Diagram sources **
- [archive_view.py](file://iter_mgt/archive_view.py#L70-L95)
- [models.py](file://iter_mgt/models.py#L380-L399)

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L70-L95)
- [models.py](file://iter_mgt/models.py#L380-L399)

## 完整归档操作示例
以下是一个完整的归档操作示例，展示了如何通过API完成一次归档操作。

```mermaid
sequenceDiagram
participant 客户端
participant 归档API
participant Jenkins
participant 数据库
客户端->>归档API : 发送归档申请
归档API->>归档API : 验证参数和状态
归档API->>数据库 : 记录归档请求
归档API->>Jenkins : 触发归档流水线
Jenkins-->>归档API : 返回任务ID
归档API-->>客户端 : 返回成功响应
loop 状态轮询
客户端->>归档API : 查询归档状态
归档API->>数据库 : 获取最新日志
归档API-->>客户端 : 返回当前状态
end
Jenkins->>归档API : 发送完成通知
归档API->>数据库 : 更新最终状态
归档API->>客户端 : 发送完成通知
```

**Diagram sources **
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [urls.py](file://iter_mgt/urls.py#L1-L68)

**Section sources**
- [archive_view.py](file://iter_mgt/archive_view.py#L1-L447)
- [urls.py](file://iter_mgt/urls.py#L1-L68)

## 归档日志记录
归档过程中的所有操作都会被记录到`IterMgtArchiveLog`模型中，便于后续审计和问题排查。

```mermaid
erDiagram
ITER_MGT_ARCHIVE_LOG {
string iteration_id PK
string step_order
string step_name
string step_desc
string step_status
json request_params
json response_result
datetime opt_time
string opt_user
}
```

**Diagram sources **
- [models.py](file://iter_mgt/models.py#L380-L399)

**Section sources**
- [models.py](file://iter_mgt/models.py#L380-L399)