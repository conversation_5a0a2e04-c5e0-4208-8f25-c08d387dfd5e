# 可用区配置API

<cite>
**本文档引用文件**  
- [models.py](file://env_mgt/models.py)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql)
- [urls.py](file://env_mgt/urls.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
</cite>

## 目录
1. [简介](#简介)
2. [可用区定义与核心模型](#可用区定义与核心模型)
3. [资源配置与模板管理](#资源配置与模板管理)
4. [REST端点与接口说明](#rest端点与接口说明)
5. [可用区与物理机房及灾备策略映射](#可用区与物理机房及灾备策略映射)
6. [跨可用区部署配置示例](#跨可用区部署配置示例)
7. [容量监控与自动扩容机制](#容量监控与自动扩容机制)
8. [权限控制与配置变更流程](#权限控制与配置变更流程)
9. [SQL Schema参考](#sql-schema参考)
10. [典型部署拓扑图](#典型部署拓扑图)

## 简介
本文档详细描述了`env_mgt`模块中关于可用区（Availability Zone）的配置管理API。涵盖可用区的定义、资源配置、模板管理、与节点的关联关系、REST端点、网络配置、与物理机房和灾备策略的映射、跨可用区部署、容量监控、权限控制及配置变更流程。同时提供SQL Schema和部署拓扑图，为系统管理员和开发者提供全面的技术参考。

## 可用区定义与核心模型

可用区是系统中用于组织和隔离计算资源的逻辑单元，通常与物理机房或云服务商的可用区概念相对应。在本系统中，可用区通过`Zone`模型进行管理，其核心属性包括：

- **zone_code**: 可用区编码，全局唯一标识。
- **zone_name**: 可用区名称，如“上海唐镇可用区EHB-生产”。
- **zone_desc**: 可用区说明。
- **zone_is_active**: 可用区是否启用。
- **cmdb_* 字段**: 与CMDB（配置管理数据库）系统对接的字段，用于同步和映射。
- **bread_domain_code**: 与miabao资源域的映射编码。

可用区与“可用域”（Region）存在关联关系。一个可用域可以包含多个可用区，通过`RegionZone`模型实现多对多关联。可用域代表一个更大的地理或逻辑区域，如“上海唐镇”，而可用区是其下的具体部署单元。

**Section sources**
- [models.py](file://env_mgt/models.py#L380-L414)

## 资源配置与模板管理

### 资源规格
可用区的资源配置通过“机型”（VM Type）模板来定义。每个机型模板包含以下资源规格：
- **vm_cpu**: CPU核心数。
- **vm_memory**: 内存大小（MB）。
- **vm_disk**: 硬盘容量和类型。
- **vm_network**: 网卡个数。
- **vm_os**: 操作系统。

这些信息在`NodeVm`模型中定义。

### 模板管理
可用区与机型模板的关联通过`ZoneVm`模型（或历史表`node_apply_vm_bind`）实现。该模型将特定的机型绑定到特定的可用区，从而定义了该可用区可以使用的资源规格。

- **zone_id**: 关联的可用区ID。
- **vm_id**: 关联的机型ID。
- **bind_desc**: 绑定说明。
- **bind_is_active**: 绑定是否生效。

通过启用或禁用`bind_is_active`字段，可以动态控制某个机型模板在特定可用区的可用性，实现资源的灵活管理。

**Section sources**
- [models.py](file://env_mgt/models.py#L340-L360)
- [models.py](file://env_mgt/models.py#L362-L378)
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L110-L134)

## REST端点与接口说明

`env_mgt`模块通过Django REST framework提供了一系列REST API端点，用于管理可用区及其相关资源。

### 核心API端点
根据`urls.py`和`env_mgt.py`中的路由注册，主要的可用区相关API端点如下：

```mermaid
flowchart TD
A["可用区管理API"] --> B["获取可用区列表"]
A --> C["获取可用域-可用区关联"]
A --> D["获取可用区机型模板"]
A --> E["节点申请"]
A --> F["节点回收"]
A --> G["环境套管理"]
B --> H["GET /spider/env_mgt/node_zone_api/"]
C --> I["GET /spider/env_mgt/region_zone_api/"]
D --> J["GET /spider/env_mgt/zone_vm_api/"]
E --> K["POST /spider/env_mgt/node_apply_api/"]
F --> L["POST /spider/env_mgt/recycle_order_api/"]
G --> M["GET /spider/env_mgt/env_mgt_suite_api/"]
```

**Diagram sources**
- [urls.py](file://env_mgt/urls.py#L163-L176)
- [env_mgt.py](file://env_mgt/env_mgt.py#L1092-L1152)

### 创建可用区时的参数
创建或申请节点时，需要指定以下与可用区相关的参数：
- `region_name`: 可用域名称。
- `suite_code`: 环境套编码。
- `zone_code`: 可用区编码。
- `vm_id`: 机型ID。
- `vm_count`: 申请数量。
- `distribute_policy`: 服务器分布策略。

网络配置参数通常由环境套（`Suite`）预定义，包括是否支持Docker、部署路径等。

### 可用区模板版本管理
系统通过`stamp`（版本戳）字段来管理数据的并发更新。所有核心模型（`Zone`, `NodeVm`, `ZoneVm`等）都包含`stamp`字段。在更新记录时，系统会检查`stamp`值，确保数据的一致性，防止并发修改冲突。这可以视为一种乐观锁机制，用于实现模板配置的版本控制。

**Section sources**
- [urls.py](file://env_mgt/urls.py#L163-L176)
- [models.py](file://env_mgt/models.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)

## 可用区与物理机房及灾备策略映射

可用区的设计直接映射了物理机房和灾备策略。

### 物理机房映射
- **上海唐镇**: 对应可用域`howbuy-sh-bs`，下辖多个可用区，如`howbuy-sh-bs-prdehb`（生产）、`howbuy-sh-bs-a`（仿真）。
- **上海外高桥**: 对应可用域`howbuy-sh-wgq`，下辖`howbuy-sh-wgq-ops`等可用区。

`Region`模型中的`addr_name`和`addr_short_name`字段明确标识了物理机房。

### 灾备策略映射
灾备策略通过特定的可用区来实现。例如：
- **宝山PA区**: 专门用于灾备，其`zone_name`为“上海宝山可用区PA-灾备”，`cmdb_env_name`为“灾备环境”。
- **唐镇灾备区**: 在`node_apply_zone`表中存在`relative_region_code = 'bs-zb'`的记录，明确标识了灾备可用区。

通过将应用部署到主生产可用区和灾备可用区，可以实现高可用和灾难恢复。`NodeApply`模型中的`distribute_policy`字段可以用于控制服务器在不同可用区间的分布策略。

**Section sources**
- [models.py](file://env_mgt/models.py#L1-L33)
- [db/分支2_x/2.2.0/11-2.2.0spider上线SQL整理.sql](file://db/分支2_x/2.2.0/11-2.2.0spider上线SQL整理.sql#L155-L156)
- [db/spider迭代3_2_9/01-可用区节点相关-唐镇灾备4个可用区禁用.sql](file://db/spider迭代3_2_9/01-可用区节点相关-唐镇灾备4个可用区禁用.sql#L0-L23)

## 跨可用区部署配置示例

以下是一个跨可用区部署的应用配置示例，以一个名为`howbuy-gateway`的应用为例：

```json
{
  "module_name": "howbuy-gateway",
  "region_name": "上海唐镇",
  "suite_code": "bs-prod-suite",
  "zones": [
    {
      "zone_code": "howbuy-sh-bs-prdehb",
      "vm_id": 1508,
      "vm_count": 4,
      "distribute_policy": "HIGH_AVAILABILITY"
    },
    {
      "zone_code": "howbuy-sh-bs-prdehbfep",
      "vm_id": 1508,
      "vm_count": 2,
      "distribute_policy": "HIGH_AVAILABILITY"
    }
  ],
  "deploy_path": "/opt/howbuy-gateway",
  "health_check_url": "/actuator/health"
}
```

此配置表示`howbuy-gateway`应用在“上海唐镇”可用域的“EHB”和“EHB_FEP”两个可用区进行部署，总共6个节点，采用高可用分布策略，确保服务的稳定性和容错能力。

**Section sources**
- [models.py](file://env_mgt/models.py#L270-L339)

## 容量监控与自动扩容机制

### 容量监控接口
虽然核心模型中未直接体现监控数据，但`Node`模型记录了所有节点的IP、状态和所属可用区。结合外部监控系统，可以通过以下方式实现容量监控：
1.  **获取可用区节点列表**: 调用`GET /spider/env_mgt/env_mgt_node_api/`并过滤`region_id`或`node_ip`。
2.  **统计活跃节点数**: 根据`node_status`（0为使用中）统计每个可用区的资源使用量。
3.  **关联监控数据**: 将节点列表与外部监控系统（如Prometheus）的指标关联，获取CPU、内存、磁盘等实时使用率。

### 自动扩容触发条件
自动扩容的触发逻辑通常在调度系统中实现，但其基础数据来源于此模块。可能的触发条件包括：
- **资源使用率过高**: 当某个可用区的平均CPU或内存使用率持续超过阈值（如80%）超过一定时间。
- **节点负载过高**: 当某个应用在特定可用区的节点平均负载过高。
- **业务流量激增**: 结合业务监控，当QPS或TPS达到预设阈值。

扩容操作会调用`node_apply_api`，根据预设的模板（`vm_id`, `vm_count`）向指定的可用区申请新的节点。

**Section sources**
- [models.py](file://env_mgt/models.py#L200-L269)

## 权限控制与配置变更流程

### 可用区级权限控制
系统通过`create_user`和`update_user`字段记录操作者。更细粒度的权限控制依赖于外部的用户和团队管理系统（`team_mgt`模块）。`NodeApply`模型中的`team_name`和`apply_user`字段用于标识申请团队和申请人，为权限审批提供依据。

### 配置变更审批流程
可用区的配置变更（如创建、修改、禁用）通常遵循以下审批流程：
1.  **申请**: 用户通过前端或API提交`NodeApply`申请。
2.  **基础设施审核**: `set_node_status_infra_audit_pass`和`set_node_status_infra_reject`端点用于处理基础设施团队的审核。
3.  **运维操作**: `OpsOperateNodeApply`端点用于运维团队执行最终的批准或拒绝操作。
4.  **执行**: 审核通过后，系统执行变更（如创建节点、更新绑定关系）。
5.  **通知**: `NodeShutdownResult`和`NodeDeleteResult`端点用于接收和处理节点回收完成的通知。

### 变更后配置同步机制
配置变更后，系统通过以下机制保证同步：
- **版本戳（stamp）**: 所有更新操作都基于`stamp`进行，确保数据一致性。
- **事件通知**: 通过`NodeShutdownResult`等API接收来自底层基础设施（如虚拟化平台）的操作结果，反向同步节点状态。
- **定时同步**: 系统可能通过定时任务从CMDB同步最新的节点和可用区信息，确保数据源一致。

**Section sources**
- [urls.py](file://env_mgt/urls.py#L20-L30)
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py)

## SQL Schema参考

以下是可用区相关核心表的SQL Schema定义：

```sql
-- 可用区表
CREATE TABLE env_mgt_zone (
  id BIGINT(11) auto_increment,
  zone_code VARCHAR(100) NOT NULL UNIQUE COMMENT '可用区编码',
  zone_name VARCHAR(100) COMMENT '可用区名称',
  zone_desc VARCHAR(255) COMMENT '可用区说明',
  zone_is_active TINYINT(1) COMMENT '可用区是否可用',
  cmdb_provider_code VARCHAR(100) COMMENT 'cmdb供应商编码',
  cmdb_provider_name VARCHAR(100) COMMENT 'cmdb供应商名称',
  cmdb_region_code VARCHAR(100) COMMENT 'cmdb可用域编码',
  cmdb_region_name VARCHAR(100) COMMENT 'cmdb可用域名称',
  cmdb_zone_code VARCHAR(100) COMMENT 'cmdb可用区编码',
  cmdb_zone_name VARCHAR(100) COMMENT 'cmdb可用区名称',
  cmdb_env_code VARCHAR(100) COMMENT 'cmdb环境编码',
  cmdb_env_name VARCHAR(100) COMMENT 'cmdb环境名称',
  bread_domain_code VARCHAR(100) COMMENT 'miabao资源域编码',
  bread_domain_name VARCHAR(100) COMMENT 'miabao资源域名称',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '可用区表';

-- 可用区机型绑定表
CREATE TABLE env_mgt_zone_vm (
  id BIGINT(11) auto_increment,
  zone_id BIGINT(11) COMMENT '可用区ID',
  zone_code VARCHAR(100) COMMENT '可用区编码',
  vm_id BIGINT(11) COMMENT '机型ID',
  vm_code BIGINT(11) COMMENT '机型编码',
  bind_desc VARCHAR(255) COMMENT '绑定说明',
  create_user VARCHAR(20) COMMENT '创建人',
  create_time DATETIME(0) COMMENT '创建时间',
  update_user VARCHAR(20) COMMENT '修改人',
  update_time DATETIME(0) COMMENT '修改时间',
  stamp BIGINT(20) COMMENT '版本',
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT = '可用区机型绑定表';
```

**Diagram sources**
- [01-env_mgt_schema.sql](file://env_mgt/db/schema/01-env_mgt_schema.sql#L110-L134)

## 典型部署拓扑图

```mermaid
graph TD
subgraph "上海唐镇可用域"
subgraph "生产可用区"
A["howbuy-sh-bs-prdehb<br/>(EHB-生产)"]
B["howbuy-sh-bs-prdehbfep<br/>(EHB_FEP-生产)"]
end
subgraph "灾备可用区"
C["howbuy-sh-bs-zb<br/>(唐镇灾备)"]
end
end
subgraph "上海外高桥可用域"
D["howbuy-sh-wgq-ops<br/>(外高桥OPS)"]
E["howbuy-sh-wgq-gw<br/>(代理网关)"]
end
A --> |主服务| App[应用: howbuy-gateway]
B --> |主服务| App
C --> |灾备服务| App
D --> |运维服务| Ops[运维平台]
E --> |网关服务| Gateway[API网关]
style A fill:#e6f7ff,stroke:#1890ff
style B fill:#e6f7ff,stroke:#1890ff
style C fill:#fff2e8,stroke:#fa8c16
style D fill:#f9f0ff,stroke:#722ed1
style E fill:#f9f0ff,stroke:#722ed1
```

**Diagram sources**
- [models.py](file://env_mgt/models.py)
- [db/迭代3_0_6/01-基础信息-节点可用区-唐镇新增5个可用区_for_李拓.sql](file://db/迭代3_0_6/01-基础信息-节点可用区-唐镇新增5个可用区_for_李拓.sql#L43-L50)
- [db/迭代2_x/迭代2.6.9/01-外高桥新增三个可用区_for李拓.sql](file://db/迭代2_x/迭代2.6.9/01-外高桥新增三个可用区_for李拓.sql#L9-L11)