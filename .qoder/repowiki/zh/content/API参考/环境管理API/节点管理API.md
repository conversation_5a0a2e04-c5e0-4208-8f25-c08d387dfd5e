# 节点管理API

<cite>
**本文档引用的文件**   
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py)
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py)
- [models.py](file://env_mgt/models.py)
- [urls.py](file://env_mgt/urls.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心接口详解](#核心接口详解)
3. [节点状态机与生命周期](#节点状态机与生命周期)
4. [节点与环境、应用的绑定关系](#节点与环境应用的绑定关系)
5. [批量操作与异步任务](#批量操作与异步任务)
6. [健康检查与故障隔离](#健康检查与故障隔离)
7. [权限控制与审计日志](#权限控制与审计日志)
8. [错误处理](#错误处理)

## 简介

本API文档旨在全面阐述`env_mgt`模块中关于节点管理的核心功能。该系统负责管理物理或虚拟服务器节点的全生命周期，包括注册、配置、启用/禁用、解绑及状态查询等操作。节点是应用部署和运行的基础资源，通过本API，用户可以实现对节点的自动化申请、回收、状态变更和关系绑定，从而支撑高效的DevOps流程。

**节点管理核心流程**：
1.  **申请 (Apply)**：用户通过API提交节点申请单，指定所需资源（如可用区、机型、数量）。
2.  **审核 (Audit)**：申请单需经过`app_ops`和`infra`团队的双重审核。
3.  **开通 (Provision)**：审核通过后，系统调用底层平台（如“面包”平台）创建节点，并通过回调接口将节点信息注册到本系统。
4.  **绑定 (Bind)**：新节点自动与指定的应用和环境套建立绑定关系，使其可被部署系统识别和使用。
5.  **回收 (Recycle)**：当节点不再需要时，可发起回收流程，系统将节点从可用状态移除并最终释放资源。

## 核心接口详解

### 节点注册与信息提交

节点信息主要通过`mianbao`平台的回调接口进行注册，而非由用户直接创建。

**接口**: `POST /node_apply_result`
**功能**: 处理来自`mianbao`平台的节点开通结果回调。
**请求体 (JSON Schema)**:
```json
{
  "order_code": "string", // 关联的节点申请单号
  "node_list": [
    {
      "node_name": "string", // 节点主机名
      "node_ip": "string",   // 节点IP地址
      "minion_id": "string", // SaltStack中的唯一标识符
      "node_os": "string"    // 操作系统信息
    }
  ]
}
```
**响应**:
- 成功：返回`ApiResult.success_dict`，表示节点信息处理成功。
- 失败：返回`ApiResult.failed_dict`，包含错误信息。

**处理逻辑**:
1.  验证申请单状态是否为“infra审核通过”（状态码4）。
2.  根据申请单信息，查找或创建应用的“默认分组”。
3.  在`env_mgt_node`表中创建新的节点记录。
4.  在`env_mgt_node_bind`表中创建节点与应用、环境套的绑定关系。

**Section sources**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L550-L640)

### 节点状态查询

提供查询所有可用产线节点列表的接口。

**接口**: `GET /list`
**功能**: 获取所有状态为“使用中”（`node_status=0`）且IP以`10`开头的产线节点列表。
**响应 (JSON Schema)**:
```json
{
  "msg": "获取「所有产线节点」列表成功",
  "data": {
    "data_list": [
      {
        "id": 123,
        "node_ip": "************",
        "minion_id": "i-1234567890abcdef0",
        "region_id": 1,
        "module_name": "crm-mobile-webapp"
      }
    ]
  }
}
```
**实现**: 该接口调用`env_node_mgt_ser.get_prod_nodes()`函数，执行一个SQL查询，从`env_mgt_node`表中左连接`env_mgt_node_bind`表，筛选出符合条件的节点。

**Section sources**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L15-L25)
- [env_node_mgt_ser.py](file://env_mgt/env_node_mgt_ser.py#L3-L18)

### 节点启用/禁用

节点的启用和禁用通过直接修改`env_mgt_node`表中的`node_status`字段来实现。该操作通常由运维人员通过SQL或管理后台执行。

- **启用**: 将`node_status`从`-1`（待分配）或`1`（待回收）更新为`0`（使用中）。
- **禁用**: 将`node_status`从`0`（使用中）更新为`1`（待回收）。

**示例SQL (启用)**:
```sql
UPDATE env_mgt_node 
SET node_status = 0, 
    update_user = 'huaitian.zhang', 
    update_time = '2023-12-15 10:11:12' 
WHERE id = 5074 AND node_status = -1;
```

**Section sources**
- [models.py](file://env_mgt/models.py#L110-L118)
- [db/迭代3.0.2/04-基础信息-外高桥灾备-启用记录-20231215.sql](file://db/迭代3.0.2/04-基础信息-外高桥灾备-启用记录-20231215.sql#L4-L12)

### 节点解绑

节点的解绑操作通过删除`env_mgt_node_bind`表中的相关记录来完成。当一个节点被回收时，其与所有应用的绑定关系都会被清除。

**示例SQL (解绑并删除节点)**:
```sql
DELETE node, bind 
FROM env_mgt_node node 
LEFT JOIN env_mgt_node_bind bind ON bind.node_id = node.id 
WHERE node.node_ip = '***************' and node.node_name = 'CS_0264';
```

**Section sources**
- [models.py](file://env_mgt/models.py#L130-L144)
- [db/迭代2_x/迭代2.7.2/02-手动清理已删除节点_for李拓.sql](file://db/迭代2_x/迭代2.7.2/02-手动清理已删除节点_for李拓.sql#L22-L42)

## 节点状态机与生命周期

节点在其生命周期中会经历一系列状态转换，这些状态由`env_mgt_node`表中的`node_status`字段定义。

```mermaid
stateDiagram-v2
[*] --> 待分配
待分配 : node_status = -1
待分配 --> 使用中 : 申请单审核通过并开通
使用中 : node_status = 0
使用中 --> 待回收 : 发起回收申请
待回收 : node_status = 1
待回收 --> 回收中 : infra审核通过
回收中 : node_status = 2
回收中 --> 已回收 : 回收完成
已回收 : node_status = 3
```

**触发条件与转换逻辑**:
- **待分配 → 使用中**: 当`NodeApplyResult`接口成功处理`mianbao`的回调时，新创建的节点初始状态为`0`（使用中）。如果节点是通过其他方式（如对账）手工插入的，其状态可能为`-1`（待分配），需手动更新为`0`。
- **使用中 → 待回收**: 用户通过`NodeRecycleOrderApi`创建回收单，将节点标记为待回收。
- **待回收 → 回收中**: `infra`团队审核通过回收单，系统将节点状态更新为`2`。
- **回收中 → 已回收**: `mianbao`平台完成节点的物理删除或释放后，通过`node_delete_notify`等接口通知本系统，最终将状态更新为`3`。

**Diagram sources**
- [models.py](file://env_mgt/models.py#L110-L118)

## 节点与环境、应用的绑定关系

节点与应用、环境之间的关系通过`env_mgt_node_bind`（节点绑定表）进行管理。

**核心表结构**:
- **`env_mgt_node`**: 存储节点的元数据，如IP、主机名、操作系统、`minion_id`、可用区（`region_id`）等。
- **`env_mgt_node_bind`**: 核心关联表，定义了节点与应用、环境套的绑定关系。
  - `module_name`: 应用模块名称。
  - `suite_id`: 环境套ID，通过`Suite`表关联。
  - `node_id`: 节点ID，通过`Node`表关联。
  - `deploy_group`: 部署分组ID，用于对同一应用的不同节点进行分组管理。
  - `enable_bind`: 绑定关系是否启用（0: 禁用, 1: 启用），用于实现节点的逻辑启用/禁用。

**关系管理机制**:
1.  **自动绑定**: 当新节点通过`NodeApplyResult`接口注册时，系统会自动为其创建一条与申请单中指定应用和环境套的绑定记录。
2.  **手动绑定/解绑**: 运维人员可以通过管理后台或直接操作数据库来修改`env_mgt_node_bind`表，实现节点的重新绑定或解绑。

**Section sources**
- [models.py](file://env_mgt/models.py#L130-L144)

## 批量操作与异步任务

系统支持节点的批量回收操作，该操作通过创建“回收批次单”来实现。

**接口**: `POST /save_node_recycle_order`
**功能**: 创建一个包含多个节点的批量回收单。
**请求体**:
```json
{
  "data": {
    "node_list": [
      "{\"id\": 3149, \"node_name\": \"CS_0264\", \"node_ip\": \"***************\"}",
      "{\"id\": 3157, \"node_name\": \"CS_0296\", \"node_ip\": \"***************\"}"
    ]
  }
}
```
**处理流程**:
1.  生成唯一的回收批次号（`recycle_batch_code`）。
2.  在`node_apply_recycle_batch`表中创建批次记录。
3.  为`node_list`中的每个节点，在`node_apply_recycle_order`表中创建一条回收订单记录。
4.  发送邮件通知相关人员进行审核。

此过程在一个数据库事务中执行，确保了数据的一致性。这是一个典型的异步任务入口，后续的审核、执行等步骤由其他系统或人工介入完成。

**Section sources**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L720-L780)

## 健康检查与故障隔离

虽然当前代码中未直接体现健康检查的集成逻辑，但`env_mgt_node_bind`表中预留了`health_check_url`字段，用于存储该节点上应用的健康检查地址。

**集成方式**:
1.  在创建或更新节点绑定关系时，可以填充`health_check_url`字段。
2.  外部的健康检查系统（如Prometheus、Zabbix）可以定期访问此URL，探测节点上应用的健康状态。

**故障节点自动隔离策略**:
- 本系统本身不直接执行隔离，但可以作为隔离策略的**数据源**。
- 当健康检查系统发现某个节点持续不可用时，它可以调用本系统的API（如`NodeInfraReject`或`NodeRecycleOrderApi`），将该节点的状态标记为“待回收”或直接发起回收流程，从而实现自动隔离。

**Section sources**
- [models.py](file://env_mgt/models.py#L138)

## 权限控制与审计日志

### 权限控制

系统通过用户角色来控制对节点管理API的访问权限。

**接口**: `GET /get_ops_permission`
**功能**: 检查当前用户是否具有`app_ops`权限。
**逻辑**:
- 调用`env_mgt_ser.get_ops_permission(username)`获取用户权限ID。
- 如果权限ID为`60`，则认为用户有权限，返回`permission_disable: false`；否则返回`true`。
- 该权限主要用于控制用户是否可以审核节点申请单或回收单。

**Section sources**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L650-L660)

### 审计日志

系统通过以下方式实现审计：
1.  **数据库字段**: 每个核心数据表（如`Node`, `NodeBind`, `NodeApplyOrder`）都包含`create_user`, `create_time`, `update_user`, `update_time`字段，记录了数据的创建和修改历史。
2.  **邮件通知**: 关键操作（如创建申请单、审核通过/拒绝、节点交付、发起回收）都会触发邮件通知，邮件内容包含了操作的详细信息，如订单号、操作人、操作类型、服务器列表等，形成了完整的审计线索。

**Section sources**
- [models.py](file://env_mgt/models.py#L116-L117)
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L120-L140)

## 错误处理

API采用统一的响应格式进行错误处理。

**错误响应格式**:
```json
{
  "status": "failed",
  "msg": "具体的错误信息",
  "data": {}
}
```

**常见错误场景**:
- **参数校验失败**: 例如，`NodeApplyOrderApi.create`接口会检查`zone_id`和`zone_code`是否为空，若为空则返回“zone_id不能为空”或“zone_code不能为空”的错误。
- **业务逻辑错误**: 例如，`NodeApplyApi.create`在调用`mianbao`接口失败时，会返回“节点申请失败，面包接口调用异常”的错误。
- **数据库异常**: 例如，在`NodeApplyResult.create`方法中，如果数据库事务执行失败，会捕获`Exception`并返回“申请单{}对应的节点信息处理失败”的错误。

**Section sources**
- [env_node_mgt.py](file://env_mgt/env_node_mgt.py#L180-L190)