# 扩展开发

<cite>
**本文档中引用的文件**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [task_queue.py](file://task_mgt/task_queue.py)
- [script_param_bo.py](file://task_mgt/async_exec_scripts/script_param_bo.py)
- [publish_plan_view.py](file://iter_mgt/publish_plan/publish_plan_view.py)
- [app_auto_test_result_check.py](file://iter_mgt/app_auto_test_result_check.py)
- [servers_view.py](file://ci_cd_mgt/server/servers_view.py)
- [jenkins_job_auto_mgt](file://jenkins_mgt/jenkins_job_auto_mgt)
- [PRPs/templates/prp_base.md](file://PRPs/templates/prp_base.md)
- [businesscode-root-paths-interface.md](file://PRPs/businesscode-root-paths-interface.md)
- [settings.py](file://spider/settings.py)
- [models.py](file://app_mgt/models.py)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
- [db_iter_mgt_pipeline_ser.py](file://db_mgt/db_iter_mgt_pipeline_ser.py)
- [saltapi.py](file://public/saltapi.py)
- [call_script.py](file://public/call_script.py)
- [cmdb.py](file://public/cmdb.py)
- [generate_config.py](file://generate_config.py)
</cite>

## 目录
1. [引言](#引言)
2. [系统架构与扩展点](#系统架构与扩展点)
3. [API设计原则与自定义集成](#api设计原则与自定义集成)
4. [自定义模块开发示例](#自定义模块开发示例)
5. [外部系统集成方法](#外部系统集成方法)
6. [数据模型扩展最佳实践](#数据模型扩展最佳实践)
7. [性能优化建议](#性能优化建议)
8. [测试策略与质量保证](#测试策略与质量保证)
9. [发布与版本管理](#发布与版本管理)
10. [附录](#附录)

## 引言
本指南旨在为开发者提供一套完整的扩展开发框架，涵盖插件机制、自定义集成、二次开发等核心内容。系统基于模块化设计，支持通过API、任务队列、配置管理等方式进行功能扩展。主要扩展领域包括应用管理、环境管理、迭代发布、CI/CD流水线等。通过本指南，开发者可快速理解系统扩展机制并实现定制化功能。

## 系统架构与扩展点
系统采用分层微服务架构，各业务模块独立部署并通过统一接口交互。核心扩展点分布在应用管理、环境管理、迭代管理和发布管理等模块中。

```mermaid
graph TB
subgraph "前端界面"
UI[用户界面]
end
subgraph "API网关"
Gateway[HTTP路由分发]
end
subgraph "业务服务层"
AppMgt[应用管理模块]
EnvMgt[环境管理模块]
IterMgt[迭代管理模块]
Pipeline[流水线模块]
Publish[发布管理模块]
CICD[CI/CD管理模块]
end
subgraph "基础支撑层"
TaskQueue[异步任务队列]
Config[配置中心]
CMDB[配置管理数据库]
SaltAPI[Salt远程执行]
end
UI --> Gateway
Gateway --> AppMgt
Gateway --> EnvMgt
Gateway --> IterMgt
Gateway --> Pipeline
Gateway --> Publish
Gateway --> CICD
AppMgt --> TaskQueue
EnvMgt --> TaskQueue
IterMgt --> TaskQueue
Pipeline --> TaskQueue
Publish --> TaskQueue
TaskQueue --> Config
TaskQueue --> CMDB
TaskQueue --> SaltAPI
```

**图示来源**
- [spider/settings.py](file://spider/settings.py)
- [app_mgt/models.py](file://app_mgt/models.py)
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)

**本节来源**
- [说明/software_structure.md](file://说明/software_structure.md)
- [说明/README.md](file://说明/README.md)

## API设计原则与自定义集成
系统API遵循RESTful设计规范，采用统一的请求/响应结构和错误码体系。所有接口通过`ViewSet`实现，支持参数校验、权限控制和日志记录。

关键设计原则：
- **接口幂等性**：GET/PUT/DELETE操作保证幂等
- **版本控制**：通过URL路径实现版本管理（如/v1/...）
- **统一响应格式**：
```json
{
  "status": "success|error",
  "data": {},
  "message": ""
}
```
- **异常处理**：全局异常处理器统一返回标准错误码

自定义集成需遵循以下规范：
1. 继承`ViewSet`基类实现新接口
2. 使用`@action`装饰器定义资源操作
3. 通过`serializer`进行参数校验
4. 记录操作日志到`OpLogs`

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Gateway as "API网关"
participant Service as "业务服务"
participant TaskQueue as "任务队列"
Client->>Gateway : HTTP请求
Gateway->>Service : 路由分发
Service->>Service : 参数校验
Service->>Service : 权限验证
Service->>TaskQueue : 提交异步任务
TaskQueue-->>Service : 任务ID
Service-->>Client : 返回任务状态
TaskQueue->>TaskQueue : 执行任务
TaskQueue->>Client : 回调通知(可选)
```

**图示来源**
- [app_mgt/app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [iter_mgt/publish_plan/publish_plan_view.py](file://iter_mgt/publish_plan/publish_plan_view.py)
- [ci_cd_mgt/server/servers_view.py](file://ci_cd_mgt/server/servers_view.py)

**本节来源**
- [app_mgt/app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [tapd_mgt/tapd_views.py](file://tapd_mgt/tapd_views.py)

## 自定义模块开发示例
### 代码结构
新模块应遵循标准目录结构：
```
custom_module/
├── models.py        # 数据模型
├── views.py         # 接口视图
├── serializers.py   # 序列化器
├── tasks.py         # 异步任务
├── urls.py          # 路由配置
└── tests.py         # 单元测试
```

### 依赖管理
通过`requirements.txt`管理Python依赖，新增依赖需经过安全审查。内部库使用私有PyPI源配置：
```ini
[global]
index-url = http://pypi.howbuy.pa/simple
trusted-host = pypi.howbuy.pa
```

### 部署方式
1. **代码提交**：推送到指定Git仓库
2. **构建触发**：通过Jenkins流水线自动构建
3. **部署执行**：SaltStack批量部署到目标节点
4. **服务注册**：自动注册到服务发现系统

```mermaid
flowchart TD
Start([代码提交]) --> Trigger["触发Jenkins构建"]
Trigger --> Build["执行构建脚本"]
Build --> Test["运行单元测试"]
Test --> Package["打包制品"]
Package --> Deploy["Salt部署到目标节点"]
Deploy --> Register["服务注册"]
Register --> Health["健康检查"]
Health --> End([服务就绪])
Test --> |失败| Fail["构建失败"]
Deploy --> |失败| Rollback["自动回滚"]
```

**图示来源**
- [jenkins_mgt/jenkins_job_auto_mgt](file://jenkins_mgt/jenkins_job_auto_mgt)
- [task_mgt/salt_ser.py](file://task_mgt/salt_ser.py)
- [public/saltapi.py](file://public/saltapi.py)

**本节来源**
- [jenkins_mgt/jenkins_job_auto_mgt](file://jenkins_mgt/jenkins_job_auto_mgt)
- [generate_config.py](file://generate_config.py)

## 外部系统集成方法
### REST API集成
使用`HttpTask`封装HTTP客户端，支持GET/POST/PUT/DELETE方法：
```python
with HttpTask() as http_task:
    status, result = http_task.send_request(
        method="post",
        url=request_url,
        params=params,
        interface_name="create_resource",
        headers=None
    )
```

### 消息队列集成
通过任务队列实现异步通信，支持以下消息类型：
- `interface`：接口调用
- `script`：脚本执行
- `config`：配置同步

消息发送示例：
```python
task_queue.enter_queue(
    TaskTypeObject.interface,
    "check_config_merge",
    action_id,
    {
        "iteration_number": br_name,
        "app_name": namespace
    }
)
```

### 数据库同步
使用`db_query.py`和`db_query_pool.py`实现多数据库连接，支持：
- 跨库查询
- 事务管理
- 连接池优化

```mermaid
graph LR
A[应用服务] --> B[主数据库]
A --> C[CMDB]
A --> D[日志库]
B --> |定时同步| E[数据仓库]
C --> |实时同步| F[配置中心]
D --> |日志采集| G[ELK集群]
```

**图示来源**
- [task_mgt/task_queue.py](file://task_mgt/task_queue.py)
- [db_mgt/db_iter_mgt_pipeline_ser.py](file://db_mgt/db_iter_mgt_pipeline_ser.py)
- [public/http_task.py](file://public/http_task.py)

**本节来源**
- [task_mgt/task_queue.py](file://task_mgt/task_queue.py)
- [external_interaction/mantis_mgt/mantis_adapter_view.py](file://external_interaction/mantis_mgt/mantis_adapter_view.py)

## 数据模型扩展最佳实践
### 字段添加
1. 创建Django迁移文件：
```bash
python manage.py makemigrations
```
2. 遵循命名规范：
   - 字段名使用小写下划线
   - 添加`verbose_name`注释
   - 设置合理的`max_length`

### 关系建立
支持三种关系类型：
- `ForeignKey`：一对多
- `OneToOneField`：一对一
- `ManyToManyField`：多对多

### 迁移策略
1. **正向迁移**：先更新数据库，再部署代码
2. **反向兼容**：新代码兼容旧数据结构
3. **数据初始化**：通过`data migration`填充默认值

```mermaid
erDiagram
APP_MGT_APP_BUILD {
string module_name PK
string package_type
string git_url
datetime created_at
boolean is_active
}
ENV_MGT_NODE_BIND {
int id PK
string app_name FK
string node_ip
string env_type
datetime bind_time
}
PUBLISH_PLAN {
int id PK
string iteration_id FK
string publish_type
json config_params
datetime create_time
}
APP_MGT_APP_BUILD ||--o{ ENV_MGT_NODE_BIND : "应用-节点绑定"
PUBLISH_PLAN }o--|| APP_MGT_APP_BUILD : "发布-应用关联"
```

**图示来源**
- [app_mgt/models.py](file://app_mgt/models.py)
- [env_mgt/models.py](file://env_mgt/models.py)
- [iter_mgt/models.py](file://iter_mgt/models.py)

**本节来源**
- [app_mgt/migrations](file://app_mgt/migrations)
- [db/schema](file://db/db/schema)

## 性能优化建议
1. **数据库优化**：
   - 为常用查询字段添加索引
   - 避免N+1查询问题
   - 使用`select_related`和`prefetch_related`

2. **缓存策略**：
   - 频繁读取的配置信息使用Redis缓存
   - 设置合理的过期时间
   - 缓存穿透防护

3. **异步处理**：
   - 耗时操作放入任务队列
   - 控制并发任务数量
   - 实现任务重试机制

4. **代码层面**：
   - 避免在循环中进行数据库操作
   - 使用生成器处理大数据集
   - 及时释放资源

**本节来源**
- [task_mgt/mysql_queue.py](file://task_mgt/mysql_queue.py)
- [public/saltapi.py](file://public/saltapi.py)
- [db_query_pool.py](file://db_query_pool.py)

## 测试策略与质量保证
### 测试类型
1. **单元测试**：验证单个函数/方法
2. **集成测试**：验证模块间交互
3. **端到端测试**：验证完整业务流程
4. **性能测试**：验证系统负载能力

### 质量保证要求
1. **代码覆盖率**：单元测试覆盖率不低于80%
2. **静态检查**：通过flake8、mypy等工具检查
3. **安全扫描**：定期进行依赖漏洞扫描
4. **自动化测试**：CI流水线中集成自动化测试

测试执行流程：
```mermaid
flowchart TD
A[代码提交] --> B[触发CI流水线]
B --> C[代码静态检查]
C --> D[单元测试]
D --> E[集成测试]
E --> F[安全扫描]
F --> G[构建制品]
G --> H[部署到测试环境]
H --> I[端到端测试]
I --> J[人工评审]
J --> K[部署到生产环境]
```

**图示来源**
- [iter_mgt/app_auto_test_result_check.py](file://iter_mgt/app_auto_test_result_check.py)
- [task_mgt/jacoco_ser.py](file://task_mgt/jacoco_ser.py)

**本节来源**
- [iter_mgt/app_auto_test_result_check.py](file://iter_mgt/app_auto_test_result_check.py)
- [test_mgt/models.py](file://test_mgt/models.py)

## 发布与版本管理
### 版本管理
采用语义化版本控制（SemVer）：
- `MAJOR.MINOR.PATCH` 格式
- 主版本号变更表示不兼容的API修改
- 次版本号变更表示向后兼容的功能新增
- 修订号变更表示向后兼容的问题修正

版本管理流程：
```mermaid
graph TB
A[功能开发] --> B[代码审查]
B --> C[合并到develop]
C --> D[创建发布分支]
D --> E[测试验证]
E --> F[修复缺陷]
F --> E
E --> G[打版本标签]
G --> H[生产发布]
H --> I[版本归档]
```

### 发布流程
1. 创建发布计划
2. 执行预发布检查
3. 分阶段灰度发布
4. 监控运行状态
5. 完成全量发布

**本节来源**
- [PRPs/version-management.md](file://PRPs/version-management.md)
- [iter_mgt/archive_view.py](file://iter_mgt/archive_view.py)

## 附录
### 常用配置参数
**系统配置**
- `settings.py`：主配置文件
- `gunicorn.conf.py`：Gunicorn服务器配置
- `Dockerfile`：容器化配置

**任务参数**
```python
class ScriptParamBO:
    app_name: str
    suite_code: str
    res_type: str
    opt_type: str
    ip: str
    action_id: int
    end_ver: str
    iteration_id: str
```

### 错误码定义
| 错误码 | 含义 | 处理建议 |
|-------|------|---------|
| 400 | 请求参数错误 | 检查参数格式和必填项 |
| 401 | 未授权 | 检查认证信息 |
| 403 | 禁止访问 | 检查权限配置 |
| 404 | 资源不存在 | 检查资源ID |
| 500 | 服务器内部错误 | 查看服务日志 |

**本节来源**
- [PRPs/templates/prp_base.md](file://PRPs/templates/prp_base.md)
- [businesscode-root-paths-interface.md](file://PRPs/businesscode-root-paths-interface.md)
- [exceptions.py](file://public/exceptions.py)