# 配置管理

<cite>
**本文档中引用的文件**  
- [settings.ini](file://be-scripts/settings.ini) - *在最近的提交中更新*
- [settings.py](file://be-scripts/settings.py) - *在最近的提交中更新*
- [k8s.ini](file://be-scripts/publish_tool/config/k8s.ini) - *在最近的提交中新增*
</cite>

## 更新摘要
**变更内容**   
- 更新了 `settings.ini` 和 `settings.py` 文件以支持新的 K8S 配置
- 新增了 `k8s.ini` 配置文件用于管理 Kubernetes 操作
- 在配置加载机制中增加了对多配置文件的支持
- 更新了配置参数清单以包含新的 K8S 操作配置项
- 修正了配置文件结构和加载机制的描述以反映最新实现

## 目录
1. [简介](#简介)
2. [配置文件结构](#配置文件结构)
3. [配置加载机制与优先级](#配置加载机制与优先级)
4. [配置参数清单](#配置参数清单)
5. [系统行为定制](#系统行为定制)
6. [运维最佳实践与常见陷阱](#运维最佳实践与常见陷阱)

## 简介
本项目通过 `settings.ini` 和 `settings.py` 两个核心文件实现配置管理。`settings.ini` 是主要的配置存储文件，采用 INI 格式，包含多个命名节（section）和键值对（key-value），用于定义数据库连接、外部服务地址、环境标识等系统参数。`settings.py` 是配置加载和解析的 Python 模块，负责读取 `settings.ini` 文件内容，并将其转换为 Python 字典或变量，供其他模块导入和使用。此外，项目新增了 `k8s.ini` 文件专门用于管理 Kubernetes 相关操作的配置。这种分离设计使得配置管理更加灵活，便于维护和扩展。

**Section sources**
- [settings.ini](file://be-scripts/settings.ini#L1-L585)
- [settings.py](file://be-scripts/settings.py#L1-L823)
- [k8s.ini](file://be-scripts/publish_tool/config/k8s.ini#L1-L29)

## 配置文件结构

### settings.ini 文件结构
`settings.ini` 文件由多个节（section）组成，每个节包含一组相关的配置项。主要节包括：

- **[CONFIG_DIR_FLAG]**: 定义环境标识与目录名称的映射。
- **[CONFIG_STORE]**: 配置库的本地路径和 Git URL。
- **[INTERFACE_URL]**: 各个外部服务（如 Zeus、Spider、Mantis）的 API 地址和端口。
- **[SALT_API]**: SaltStack API 的访问地址，按环境区分。
- **[SALT_API_PASSWORD]** 和 **[SALT_API_USER]**: SaltStack API 的用户名和密码。
- **[TRANSIT_IP]**: 不同环境的中转服务器 IP 地址。
- **[EMAIL]**: 邮件服务器配置，包括主机、发件人和密码。
- **[GITCODESTORE]**: GitLab 代码仓库的 HTTP 地址和访问令牌。
- **[JENKINS_INFO]**: Jenkins 服务器的连接信息，包括 IP、URL、用户名和密码。
- **[MYSQL]**: MySQL 数据库的连接参数。
- **[PRODUCT_STORE]**: 制品库的连接信息。
- **[DUMP]**: 数据库导出（dump）操作的配置。
- **[ARCHIVE_PATH]**: 归档文件的存储路径。
- **[TEST_PUBLISH_AIO]**: 一键发布测试环境的详细配置。
- **[LIB_REPO]**: 依赖库仓库的配置。
- **[WITCHER_CMDB]**: CMDB 数据库的连接信息。
- **[CMD_EXECUTOR_FAIL]**: 命令执行失败时的关键字匹配规则。
- **[EXCEPT_SWITCH]**: 异常拦截开关，用于控制特定功能的开启或关闭。
- **[ROCKETMQ]**: RocketMQ 消息队列的配置。
- **[SYNC]**: 同步工具的配置。

### settings.py 文件结构
`settings.py` 文件的主要职责是加载和解析 `settings.ini` 文件。其结构如下：

1.  **导入与初始化**: 导入必要的模块（如 `os`, `configparser`, `platform`），并确定项目根目录 `BASE_DIR` 和操作系统编码。
2.  **配置解析**: 创建 `configparser.ConfigParser` 实例，读取 `settings.ini` 文件。
3.  **日志配置**: 使用 `log.ini` 文件配置日志系统。
4.  **配置项映射**: 将 `settings.ini` 中的各个节和键值对提取出来，转换为 Python 字典或变量。例如，`DATABASES` 字典包含了从 `[MYSQL]` 节读取的数据库连接信息。
5.  **常量定义**: 定义一些在代码中广泛使用的常量，如 `URL_DEF`。

### k8s.ini 文件结构
新增的 `k8s.ini` 文件专门用于管理 Kubernetes 相关操作的配置，其结构如下：

- **[DELETE_CONFIG]**: 定义删除外移配置的脚本和参数。
- **[CREATE_CONFIG]**: 定义创建外移配置的脚本和参数。
- **[UPDATE_PKG]**: 定义制作镜像从中间库到目标服务的脚本和参数。
- **[RESTART_PKG]**: 定义重启镜像服务的脚本和参数。
- **[DELETE_PKG]**: 定义回收 POD 的脚本和参数。
- **[AGENT_SYNC]**: 定义 agent 同步的脚本和参数。

每个节都包含 `script`（执行脚本路径）、`call_params`（调用参数）和 `desc`（功能描述）三个配置项。

```mermaid
flowchart TD
A["settings.py"] --> B["导入模块"]
B --> C["确定 BASE_DIR"]
C --> D["创建 ConfigParser"]
D --> E["读取 settings.ini"]
E --> F["配置日志系统"]
F --> G["提取配置项"]
G --> H["转换为 Python 字典/变量"]
H --> I["供其他模块使用"]
J["analysis_ini.py"] --> K["加载 k8s.ini"]
K --> L["解析 K8S 操作配置"]
L --> M["提供给发布工具使用"]
```

**Diagram sources**
- [settings.py](file://be-scripts/settings.py#L1-L30)
- [settings.ini](file://be-scripts/settings.ini#L1-L585)
- [k8s.ini](file://be-scripts/publish_tool/config/k8s.ini#L1-L29)
- [analysis_ini.py](file://be-scripts/publish_tool/config/analysis_ini.py#L1-L34)

**Section sources**
- [settings.ini](file://be-scripts/settings.ini#L1-L585)
- [settings.py](file://be-scripts/settings.py#L1-L823)
- [k8s.ini](file://be-scripts/publish_tool/config/k8s.ini#L1-L29)

## 配置加载机制与优先级

### 加载机制
配置的加载机制在 `settings.py` 文件中实现。其核心流程如下：

1.  **确定路径**: 使用 `os.path.dirname(os.path.abspath(__file__))` 获取 `settings.py` 文件的绝对路径，并以此为基础构建 `settings.ini` 的路径。
2.  **读取文件**: 使用 `configparser.ConfigParser` 的 `read()` 方法读取 `settings.ini` 文件，指定编码为 UTF-8。
3.  **提取数据**: 通过 `local_settings.get(section, key)` 方法从解析后的配置对象中获取指定节和键的值。
4.  **构建字典**: 将获取到的值组织成结构化的 Python 字典（如 `DATABASES`, `INTERFACE_URL`），便于在代码中通过点号或键名访问。

对于 `k8s.ini` 等专用配置文件，项目使用 `publish_tool/config/analysis_ini.py` 中的 `LoadConfig` 类进行加载和解析。该类可以扫描配置目录下的所有 `.ini` 文件，并提供统一的接口来访问这些配置。

### 优先级
本项目的配置系统主要依赖于单一的 `settings.ini` 文件，因此不存在复杂的优先级覆盖机制。所有配置项都直接从该文件读取。然而，存在一个动态生成配置的机制，这可以被视为一种优先级更高的配置来源：

- **Nacos 配置中心**: 项目中存在 `utils/config/generate_config.py` 和 `job/jenkins/nacos_util/generate_config.py` 等脚本，它们可以从 Nacos 配置中心拉取最新的配置，并动态生成 `settings.ini` 文件。这意味着，当这些脚本被执行时，Nacos 中的配置会覆盖本地 `settings.ini` 文件的内容，从而实现配置的集中管理和动态更新。因此，**Nacos 配置中心的优先级高于本地 `settings.ini` 文件**。

```mermaid
graph LR
A[Nacos 配置中心] -- 高优先级 --> B[generate_config.py]
C[本地 settings.ini] -- 低优先级 --> D[settings.py]
D --> E[应用程序]
F[k8s.ini] --> G[analysis_ini.py]
G --> H[发布工具]
```

**Diagram sources**
- [settings.py](file://be-scripts/settings.py#L1-L30)
- [utils/config/generate_config.py](file://be-scripts/utils/config/generate_config.py#L1-L50)
- [job/jenkins/nacos_util/generate_config.py](file://be-scripts/job/jenkins/nacos_util/generate_config.py#L1-L107)
- [analysis_ini.py](file://be-scripts/publish_tool/config/analysis_ini.py#L1-L34)

**Section sources**
- [settings.py](file://be-scripts/settings.py#L1-L823)
- [utils/config/generate_config.py](file://be-scripts/utils/config/generate_config.py#L1-L50)
- [analysis_ini.py](file://be-scripts/publish_tool/config/analysis_ini.py#L1-L34)

## 配置参数清单

以下是一份完整的配置参数清单，列出了 `settings.ini` 和 `k8s.ini` 文件中主要配置项的用途、默认值和有效范围。

| 配置项 | 所属节 | 用途 | 默认值/示例 | 有效范围 |
| :--- | :--- | :--- | :--- | :--- |
| `bs-uat` | CONFIG_DIR_FLAG | UAT 环境的目录标识 | `uat` | 字符串 |
| `local_path` | CONFIG_STORE | 配置库的本地存储路径 | `D:\GITProject\app_resource` | 文件系统路径 |
| `url` | CONFIG_STORE | 配置库的 Git SSH 地址 | `******************************:app-ops/app_resource.git` | Git URL |
| `zeus` | INTERFACE_URL | Zeus 服务的主机地址 | `http://***************` | IP 地址或域名 |
| `zeus_port` | INTERFACE_URL | Zeus 服务的端口号 | `80` | 端口号 |
| `spider_api_Authorization` | INTERFACE_URL | Spider API 的认证令牌 | `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` | JWT 字符串 |
| `bs-prod` | SALT_API | 生产环境 SaltStack API 地址 | `https://************:8000/` | HTTPS URL |
| `bs-prod` | SALT_API_PASSWORD | 生产环境 SaltStack API 密码 | `howbuy.com` | 密码字符串 |
| `bs-prod` | SALT_API_USER | 生产环境 SaltStack API 用户名 | `saltapi` | 用户名字符串 |
| `bs-uat` | TRANSIT_IP | UAT 环境中转服务器 IP | `***************` | IP 地址 |
| `mail_host` | EMAIL | 邮件服务器主机 | `smtp.corpease.net` | 域名或 IP |
| `gitlab_http` | GITCODESTORE | GitLab 代码仓库 HTTP 地址 | `http://gitlab-code.howbuy.pa` | HTTP URL |
| `gitlab_token` | GITCODESTORE | GitLab 个人访问令牌 | `********************` | 令牌字符串 |
| `ip` | JENKINS_INFO | Jenkins 服务器 IP | `***************` | IP 地址 |
| `url` | JENKINS_INFO | Jenkins 服务器 Web 地址 | `http://jkp-master.howbuy.pa/jenkins` | HTTP URL |
| `user` | JENKINS_INFO | Jenkins 用户名 | `howbuyscm` | 用户名字符串 |
| `password` | JENKINS_INFO | Jenkins 密码 | `Howbuy!@#` | 密码字符串 |
| `ip` | MYSQL | MySQL 数据库主机 | `**************` | IP 地址 |
| `port` | MYSQL | MySQL 数据库端口 | `3306` | 端口号 |
| `user` | MYSQL | MySQL 数据库用户名 | `scm` | 用户名字符串 |
| `password` | MYSQL | MySQL 数据库密码 | `123456` | 密码字符串 |
| `db` | MYSQL | MySQL 数据库名 | `spider` | 数据库名 |
| `charset` | MYSQL | 数据库连接字符集 | `utf8` | 字符集名称 |
| `url` | PRODUCT_STORE | 制品库 Git URL | `<EMAIL>:/data/git-code` | Git URL |
| `ip` | PRODUCT_STORE | 制品库服务器 IP | `***************` | IP 地址 |
| `path` | ARCHIVE_PATH | SQL 归档路径 | `/data/archive_sql/devops-sql/` | 文件系统路径 |
| `root_path` | TEST_PUBLISH_AIO | 一键发布根路径 | `/data/test_publish_aio/` | 文件系统路径 |
| `nfs_root_path` | TEST_PUBLISH_AIO | NFS 根路径 | `/data/devops_nfs/test_publish_aio_nfs` | 文件系统路径 |
| `ip` | WITCHER_CMDB | CMDB 数据库 IP | `**************` | IP 地址 |
| `error_keyword` | CMD_EXECUTOR_FAIL | 命令执行失败的关键字 | `['ERROR 1062 (23000)']` | 字符串列表 |
| `test_data_import` | EXCEPT_SWITCH | 测试数据导入开关 | `open` | `open` 或 `close` |
| `namesrv` | ROCKETMQ | RocketMQ NameServer 地址 | `*************:9876` | IP:Port |
| `sync_env_flag` | SYNC | 同步环境标识 | `test` | 环境名称 |
| `script` | DELETE_CONFIG | 删除外移配置的执行脚本 | `/home/<USER>/hb-k8s/k8s_build_script/k8s_building_cm.py delete` | 文件路径 |
| `call_params` | DELETE_CONFIG | 删除外移配置的调用参数 | `{"suite_code":"环境套","container_name":"容器名"}` | JSON 对象 |
| `desc` | DELETE_CONFIG | 功能描述 | `删除外移配置` | 字符串 |
| `script` | CREATE_CONFIG | 创建外移配置的执行脚本 | `/home/<USER>/hb-k8s/k8s_build_script/k8s_building_cm.py create` | 文件路径 |
| `call_params` | CREATE_CONFIG | 创建外移配置的调用参数 | `{"suite_code":"环境套","container_name":"容器名"}` | JSON 对象 |
| `desc` | CREATE_CONFIG | 功能描述 | `创建外移配置` | 字符串 |
| `script` | UPDATE_PKG | 制作镜像的执行脚本 | `/home/<USER>/hb-k8s/k8s_build_script/k8s_app_deploy/k8s_kustomsize_build.py` | 文件路径 |
| `call_params` | UPDATE_PKG | 制作镜像的调用参数 | `{"suite_code":"环境套","container_name":"容器名"}` | JSON 对象 |
| `desc` | UPDATE_PKG | 功能描述 | `制作镜像》》中转库到目标服务` | 字符串 |
| `script` | RESTART_PKG | 重启镜像的执行脚本 | `/home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_pod_restart.py` | 文件路径 |
| `call_params` | RESTART_PKG | 重启镜像的调用参数 | `{"suite_code":"环境套","container_name":"容器名"}` | JSON 对象 |
| `desc` | RESTART_PKG | 功能描述 | `重启镜像 》》 重启服务` | 字符串 |
| `script` | DELETE_PKG | 回收 POD 的执行脚本 | `/home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_pod_stop.py` | 文件路径 |
| `call_params` | DELETE_PKG | 回收 POD 的调用参数 | `{"suite_code":"环境套","container_name":"容器名", "operation":"recycle"}` | JSON 对象 |
| `desc` | DELETE_PKG | 功能描述 | `回收POD` | 字符串 |
| `script` | AGENT_SYNC | agent 同步的执行脚本 | `/home/<USER>/hb-k8s/k8s_build_script/docker_k8s_manage/docker_push_agent.py` | 文件路径 |
| `call_params` | AGENT_SYNC | agent 同步的调用参数 | `{"suite_code":"环境名","container_name":"agent名称"}` | JSON 对象 |
| `desc` | AGENT_SYNC | 功能描述 | `agent同步` | 字符串 |

**Section sources**
- [settings.ini](file://be-scripts/settings.ini#L1-L585)
- [k8s.ini](file://be-scripts/publish_tool/config/k8s.ini#L1-L29)

## 系统行为定制

通过修改 `settings.ini` 文件中的配置项，可以灵活地定制系统的各种行为。

### 调整日志级别
日志级别主要由 `log.ini` 文件控制，但 `settings.py` 会加载此文件。要调整日志级别，需要修改 `log.ini` 文件中的 `level` 配置。例如，将 `level=INFO` 改为 `level=DEBUG` 可以输出更详细的调试信息。

### 更改外部服务的连接信息
这是最常见的定制需求。例如，要将 `Zeus` 服务的地址从 `***************` 迁移到 `***************`，只需修改 `settings.ini` 文件中的对应项：
```ini
[INTERFACE_URL]
zeus = http://***************
```
修改后，所有通过 `settings.py` 导入 `INTERFACE_URL` 字典的模块都会使用新的地址。

### 修改数据库连接
要切换数据库，可以修改 `[MYSQL]` 节中的 `ip`, `port`, `user`, `password`, `db` 等参数。例如，连接到一个测试数据库：
```ini
[MYSQL]
ip = **************
db = spider_test
```

### 控制功能开关
通过 `[EXCEPT_SWITCH]` 节可以控制某些功能的开启或关闭。例如，如果要临时关闭测试数据导入功能，可以将配置改为：
```ini
[EXCEPT_SWITCH]
test_data_import = close
```
在代码中，可以通过检查 `EXCEPT_SWITCH["test_data_import"]` 的值来决定是否执行相关逻辑。

### 定制 K8S 操作行为
通过修改 `k8s.ini` 文件中的配置项，可以定制 Kubernetes 相关操作的行为。例如，要更改重启镜像的脚本路径，可以修改 `RESTART_PKG` 节：
```ini
[RESTART_PKG]
script = /new/path/to/docker_pod_restart.py
call_params = {"suite_code":"环境套","container_name":"容器名"}
desc = 重启镜像 》》 重启服务
```

## 运维最佳实践与常见陷阱

### 最佳实践
1.  **使用 Nacos 进行集中管理**: 尽可能通过 `generate_config.py` 脚本从 Nacos 拉取配置，避免手动修改 `settings.ini`，以保证配置的一致性和可追溯性。
2.  **敏感信息保护**: `settings.ini` 文件中包含数据库密码、API 令牌等敏感信息。确保该文件的权限设置正确（如 `600`），并避免将其提交到公共代码仓库。
3.  **环境隔离**: 为不同环境（开发、测试、生产）维护独立的 `settings.ini` 文件或 Nacos 配置，防止配置混淆。
4.  **配置备份**: 在修改 `settings.ini` 文件前，务必备份原文件，以便在出现问题时快速回滚。
5.  **文档化变更**: 对 `settings.ini` 的任何重要修改都应在文档或变更管理系统中记录原因和影响。
6.  **K8S 配置管理**: 对于 `k8s.ini` 文件中的配置，应确保脚本路径的正确性和可访问性，并在修改后进行充分测试。

### 常见陷阱
1.  **路径错误**: `settings.py` 中的 `BASE_DIR` 是基于 `__file__` 计算的。如果脚本的执行路径发生变化，可能会导致 `settings.ini` 文件无法找到。确保脚本在预期的目录下运行。
2.  **编码问题**: `settings.ini` 文件必须保存为 UTF-8 编码，否则在非 Windows 系统上读取时可能会出现乱码。`settings.py` 中明确指定了 `encoding="utf-8"`。
3.  **配置未生效**: 修改 `settings.ini` 后，必须重启使用该配置的应用程序，新的配置才能生效。Python 解释器会缓存已导入的模块。
4.  **Nacos 配置覆盖**: 如果使用了 `generate_config.py` 脚本，手动对 `settings.ini` 的修改可能会在下次执行脚本时被 Nacos 的配置覆盖，导致修改丢失。
5.  **节或键名错误**: `configparser` 对节名和键名是大小写敏感的（默认情况下）。确保在 `settings.py` 中使用的节名和键名与 `settings.ini` 文件中的完全一致。例如，`local_settings.get('MYSQL', 'IP')` 必须与 `[MYSQL]` 节下的 `IP` 键匹配。
6.  **K8S 脚本路径错误**: 修改 `k8s.ini` 文件中的脚本路径时，必须确保新路径下的脚本存在且具有可执行权限，否则会导致 K8S 操作失败。

**Section sources**
- [settings.ini](file://be-scripts/settings.ini#L1-L585)
- [settings.py](file://be-scripts/settings.py#L1-L823)
- [utils/config/generate_config.py](file://be-scripts/utils/config/generate_config.py#L1-L50)
- [k8s.ini](file://be-scripts/publish_tool/config/k8s.ini#L1-L29)