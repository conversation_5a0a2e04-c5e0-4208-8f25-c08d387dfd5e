# 传输安全

<cite>
**本文档引用的文件**
- [settings.py](file://spider/settings.py)
- [Dockerfile](file://Dockerfile)
- [docker/Dockerfile](file://docker/Dockerfile)
- [settings.ini](file://spider/settings.ini)
</cite>

## 目录
1. [引言](#引言)
2. [HTTPS配置方案](#https配置方案)
3. [反向代理在传输安全中的角色](#反向代理在传输安全中的角色)
4. [HSTS头设置与作用](#hsts头设置与作用)
5. [API接口传输安全要求](#api接口传输安全要求)
6. [基于SECURE_*配置项的安全传输实现](#基于secure_配置项的安全传输实现)
7. [网络层安全防护措施](#网络层安全防护措施)
8. [Docker部署环境中的传输安全配置示例](#docker部署环境中的传输安全配置示例)

## 引言
本文件旨在全面说明系统在数据传输过程中的安全防护措施，涵盖HTTPS配置、反向代理（如Nginx）的角色、HSTS头设置、API接口认证令牌的安全传输机制、基于Django设置的安全配置以及网络层防护措施。通过详细分析系统配置和部署文件，确保数据在传输过程中得到充分保护。

## HTTPS配置方案
系统通过Django框架和外部反向代理共同实现HTTPS安全传输。虽然`settings.py`中未直接启用HTTPS强制跳转，但通过`django.middleware.security.SecurityMiddleware`中间件提供了基础安全支持。该中间件可配合前端反向代理（如Nginx）实现TLS加密传输。

TLS版本支持方面，系统依赖于反向代理服务器的配置。建议在Nginx等反向代理中启用TLS 1.2及以上版本，禁用不安全的SSLv3和TLS 1.0/1.1协议。加密套件应优先选择前向安全的ECDHE系列算法，如`ECDHE-RSA-AES256-GCM-SHA384`，并禁用弱加密算法。

证书管理流程由反向代理服务器统一处理。建议采用自动化证书管理工具（如Let's Encrypt）实现证书的自动申请、续期和部署，确保证书的有效性和及时更新。

**Section sources**
- [settings.py](file://spider/settings.py#L85-L143)

## 反向代理在传输安全中的角色
反向代理（如Nginx）在传输安全中扮演关键角色。它作为系统的入口网关，负责处理所有外部HTTPS请求，执行TLS终止，并将解密后的请求转发给后端应用服务器。这种架构不仅减轻了应用服务器的加密计算负担，还提供了额外的安全防护层。

Nginx配置要点包括：正确配置SSL证书和私钥路径、启用HTTP/2以提升性能、设置安全的加密套件、启用OCSP装订以提高证书验证效率，以及配置适当的会话缓存以优化TLS握手性能。此外，Nginx还可实现请求过滤、速率限制和DDoS防护等安全功能。

**Section sources**
- [settings.py](file://spider/settings.py#L85-L143)

## HSTS头设置与作用
HTTP严格传输安全（HSTS）是一种安全策略机制，通过响应头`Strict-Transport-Security`告知浏览器只能通过HTTPS与服务器通信。这可以有效防止SSL剥离攻击和中间人攻击。

尽管`settings.py`中未显式配置HSTS，但可通过反向代理服务器添加该头。建议在Nginx配置中添加`add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";`指令，设置较长的有效期（如一年），并包含子域名。`preload`指令可将域名提交至浏览器HSTS预加载列表，提供更高级别的保护。

**Section sources**
- [settings.py](file://spider/settings.py#L85-L143)

## API接口传输安全要求
API接口的安全传输通过JWT（JSON Web Token）认证机制实现。系统使用`rest_framework_simplejwt`库进行令牌管理，配置在`settings.py`的`SIMPLE_JWT`中。访问令牌有效期为12小时，刷新令牌有效期为3天，采用HS256算法进行签名。

认证令牌通过HTTP Authorization头以Bearer模式传输，确保令牌不会被记录在服务器日志中。系统要求所有API请求必须通过HTTPS传输，防止令牌在传输过程中被窃取。此外，通过`SESSION_COOKIE_HTTPONLY = True`和`SESSION_COOKIE_SECURE = False`（生产环境应设为True）等配置，保护会话cookie的安全。

**Section sources**
- [settings.py](file://spider/settings.py#L172-L179)

## 基于SECURE_*配置项的安全传输实现
虽然`settings.py`中未直接使用Django的SECURE_*安全配置项，但可通过类似机制增强传输安全。例如，`SESSION_COOKIE_SECURE`应设置为`True`以确保会话cookie仅通过HTTPS传输。`SECURE_PROXY_SSL_HEADER`可用于识别反向代理后的HTTPS连接。

系统通过`CORS_ORIGIN_ALLOW_ALL = True`允许所有跨域请求，这在生产环境中存在安全风险。建议限制为特定可信源，并启用`CORS_ALLOW_CREDENTIALS = True`时确保`CORS_ORIGIN_ALLOW_ALL`为False。此外，`SECURE_CONTENT_TYPE_NOSNIFF`和`SECURE_BROWSER_XSS_FILTER`等安全头可通过中间件或反向代理配置启用。

**Section sources**
- [settings.py](file://spider/settings.py#L143-L170)

## 网络层安全防护措施
网络层安全防护通过防火墙规则、WAF（Web应用防火墙）和安全组策略实现。系统部署在受控网络环境中，通过防火墙限制仅允许443端口的HTTPS流量进入。WAF可检测和阻止常见的Web攻击，如SQL注入、XSS和CSRF。

在Docker部署环境中，通过网络隔离和端口映射限制服务暴露。`Dockerfile`中未显式配置安全规则，但应在运行时通过Docker网络策略和宿主机防火墙进行控制。建议使用专用的DMZ区域部署反向代理，将应用服务器置于内网，形成纵深防御体系。

**Section sources**
- [Dockerfile](file://Dockerfile#L0-L51)
- [docker/Dockerfile](file://docker/Dockerfile#L0-L49)

## Docker部署环境中的传输安全配置示例
在Docker部署环境中，传输安全配置需结合Dockerfile和运行时参数。以下为安全配置示例：

```Dockerfile
# 使用安全的基础镜像
FROM harbor-test.inner.howbuy.com/pa/centos:7.9-base

# 设置安全环境变量
ENV UV_PYTHON_INSTALL_MIRROR=file:///data/zt-uv/mirror
ENV UV_CACHE_DIR=/data/zt-uv/cache

# 安装系统依赖
RUN yum install gcc gcc-c++ make python3-devel mariadb-devel -y

# 创建应用目录
RUN mkdir -p /data/app/spider && \
    mkdir -p /data/logs/spider/audit

# 复制应用代码和依赖
COPY spider/pyproject.toml /data/app/
COPY spider/.python-version /data/app/
COPY spider/uv.lock /data/app/

# 同步Python依赖
RUN uv sync

# 配置安全启动命令
ENTRYPOINT ["/bin/bash", "-c", "/data/app/.venv/bin/python3 manage.py runserver 0.0.0.0:9000 --noreload > /data/logs/spider/spider-web.log 2>&1"]
```

运行容器时，应使用以下安全参数：
```bash
docker run -itd --name spider \
  -p 443:443 \
  --network secure-network \
  -v /data/ztws/zt-spider/spider:/data/app/spider/ \
  -v /data/ztws/zt-spider/spider_logs:/data/logs/spider/ \
  --read-only \
  --cap-drop=ALL \
  --security-opt no-new-privileges \
  spider:latest
```

**Section sources**
- [Dockerfile](file://Dockerfile#L0-L51)
- [docker/Dockerfile](file://docker/Dockerfile#L0-L49)