# 权限管理体系

<cite>
**本文档引用文件**  
- [user/models.py](file://user/models.py)
- [user/authentication.py](file://user/authentication.py)
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
- [env_mgt/env_node_mgt.py](file://env_mgt/env_node_mgt.py)
</cite>

## 目录
1. [简介](#简介)
2. [核心模型设计](#核心模型设计)
3. [认证机制实现](#认证机制实现)
4. [权限检查与应用](#权限检查与应用)
5. [权限分配与验证流程](#权限分配与验证流程)
6. [存储结构与查询优化](#存储结构与查询优化)
7. [权限配置最佳实践](#权限配置最佳实践)
8. [结论](#结论)

## 简介
本系统采用基于角色的访问控制（RBAC）模型，通过Django框架实现用户权限管理。系统通过`user/models.py`中的核心模型定义权限结构，并结合自定义认证后端进行权限验证。权限控制贯穿于视图层和API接口，确保系统操作的安全性与合规性。

## 核心模型设计

系统权限管理的核心模型包括`GitMembers`、`UserGitlabMembersExtend`等，分别用于存储用户在GitLab中的权限信息及扩展权限等级。

### 用户与权限模型

```mermaid
classDiagram
class GitMembers {
+git_group_name : CharField
+username : CharField
+permission : CharField
+cn_name : CharField
+git_user_id : IntegerField
}
class UserGitlabMembersExtend {
+username : CharField
+permission : IntegerField
}
GitMembers --> CnNameModel : "关联中文名"
UserGitlabMembersExtend --> GitMembers : "扩展权限"
```

**图示来源**  
- [user/models.py](file://user/models.py#L6-L28)

#### GitMembers 模型
该模型记录用户在GitLab组中的权限信息，主要字段如下：
- `git_group_name`: 所属Git组名
- `username`: 用户名
- `permission`: 权限级别（字符串）
- `cn_name`: 中文姓名
- `git_user_id`: Git用户ID

数据库表名为 `user_gitlab_members`。

#### UserGitlabMembersExtend 模型
该模型为权限扩展表，用于定义更细粒度的操作权限：
- `username`: 用户名
- `permission`: 权限等级（整数）

数据库表名为 `user_gitlab_members_extend`，其中权限值60及以上表示高权限用户。

#### CnNameModel 模型
用于映射用户名与中文名的对应关系：
- `username`: 用户名（主键）
- `cn_name`: 中文名

数据库表名为 `base_cn_name_specific`。

**本节来源**  
- [user/models.py](file://user/models.py#L1-L72)

## 认证机制实现

系统通过继承Django REST Framework的`BaseAuthentication`类，实现了自定义登录认证逻辑。

### 自定义认证后端

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Auth as "LoginAuthentication"
participant Session as "SessionStore"
Client->>Auth : 发送请求含Cookie
Auth->>Session : 检查Session是否存在
alt Session不存在
Auth-->>Client : 抛出“未登入”异常
else Session存在
Session->>Session : 加载Session数据
Session->>Auth : 返回Session项
loop 遍历Session项
Auth->>Auth : 查找username和is_login
end
alt 已登录
Auth-->>Client : 返回(user, None)
else 未登录或过期
Auth-->>Client : 抛出“未登入”或“session过期”异常
end
end
```

**图示来源**  
- [user/authentication.py](file://user/authentication.py#L1-L46)

#### 认证流程说明
1. 从请求中提取`COOKIES`，获取`SESSION_COOKIE_NAME`。
2. 使用`SessionStore`检查该Session是否有效。
3. 若Session存在，则加载其内容并遍历查找`username`和`is_login`标识。
4. 若`is_login=True`，则认证成功，返回用户信息；否则抛出相应异常。

该认证方式依赖于Django的数据库Session后端，确保会话状态持久化与安全性。

**本节来源**  
- [user/authentication.py](file://user/authentication.py#L1-L46)

## 权限检查与应用

权限检查主要在视图层通过调用服务层函数实现，典型场景包括节点绑定、操作权限判断等。

### 视图层权限检查

```mermaid
flowchart TD
Start([请求进入]) --> GetUsername["获取当前用户"]
GetUsername --> QueryPermission["调用get_ops_permission()"]
QueryPermission --> DB[(查询 user_gitlab_members_extend)]
DB --> CheckLevel{"权限 >= 60?"}
CheckLevel --> |是| AllowEdit["允许编辑操作"]
CheckLevel --> |否| DisableEdit["禁用编辑功能"]
AllowEdit --> Response["返回权限状态"]
DisableEdit --> Response
Response --> End([响应返回])
```

**图示来源**  
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L432-L442)
- [env_mgt/env_node_mgt.py](file://env_mgt/env_node_mgt.py#L559-L568)

#### 权限查询服务
`env_mgt_ser.get_ops_permission(username)` 函数通过原生SQL查询用户权限：
```python
sql = "SELECT permission from user_gitlab_members_extend WHERE username = '{}'"
```
返回用户的权限等级整数值。

#### 视图权限控制
在`GetOpsPermission`视图中，若权限值为60，则`permission_disable=False`，表示可执行操作；否则禁止关键操作。

**本节来源**  
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L432-L442)
- [env_mgt/env_node_mgt.py](file://env_mgt/env_node_mgt.py#L559-L568)

## 权限分配与验证流程

完整的权限验证流程涉及用户登录、Session创建、权限查询与行为控制四个阶段。

```mermaid
sequenceDiagram
participant User as "用户"
participant Frontend as "前端"
participant Backend as "后端"
participant DB as "数据库"
User->>Frontend : 登录操作
Frontend->>Backend : 提交凭证
Backend->>Backend : 验证身份Django authenticate
Backend->>Backend : 创建Session并设置is_login=True
Backend-->>Frontend : 返回Set-Cookie
Frontend->>Backend : 后续请求携带Session ID
Backend->>Backend : 调用LoginAuthentication.authenticate()
Backend->>DB : 查询UserGitlabMembersExtend获取permission
DB-->>Backend : 返回权限等级
Backend->>Backend : 判断权限是否足够
alt 权限足够
Backend-->>Frontend : 允许操作
else 权限不足
Backend-->>Frontend : 禁用UI或返回403
end
```

**图示来源**  
- [user/authentication.py](file://user/authentication.py#L1-L46)
- [user/models.py](file://user/models.py#L29-L35)
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L432-L442)

该流程确保了从认证到授权的完整闭环，所有敏感操作均需通过权限校验。

## 存储结构与查询优化

### 数据库存储结构

```mermaid
erDiagram
user_gitlab_members {
string git_group_name PK
string username PK
string permission
string cn_name
int git_user_id
}
user_gitlab_members_extend {
string username PK FK
int permission
}
base_cn_name_specific {
string username PK
string cn_name
}
user_gitlab_members_extend ||--|| user_gitlab_members : "扩展权限"
base_cn_name_specific ||--|| user_gitlab_members : "关联中文名"
```

**图示来源**  
- [user/models.py](file://user/models.py#L6-L35)

### 查询优化策略
- 使用`username`作为`user_gitlab_members_extend`表的查询主键，建立唯一索引。
- 在频繁查询场景中，采用`cursor.fetchone()`避免加载多余数据。
- 通过`GitMembers.objects.filter()`进行ORM查询，利用Django缓存机制提升性能。

## 权限配置最佳实践

### 最小权限原则应用
- 默认权限设置为低于60，仅对特定运维人员授予高权限。
- 敏感操作（如节点解绑、发布屏蔽）需权限≥60方可执行。
- 通过`access_status`字段动态控制前端按钮可用状态，防止越权操作。

### 权限审计方法
- 所有用户操作记录至`user_action_record`表，包含：
  - `username`: 操作人
  - `operate_time`: 操作时间
  - `action_item`: 行为项
  - `action_value`: 执行值
- 定期分析`ActionRecord`模型数据，识别异常行为模式。
- 结合`spider/settings.py`中的日志配置，实现操作日志追踪。

### 安全建议
- 避免在代码中硬编码权限判断逻辑，应统一调用`get_ops_permission`服务。
- 定期清理无效用户权限记录，防止权限泄露。
- 对`SessionStore`启用加密存储，防止Session劫持。

**本节来源**  
- [user/models.py](file://user/models.py#L30-L44)
- [env_mgt/env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L432-L442)

## 结论
本系统通过Django ORM与自定义认证机制实现了基于角色的访问控制。核心模型清晰定义了用户、权限与角色的关系，认证流程安全可靠，权限检查贯穿于关键业务流程。建议进一步引入Django Guardian或Role-Based Middleware以支持更复杂的权限策略，并考虑将权限数据缓存至Redis以提升查询性能。