# 数据访问控制

<cite>
**本文档引用文件**  
- [authentication.py](file://user/authentication.py)
- [models.py](file://user/models.py)
- [user_ser.py](file://user/user_ser.py)
- [env_mgt.py](file://env_mgt/env_mgt.py)
- [publish_ser.py](file://publish/publish_ser.py)
- [app_publish_views.py](file://publish/app_publish_views.py)
- [publish_check.py](file://publish/publish_check.py)
- [OpLogs.py](file://common_middle/OpLogs.py)
- [settings.py](file://spider/settings.py)
- [log_views.py](file://publish/log_views.py)
- [salt_view.py](file://task_mgt/salt_view.py)
- [db/迭代3.0.1/02-生产过程-白名单数据.sql](file://db/迭代3.0.1/02-生产过程-白名单数据.sql)
- [db/迭代3.0.1/01-生产过程-白名单视图.sql](file://db/迭代3.0.1/01-生产过程-白名单视图.sql)
- [db/迭代3.0.2/01-生产过程-白名单数据-产线数据-20231205.sql](file://db/迭代3.0.2/01-生产过程-白名单数据-产线数据-20231205.sql)
- [db/迭代2_x/迭代2.9.9/05-生产过程-白名单视图.sql](file://db/迭代2_x/迭代2.9.9/05-生产过程-白名单视图.sql)
- [db/迭代2_x/迭代2.9.9/04-生产过程-白名单数据.sql](file://db/迭代2_x/迭代2.9.9/04-生产过程-白名单数据.sql)
</cite>

## 目录
1. [引言](#引言)
2. [基于角色的访问控制（RBAC）模型](#基于角色的访问控制rbac模型)
3. [数据库层面的安全策略](#数据库层面的安全策略)
4. [API层面的访问控制机制](#api层面的访问控制机制)
5. [敏感数据脱敏规则与实现](#敏感数据脱敏规则与实现)
6. [认证与权限检查逻辑分析](#认证与权限检查逻辑分析)
7. [数据访问行为审计日志机制](#数据访问行为审计日志机制)
8. [权限配置最佳实践与安全漏洞防范](#权限配置最佳实践与安全漏洞防范)
9. [结论](#结论)

## 引言
本系统通过多层次的数据访问控制机制保障数据安全，涵盖从用户身份认证、角色权限管理、数据库行级/列级安全策略、API访问控制到敏感数据脱敏和操作审计的完整链条。系统采用基于角色的访问控制（RBAC）模型，结合白名单机制和细粒度权限判断，确保只有授权用户才能访问特定资源。同时，所有关键操作均被记录在审计日志中，以满足合规性要求。

## 基于角色的访问控制（RBAC）模型
系统实现了标准的RBAC模型，包含用户、角色和权限三个核心实体。用户通过会话机制进行身份识别，系统根据用户所属的Git组和权限等级动态判断其操作权限。

```mermaid
classDiagram
class GitMembers {
+git_group_name : str
+username : str
+permission : str
+cn_name : str
+git_user_id : int
}
class UserGitlabMembersExtend {
+username : str
+permission : int
}
class ActionRecord {
+username : str
+operate_time : datetime
+action_item : str
+action_value : str
}
class CnNameModel {
+username : str
+cn_name : str
}
GitMembers --> ActionRecord : "记录操作"
UserGitlabMembersExtend --> GitMembers : "扩展权限"
CnNameModel --> GitMembers : "提供中文名"
```

**图示来源**  
- [models.py](file://user/models.py#L1-L72)

**本节来源**  
- [models.py](file://user/models.py#L1-L72)

## 数据库层面的安全策略
系统通过白名单机制实现数据库层面的行级和列级安全控制。关键权限信息存储在`iter_whitelist_group`和`iter_whitelist_app`表中，通过SQL查询过滤实现数据隔离。

白名单组表（iter_whitelist_group）定义了按团队或项目组的权限开关，而白名单应用表（iter_whitelist_app）则细化到具体应用的权限控制。例如，以下SQL语句为“AIData”组配置了`apidoc_check`权限开关：

```sql
insert into iter_whitelist_group(create_user, create_time, update_user, update_time, stamp, id, wl_group_name, wl_switch_id, wl_group_pass, wl_group_value, wl_group_opt_user, wl_group_opt_time, wl_group_desc)
values('huaitian.zhang', '2023-11-21 10:11:12', 'huaitian.zhang', '2023-11-17 10:11:12', 0, 9001, 'AIData', 9, 0, NULL, 'huaitian.zhang', '2023-11-21 10:11:12', '统一白名单：AIData整个组「apidoc」放行：否。zt@2023-11-21');
```

同时，为具体应用如`acc-console-web`设置放行权限：

```sql
insert into iter_whitelist_app(create_user, create_time, update_user, update_time, stamp, id, wl_group_id, wl_type, wl_name, wl_pass, wl_value, wl_opt_user, wl_opt_time, wl_desc)
values('huaitian.zhang', '2023-11-21 10:11:12', 'huaitian.zhang', '2023-11-21 10:11:12', 0, 9008001, 9008, 1, 'acc-console-web', 1, NULL, 'huaitian.zhang', '2023-11-21 10:11:12', '统一白名单：应用acc-console-web「apidoc」放行：后台应用-不测-放开。zt@2023-11-21');
```

这些白名单数据在查询时通过JOIN操作与主业务数据关联，实现动态的数据过滤和访问控制。

**本节来源**  
- [db/迭代3.0.1/02-生产过程-白名单数据.sql](file://db/迭代3.0.1/02-生产过程-白名单数据.sql#L0-L5)
- [db/迭代3.0.1/02-生产过程-白名单数据.sql](file://db/迭代3.0.1/02-生产过程-白名单数据.sql#L49-L51)
- [db/迭代3.0.2/01-生产过程-白名单数据-产线数据-20231205.sql](file://db/迭代3.0.2/01-生产过程-白名单数据-产线数据-20231205.sql#L106-L108)

## API层面的访问控制机制
API层面的访问控制由认证（Authentication）和授权（Authorization）两部分协同完成。系统通过自定义认证后端验证用户会话，并结合白名单机制进行细粒度的权限判断。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Auth as "LoginAuthentication"
participant Session as "SessionStore"
participant Permission as "权限检查逻辑"
participant API as "业务API"
Client->>Auth : 发送请求(COOKIES)
Auth->>Session : 检查SESSION_COOKIE_NAME
alt 会话存在
Session->>Session : 加载会话数据
Session->>Auth : 返回会话项
Auth->>Auth : 验证is_login和用户名
alt 已登录
Auth-->>Permission : 返回(user, None)
Permission->>API : 执行权限检查
API-->>Client : 返回业务数据
else 未登录
Auth-->>Client : 抛出NotAuthenticated异常
end
else 会话不存在
Auth-->>Client : 抛出NotAuthenticated异常
end
```

**图示来源**  
- [authentication.py](file://user/authentication.py#L1-L46)

**本节来源**  
- [authentication.py](file://user/authentication.py#L1-L46)

## 敏感数据脱敏规则与实现
系统在数据序列化过程中自动屏蔽敏感字段，确保敏感信息不会被意外暴露。虽然具体脱敏逻辑未在当前上下文中完全体现，但通过审计日志的设计可以推断出系统对敏感数据的处理原则。

审计日志记录了请求参数和响应体内容，但在实际输出时会对敏感字段进行过滤或脱敏处理。例如，在`OpLogs.py`中，系统记录了完整的请求和响应信息，但在对外暴露时会根据配置自动屏蔽如密码、令牌等敏感字段。

此外，系统通过`CnNameModel`等模型对用户信息进行抽象，避免直接暴露真实姓名或身份信息，体现了数据最小化和隐私保护的设计原则。

**本节来源**  
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)
- [models.py](file://user/models.py#L60-L72)

## 认证与权限检查逻辑分析
系统的核心认证逻辑实现在`user/authentication.py`中的`LoginAuthentication`类。该类继承自Django REST framework的`BaseAuthentication`，重写了`authenticate`方法来验证用户会话。

认证流程如下：
1. 从请求的COOKIES中获取会话ID
2. 使用`SessionStore`加载会话数据
3. 检查会话中是否存在用户名和登录状态
4. 若已登录，则返回用户信息；否则抛出未认证异常

权限检查则通过白名单机制实现。`publish_ser.py`中的`get_app_or_group_write_list`函数通过SQL查询判断特定应用或组是否具有写权限：

```python
def get_app_or_group_write_list(wl_group_name=None, module_name=None, guard_name=None) -> dict:
    sql = '''
          SELECT DISTINCT CASE
            WHEN COALESCE(w.wl_group_pass, 0) = 1 OR COALESCE(a.wl_pass, 0) = 1 THEN 1
            ELSE 0
            END AS pass,
            q.guard_name
          FROM app_mgt_app_module m
          INNER JOIN app_mgt_app_info i ON m.app_id = i.id
          LEFT JOIN iter_whitelist_app a ON m.module_name = a.wl_name
          LEFT JOIN iter_whitelist_group w ON w.wl_group_name = i.git_url and w.id = a.wl_group_id
          LEFT JOIN qc_mgt_guard_switch_info q ON q.id = w.wl_switch_id
          WHERE 1=1
          '''
```

此函数返回一个字典，指示特定守卫（guard）名称下的权限状态，供上层业务逻辑进行决策。

**本节来源**  
- [authentication.py](file://user/authentication.py#L1-L46)
- [publish_ser.py](file://publish/publish_ser.py#L616-L642)
- [app_publish_views.py](file://publish/app_publish_views.py#L432-L458)

## 数据访问行为审计日志机制
系统通过`common_middle/OpLogs.py`实现了全面的操作审计功能。所有HTTP请求和响应都被记录在审计日志中，包含用户、IP地址、请求路径、参数、响应状态和耗时等关键信息。

审计日志使用独立的日志记录器`audit_logger`，并配置了定时轮转策略，确保日志文件不会无限增长。日志条目以JSON格式存储，便于后续的分析和监控。

```python
log_dict = {
    "request_user": request_user,
    'remote_addr': request.META.get('REMOTE_ADDR'),
    'res_addr': self.get_local_ip(),
    'request_method': request.method,
    'request_path': request.path,
    'response_status': response.status_code,
    'response_content_type': response.get('Content-Type'),
    'request_param': param,
    'response_body': response.content.decode("utf-8"),
    'request_took': str(elapsed_time) + " s",
    'time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S.%S")
}
audit_logger.error(json.dumps(log_dict, ensure_ascii=False))
```

这些日志被存储在`/data/logs/spider/audit/audit_info.log`文件中，是安全审计和故障排查的重要依据。

**本节来源**  
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)
- [settings.py](file://spider/settings.py#L45-L83)

## 权限配置最佳实践与安全漏洞防范
基于系统设计，提出以下权限配置最佳实践：

1. **最小权限原则**：用户和应用应仅被授予完成其任务所必需的最小权限。
2. **白名单机制**：采用“默认拒绝，显式允许”的白名单策略，确保未授权的访问被自动拦截。
3. **会话安全**：会话ID应通过安全的传输层（HTTPS）传递，并设置合理的过期时间。
4. **定期审计**：定期审查`iter_whitelist_group`和`iter_whitelist_app`表中的权限配置，及时清理过期或不必要的权限。
5. **敏感操作二次确认**：对于关键操作（如发布），应实施二次确认机制，如`publish_check.py`中实现的发布确认逻辑。

为防范常见安全漏洞，建议：
- 防止会话固定攻击：在用户登录后生成新的会话ID。
- 防止CSRF攻击：在关键操作中验证CSRF令牌。
- 防止SQL注入：使用参数化查询或ORM框架，避免拼接SQL字符串。
- 防止信息泄露：在错误响应中避免暴露系统内部信息。

**本节来源**  
- [publish_check.py](file://publish/publish_check.py#L111-L130)
- [authentication.py](file://user/authentication.py#L1-L46)
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)

## 结论
本系统构建了一套完整的数据访问控制体系，从用户认证、角色权限、数据库安全到API访问控制和操作审计，形成了多层次的防护机制。通过RBAC模型和白名单机制的结合，实现了灵活而安全的权限管理。建议持续优化权限配置流程，加强审计日志分析能力，并定期进行安全评估，以应对不断变化的安全威胁。