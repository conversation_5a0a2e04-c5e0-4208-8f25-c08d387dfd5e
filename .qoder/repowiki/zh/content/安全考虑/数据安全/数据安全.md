# 数据安全

<cite>
**本文档引用的文件**  
- [01-ftx-online-web的描述符key.sql](file://db/分支2_x/2.8.0/01-ftx-online-web的描述符key.sql)
- [03-ftx-online-web的描述符value.sql](file://db/分支2_x/2.8.0/03-ftx-online-web的描述符value.sql)
- [06-ftx-online-search-web的描述符value.sql](file://db/分支2_x/2.8.0/06-ftx-online-search-web的描述符value.sql)
- [it888备份数据.sql](file://db/迭代3_1_6/it888备份数据.sql)
- [db_mgt_info](file://db_mgt/models.py)
- [db_mgt_app_bind](file://db_mgt/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [数据分类与敏感信息识别](#数据分类与敏感信息识别)
3. [数据存储安全](#数据存储安全)
4. [数据传输安全](#数据传输安全)
5. [数据访问控制](#数据访问控制)
6. [数据备份与恢复安全](#数据备份与恢复安全)
7. [数据生命周期管理](#数据生命周期管理)
8. [防数据泄露技术措施](#防数据泄露技术措施)
9. [模型层数据验证与清洗](#模型层数据验证与清洗)

## 引言

本数据安全文档旨在全面阐述系统中数据的保护机制，涵盖从数据分类、存储、传输、访问控制到备份恢复的全生命周期安全管理。文档基于系统实际配置和数据库结构，详细说明敏感数据的加密存储方案、数据传输过程中的安全措施、数据访问控制策略以及防止数据泄露的技术手段。

## 数据分类与敏感信息识别

系统中的数据根据其敏感程度被划分为不同类别。敏感信息主要包括数据库连接密码、应用配置密钥、用户身份信息等。通过数据库字段命名规范和配置描述符，可以识别出敏感数据。例如，数据库表`db_mgt_info`中的`db_info_password`字段明确存储数据库密码，属于高敏感信息。

**Section sources**
- [db_mgt/models.py](file://db_mgt/models.py#L1-L50)

## 数据存储安全

### 数据库字段加密

系统通过配置描述符机制实现数据库字段的加密存储。敏感字段如数据库密码不以明文形式存储，而是通过加密标识进行管理。在`01-ftx-online-web的描述符key.sql`中，定义了多个与密码相关的描述符，如`ds_ftx-online-web_password`（数据源密码）和`oracle_ftx-online-web_password`（Oracle数据库密码），其描述字段`tag_desc`分别对应`ds.password`和`DB.ftxonlinedb.PassWord`，表明这些值在存储时已被加密处理。

```mermaid
erDiagram
ZEUS_TAG ||--o{ ZEUS_TAG_VAL : "包含"
ZEUS_TAG {
int id PK
string tag_code
string tag_name
string tag_desc "描述，如ds.password或DB.ftxonlinedb.PassWord"
}
ZEUS_TAG_VAL {
int id PK
int env_id
int tag_id FK
string et_val "加密后的值"
string et_desc
}
```

**Diagram sources**
- [01-ftx-online-web的描述符key.sql](file://db/分支2_x/2.8.0/01-ftx-online-web的描述符key.sql#L20-L23)
- [03-ftx-online-web的描述符value.sql](file://db/分支2_x/2.8.0/03-ftx-online-web的描述符value.sql#L132-L135)

### 配置文件加密

系统通过`config.decrypt`参数控制配置文件的解密行为。在多个环境的描述符值中，`config.decrypt=false`被广泛设置，表明系统默认不自动解密配置，需要显式处理。例如，在`03-ftx-online-web的描述符value.sql`和`06-ftx-online-search-web的描述符value.sql`中，多个环境（env_id 40, 42, 44, 45等）的`config.decrypt`值均被设置为`false`，确保了配置文件中敏感信息的安全。

**Section sources**
- [03-ftx-online-web的描述符value.sql](file://db/分支2_x/2.8.0/03-ftx-online-web的描述符value.sql#L116-L119)
- [06-ftx-online-search-web的描述符value.sql](file://db/分支2_x/2.8.0/06-ftx-online-search-web的描述符value.sql#L113-L116)

## 数据传输安全

### HTTPS与TLS配置

文档中未直接提及HTTPS或TLS的具体配置，但系统通过安全的数据库连接字符串和外部服务通信来保障数据传输安全。数据库连接使用JDBC URL，其中包含`useSSL=false`等参数，表明系统明确管理SSL连接状态。

### 证书管理

文档中未发现关于证书管理的具体信息。

## 数据访问控制

### 数据库权限管理

系统的数据库访问权限通过`db_mgt_app_bind`表进行管理。该表将应用模块（`app_module_name`）与数据库信息（`db_info_id`）绑定，并可设置独立的用户名和密码（`db_app_bind_username`, `db_app_bind_password`）。当这些字段为空时，系统使用`db_mgt_info`表中的默认凭据，实现了灵活的权限分配。

```mermaid
erDiagram
DB_MGT_INFO ||--o{ DB_MGT_APP_BIND : "绑定"
DB_MGT_INFO {
int id PK
string db_info_username
string db_info_password "加密存储"
string db_info_conn_param
}
DB_MGT_APP_BIND {
int id PK
string app_module_name
int db_info_id FK
string db_app_bind_username "可覆盖默认值"
string db_app_bind_password "可覆盖默认值"
}
```

**Diagram sources**
- [db_mgt/models.py](file://db_mgt/models.py#L100-L150)
- [05-db版本化绑定数据_for_zt.sql](file://db/迭代2_x/迭代2.8.1/05-db版本化绑定数据_for_zt.sql#L23-L33)

### 敏感信息脱敏规则

系统通过描述符的`tag_desc`字段定义了敏感信息的逻辑路径，但实际的脱敏规则（如日志脱敏）未在现有文件中明确说明。

## 数据备份与恢复安全

### 备份过程安全

在`it888备份数据.sql`中，`db_mgt_info`表的备份数据明确显示`db_info_password`字段的值为`DB.tms.deal_0_mysql.PassWord`和`DB.tms.deal_1_mysql.PassWord`。这表明在备份过程中，原始密码被替换为指向加密存储的标识符，而非明文密码，有效防止了备份文件中的数据泄露。

**Section sources**
- [it888备份数据.sql](file://db/迭代3_1_6/it888备份数据.sql#L387-L388)

## 数据生命周期管理

### 数据创建与存储

数据在创建时即遵循安全规范。例如，`db_mgt_info`表在插入新记录时，`db_info_password`字段直接存储加密标识符（如`DB.tms.deal_0_mysql.PassWord`），而非原始密码。

### 数据使用

数据在使用时通过描述符系统进行解密和注入。应用在运行时根据环境ID（`env_id`）和描述符ID（`tag_id`）从`zeus_tag_val`表中获取对应的加密值（`et_val`），并由应用框架进行解密后使用。

### 数据归档与销毁

文档中未提供关于数据归档和销毁的具体策略。

## 防数据泄露技术措施

### SQL注入防护

文档中未直接提及SQL注入防护措施。

### 日志脱敏

文档中未提供日志脱敏的具体实现。

### 错误信息处理

文档中未提供错误信息处理的安全策略。

## 模型层数据验证与清洗

文档中未提供模型层数据验证和清洗的具体代码示例。