# 密钥管理策略

<cite>
**本文档引用的文件**  
- [settings.py](file://spider/settings.py)
- [serializers.py](file://public/serializers.py)
</cite>

## 目录
1. [密钥配置与存储](#密钥配置与存储)  
2. [密钥轮换策略](#密钥轮换策略)  
3. [密钥访问控制](#密钥访问控制)  
4. [加密与解密操作](#加密与解密操作)  
5. [应急恢复与审计](#应急恢复与审计)  
6. [安全管理最佳实践](#安全管理最佳实践)  
7. [安全事件检测与响应](#安全事件检测与响应)

## 密钥配置与存储

系统中的主密钥和密钥派生参数在 `spider/settings.py` 文件中进行配置。主密钥 `SECRET_KEY` 和令牌密钥 `TOKEN_SECRET_KEY` 作为 Django 框架的核心安全配置，直接定义在设置文件中。这些密钥用于保障会话安全、数据加密和 JWT 令牌的签名验证。

密钥的存储位置主要依赖于外部配置文件 `settings.ini`，通过 `configparser` 模块读取。敏感信息如数据库密码、Jenkins 凭据、Salt API 凭据等均从该配置文件中获取，避免了在代码中硬编码。例如，Salt API 的用户名和密码分别通过 `SALT_API_USER` 和 `SALT_API_PASSWORD` 字典从 `settings.ini` 中读取，确保了凭据的集中管理和环境隔离。

此外，系统通过 `local_settings.get()` 方法从配置文件中动态加载密钥和参数，支持不同环境（如生产、测试、灾备）使用不同的密钥配置。这种设计提高了配置的灵活性和安全性，允许在部署时注入环境特定的密钥。

**Section sources**  
- [settings.py](file://spider/settings.py#L29-L30)  
- [settings.py](file://spider/settings.py#L439-L459)  
- [settings.py](file://spider/settings.py#L499-L516)

## 密钥轮换策略

系统目前未实现自动化的密钥轮换机制，密钥轮换主要依赖于手动操作。当需要轮换密钥时，运维人员需更新 `settings.ini` 配置文件中的相应条目，并重启服务以使新密钥生效。此过程需要严格遵循变更管理流程，确保在轮换期间服务的连续性和数据的完整性。

旧密钥的保留周期未在代码中明确定义，通常由运维团队根据安全策略和业务需求决定。在轮换过程中，建议保留旧密钥一段时间，以便处理可能存在的未完成的加密数据或会话。数据重新加密的迁移方案也未在现有代码中体现，通常需要开发专门的脚本或任务来批量处理已加密的数据。

新密钥的激活方式是通过更新配置文件并重启应用服务。由于 Django 的 `SECRET_KEY` 用于会话签名，轮换后所有现有会话将失效，用户需要重新登录。因此，密钥轮换通常安排在维护窗口期进行，以最小化对用户的影响。

**Section sources**  
- [settings.py](file://spider/settings.py#L439-L459)  
- [settings.py](file://spider/settings.py#L499-L516)

## 密钥访问控制

系统的密钥访问控制主要通过配置文件的权限管理和环境隔离来实现。`settings.ini` 文件的访问权限应严格限制，仅允许授权的运维和开发人员读取。在生产环境中，该文件应存储在安全的配置管理服务或密钥管理服务（KMS）中，而非直接暴露在代码仓库或应用服务器上。

在代码层面，密钥的访问通过 `local_settings.get()` 方法进行，该方法提供了默认值和错误处理机制，防止因配置缺失导致服务中断。对于不同环境的密钥，系统使用独立的配置项（如 `SALT_API_PASSWORD['prod']`, `SALT_API_PASSWORD['zb']`），实现了基于环境的访问控制。

此外，系统通过 Django 的认证后端（`AUTHENTICATION_BACKENDS`）和 LDAP 集成，实现了对用户访问权限的控制。只有经过身份验证的用户才能访问涉及密钥配置的管理接口，从而防止未授权访问。

**Section sources**  
- [settings.py](file://spider/settings.py#L439-L459)  
- [settings.py](file://spider/settings.py#L499-L516)  
- [settings.py](file://spider/settings.py#L176)

## 加密与解密操作

在 `public/serializers.py` 文件中，虽然没有直接的加密/解密逻辑，但该文件定义了数据序列化的接口，是加密操作的前置步骤。`ScriptMainSerializer` 和 `ScriptMinorSerializer` 类负责将数据库模型对象序列化为 JSON 格式，这些数据在传输前可能需要进行加密处理。

密钥在加密和解密操作中的使用方式主要体现在 JWT 令牌的生成和验证上。在 `settings.py` 中，`SIMPLE_JWT` 配置项指定了使用 `HS256` 算法和 `TOKEN_SECRET_KEY` 作为签名密钥。当用户登录时，系统使用该密钥生成 JWT 令牌；在后续请求中，系统使用同一密钥验证令牌的有效性。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant Auth as "认证服务"
participant Settings as "settings.py"
Client->>Auth : 提交登录凭证
Auth->>Settings : 获取TOKEN_SECRET_KEY
Auth->>Auth : 使用HS256算法生成JWT
Auth-->>Client : 返回JWT令牌
Client->>Auth : 发送带JWT的请求
Auth->>Settings : 获取TOKEN_SECRET_KEY
Auth->>Auth : 验证JWT签名
Auth-->>Client : 返回请求数据
```

**Diagram sources**  
- [settings.py](file://spider/settings.py#L30)  
- [settings.py](file://spider/settings.py#L176)

**Section sources**  
- [settings.py](file://spider/settings.py#L176)  
- [serializers.py](file://public/serializers.py#L1-L31)

## 应急恢复与审计

在应急情况下，密钥恢复主要依赖于备份的 `settings.ini` 文件。建议将该文件的备份存储在离线或加密的存储介质中，并定期测试恢复流程。灾难恢复预案应包括密钥恢复的步骤，确保在系统完全重建后能够快速恢复服务。

审计日志记录方面，系统配置了独立的审计日志记录器 `audit_logger`，使用 `TimedRotatingFileHandler` 将日志写入 `/data/logs/spider/audit/audit_info.log`。该日志记录器专门用于记录安全相关事件，如登录尝试、配置变更等。通过分析这些日志，可以追踪密钥的使用情况和潜在的安全事件。

```mermaid
flowchart TD
A[安全事件发生] --> B{是否为密钥相关?}
B --> |是| C[记录到审计日志]
B --> |否| D[记录到常规日志]
C --> E[发送告警通知]
D --> F[存档日志文件]
E --> G[安全团队响应]
F --> H[定期日志审计]
```

**Diagram sources**  
- [settings.py](file://spider/settings.py#L58-L67)

**Section sources**  
- [settings.py](file://spider/settings.py#L58-L67)

## 安全管理最佳实践

为确保密钥安全，系统应遵循以下最佳实践：
- **防止密钥硬编码**：虽然 `SECRET_KEY` 和 `TOKEN_SECRET_KEY` 在 `settings.py` 中直接定义，但应将其移至环境变量或密钥管理服务中，避免在代码中暴露。
- **定期安全审计**：定期审查 `settings.ini` 文件的访问权限和内容，确保没有未授权的修改。使用自动化工具扫描代码库，防止新的硬编码密钥被引入。
- **权限最小化原则**：严格限制对 `settings.ini` 文件和密钥管理服务的访问权限，确保只有必要的人员和系统组件能够访问密钥。
- **使用密钥管理服务**：建议将密钥存储在专业的密钥管理服务（如 HashiCorp Vault 或 AWS KMS）中，利用其提供的密钥轮换、访问控制和审计功能。

**Section sources**  
- [settings.py](file://spider/settings.py#L29-L30)  
- [settings.py](file://spider/settings.py#L439-L459)

## 安全事件检测与响应

系统通过日志监控和异常检测来识别密钥相关的安全事件。例如，对 `audit_info.log` 日志的持续监控可以发现频繁的登录失败或异常的配置访问模式。未授权访问尝试通常表现为来自未知 IP 地址的认证请求或对敏感 API 的非法调用。

一旦检测到密钥泄露，应立即启动应急响应流程：
1. 立即轮换所有受影响的密钥。
2. 撤销使用旧密钥签发的所有令牌。
3. 调查泄露原因，并修复安全漏洞。
4. 通知相关方，并根据需要进行法律申报。

系统目前缺乏自动化的入侵检测和响应机制，建议集成 SIEM（安全信息和事件管理）系统，实现对安全事件的实时告警和自动化响应。

**Section sources**  
- [settings.py](file://spider/settings.py#L58-L67)  
- [settings.py](file://spider/settings.py#L176)