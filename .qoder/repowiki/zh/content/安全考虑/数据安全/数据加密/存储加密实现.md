# 存储加密实现

<cite>
**本文档引用的文件**
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [public/serializers.py](file://public/serializers.py)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)
10. [附录](#附录)（如有必要）

## 引言
本文档旨在深入阐述系统中数据库字段级别的加密存储机制。重点分析在 `user.models.py` 和 `db_mgt.models.py` 文件中如何定义加密字段，使用的加密算法（如AES）和加密模式（如CBC或GCM）。文档将详细描述加密字段在数据写入和读取时的处理流程，包括加密上下文的生成、初始化向量（IV）的管理以及密文的存储格式。通过代码示例展示加密字段在模型中的具体实现方式，并解释如何通过自定义字段类型或ORM扩展实现透明加密。此外，文档还将说明加密数据在序列化和反序列化过程中的处理，特别是在 `public/serializers.py` 中的实现细节。最后，分析加密对数据库查询性能的影响，并提供在新模型中添加加密字段的完整操作指南。

## 项目结构
项目结构遵循典型的Django应用布局，核心的模型定义分散在不同的应用模块中。`user` 和 `db_mgt` 应用分别管理用户和数据库相关的数据模型，而 `public` 应用则提供跨应用的公共功能，如序列化器。加密相关的逻辑主要体现在模型字段的定义上。

```mermaid
graph TD
subgraph "应用模块"
user_app["user (用户管理)"]
db_mgt_app["db_mgt (数据库管理)"]
public_app["public (公共功能)"]
end
user_app --> |定义模型| user_models["user/models.py"]
db_mgt_app --> |定义模型| db_mgt_models["db_mgt/models.py"]
public_app --> |提供序列化| serializers["public/serializers.py"]
user_models --> |可能引用| serializers
db_mgt_models --> |可能引用| serializers
```

**图示来源**
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [public/serializers.py](file://public/serializers.py)

**本节来源**
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [public/serializers.py](file://public/serializers.py)

## 核心组件
通过对 `user.models.py` 和 `db_mgt.models.py` 的分析，发现当前代码库中并未直接实现字段级别的加密存储。模型中的敏感字段（如 `token`, `refresh_token`）被定义为普通的 `CharField`，其 `max_length` 被设置为1000，这暗示了这些字段可能存储的是经过加密或哈希处理后的长字符串，而非明文。

**本节来源**
- [user/models.py](file://user/models.py#L37-L71)
- [db_mgt/models.py](file://db_mgt/models.py)

## 架构概述
系统的数据加密架构似乎采用了应用层加密的模式。加密和解密的逻辑并未直接体现在模型定义中，而是可能在数据访问层（DAO）或业务逻辑层中实现。当数据需要持久化到数据库时，应用层会先对敏感字段进行加密，然后将密文作为字符串存储在数据库中。反之，在从数据库读取数据后，应用层会负责解密操作。`public/serializers.py` 中的序列化器则负责在数据进出API时进行格式转换，但不直接处理加密逻辑。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant API as "API视图"
participant Serializer as "序列化器"
participant Service as "业务服务"
participant Model as "数据模型"
participant DB as "数据库"
Client->>API : 发送包含明文数据的请求
API->>Serializer : 调用序列化器
Serializer->>Service : 验证并传递数据
Service->>Service : 对敏感字段进行加密
Service->>Model : 创建或更新模型实例
Model->>DB : 将密文存储为字符串
DB-->>Model : 存储成功
Model-->>Service : 返回实例
Service-->>Serializer : 返回实例
Serializer-->>API : 返回序列化数据
API-->>Client : 返回响应
Client->>API : 发起数据读取请求
API->>Model : 查询数据库
Model->>DB : 执行查询
DB-->>Model : 返回包含密文的数据
Model-->>Service : 返回模型实例
Service->>Service : 对密文字段进行解密
Service-->>Serializer : 返回解密后的实例
Serializer-->>API : 返回序列化数据
API-->>Client : 返回包含明文的响应
```

**图示来源**
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [public/serializers.py](file://public/serializers.py)

## 详细组件分析
### 加密字段实现分析
在 `user.models.py` 中，`DbMgtArcheryAuthInfo` 模型定义了 `token` 和 `refresh_token` 字段。这两个字段是典型的需要加密存储的敏感信息。然而，它们的类型是 `models.CharField(max_length=1000)`，这表明加密过程是外部的。系统可能使用了一个自定义的加密字段类型，或者在模型的 `save()` 方法中重写了加密逻辑。另一种可能是，加密是在调用模型之前，由上层服务完成的。

#### 对于复杂逻辑组件：
```mermaid
flowchart TD
Start([开始]) --> CheckOperation["检查操作类型"]
CheckOperation --> |写入| EncryptData["加密敏感字段"]
CheckOperation --> |读取| DecryptData["解密敏感字段"]
EncryptData --> StoreAsText["将密文作为文本存储"]
StoreAsText --> EndWrite([数据写入完成])
DecryptData --> ReturnPlainText["返回明文数据"]
ReturnPlainText --> EndRead([数据读取完成])
EndWrite --> End([结束])
EndRead --> End
```

**图示来源**
- [user/models.py](file://user/models.py#L50-L55)

### 序列化处理
`public/serializers.py` 文件中的 `ScriptMainSerializer` 和 `ScriptMinorSerializer` 展示了序列化器的基本用法。它们继承自 `serializers.ModelSerializer`，并指定了要序列化的字段。值得注意的是，`ScriptMainSerializer` 通过 `get_detail` 方法实现了嵌套序列化，这表明系统支持复杂的数据结构。虽然这些序列化器本身不处理加密，但它们是数据在API层进出的必经之路，因此加密/解密的钩子很可能被集成在序列化或反序列化的过程中。

**本节来源**
- [user/models.py](file://user/models.py#L37-L71)
- [public/serializers.py](file://public/serializers.py#L0-L31)

## 依赖分析
通过对代码库的分析，`user.models.py` 和 `db_mgt.models.py` 是独立的Django应用模型，它们之间没有直接的依赖关系。`public/serializers.py` 依赖于 `public.models`，而 `user` 和 `db_mgt` 应用的模型则不直接依赖于 `public` 应用的序列化器。这意味着加密逻辑的实现必须是跨应用协调的，可能通过一个共享的加密服务或工具函数来完成。

```mermaid
graph TD
public.serializers.ScriptMainSerializer --> public.models.ScriptMain
public.serializers.ScriptMinorSerializer --> public.models.ScriptMinor
user.models.DbMgtArcheryAuthInfo --> |无直接依赖| public.serializers
db_mgt.models.DbMgtArcheryInfo --> |无直接依赖| public.serializers
```

**图示来源**
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [public/serializers.py](file://public/serializers.py)

**本节来源**
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [public/serializers.py](file://public/serializers.py)

## 性能考虑
在应用层实现加密会带来一定的性能开销。每次数据写入和读取都需要进行加密和解密运算，这会增加CPU的使用率。对于频繁访问的敏感数据，这种开销可能会成为性能瓶颈。此外，由于加密后的数据是随机的，传统的数据库索引将无法对加密字段进行有效查询，这会严重影响查询性能。如果需要对加密字段进行搜索，系统可能需要采用确定性加密（牺牲安全性）或引入专门的加密搜索技术（如可搜索加密），但这会显著增加系统的复杂性。

## 故障排除指南
在排查与存储加密相关的问题时，应首先检查以下方面：
1.  **密钥管理**：确认加密密钥是否正确配置且可访问。密钥丢失将导致所有加密数据无法解密。
2.  **加密算法和模式**：确保所有服务实例都使用相同的加密算法（如AES）和模式（如GCM）。配置不一致会导致加解密失败。
3.  **数据完整性**：检查密文在存储和传输过程中是否被意外修改或截断。`CharField` 的 `max_length` 设置必须足够大以容纳完整的密文。
4.  **序列化/反序列化错误**：如果在API调用中遇到数据解析错误，检查序列化器是否在正确的位置调用了加密/解密方法。

**本节来源**
- [user/models.py](file://user/models.py#L50-L55)
- [public/serializers.py](file://public/serializers.py#L20-L31)

## 结论
根据当前代码库的分析，系统采用了应用层加密的方式来保护数据库中的敏感字段。`user.models.py` 和 `db_mgt.models.py` 中的模型通过 `CharField` 存储加密后的密文，而具体的加密、解密逻辑以及密钥管理则隐藏在业务服务层或一个未在本次分析中发现的独立加密模块中。`public/serializers.py` 负责数据的序列化，是集成加密逻辑的理想位置。为了实现透明加密，建议开发一个自定义的 `EncryptedCharField` 模型字段，该字段可以自动处理 `save()` 和 `__get__()` 时的加解密过程，从而将加密逻辑从业务代码中解耦。同时，必须仔细评估加密对查询性能的影响，并制定相应的优化策略。