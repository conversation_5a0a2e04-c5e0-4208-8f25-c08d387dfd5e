# 数据加密

<cite>
**本文档引用的文件**  
- [user/models.py](file://user/models.py)
- [db_mgt/models.py](file://db_mgt/models.py)
- [app_mgt/models.py](file://app_mgt/models.py)
- [app_mgt/app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py)
- [app_mgt/app_mgt_interface.py](file://app_mgt/app_mgt_interface.py)
- [app_mgt/app_mgt_ser.py](file://app_mgt/app_mgt_ser.py)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档详细阐述系统中敏感数据的加密存储机制，重点分析数据库字段级别的加密实现方式、配置文件中敏感信息的保护措施、模型层加密字段的集成方法、加密数据的序列化与反序列化过程、查询性能影响及优化策略、密钥轮换机制和应急解密流程。结合实际代码展示加密字段在 `user.models.py` 和 `db_mgt.models.py` 中的应用实例，并提供加密模块的使用指南。

## 项目结构
项目采用模块化设计，主要包含 `user`、`db_mgt`、`app_mgt` 等核心模块，分别负责用户管理、数据库管理及应用管理。敏感数据的加密主要集中在 `user` 和 `db_mgt` 模块的模型层，通过字段级别的加密配置实现。配置文件中的敏感信息（如密码、API密钥）通过外部系统（如宙斯配置中心）进行管理，并通过描述符控制是否启用解密。

```mermaid
graph TD
subgraph "核心模块"
user[用户管理模块]
db_mgt[数据库管理模块]
app_mgt[应用管理模块]
end
subgraph "数据存储"
DB[(数据库)]
end
user --> DB
db_mgt --> DB
app_mgt --> DB
```

**图示来源**  
- [user/models.py](file://user/models.py#L1-L72)
- [db_mgt/models.py](file://db_mgt/models.py#L1-L197)
- [app_mgt/models.py](file://app_mgt/models.py#L1-L479)

**本节来源**  
- [user/models.py](file://user/models.py#L1-L72)
- [db_mgt/models.py](file://db_mgt/models.py#L1-L197)
- [app_mgt/models.py](file://app_mgt/models.py#L1-L479)

## 核心组件
系统中的核心加密组件包括：
- **DbMgtArcheryAuthInfo**：存储 Archery 系统的认证信息，包含 token 和 refresh_token 等敏感字段。
- **AppMgtInterfaceInfo**：存储应用接口信息，包含 `encryption` 字段用于标识接口参数的加密类型。
- **配置中心集成**：通过宙斯配置中心的描述符 `config.decrypt=false` 控制应用是否启用配置解密。

这些组件通过模型层的字段定义和外部配置协同工作，实现敏感数据的安全存储与访问。

**本节来源**  
- [user/models.py](file://user/models.py#L54-L63)
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)
- [db/分支2_x/2.8.0/06-ftx-online-search-web的描述符value.sql](file://db/分支2_x/2.8.0/06-ftx-online-search-web的描述符value.sql#L113-L116)

## 架构概述
系统采用分层加密架构，数据在存储层和应用层之间通过加密字段进行保护。模型层定义了需要加密的字段，应用层在序列化和反序列化时根据配置决定是否进行加解密操作。外部配置中心（如宙斯）通过全局开关控制解密行为，确保生产环境的安全性。

```mermaid
graph TB
Client[客户端] --> |请求| API[API接口]
API --> |读取| Model[模型层]
Model --> |加密存储| DB[(数据库)]
Model --> |解密读取| API
Config[配置中心] --> |控制解密开关| API
```

**图示来源**  
- [user/models.py](file://user/models.py#L54-L63)
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)
- [app_mgt/app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L61-L169)

## 详细组件分析

### DbMgtArcheryAuthInfo 分析
该模型用于存储 Archery 系统的认证信息，包含 `token` 和 `refresh_token` 等敏感字段。这些字段在数据库中以明文形式存储，但通过应用层的访问控制和配置中心的权限管理来保障安全。

```mermaid
classDiagram
class DbMgtArcheryAuthInfo {
+id : BigAutoField
+archery_id : IntegerField
+username : CharField
+token : CharField
+refresh_token : CharField
+last_login_time : CharField
}
```

**图示来源**  
- [user/models.py](file://user/models.py#L54-L63)

**本节来源**  
- [user/models.py](file://user/models.py#L54-L63)

### AppMgtInterfaceInfo 分析
该模型用于存储应用接口信息，其中 `encryption` 字段用于标识接口参数的加密类型。该字段在接口文档生成和调用时被读取，以决定是否对请求参数进行加密处理。

```mermaid
classDiagram
class AppMgtInterfaceInfo {
+module_name : CharField
+branch_name : CharField
+interface_name : CharField
+interface_path : CharField
+interface_method : CharField
+interface_type : CharField
+content_type : CharField
+encryption : CharField
+request_params : JSONField
+response_params : JSONField
+defines_params : JSONField
+status : IntegerField
+create_version : CharField
+create_user : CharField
+create_time : DateTimeField
+update_user : CharField
+update_time : DateTimeField
}
```

**图示来源**  
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)

**本节来源**  
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)
- [app_mgt/app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L61-L169)
- [app_mgt/app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L82-L83)

## 依赖分析
系统中的加密功能依赖于多个组件的协同工作：
- **模型层**：定义加密字段的数据结构。
- **应用层**：实现加密字段的序列化与反序列化逻辑。
- **配置中心**：提供全局的加密开关控制。
- **数据库**：存储加密后的数据。

```mermaid
graph TD
A[模型层] --> B[应用层]
B --> C[配置中心]
B --> D[数据库]
C --> B
D --> B
```

**图示来源**  
- [user/models.py](file://user/models.py#L54-L63)
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)
- [app_mgt/app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L61-L169)

**本节来源**  
- [user/models.py](file://user/models.py#L54-L63)
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)
- [app_mgt/app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L61-L169)

## 性能考虑
加密字段的使用会对查询性能产生一定影响，主要体现在：
- **索引效率**：加密后的数据无法有效利用数据库索引，可能导致全表扫描。
- **计算开销**：加解密操作会增加 CPU 计算负担。
- **存储空间**：加密数据通常比明文占用更多存储空间。

优化策略包括：
- 对频繁查询的字段采用确定性加密（如哈希）以便索引。
- 使用缓存机制减少重复加解密操作。
- 在应用层进行批量加解密处理，减少数据库交互次数。

## 故障排除指南
当遇到加密相关问题时，可按以下步骤排查：
1. 检查配置中心的 `config.decrypt` 描述符是否正确设置。
2. 确认模型层的加密字段定义是否正确。
3. 验证应用层的序列化/反序列化逻辑是否按预期工作。
4. 检查数据库中加密字段的存储格式是否符合预期。

**本节来源**  
- [db/分支2_x/2.8.0/06-ftx-online-search-web的描述符value.sql](file://db/分支2_x/2.8.0/06-ftx-online-search-web的描述符value.sql#L113-L116)
- [user/models.py](file://user/models.py#L54-L63)
- [app_mgt/models.py](file://app_mgt/models.py#L296-L296)

## 结论
本文档详细分析了系统中敏感数据的加密存储机制，涵盖了数据库字段加密、配置文件保护、模型层集成、性能优化等多个方面。通过合理的架构设计和组件协同，系统实现了对敏感数据的有效保护。建议在新增加密字段时，遵循现有模式，确保加密逻辑的一致性和安全性。