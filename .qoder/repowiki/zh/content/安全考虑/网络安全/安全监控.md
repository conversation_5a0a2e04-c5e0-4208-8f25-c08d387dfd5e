# 安全监控

<cite>
**本文档引用的文件**  
- [decorator.py](file://public/decorator.py)
- [settings.py](file://spider/settings.py)
- [OpLogs.py](file://common_middle/OpLogs.py)
- [log.ini](file://spider/log.ini)
- [log_views.py](file://publish/log_views.py)
- [salt_view.py](file://task_mgt/salt_view.py)
</cite>

## 目录
1. [引言](#引言)
2. [日志审计配置](#日志审计配置)
3. [速率限制与防暴力破解机制](#速率限制与防暴力破解机制)
4. [入侵检测系统配置](#入侵检测系统配置)
5. [安全扫描流程](#安全扫描流程)
6. [安全监控钩子代码示例](#安全监控钩子代码示例)
7. [安全事件响应流程](#安全事件响应流程)
8. [安全仪表板与关键指标监控](#安全仪表板与关键指标监控)

## 引言
本文档全面描述了系统的安全监控机制，重点涵盖安全事件检测、响应机制、日志审计、速率限制、入侵检测、安全扫描及安全仪表板配置。通过分析系统架构和关键代码实现，详细说明各项安全功能的配置方法和运行机制，为系统安全运维提供指导。

## 日志审计配置

系统实现了全面的日志审计机制，包括访问日志、操作日志和安全日志的收集与管理。日志审计配置主要通过以下方式实现：

1. **日志存储路径**：系统日志存储在`/data/logs/spider/audit`目录下，文件名为`audit_info.log`，采用定时轮转机制，保留100个历史日志文件。
2. **日志记录内容**：日志记录包含请求用户、远程地址、响应状态码、请求方法、请求路径、请求参数、响应内容、请求耗时和时间戳等关键信息。
3. **日志级别**：审计日志使用ERROR级别，确保重要安全事件被记录。
4. **日志格式**：采用标准格式记录，包含时间戳、文件名、行号、日志级别和消息内容。

日志审计功能通过`common_middle/OpLogs.py`中的中间件实现，该中间件在请求处理完成后自动记录请求和响应信息，并将日志写入专门的审计日志记录器。

```mermaid
flowchart TD
Start([请求开始]) --> Process["处理请求"]
Process --> Log["记录审计日志"]
Log --> End([请求结束])
subgraph "日志记录内容"
A["请求用户"]
B["远程地址"]
C["响应状态码"]
D["请求方法"]
E["请求路径"]
F["请求参数"]
G["响应内容"]
H["请求耗时"]
I["时间戳"]
end
Log --> A
Log --> B
Log --> C
Log --> D
Log --> E
Log --> F
Log --> G
Log --> H
Log --> I
```

**Diagram sources**  
- [settings.py](file://spider/settings.py#L45-L83)
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)

**Section sources**  
- [settings.py](file://spider/settings.py#L45-L83)
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)

## 速率限制与防暴力破解机制

系统通过配置Salt API的访问控制来实现速率限制和防暴力破解机制。具体实现方式如下：

1. **基于环境的访问控制**：系统为不同环境（如生产、预发布、灾备等）配置了独立的Salt API用户和密码，实现访问隔离。
2. **API访问凭证**：通过`SALT_LOG_API_USER`和`SALT_LOG_API_PASSWORD`配置项为不同环境设置独立的API访问凭证。
3. **IP访问限制**：虽然未在代码中直接体现，但通过SaltStack的配置可以实现基于IP的访问限制。
4. **会话管理**：系统通过维护日志访问队列来管理并发访问，当同一节点的日志被多个用户尝试访问时，会进行冲突检测和提示。

速率限制机制通过在`spider/settings.py`中配置不同环境的API访问凭证来实现，确保只有授权用户才能访问特定环境的日志和执行操作。

**Section sources**  
- [settings.py](file://spider/settings.py#L461-L497)

## 入侵检测系统配置

系统通过以下方式实现入侵检测功能：

1. **异常登录检测**：通过审计日志分析异常登录行为，如频繁失败的登录尝试、非常规时间的登录等。
2. **可疑请求模式识别**：通过分析请求日志，识别可疑的请求模式，如大量重复请求、异常请求参数等。
3. **自动化告警机制**：当检测到可疑行为时，系统可以通过邮件或其他方式发送告警通知。

入侵检测系统主要依赖于日志审计功能，通过分析`audit_info.log`中的日志条目来识别潜在的安全威胁。系统记录了所有请求的详细信息，为后续的安全分析提供了数据基础。

**Section sources**  
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)

## 安全扫描流程

系统实现了定期的安全扫描流程，主要包括：

1. **依赖库安全检查**：通过集成外部服务，定期检查应用依赖库的安全漏洞。
2. **配置合规性评估**：通过白名单机制，确保系统配置符合安全要求。
3. **定期漏洞扫描**：通过集成安全扫描工具，定期对系统进行漏洞扫描。

安全扫描流程通过`app_mgt/app_mgt_interface.py`中的接口扫描功能实现，该功能定期扫描应用接口并记录扫描结果，同时通过白名单机制控制哪些应用或模块需要进行安全检查。

**Section sources**  
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L301-L360)

## 安全监控钩子代码示例

系统通过Python装饰器实现安全监控钩子，`public/decorator.py`中的`MakeDir`类展示了这一实现：

```mermaid
classDiagram
class MakeDir {
+paths : tuple
+__init__(self, *args)
+make_dir(obj, paths)
+__call__(func)
}
MakeDir --> "0..*" Path : contains
MakeDir "1" --> "1" Logger : uses
MakeDir "1" --> "1" SSH : uses
```

该装饰器的主要功能包括：
- 在函数执行前创建必要的目录结构
- 记录函数调用的参数和关键字参数
- 执行目录创建命令并通过SSH在远程服务器上执行
- 记录操作日志

这种装饰器模式可以用于在关键操作前后插入安全检查、日志记录等监控逻辑。

**Diagram sources**  
- [decorator.py](file://public/decorator.py#L1-L26)

**Section sources**  
- [decorator.py](file://public/decorator.py#L1-L26)

## 安全事件响应流程

当安全事件发生时，系统遵循以下响应流程：

1. **告警通知**：通过邮件或其他通知渠道向相关人员发送告警。
2. **事件分析**：收集相关日志和系统信息，分析事件的性质和影响范围。
3. **应急处理**：根据事件类型采取相应的应急措施，如隔离受影响的系统、修改访问凭证等。
4. **事后复盘**：事件处理完成后进行复盘，总结经验教训并改进安全策略。

安全事件响应流程通过`publish_mgt/publish_mgt_view.py`中的审计功能实现，该功能在执行关键操作时会发送审计邮件并记录操作历史。

**Section sources**  
- [publish_mgt_view.py](file://publish_mgt/publish_mgt_view.py#L611-L635)

## 安全仪表板与关键指标监控

系统通过以下方式实现安全仪表板配置和关键安全指标监控：

1. **日志路径维护**：通过`publish/log_views.py`提供日志路径的维护接口，确保日志收集的完整性。
2. **关键指标监控**：监控系统日志的生成频率、错误率、访问模式等关键指标。
3. **可视化展示**：通过集成的API文档和管理界面，提供安全指标的可视化展示。

安全仪表板的配置依赖于系统的日志审计功能和API监控能力，通过集中收集和分析各类日志数据，为安全监控提供数据支持。

**Section sources**  
- [log_views.py](file://publish/log_views.py#L28-L35)
- [salt_view.py](file://task_mgt/salt_view.py#L46-L67)