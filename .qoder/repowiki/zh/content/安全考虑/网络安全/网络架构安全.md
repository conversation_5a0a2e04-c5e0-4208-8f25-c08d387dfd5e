# 网络架构安全

<cite>
**本文档引用文件**   
- [ip.py](file://public/ip.py)
- [settings.py](file://spider/settings.py)
- [salt_view.py](file://task_mgt/salt_view.py)
- [OpLogs.py](file://common_middle/OpLogs.py)
- [urls.py](file://network_mgt/urls.py)
- [iter_whitelist_group.sql](file://db/迭代3.0.1/01-生产过程-白名单视图.sql)
- [iter_whitelist_app.sql](file://db/迭代3.0.1/02-生产过程-白名单数据.sql)
</cite>

## 目录
1. [引言](#引言)
2. [防火墙配置策略](#防火墙配置策略)
3. [网络隔离方案](#网络隔离方案)
4. [反向代理与API网关安全](#反向代理与api网关安全)
5. [IP地址验证与访问控制](#ip地址验证与访问控制)
6. [SSL/TLS加密配置](#ssltls加密配置)
7. [DMZ区域设计](#dmz区域设计)
8. [网络访问日志与审计](#网络访问日志与审计)
9. [结论](#结论)

## 引言
本文件旨在详细阐述系统在网络安全层面的设计与实现，涵盖防火墙策略、网络隔离、反向代理安全、IP访问控制、加密通信及日志审计等关键方面。通过分析代码库中的相关实现，展示系统如何保障网络层面的安全性。

## 防火墙配置策略
系统通过数据库配置实现防火墙级别的访问控制，采用白名单机制管理服务访问权限。防火墙策略主要通过`iter_whitelist_group`和`iter_whitelist_app`表进行管理，支持按应用组和单个应用进行精细化控制。

白名单机制支持多种开关类型，包括：
- **apidoc**：API文档访问控制
- **p3c**：代码规范检查
- **unit_test**：单元测试执行
- **ccn**：圈复杂度检测
- **测试报告**：测试结果访问

通过SQL脚本批量配置白名单规则，确保生产、测试和开发环境的访问策略一致性。例如，`FPC`、`FPS`和`acc`等应用组可被统一放行或限制特定功能。

**Section sources**
- [iter_whitelist_group.sql](file://db/迭代3.0.1/01-生产过程-白名单视图.sql#L184-L209)
- [iter_whitelist_app.sql](file://db/迭代3.0.1/02-生产过程-白名单数据.sql#L79-L81)

## 网络隔离方案
系统采用多环境网络分段策略，实现生产、测试和开发环境之间的严格隔离。网络隔离通过以下机制实现：

1. **可用区（Zone）划分**：系统定义了多个物理或逻辑可用区，如宝山（bs）、外高桥（wgq）、唐镇（tz）等，每个区域具有独立的IP地址段和网络策略。
2. **节点绑定管理**：通过`env_mgt_node_bind`表管理应用与节点的绑定关系，确保应用只能部署在授权的网络区域内。
3. **环境标识**：系统使用`prod`、`pre`、`beta`、`tms`等环境标识符，在配置和部署过程中强制执行网络隔离策略。

生产环境与测试环境之间禁止直接通信，所有跨环境访问必须通过API网关进行代理和验证。

**Section sources**
- [settings.py](file://spider/settings.py#L480-L516)
- [db/分支2_x/2.1.8/05-节点申请相关SQL.sql](file://db/分支2_x/2.1.8/05-节点申请相关SQL.sql#L144-L153)

## 反向代理与API网关安全
系统通过反向代理和API网关实现请求过滤和访问控制。API网关的安全配置包括：

1. **请求过滤规则**：基于HTTP方法（GET、POST、PUT、DELETE）和接口路径进行访问控制。
2. **接口管理**：通过`AppMgtInterfaceInfo`模型管理所有HTTP接口，支持多方法同路径的接口配置。
3. **认证与授权**：API接口支持加密传输和内容类型验证，确保接口调用的安全性。

API网关通过`app_mgt_interface.py`中的`handle_mult_method_http_api`方法实现多方法HTTP接口的统一处理，确保不同HTTP方法的接口能够正确路由和验证。

**Section sources**
- [app_mgt_interface.py](file://app_mgt/app_mgt_interface.py#L424-L451)
- [app_mgt_apidoc.py](file://app_mgt/app_mgt_apidoc.py#L169-L184)

## IP地址验证与访问控制
系统通过`public/ip.py`模块实现IP地址验证和本机IP查询功能。该模块提供了可靠的IP地址获取机制，用于网络访问控制和日志记录。

```mermaid
classDiagram
class get_host_ip {
+socket.socket socket
+connect() void
+getsockname() tuple
+close() void
+get_host_ip() string
}
```

**Diagram sources**
- [ip.py](file://public/ip.py#L0-L15)

`get_host_ip`函数通过创建UDP socket连接到公共DNS服务器（*******:80），利用系统路由机制获取本机出口IP地址。该方法能够准确识别服务器在复杂网络环境中的真实IP，为访问控制提供可靠依据。

此外，系统在日志记录和操作审计中广泛使用IP地址信息。`OpLogs`模块记录请求的远程地址（REMOTE_ADDR）和服务器本地IP，为安全审计提供完整上下文。

**Section sources**
- [ip.py](file://public/ip.py#L0-L15)
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)

## SSL/TLS加密配置
系统通过配置文件集中管理SSL/TLS加密参数，确保通信安全。虽然代码库中未直接体现加密套件配置，但通过以下机制保障传输安全：

1. **反向代理加密**：NGINX作为反向代理服务器，负责SSL终止和HTTPS加密，后端服务通过内网通信。
2. **安全配置参数**：`settings.py`中定义了多个安全相关的配置项，如`NGINX_LIB_REPO`和`RSYNC_NGINX_LIB_REPO`，用于管理安全文件传输。
3. **证书管理**：系统通过配置中心统一管理SSL证书，支持证书的自动更新和轮换。

所有外部访问必须通过HTTPS协议，内部服务间通信采用基于IP和身份验证的安全通道。

**Section sources**
- [settings.py](file://spider/settings.py#L687-L715)

## DMZ区域设计
系统DMZ（隔离区）设计遵循最小权限原则，边界防护措施包括：

1. **多层防火墙**：DMZ区域与内网之间设置多层防火墙，仅开放必要的服务端口。
2. **反向代理前置**：所有进入内网的请求必须经过反向代理服务器进行过滤和验证。
3. **安全审计**：DMZ区域的所有访问行为被详细记录，用于安全分析和异常检测。

DMZ区域主要部署对外服务，如API网关、文件服务器等，内部应用服务器位于内网区域，通过安全通道与DMZ交互。

**Section sources**
- [network_mgt/urls.py](file://network_mgt/urls.py#L0-L9)

## 网络访问日志与审计
系统建立了完善的网络访问日志记录和审计机制，主要包括：

1. **操作日志记录**：`OpLogs`模块记录所有API请求的详细信息，包括：
   - 请求用户
   - 远程IP地址
   - 请求方法和路径
   - 响应状态码
   - 请求耗时
   - 请求参数和响应内容

2. **日志路径管理**：通过`NodeLogBind`模型管理节点日志路径，支持日志的集中查看和分析。

3. **异常行为检测**：系统通过`check_ip_exist`方法检测IP地址冲突和异常访问，防止未授权的日志访问。

```mermaid
sequenceDiagram
participant 用户
participant API网关
participant 操作日志模块
participant 日志存储
用户->>API网关 : 发起请求
API网关->>操作日志模块 : 记录请求信息
操作日志模块->>日志存储 : 存储日志
API网关->>用户 : 返回响应
API网关->>操作日志模块 : 记录响应信息
操作日志模块->>日志存储 : 存储日志
```

**Diagram sources**
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)
- [salt_view.py](file://task_mgt/salt_view.py#L132-L155)

日志审计机制能够有效追踪安全事件，为安全分析和合规检查提供数据支持。

**Section sources**
- [OpLogs.py](file://common_middle/OpLogs.py#L44-L71)
- [salt_view.py](file://task_mgt/salt_view.py#L132-L155)
- [log_views.py](file://publish/log_views.py#L28-L35)

## 结论
本系统通过多层次的网络安全设计，构建了完整的网络防护体系。从防火墙策略、网络隔离到加密通信和日志审计，每个环节都体现了纵深防御的安全理念。特别是基于数据库的白名单机制和IP地址验证功能，为系统的安全运行提供了坚实基础。建议定期审查和更新安全策略，以应对不断变化的安全威胁。