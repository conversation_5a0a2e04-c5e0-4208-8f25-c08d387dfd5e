# 部署策略

<cite>
**本文档中引用的文件**
- [app_view.py](file://ci_cd_mgt/h5/app_view.py)
- [publish_ser.py](file://publish/publish_ser.py)
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py)
- [publish_mgt_ser.py](file://publish_mgt/publish_mgt_ser.py)
- [models.py](file://ci_cd_mgt/h5/models.py)
- [models.py](file://publish/models.py)
- [models.py](file://iter_mgt/models.py)
- [models.py](file://env_mgt/models.py)
- [models.py](file://publish_mgt/models.py)
</cite>

## 目录
1. [引言](#引言)
2. [项目结构](#项目结构)
3. [核心组件](#核心组件)
4. [架构概述](#架构概述)
5. [详细组件分析](#详细组件分析)
6. [依赖分析](#依赖分析)
7. [性能考虑](#性能考虑)
8. [故障排除指南](#故障排除指南)
9. [结论](#结论)

## 引言
本文档旨在深入分析和说明当前系统中实现的三种核心部署策略：蓝绿部署、灰度发布和滚动更新。结合代码库中的发布服务逻辑，详细阐述每种策略的技术实现机制、应用场景、配置方式以及监控反馈闭环设计。通过分析相关模块的代码结构与交互关系，为运维团队和开发人员提供清晰的部署操作指导和策略选择依据。

## 项目结构
系统采用模块化设计，部署策略相关功能分散在多个子系统中，主要包括 `ci_cd_mgt`（CI/CD管理）、`publish`（发布核心）、`iter_mgt`（迭代管理）、`env_mgt`（环境管理）和 `publish_mgt`（发布管理）等模块。这些模块协同工作，共同支撑复杂的发布流程。

```mermaid
graph TB
subgraph "CI/CD 管理"
CI[ci_cd_mgt]
end
subgraph "发布核心"
P[publish]
end
subgraph "迭代管理"
I[iter_mgt]
end
subgraph "环境管理"
E[env_mgt]
end
subgraph "发布管理"
PM[publish_mgt]
end
CI --> P
I --> P
E --> P
PM --> P
P --> E
P --> I
```

**图示来源**
- [app_view.py](file://ci_cd_mgt/h5/app_view.py#L1-L50)
- [publish_ser.py](file://publish/publish_ser.py#L1-L30)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L1-L20)

**本节来源**
- [ci_cd_mgt/h5/app_view.py](file://ci_cd_mgt/h5/app_view.py#L1-L100)
- [publish/publish_ser.py](file://publish/publish_ser.py#L1-L50)

## 核心组件
部署策略的核心逻辑主要由 `publish` 模块中的 `publish_ser.py` 实现，它负责处理所有发布请求的调度与执行。`iter_mgt` 模块中的 `iter_mgt_group_publish_ser.py` 负责管理分组发布策略，是灰度发布和滚动更新的逻辑基础。`env_mgt` 模块的 `env_mgt_ser.py` 提供了环境和节点的管理能力，为蓝绿部署的环境切换提供支持。`ci_cd_mgt` 模块的 `app_view.py` 是外部API的入口点，接收来自前端或其他服务的发布指令。

**本节来源**
- [publish_ser.py](file://publish/publish_ser.py#L25-L100)
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py#L15-L80)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L10-L40)

## 架构概述
整个部署系统采用分层架构。最上层是API接口层（如 `app_view.py`），接收发布请求。中间是业务逻辑层（`publish_ser.py`, `iter_mgt_group_publish_ser.py`），负责解析请求、选择部署策略、管理发布流程。底层是资源管理层（`env_mgt_ser.py`），负责与具体的服务器节点和环境进行交互。数据模型层（各模块的 `models.py`）定义了发布计划、环境、应用等核心实体。

```mermaid
graph TD
A[API 接口层<br>app_view.py] --> B[业务逻辑层<br>publish_ser.py<br>iter_mgt_group_publish_ser.py]
B --> C[资源管理层<br>env_mgt_ser.py]
C --> D[数据模型层<br>models.py]
B --> D
A --> D
```

**图示来源**
- [app_view.py](file://ci_cd_mgt/h5/app_view.py#L50-L80)
- [publish_ser.py](file://publish/publish_ser.py#L100-L150)
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py#L50-L90)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L30-L60)

## 详细组件分析

### 发布服务逻辑分析
该组件是部署策略的执行中枢，根据请求参数决定采用何种发布模式。

#### 对于API/服务组件：
```mermaid
sequenceDiagram
participant Frontend as 前端/调用方
participant AppView as app_view.py
participant PublishSer as publish_ser.py
participant GroupSer as iter_mgt_group_publish_ser.py
participant EnvSer as env_mgt_ser.py
Frontend->>AppView : POST /api/publish
AppView->>PublishSer : 调用 start_publish()
PublishSer->>PublishSer : 解析发布策略 (蓝绿/灰度/滚动)
alt 灰度或滚动发布
PublishSer->>GroupSer : 调用 execute_group_publish()
GroupSer->>GroupSer : 计算批次/流量比例
GroupSer->>EnvSer : 获取目标节点列表
EnvSer-->>GroupSer : 返回节点信息
GroupSer->>PublishSer : 返回分组结果
PublishSer->>EnvSer : 批量执行部署
else 蓝绿部署
PublishSer->>EnvSer : 获取蓝绿环境信息
EnvSer-->>PublishSer : 返回环境状态
PublishSer->>EnvSer : 执行环境切换
end
PublishSer-->>AppView : 返回发布任务ID
AppView-->>Frontend : 返回成功响应
```

**图示来源**
- [app_view.py](file://ci_cd_mgt/h5/app_view.py#L100-L150)
- [publish_ser.py](file://publish/publish_ser.py#L150-L300)
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py#L100-L200)
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L50-L100)

**本节来源**
- [publish_ser.py](file://publish/publish_ser.py#L100-L400)
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py#L50-L250)

### 灰度发布策略分析
灰度发布通过 `iter_mgt_group_publish_ser.py` 中的分组发布功能实现。系统支持按节点数量或流量比例进行切分。配置信息存储在 `iter_mgt` 和 `publish` 模块的数据库模型中。发布过程中，系统会先部署第一批节点，等待健康检查通过后，再继续下一批，形成一个监控反馈闭环。

**本节来源**
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py#L1-L100)
- [models.py](file://iter_mgt/models.py#L50-L80)
- [models.py](file://publish/models.py#L30-L60)

### 蓝绿部署策略分析
蓝绿部署依赖于 `env_mgt` 模块对环境的管理能力。系统通过 `env_mgt_ser.py` 查询和切换“蓝”、“绿”两个环境的对外服务状态。数据库兼容性处理方案未在代码中直接体现，通常需要在发布前通过外部脚本或手动操作确保两个环境的数据库结构一致。

**本节来源**
- [env_mgt_ser.py](file://env_mgt/env_mgt_ser.py#L1-L50)
- [models.py](file://env_mgt/models.py#L20-L40)

### 滚动更新策略分析
滚动更新是分组发布的另一种应用形式。`iter_mgt_group_publish_ser.py` 负责计算每次更新的节点批次。健康检查集成在 `publish_ser.py` 的发布流程中，每批节点部署完成后会调用健康检查接口。如果检查失败，发布流程会中断并回滚到上一个稳定版本。

**本节来源**
- [iter_mgt_group_publish_ser.py](file://iter_mgt/iter_mgt_group_publish_ser.py#L100-L150)
- [publish_ser.py](file://publish/publish_ser.py#L200-L250)
- [publish_check.py](file://publish/publish_check.py#L1-L30)

## 依赖分析
各部署策略模块之间存在紧密的依赖关系。`publish` 模块是核心，依赖 `iter_mgt` 获取发布计划和分组信息，依赖 `env_mgt` 获取环境和节点信息。`ci_cd_mgt` 作为前端入口，依赖 `publish` 执行具体发布任务。`publish_mgt` 模块可能提供更高级的发布模板和策略管理，也依赖核心 `publish` 模块。

```mermaid
graph LR
CICD[ci_cd_mgt] --> P[publish]
ITER[iter_mgt] --> P
ENV[env_mgt] --> P
PMGT[publish_mgt] --> P
P --> DB[(数据库)]
```

**图示来源**
- [app_view.py](file://ci_cd_mgt/h5/app_view.py#L1-L20)
- [publish_ser.py](file://publish/publish_ser.py#L1-L20)
- [models.py](file://ci_cd_mgt/h5/models.py#L1-L10)
- [models.py](file://publish/models.py#L1-L10)
- [models.py](file://iter_mgt/models.py#L1-L10)
- [models.py](file://env_mgt/models.py#L1-L10)
- [models.py](file://publish_mgt/models.py#L1-L10)

**本节来源**
- [go.mod](file://go.mod#L1-L30)
- [go.sum](file://go.sum#L1-L50)

## 性能考虑
部署策略的性能主要受网络延迟、节点并发处理能力和数据库查询效率影响。滚动更新和灰度发布通过分批处理降低了对系统的瞬时压力。蓝绿部署由于涉及环境切换，其性能瓶颈通常在于DNS或负载均衡器的配置更新速度。代码中未发现明显的性能瓶颈，但大规模节点的并发部署可能需要优化任务队列。

## 故障排除指南
常见问题包括发布任务卡住、节点健康检查失败、环境切换不生效等。应首先检查 `publish` 模块的日志（`log_views.py`），确认发布流程的执行状态。对于节点问题，检查 `env_mgt` 模块是否能正确获取节点信息。对于策略配置问题，检查 `iter_mgt` 和 `publish_mgt` 模块中的相关配置数据。

**本节来源**
- [log_views.py](file://publish/log_views.py#L10-L50)
- [publish_ser.py](file://publish/publish_ser.py#L300-L350)

## 结论
该系统通过模块化设计，实现了蓝绿部署、灰度发布和滚动更新三种主流部署策略。核心逻辑清晰，各组件职责分明。灰度发布和滚动更新共享分组发布机制，提高了代码复用性。蓝绿部署依赖于独立的环境管理模块。整体架构合理，具备良好的可维护性和扩展性。建议进一步完善数据库兼容性处理的自动化流程，并增强发布过程中的实时监控和告警能力。