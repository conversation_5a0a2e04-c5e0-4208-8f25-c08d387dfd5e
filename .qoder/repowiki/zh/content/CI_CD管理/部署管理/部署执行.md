# 部署执行

<cite>
**本文档引用的文件**
- [salt_ser.py](file://task_mgt/salt_ser.py)
- [saltapi.py](file://public/saltapi.py)
- [saltcmd.sql](file://db/saltcmd.sql)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql)
</cite>

## 目录
1. [引言](#引言)
2. [部署执行核心机制](#部署执行核心机制)
3. [部署任务调度流程](#部署任务调度流程)
4. [权限控制与安全通信](#权限控制与安全通信)
5. [部署脚本组织与参数化设计](#部署脚本组织与参数化设计)
6. [超时控制与重试策略](#超时控制与重试策略)
7. [多环境差异化部署](#多环境差异化部署)
8. [命令构造与结果解析](#命令构造与结果解析)
9. [审计日志记录](#审计日志记录)

## 引言
本文档深入解析基于SaltStack的部署执行系统，涵盖远程命令执行、文件分发和状态管理机制。详细说明部署任务从API请求到执行节点的完整调度链路，以及部署过程中的权限控制、加密通信和审计日志记录。文档还解释了部署脚本的组织结构、参数化设计和错误处理机制，并提供关键环节的超时控制、重试策略和失败回退方案。

## 部署执行核心机制

系统基于SaltStack实现远程命令执行、文件分发和状态管理。通过`salt_ser.py`中的`get_salt_cmd_info`函数从数据库获取特定应用、操作类型、节点IP和环境套的Salt命令信息。`default_salt_cmd`和`default_salt_func`函数提供默认的执行命令和Salt命令类型，支持部署、重启、停止、回滚、代码更新等操作。

```mermaid
flowchart TD
A[API请求] --> B[任务队列]
B --> C[执行节点]
C --> D[Salt Master]
D --> E[Minion节点]
E --> F[执行结果]
F --> G[结果解析]
```

**图示来源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L10-L105)

**本节来源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L10-L105)

## 部署任务调度流程

部署任务的调度流程从API请求开始，经过任务队列，最终到达执行节点。`saltapi.py`中的`SaltAPI`类负责与Salt Master通信，通过`asyncMasterToMinion`方法异步执行命令，`masterToMinionContent`方法获取执行结果。任务调度确保命令能够正确发送到目标Minion节点并返回执行结果。

```mermaid
sequenceDiagram
participant API as API接口
participant Queue as 任务队列
participant Executor as 执行节点
participant SaltMaster as Salt Master
participant Minion as Minion节点
API->>Queue : 提交部署任务
Queue->>Executor : 分配任务
Executor->>SaltMaster : 发送Salt命令
SaltMaster->>Minion : 执行命令
Minion-->>SaltMaster : 返回执行结果
SaltMaster-->>Executor : 返回结果
Executor-->>API : 返回最终结果
```

**图示来源**
- [saltapi.py](file://public/saltapi.py#L10-L205)

**本节来源**
- [saltapi.py](file://public/saltapi.py#L10-L205)

## 权限控制与安全通信

系统通过Salt API的认证机制实现权限控制，使用PAM认证方式。`saltapi.py`中的`__init__`方法初始化Salt API连接，包含用户名、密码和超时设置。通信过程采用HTTPS加密，通过`ssl._create_unverified_context()`创建SSL上下文，确保数据传输的安全性。`saltLogin`方法获取认证令牌，用于后续API调用的身份验证。

```mermaid
flowchart TD
A[用户认证] --> B[获取Token]
B --> C[API调用]
C --> D[权限验证]
D --> E[执行命令]
E --> F[返回结果]
```

**图示来源**
- [saltapi.py](file://public/saltapi.py#L10-L205)

**本节来源**
- [saltapi.py](file://public/saltapi.py#L10-L205)

## 部署脚本组织与参数化设计

部署脚本通过数据库表`publish_exec_salt_cmd`进行组织和管理，包含Salt函数、应用名称、执行命令、操作类型、Minion ID、环境套等字段。`get_salt_cmd_info`函数根据应用名称、操作类型、节点IP和环境套查询对应的Salt命令信息。脚本设计支持参数化，通过`default_salt_cmd`函数根据操作类型和Minion ID生成默认的执行命令。

```mermaid
erDiagram
publish_exec_salt_cmd {
string salt_func
string app_name
string exec_cmd
string operate_type
string minion_id
string suite_code
int bind_id
string operater_name
}
```

**图示来源**
- [saltcmd.sql](file://db/saltcmd.sql#L1-L9)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L1-L3759)

**本节来源**
- [saltcmd.sql](file://db/saltcmd.sql#L1-L9)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L1-L3759)

## 超时控制与重试策略

系统通过`SaltAPI`类的`timeout`参数实现超时控制，默认超时时间为300秒。在任务执行过程中，如果超过指定时间未收到响应，将触发超时机制。重试策略由上层应用逻辑控制，当命令执行失败时，可以根据业务需求进行重试。`asyncMasterToMinion`方法返回JID（Job ID），可用于查询任务执行状态和结果。

```mermaid
flowchart TD
A[开始执行] --> B{是否超时?}
B --> |否| C[继续执行]
B --> |是| D[触发超时]
D --> E[记录日志]
E --> F[返回错误]
```

**图示来源**
- [saltapi.py](file://public/saltapi.py#L10-L205)

**本节来源**
- [saltapi.py](file://public/saltapi.py#L10-L205)

## 多环境差异化部署

系统通过`suite_code`字段实现多环境（测试/预发/生产）的差异化部署。在`get_salt_cmd_info`函数中，可以根据环境套查询对应的Salt命令信息。不同环境可以配置不同的执行命令和参数，确保部署过程的安全性和可控性。`publish_exec_salt_cmd.sql`文件中包含多个环境的Salt命令配置，支持灵活的环境管理。

```mermaid
graph TD
A[测试环境] --> B[执行测试命令]
C[预发环境] --> D[执行预发命令]
E[生产环境] --> F[执行生产命令]
```

**图示来源**
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L1-L3759)

**本节来源**
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L1-L3759)

## 命令构造与结果解析

`default_salt_cmd`函数根据操作类型和Minion ID构造默认的执行命令，支持部署、重启、停止、回滚、代码更新等操作。命令构造遵循统一的格式，如`su -c "{} auto || {} auto" tomcat`。`masterToMinionContent`方法获取执行结果，解析返回的JSON数据，提取执行状态和输出信息。结果解析确保能够准确判断命令执行的成功与否。

```mermaid
flowchart TD
A[构造命令] --> B[发送命令]
B --> C[执行命令]
C --> D[获取结果]
D --> E[解析结果]
E --> F[返回状态]
```

**图示来源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L10-L105)
- [saltapi.py](file://public/saltapi.py#L10-L205)

**本节来源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L10-L105)
- [saltapi.py](file://public/saltapi.py#L10-L205)

## 审计日志记录

系统通过`logger.info(sql)`记录SQL查询日志，便于追踪和审计。`saltapi.py`中的`post`和`postRequest`方法记录请求参数和响应结果，确保所有API调用都有迹可循。审计日志记录了部署任务的完整执行过程，包括命令构造、发送、执行和结果解析等环节，为故障排查和安全审计提供支持。

```mermaid
flowchart TD
A[开始执行] --> B[记录请求]
B --> C[执行命令]
C --> D[记录响应]
D --> E[记录结果]
E --> F[完成审计]
```

**图示来源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L10-L105)
- [saltapi.py](file://public/saltapi.py#L10-L205)

**本节来源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L10-L105)
- [saltapi.py](file://public/saltapi.py#L10-L205)