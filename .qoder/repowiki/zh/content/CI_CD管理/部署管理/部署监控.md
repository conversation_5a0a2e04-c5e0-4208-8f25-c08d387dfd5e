# 部署监控

<cite>
**本文档引用文件**  
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py)
- [models.py](file://task_mgt/models.py)
- [enums.py](file://ci_cd_mgt/h5/enums.py)
- [publish_info_ser.py](file://ci_cd_mgt/publish_info_ser.py)
- [view.py](file://ci_cd_mgt/h5/view.py)
- [task_queue.py](file://task_mgt/task_queue.py)
</cite>

## 目录
1. [引言](#引言)
2. [部署状态跟踪机制](#部署状态跟踪机制)
3. [部署结果数据模型与存储](#部署结果数据模型与存储)
4. [部署成功率与耗时分析](#部署成功率与耗时分析)
5. [实时进度可视化与通知机制](#实时进度可视化与通知机制)
6. [部署质量度量体系](#部署质量度量体系)
7. [部署失败根因分析与诊断](#部署失败根因分析与诊断)
8. [外部监控系统集成](#外部监控系统集成)
9. [结论](#结论)

## 引言
本文档详细阐述了基于 `deploy_result.py` 模块的部署监控系统实现方案，涵盖部署状态跟踪、成功率统计、耗时分析、数据采集与存储、可视化展示、通知机制及质量度量等核心内容。系统通过记录H5应用的编译、发布申请及发布过程中的状态信息，为DevOps流程提供全面的监控与分析能力。

## 部署状态跟踪机制

部署状态跟踪是监控系统的核心功能，用于实时反映部署任务的执行情况。系统通过 `H5DeployResult` 模型记录每个部署操作的详细状态。

### 状态初始化
当部署流程启动时，系统会为每个应用创建或更新一条状态记录。根据操作类型（`op_type`）的不同，初始化状态也有所区别：
- 对于编译类操作（如 `ci_pipeline`, `h5_compile`），初始状态为 `compile_running`。
- 对于发布类操作，初始状态为 `publish_running` 或 `publish_apply` 的运行状态。

此过程通过 `DeployResult.insert_status_data` 或直接调用 `H5DeployResult.objects.update_or_create` 方法实现。

### 状态检查与并发控制
为防止同一应用在同一环境和迭代下发生并发部署，系统提供了状态检查机制。`DeployResult.check_deploy_status` 方法会查询指定应用、迭代、环境套和操作类型下的最新记录，若其状态为 `running`，则返回 `False`，表示当前有任务正在运行，应阻止新的部署请求。

```mermaid
sequenceDiagram
participant 用户
participant 部署服务
participant 数据库
用户->>部署服务 : 发起部署请求
部署服务->>数据库 : check_deploy_status(查询运行状态)
数据库-->>部署服务 : 返回状态 (running/非running)
alt 当前有任务在运行
部署服务-->>用户 : 拒绝请求，提示“部署中”
else 无任务在运行
部署服务->>数据库 : insert_status_data(插入running状态)
数据库-->>部署服务 : 确认
部署服务->>用户 : 接受请求，开始部署
end
```

**Diagram sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L62-L73)
- [view.py](file://ci_cd_mgt/h5/view.py#L272-L299)

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [view.py](file://ci_cd_mgt/h5/view.py#L272-L299)

## 部署结果数据模型与存储

部署结果数据的结构化存储是实现监控分析的基础。系统使用 `H5DeployResult` Django模型来持久化部署过程中的关键信息。

### 数据模型设计
`H5DeployResult` 模型定义了以下核心字段：
- **action_id**: 行为ID，关联用户操作记录。
- **app_name**: 操作的应用名称。
- **iteration_id**: 关联的迭代号。
- **suite_name**: 环境套名称（如 pre, prod）。
- **status**: 执行状态，使用预定义的枚举值。
- **message**: 信息描述，用于记录成功或失败的详细信息。
- **op_time**: 操作时间。
- **op_user**: 操作用户。
- **op_type**: 操作类型（如 test_publish, publish_apply）。
- **job_name**: 关联的Jenkins Job名称。
- **begin_ver / end_ver**: 打包的起止版本。

状态（`status`）字段的取值范围在模型中通过 `STATUS_CHOICE` 元组定义，涵盖了编译、发布、发布申请等不同阶段的运行、成功、失败状态。

### 数据采集与更新
部署结果数据通过以下方式采集和更新：
1.  **初始化**: 部署开始时，调用 `insert_status_data` 插入 `running` 状态。
2.  **更新**: 部署过程中或结束后，调用 `update_deploy_result_data_by_id` 或 `H5DeployStatusCollector.update_h5_deploy_status` 方法，根据实际结果更新 `status` 和 `message` 字段。
3.  **批量更新**: 支持通过 `update_deploy_result_data_by_ids` 方法批量更新多个部署记录的状态。

```mermaid
classDiagram
class H5DeployResult {
+action_id : IntegerField
+app_name : CharField
+iteration_id : CharField
+suite_name : CharField
+status : CharField
+message : TextField
+op_time : DateTimeField
+op_user : CharField
+op_type : CharField
+job_name : CharField
+begin_ver : CharField
+end_ver : CharField
}
H5DeployResult : (RUNNING, SUCCESS, FAILURE, C_RUNNING, C_SUCCESS, C_FAILURE, P_RUNNING, P_SUCCESS, P_FAILURE, S_FAILURE, ABORTED)
H5DeployResult : STATUS_CHOICE = ((RUNNING, '发布中'), ...)
```

**Diagram sources**
- [models.py](file://task_mgt/models.py#L156-L174)
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)

**Section sources**
- [models.py](file://task_mgt/models.py#L156-L174)
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)

## 部署成功率与耗时分析

基于存储的部署结果数据，可以进行多维度的成功率和耗时统计分析。

### 成功率统计
部署成功率是衡量发布稳定性的关键指标。计算公式为：
`成功率 = (成功次数 / 总执行次数) * 100%`

系统可以通过查询 `H5DeployResult` 表，按 `app_name`, `suite_name`, `op_type` 等维度进行分组统计，筛选出 `status` 为 `success` 或 `failure` 的记录，从而计算出各维度的成功率。

### 耗时分析
虽然 `H5DeployResult` 模型本身不直接存储耗时，但可以通过 `op_time` 字段（操作时间）结合其他系统（如Jenkins）的执行记录来计算耗时。例如，`hm_optlog_main` 表中包含了 `start_time` 和 `end_time`，可以精确计算Jenkins Job的执行耗时。对于端到端的部署耗时，可以通过计算部署开始记录和最终成功/失败记录之间的时间差来估算。

**Section sources**
- [models.py](file://task_mgt/models.py#L156-L174)
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)

## 实时进度可视化与通知机制

### 实时进度可视化
系统通过查询 `H5DeployResult` 表中 `status` 为 `running` 的记录，可以实时获取当前正在进行的部署任务列表。前端应用可以轮询或通过WebSocket接收这些数据，以列表或仪表盘的形式展示部署进度，包括应用名称、环境、操作类型、开始时间等。

### 通知机制
当部署任务完成（无论是成功还是失败）时，系统可以触发通知机制。虽然具体的通知实现（如邮件、IM）在提供的代码片段中未直接体现，但其逻辑通常如下：
1.  在更新部署状态为 `success` 或 `failure` 后，检查是否需要发送通知。
2.  调用 `public/send_email.py` 或其他消息推送服务。
3.  根据 `op_user` 和预设的订阅规则，向相关人员发送通知，内容包含部署结果、应用、环境、操作人及详细信息（`message` 字段）。

**Section sources**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [send_email.py](file://public/send_email.py)

## 部署质量度量体系

基于部署结果数据，可构建以下DevOps关键指标：

| 指标 | 定义 | 计算方法 |
| :--- | :--- | :--- |
| **首次部署成功率** | 首次尝试即成功的部署比例 | `首次成功部署次数 / 总部署次数` |
| **平均恢复时间 (MTTR)** | 从部署失败到成功恢复的平均时间 | `∑(恢复成功时间 - 失败时间) / 失败事件总数` |
| **部署频率** | 单位时间内成功部署的次数 | `成功部署次数 / 统计周期` |
| **变更失败率** | 部署失败的变更占总变更的比例 | `失败部署次数 / 总部署次数` |

这些指标的计算依赖于对 `H5DeployResult` 表中历史数据的聚合分析，可用于评估团队的交付效率和系统稳定性。

**Section sources**
- [models.py](file://task_mgt/models.py#L156-L174)

## 部署失败根因分析与诊断

### 日志关联分析
定位部署失败的根本原因需要关联分析多源日志：
1.  **部署平台日志**: `H5DeployResult.message` 字段通常包含直接的错误信息。
2.  **Jenkins执行日志**: 通过 `job_name` 字段关联 `hm_optlog_main` 表，获取详细的构建日志，分析编译或脚本执行失败的原因。
3.  **Salt执行日志**: 如果部署涉及Salt命令，可查询 `task_mgt_salt_results` 表，分析远程命令执行的详情。
4.  **用户操作日志**: 查询 `user_action_record` 表，了解操作上下文。

通过将 `action_id`、`job_name` 等作为关联键，可以串联起完整的执行链路，从而精准定位问题环节。

### 典型故障诊断流程图
```mermaid
flowchart TD
Start([部署失败]) --> CheckDeployMsg["检查部署记录消息 (message)"]
CheckDeployMsg --> ParseMsg{"消息是否明确?"}
ParseMsg --> |是| RootCause["定位根因"]
ParseMsg --> |否| CheckJenkins["查询关联的Jenkins Job"]
CheckJenkins --> JenkinsLog{"Jenkins日志是否成功?"}
JenkinsLog --> |否| JenkinsRoot["分析Jenkins构建日志"]
JenkinsLog --> |是| CheckSalt["检查Salt命令执行结果"]
CheckSalt --> SaltLog{"Salt执行是否成功?"}
SaltLog --> |否| SaltRoot["分析Salt执行日志"]
SaltLog --> |是| CheckConfig["检查配置变更记录"]
CheckConfig --> ConfigRoot["分析配置差异"]
JenkinsRoot --> RootCause
SaltRoot --> RootCause
ConfigRoot --> RootCause
RootCause --> End([解决问题])
```

**Diagram sources**
- [models.py](file://task_mgt/models.py#L156-L174)
- [models.py](file://task_mgt/models.py#L200-L218)
- [models.py](file://task_mgt/models.py#L100-L118)

## 外部监控系统集成

### Prometheus/Grafana集成
可将部署关键指标暴露给Prometheus进行采集：
1.  **指标暴露**: 开发一个HTTP端点（如 `/metrics`），查询数据库并以Prometheus格式输出指标，例如：
    - `deployment_status{app="xxx", env="prod", status="success"} 1`
    - `deployment_duration_seconds{app="xxx", env="pre"} 120.5`
2.  **Grafana展示**: 在Grafana中配置Prometheus数据源，创建仪表盘，使用图表展示部署成功率趋势、MTTR、部署频率等。

### 自定义告警规则
在Prometheus或Grafana中配置告警规则，例如：
- **部署失败告警**: `count(deployment_status{status="failure"}) by (app, env) > 0`
- **高失败率告警**: `rate(deployment_status{status="failure"}[1h]) / rate(deployment_status[1h]) > 0.2`
- **长时间运行告警**: `deployment_duration_seconds > 600`

当触发告警时，可通过Alertmanager发送邮件或IM通知给运维团队。

**Section sources**
- [models.py](file://task_mgt/models.py#L156-L174)

## 结论
本文档详细介绍了基于 `deploy_result.py` 的部署监控系统。该系统通过 `H5DeployResult` 模型实现了对部署状态的精确跟踪和数据存储，为部署成功率、耗时分析和质量度量提供了数据基础。通过实时查询和可视化，结合邮件/IM通知，形成了完整的监控闭环。未来可通过集成Prometheus/Grafana，实现更强大的监控和告警能力，持续提升DevOps交付效率和系统稳定性。