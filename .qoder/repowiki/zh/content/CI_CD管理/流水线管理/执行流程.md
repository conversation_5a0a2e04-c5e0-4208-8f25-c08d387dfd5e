# 执行流程

<cite>
**本文档引用的文件**   
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py)
- [flow_view.py](file://ci_cd_mgt/h5/flow_view.py)
- [pipeline_ser.py](file://pipeline/pipeline_ser.py)
- [call_job.py](file://task_mgt/jenkins_task/call_job.py)
</cite>

## 目录
1. [任务队列管理与调度](#任务队列管理与调度)
2. [HTTP请求处理与流水线启动](#http请求处理与流水线启动)
3. [流水线状态转换逻辑](#流水线状态转换逻辑)
4. [Jenkins系统集成机制](#jenkins系统集成机制)
5. [执行上下文、日志追踪与超时控制](#执行上下文日志追踪与超时控制)
6. [并发控制、资源隔离与重试策略](#并发控制资源隔离与重试策略)

## 任务队列管理与调度

`AppTaskQueue` 类继承自 `TaskQueue`，专门用于管理应用任务的执行队列。该类通过 `run_jenkins` 方法启动 Jenkins 构建任务，支持多个业务名称（`business_name`）和参数列表的批量处理。每个任务对象 `q_obj` 包含业务名、操作ID和参数集，系统会逐个调用 Jenkins 并记录结果。

当某个任务调用失败时，系统立即更新部署状态为“失败”，并终止后续执行；若成功，则更新为“运行中”状态。所有任务提交后，系统进入轮询阶段，通过 `check_jenkins_run_status` 方法持续检查 Jenkins 作业的实际执行状态（如成功、失败或中止），并根据最终结果更新数据库中的部署记录。

此外，该机制还实现了应用迭代状态的联动更新：只有当所有 Jenkins 任务均成功完成时，才会触发 `IterMgtAppInfo.update_sys_status` 来更新系统状态，确保状态一致性。

**节来源**
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py#L15-L112)

## HTTP请求处理与流水线启动

`HdStatusCheckApi` 是一个基于 Django REST framework 的视图集，负责处理灰度发布前的状态检查请求。当用户发起灰度部署申请时，系统首先获取请求中的应用列表和迭代ID，然后通过 `_get_app_hd_deploy_iter` 查询当前正在灰度部署的应用及其所属迭代。

接着，系统调用 `user_action_record.get_apply_user_and_time` 获取这些应用的申请人和申请时间，并进行冲突判断。为了避免覆盖正在灰度中的应用，系统使用 `_dict_filter` 对结果进行去重和筛选，保留最近一次的操作记录。如果存在冲突应用，返回提示信息；否则返回无冲突状态，允许继续发布流程。

此机制有效防止了多分支并发灰度导致的部署覆盖问题，保障了发布的安全性和可追溯性。

**节来源**
- [flow_view.py](file://ci_cd_mgt/h5/flow_view.py#L10-L67)

## 流水线状态转换逻辑

流水线的状态管理主要由 `pipeline_ser.py` 中的一系列函数实现。系统通过数据库查询和状态映射机制来维护流水线的生命周期，包括排队、执行、暂停和终止等状态。

例如，`send_to_jenkins_for_ptp` 函数在触发 Jenkins 任务前会先检查上一个构建是否仍在运行。通过 `get_job_info` 获取最后构建编号，并调用 `get_build_info` 判断其是否处于 `building` 状态。若正在运行，则跳过本次触发，避免任务堆积；否则启动新构建并记录日志。

对于更复杂的状态流转，如 mock 构建或测试发布，系统通过 `get_mock_info`、`test_publish_node_list` 等函数从数据库中提取配置信息，并结合进程管理（`Process`）执行远程命令。整个过程通过日志文件名和管道ID进行上下文关联，确保状态可追踪。

虽然当前代码未显式定义状态机，但通过数据库字段（如 `sys_status`）、日志记录和外部系统回调共同实现了状态的闭环管理。

**节来源**
- [pipeline_ser.py](file://pipeline/pipeline_ser.py#L1-L534)

## Jenkins系统集成机制

系统通过 `task_mgt` 模块中的 `JenkinsCaller` 类实现与 Jenkins 的深度集成。该类封装了 Jenkins 服务器连接、任务调用、状态查询等核心功能。

在初始化时，`JenkinsCaller` 根据业务名称动态获取 Jenkins 服务器实例，并设置超时时间为 300 秒，以适应长时间构建任务。调用任务时，`call_job` 方法支持参数化构建，能够将自定义参数（如 `suite_code`）嵌入请求体中，并通过 `build_job_use_post` 以 POST 方式发送，解决传统 GET 请求参数过长的问题。

关键创新在于引入 `jenkins_id` 参数，将 Jenkins 构建任务与本地数据库的 `JenkinsResults` 记录绑定。每次调用都会创建一条 `JenkinsResults` 记录，记录包括任务名、请求参数、开始时间、状态和队列ID。构建完成后，系统自动填充结束时间和实际结果，形成完整的审计轨迹。

此外，`build_job_use_post_return_id` 方法通过解析响应头中的 `Location` 字段提取队列ID，便于后续轮询和状态同步，提升了集成的健壮性。

**节来源**
- [call_job.py](file://task_mgt/jenkins_task/call_job.py#L1-L205)

## 执行上下文、日志追踪与超时控制

系统通过多维度机制保障执行过程的可追踪性和稳定性。首先，在任务执行层面，每个任务都携带唯一的 `pipeline_id` 和 `action_id`，并与 `JenkinsResults` 表中的记录关联，形成端到端的追踪链路。

日志方面，系统广泛使用 `logger.info` 输出关键步骤信息，包括 SQL 查询、参数内容、Jenkins 调用详情等。日志文件名采用 `pipeline_id_appName_nodeCode` 的命名规则，便于按任务和节点检索。同时，所有异常均通过 `traceback.print_exc()` 完整记录，辅助问题定位。

超时控制主要体现在两个层面：一是 Jenkins 客户端连接超时被强制设为 300 秒，防止因网络延迟导致任务中断；二是任务队列轮询机制通过 `time.sleep(3)` 实现非阻塞式状态检查，避免频繁请求造成系统压力。

此外，`SSHConnectionManager.exec_ssh_and_close` 的异步执行模式确保了远程命令不会阻塞主线程，提升了整体响应效率。

**节来源**
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py#L15-L112)
- [call_job.py](file://task_mgt/jenkins_task/call_job.py#L1-L205)
- [pipeline_ser.py](file://pipeline/pipeline_ser.py#L1-L534)

## 并发控制、资源隔离与重试策略

系统在多个层级实现了并发控制与资源隔离。在任务调度层面，`AppTaskQueue` 通过串行处理同一任务对象内的所有 Jenkins 调用，避免了并行触发导致的状态混乱。只有当前任务全部成功后，才进行状态更新，保证了原子性。

资源隔离方面，通过 `suite_code` 参数明确指定执行环境，结合数据库中的 `env_mgt_suite` 和 `env_mgt_node_bind` 表实现环境隔离。不同环境（如 it29、prod）使用独立的节点池和配置，防止资源争用。

重试策略目前主要依赖 Jenkins 自身的重试机制。系统层面未实现自动重试逻辑，但在任务失败时会记录详细错误信息，并通过 `HmOptLogMain` 记录操作日志，便于人工干预和后续分析。未来可通过扩展 `check_jenkins_run_status` 方法，在检测到临时故障时自动触发有限次数的重试。

此外，`ApplyViewSet.is_apply_running` 提供了应用级的运行锁机制，防止同一应用的重复发布，进一步增强了系统的稳定性。

**节来源**
- [app_task_queue.py](file://ci_cd_mgt/h5/app_task_queue.py#L15-L112)
- [pipeline_ser.py](file://pipeline/pipeline_ser.py#L1-L534)
- [call_job.py](file://task_mgt/jenkins_task/call_job.py#L1-L205)