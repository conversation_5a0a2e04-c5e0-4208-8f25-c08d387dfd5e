# 状态监控

<cite>
**本文档引用的文件**
- [h5_stats.py](file://ci_cd_mgt/h5/h5_stats.py)
- [pipeline_log_view.py](file://pipeline/pipeline_log_view.py)
- [salt_view.py](file://task_mgt/salt_view.py)
- [zeus_view.py](file://task_mgt/zeus_view.py)
</cite>

## 目录
1. [引言](#引言)
2. [流水线运行时监控机制](#流水线运行时监控机制)
3. [h5_stats.py 指标收集与聚合](#h5_statspy-指标收集与聚合)
4. [pipeline_log_view.py 日志查询接口](#pipeline_log_viewpy-日志查询接口)
5. [底层部署状态获取与关联分析](#底层部署状态获取与关联分析)
6. [监控告警配置方法](#监控告警配置方法)
7. [性能瓶颈识别与趋势分析](#性能瓶颈识别与趋势分析)
8. [结论](#结论)

## 引言
本文档旨在详细阐述流水线运行时的状态监控与可视化机制。重点介绍关键组件如何协同工作以实现全面的监控能力，包括构建时长、成功率、资源消耗等核心性能指标的采集、日志的实时查询与分页过滤、底层部署状态的获取与关联分析，以及告警配置和性能优化实践。

## 流水线运行时监控机制
系统通过多个核心模块协同工作，构建了一套完整的流水线运行时监控体系。`h5_stats.py` 负责收集和聚合关键性能指标；`pipeline_log_view.py` 提供了日志的创建与持久化接口；`salt_view.py` 和 `zeus_view.py` 则用于获取底层部署状态，并与流水线状态进行关联分析。这套机制确保了从构建、部署到运行的全链路可观测性。

## h5_stats.py 指标收集与聚合

该模块是流水线性能指标监控的核心，主要负责收集和展示构建任务的耗时信息。

### 功能说明
`H5ElapseStatsApi` 类提供了一个 RESTful API 接口，用于查询指定应用、分支和操作类型的构建耗时统计。其主要功能包括：
- **最近一次成功构建耗时**：查询并格式化显示最近一次成功构建的耗时。
- **当前运行构建耗时**：实时计算并显示当前正在运行的构建任务的已耗时。
- **时间格式化**：将秒数转换为 `HH:MM:SS` 的可读格式。

### 数据处理流程
1.  **参数接收**：API 接收 `app_name`（应用名）、`action_type`（操作类型）和 `br_name`（分支名）作为查询参数。
2.  **查询历史记录**：根据参数查询 `HmOptLogMain` 表，获取最近一次成功的构建记录。
3.  **计算历史耗时**：如果存在成功记录，则将其 `elapsed` 字段（秒数）通过 `second_to_readable_time` 方法转换为可读时间。
4.  **查询当前运行记录**：查询 ID 大于上一步记录 ID 且状态为 `running` 的最新构建记录。
5.  **计算实时耗时**：如果存在运行中的记录，则计算从 `start_time` 到当前时间的差值，并格式化为字符串。
6.  **返回结果**：将历史耗时和当前耗时封装成 JSON 数据返回给前端。

```mermaid
flowchart TD
A[客户端请求] --> B{接收参数<br/>app_name, action_type, br_name}
B --> C[查询最近成功构建]
C --> D{是否存在成功记录?}
D --> |是| E[格式化历史耗时]
D --> |否| F[历史耗时 = '-']
E --> G[查询当前运行构建]
F --> G
G --> H{是否存在运行记录?}
H --> |是| I[计算实时耗时 = now - start_time]
H --> |否| J[实时耗时 = '准备中']
I --> K[返回JSON数据]
J --> K
K --> L[客户端展示]
```

**图表来源**
- [h5_stats.py](file://ci_cd_mgt/h5/h5_stats.py#L1-L41)

**章节来源**
- [h5_stats.py](file://ci_cd_mgt/h5/h5_stats.py#L1-L41)

## pipeline_log_view.py 日志查询接口

该模块为流水线提供了日志记录功能，是日志查询和分析的基础。

### 功能说明
`PipeLineLogViewSet` 类定义了创建日志条目的接口，用于在流水线执行过程中记录关键操作事件。

### 接口与数据流
- **接口方法**：`create` 方法处理 HTTP POST 请求。
- **输入参数**：通过 `query_params` 接收 `opt_desc`（操作描述）、`opt_type`（操作类型）、`job_name`（任务名）、`pipeline_id`（流水线ID）和用户信息。
- **数据处理**：
    1.  接收请求参数并获取当前时间戳。
    2.  调用 `insert_log_opt` 方法，将所有信息写入数据库的 `LogOpt` 模型。
    3.  `insert_log_opt` 方法负责创建并保存 `LogOpt` 对象。
- **输出结果**：返回一个成功响应，表明日志记录已成功。

```mermaid
sequenceDiagram
participant Client as "客户端"
participant View as "PipeLineLogViewSet"
participant Model as "LogOpt 模型"
Client->>View : POST /log (opt_desc, opt_type, job_name, pipeline_id)
View->>View : 获取当前时间戳
View->>View : 调用 insert_log_opt()
View->>Model : 创建 LogOpt 对象
Model-->>View : 对象创建成功
View->>Model : 保存对象
Model-->>View : 保存成功
View-->>Client : 返回成功响应
```

**图表来源**
- [pipeline_log_view.py](file://pipeline/pipeline_log_view.py#L1-L31)

**章节来源**
- [pipeline_log_view.py](file://pipeline/pipeline_log_view.py#L1-L31)

## 底层部署状态获取与关联分析

系统通过 `salt_view.py` 和 `zeus_view.py` 两个模块，实现了对底层部署环境的深度监控和状态关联。

### salt_view.py: SaltStack 状态监控

`SlatLogApi` 类提供了通过 SaltStack 查询服务器状态的能力，主要用于获取应用的进程ID、包路径和日志信息。

#### 核心功能
- **日志流式查询**：支持分页和实时刷新，通过 `last_line_num` 参数实现增量日志获取。
- **进程状态检查**：执行 `ps` 命令检查应用进程是否存活。
- **包信息获取**：通过 `find` 和 `stat` 命令定位应用包的物理路径和修改时间。
- **并发控制**：使用 `MysqlQueue` 限制同一日志文件的并发访问，防止资源争抢。

#### 关联分析
前端可以通过应用名和IP地址调用此API，将流水线中记录的部署信息与实际服务器上的运行状态进行比对，验证部署是否成功。

### zeus_view.py: 宙斯平台状态集成

`zeus_view.py` 提供了与“宙斯”配置中心平台的集成接口，用于获取和同步配置状态。

#### 核心功能
- **版本信息查询**：`GetAppProdVersion` 和 `GetAppProdGroupVersion` 可以查询应用在生产环境的最新发布版本。
- **代码地址查询**：`GetAppMasterAddress` 获取应用的主干代码仓库地址。
- **配置状态检查**：`CheckConfigConsistentApi` 等接口用于检查配置是否已成功同步到目标环境。
- **环境信息获取**：`GetAppPublishVersionByNode` 可以获取特定环境中所有应用的发布版本。

#### 关联分析
通过将 `h5_stats.py` 记录的构建版本与 `zeus_view.py` 查询到的生产环境部署版本进行比对，可以实现从“构建”到“部署”的全链路追踪，确保发布的版本一致性。

```mermaid
graph TD
subgraph "流水线监控"
A[h5_stats.py<br/>构建指标]
B[pipeline_log_view.py<br/>操作日志]
end
subgraph "底层状态"
C[salt_view.py<br/>服务器进程/日志]
D[zeus_view.py<br/>配置中心/部署版本]
end
A --> |版本号| E((关联分析))
B --> |操作记录| E
C --> |运行状态| E
D --> |部署版本| E
E --> F[统一监控视图]
```

**图表来源**
- [salt_view.py](file://task_mgt/salt_view.py#L1-L208)
- [zeus_view.py](file://task_mgt/zeus_view.py#L1-L446)

**章节来源**
- [salt_view.py](file://task_mgt/salt_view.py#L1-L208)
- [zeus_view.py](file://task_mgt/zeus_view.py#L1-L446)

## 监控告警配置方法

虽然核心代码中未直接体现告警配置逻辑，但基于现有监控数据，可以建立以下告警策略。

### 告警规则配置
1.  **构建超时告警**：
    - **阈值设置**：当 `h5_stats.py` 返回的 `current_elapsed` 时间超过预设阈值（如30分钟）时触发。
    - **通知渠道**：通过 `public/send_email.py` 模块发送邮件通知给相关负责人。
2.  **构建失败告警**：
    - **阈值设置**：当 `HmOptLogMain` 表中 `status` 字段为 `failure` 时立即触发。
    - **异常检测**：结合 `pipeline_log_view.py` 的日志，分析失败原因。
3.  **部署状态不一致告警**：
    - **阈值设置**：当 `h5_stats.py` 记录的成功构建版本与 `zeus_view.py` 查询到的生产环境版本不一致时触发。
    - **通知渠道**：邮件或企业微信通知。
4.  **进程缺失告警**：
    - **阈值设置**：通过定时调用 `salt_view.py` 的接口，若返回的 `process_id` 为空，则认为应用进程已停止。

### 配置最佳实践
- **分级告警**：根据严重程度设置不同级别的告警（如P0、P1），并配置不同的通知策略。
- **告警聚合**：避免短时间内对同一问题重复发送告警。
- **静默期**：在已知的维护窗口期内，可以临时关闭相关告警。

## 性能瓶颈识别与趋势分析

### 性能瓶颈识别技巧
1.  **分析构建耗时**：利用 `h5_stats.py` 的数据，识别哪些应用或分支的构建时间异常增长，可能是代码量增加、依赖下载慢或编译脚本效率低下的信号。
2.  **检查日志延迟**：如果 `pipeline_log_view.py` 的日志写入出现延迟，可能表明数据库或网络存在瓶颈。
3.  **监控Salt API调用**：`salt_view.py` 中的 `salt_task.salt_run` 调用耗时过长，可能意味着Salt Master负载过高或目标服务器响应慢。

### 历史趋势分析最佳实践
1.  **数据持久化**：将 `h5_stats.py` 的历史构建耗时数据定期归档到数据仓库。
2.  **可视化报表**：使用BI工具（如Superset）创建仪表板，展示构建成功率、平均耗时、90分位耗时等指标的历史趋势。
3.  **根因分析**：当发现性能下降趋势时，结合 `pipeline_log_view.py` 的详细日志和 `salt_view.py` 的系统状态，进行深入的根因分析。
4.  **容量规划**：根据历史增长趋势，预测未来的资源需求，提前进行扩容。

## 结论
本文档详细解析了系统的状态监控架构。通过 `h5_stats.py`、`pipeline_log_view.py`、`salt_view.py` 和 `zeus_view.py` 四个核心模块的协同工作，实现了对流水线从构建、日志到部署状态的全方位监控。在此基础上，通过合理的告警配置和深入的趋势分析，团队可以快速发现并解决潜在问题，持续优化流水线性能，保障系统的稳定交付。