# Salt状态管理

<cite>
**本文档引用的文件**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py)
- [saltapi.py](file://public/saltapi.py)
- [salt_ser.py](file://task_mgt/salt_ser.py)
- [salt_view.py](file://task_mgt/salt_view.py)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql)
- [settings.py](file://spider/settings.py)
</cite>

## 目录
1. [引言](#引言)
2. [状态文件组织结构](#状态文件组织结构)
3. [环境隔离与版本控制](#环境隔离与版本控制)
4. [部署后置操作实现](#部署后置操作实现)
5. [原子性保障与回滚策略](#原子性保障与回滚策略)
6. [典型状态文件示例](#典型状态文件示例)
7. [安全审计与变更追踪](#安全审计与变更追踪)
8. [结论](#结论)

## 引言
本文档详细阐述了Salt状态管理在部署流程中的应用，重点介绍状态文件（State SLS）的组织结构、环境隔离策略、版本控制方案以及原子性保障机制。通过分析`deploy_result.py`中的部署后置操作实现，说明如何利用Salt执行服务启动、配置文件分发和环境初始化等关键任务。同时，文档还讨论了状态管理的安全审计和变更追踪机制，为系统稳定性和可维护性提供支持。

## 状态文件组织结构
Salt状态文件（SLS）采用模块化设计，按照应用和环境进行组织。每个应用的状态文件定义了其部署、配置更新、服务启停等操作。状态文件通过`state.sls`函数调用执行，例如`c-app.ec.cooperation.app_resource`表示合作应用的资源配置。状态文件中包含多种Salt执行函数，如`cmd.run`用于执行Shell命令，`cp.get_dir`用于文件分发，`rsync.rsync`用于代码同步。

状态文件的执行命令存储在数据库表`publish_exec_salt_cmd`中，通过`app_name`、`operate_type`、`minion_id`和`suite_code`等字段进行索引。不同操作类型对应不同的Salt函数和执行命令，如`update`操作通常使用`state.sls`，而`deploy`、`restart`等操作使用`cmd.run`。

```mermaid
graph TD
A[状态文件] --> B[应用模块]
A --> C[环境套]
B --> D[cooperation]
B --> E[member-remote]
B --> F[cgi-howbuy]
C --> G[prod]
C --> H[bs-zb]
C --> I[ucloud-prod]
D --> J[state.sls]
D --> K[cmd.run]
E --> J
E --> K
F --> J
F --> K
```

**图源**
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L984-L988)

**节源**
- [salt_ser.py](file://task_mgt/salt_ser.py#L30-L75)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L1-L3759)

## 环境隔离与版本控制
环境隔离通过`suite_code`字段实现，不同的环境套（如`prod`、`bs-zb`、`ucloud-prod`）对应不同的Salt API端点和配置。在`spider/settings.py`中定义了多个Salt API配置，每个环境套对应一个独立的Salt Master实例，确保环境间的完全隔离。

```mermaid
graph TB
subgraph "生产环境"
A[SALT_API_PROD]
B[外高桥生产]
C[移动端灾备]
end
subgraph "测试环境"
D[SALT_API]
E[唐镇灾备]
F[宝山生产]
end
A --> B
A --> C
D --> E
D --> F
```

**图源**
- [settings.py](file://spider/settings.py#L383-L415)

版本控制通过数据库表`publish_exec_salt_cmd`实现，每次变更都会记录到该表中。通过SQL脚本（如`publish_exec_salt_cmd数据修正.sql`）进行版本更新，确保状态文件的变更可追溯。环境套的优先级通过`env_mgt_suite`表管理，支持灰度发布和滚动更新。

**节源**
- [settings.py](file://spider/settings.py#L383-L415)
- [publish_exec_salt_cmd数据修正.sql](file://db/分支2_x/2.27.0/publish_exec_salt_cmd数据修正.sql#L657-L663)

## 部署后置操作实现
`deploy_result.py`文件中的`DeployResult`类负责管理部署结果和状态。该类通过`insert_status_data`和`insert_result_data`方法记录部署状态，支持`test_publish`、`publish_apply`、`hd_publish`和`pro_publish`等多种操作类型。状态码映射通过`op_type_to_status`字典实现，将操作类型映射到具体的数据库状态值。

部署后置操作通过Salt API异步执行，`asyncMasterToMinion`方法用于触发远程执行。在`task_mgt/salt_ser.py`中，`get_salt_cmd_info`函数根据应用名、操作类型和节点IP获取对应的Salt命令，确保命令的准确性和一致性。对于未配置的默认操作，系统提供`default_salt_cmd`和`default_salt_func`函数生成默认命令。

```mermaid
sequenceDiagram
participant 用户 as 用户
participant DeployResult as DeployResult
participant SaltAPI as SaltAPI
participant Minion as Minion
用户->>DeployResult : 提交部署请求
DeployResult->>DeployResult : insert_status_data()
DeployResult->>SaltAPI : asyncMasterToMinion()
SaltAPI->>Minion : 执行state.sls
Minion-->>SaltAPI : 返回执行结果
SaltAPI-->>DeployResult : 返回JID
DeployResult->>DeployResult : update_deploy_result_data_by_id()
DeployResult-->>用户 : 返回部署结果
```

**图源**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L20-L50)
- [saltapi.py](file://public/saltapi.py#L100-L130)

**节源**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L1-L108)
- [saltapi.py](file://public/saltapi.py#L1-L205)

## 原子性保障与回滚策略
状态执行的原子性通过Salt的`state.sls`函数保障，该函数确保所有配置操作要么全部成功，要么全部失败。在`task_mgt/http_task.py`中，系统不再分析配置更新结果，而是直接通过Salt返回的状态码判断执行结果，简化了错误处理逻辑。

回滚策略通过`rollback`操作类型实现，每个应用都配置了对应的回滚命令。例如，`cooperation`应用在`prod`环境的回滚命令为`su -c "/home/<USER>/shell/tomcat-cooperation.sh rollback" tomcat`。回滚操作同样通过Salt执行，确保操作的一致性和可靠性。

```mermaid
flowchart TD
Start([开始]) --> CheckStatus["检查部署状态"]
CheckStatus --> StatusValid{"状态有效?"}
StatusValid --> |否| ReturnError["返回错误"]
StatusValid --> |是| ExecuteSalt["执行Salt命令"]
ExecuteSalt --> SaltResult{"执行成功?"}
SaltResult --> |否| ExecuteRollback["执行回滚"]
ExecuteRollback --> RollbackResult{"回滚成功?"}
RollbackResult --> |否| FinalError["最终失败"]
RollbackResult --> |是| FinalSuccess["回滚成功"]
SaltResult --> |是| FinalSuccess
FinalSuccess --> End([结束])
FinalError --> End
ReturnError --> End
```

**图源**
- [http_task.py](file://task_mgt/http_task.py#L333-L352)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L2860-L2864)

**节源**
- [http_task.py](file://task_mgt/http_task.py#L333-L352)
- [salt_ser.py](file://task_mgt/salt_ser.py#L77-L105)

## 典型状态文件示例
以下为典型的状态文件应用示例：

### 应用部署示例
```sql
INSERT INTO `publish_exec_salt_cmd`(`salt_func`, `app_name`, `exec_cmd`, `operate_type`, `minion_id`, `suite_code`) 
VALUES ('cmd.run', 'cooperation', 'su -c "/home/<USER>/shell/tomcat-cooperation.sh auto" tomcat', 'deploy', 'w-cooperation-10-12-51-17', 'prod');
```
该示例展示了`cooperation`应用在生产环境的部署命令，使用`cmd.run`执行自动化部署脚本。

### 中间件配置示例
```sql
INSERT INTO `publish_exec_salt_cmd`(`salt_func`, `app_name`, `exec_cmd`, `operate_type`, `minion_id`, `suite_code`) 
VALUES ('state.sls', 's-howbuy', 'c-app.ec.search.app_resource', 'update', 'j-search-10-11-16-76', 'bs-zb');
```
该示例展示了搜索服务在灾备环境的配置更新，使用`state.sls`执行状态文件，确保配置的一致性。

**节源**
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L923-L928)
- [publish_exec_salt_cmd.sql](file://db/分支2_x/2.26.0/publish_exec_salt_cmd.sql#L1320-L1324)

## 安全审计与变更追踪
安全审计通过Salt API的认证机制实现，所有操作都需要有效的Token。`SaltAPI`类中的`saltLogin`方法负责获取认证Token，确保只有授权用户才能执行操作。操作日志记录在`H5DeployResult`表中，包含操作用户、时间、类型和结果等信息。

变更追踪通过数据库版本控制实现，每次状态文件变更都通过SQL脚本记录。`publish_exec_salt_cmd`表的变更历史可追溯，支持审计和问题排查。系统还提供了`check_deploy_status`方法，防止同一应用在相同环境下的并发部署，确保操作的串行化。

**节源**
- [deploy_result.py](file://ci_cd_mgt/h5/db_ser/deploy_result.py#L70-L85)
- [saltapi.py](file://public/saltapi.py#L50-L80)

## 结论
Salt状态管理通过模块化的状态文件、严格的环境隔离和完善的版本控制，实现了部署流程的自动化和标准化。部署后置操作通过`deploy_result.py`中的`DeployResult`类进行管理，确保状态的准确记录。原子性保障和回滚策略提高了系统的可靠性，而安全审计和变更追踪机制则增强了系统的可维护性和安全性。整体方案为大规模应用部署提供了稳定、高效的基础支撑。