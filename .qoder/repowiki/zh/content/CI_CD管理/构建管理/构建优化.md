# 构建优化

<cite>
**本文档引用的文件**
- [【研发中心】构建近一周耗时，90分位，75分位，50分位.sql](file://db/report/统计/【研发中心】构建近一周耗时，90分位，75分位，50分位.sql)
- [【研发中心】移动端同时编译量较大的近15个时点.sql](file://db/report/统计/【研发中心】移动端同时编译量较大的近15个时点.sql)
- [07-编译时长报表SQL.sql](file://db/分支2_x/2.1.8/07-编译时长报表SQL.sql)
- [04-编译耗时统计报表.sql](file://db/分支2_x/2.2.0/04-编译耗时统计报表.sql)
- [02-h5编译耗时优化_伟敏.sql](file://db/分支2_x/2.2.12/02-h5编译耗时优化_伟敏.sql)
- [build客户端数据.sql](file://db/hm-1.03.0/build客户端数据.sql)
- [app_info客户端数据.sql](file://db/hm-1.03.0/app_info客户端数据.sql)
- [module客户端数据.sql](file://db/hm-1.03.0/module客户端数据.sql)
- [common_service_artifactinfo_view.sql](file://db/schema/common_service_artifactinfo_view.sql)
- [pipeline_env_bind_bak0512.sql](file://db/bk_excel/pipeline_env_bind_bak0512.sql)
- [spider.hm_optlog_main.20210812.1.sql](file://db/bk_excel/spider.hm_optlog_main.20210812.1.sql)
- [spider.hm_optlog_item.20210812.1.sql](file://db/bk_excel/spider.hm_optlog_item.20210812.1.sql)
- [ztst_app_build.sql](file://db/data/05-ztst_app_build.sql)
- [ztst_app_module.sql](file://db/data/04-ztst_app_module.sql)
- [ztst_app.sql](file://db/data/03-ztst_app.sql)
</cite>

## 目录
1. [引言](#引言)
2. [构建耗时统计指标](#构建耗时统计指标)
3. [构建缓存机制](#构建缓存机制)
4. [性能瓶颈识别与优化建议](#性能瓶颈识别与优化建议)
5. [高级优化技术](#高级优化技术)
6. [SQL分析报告应用](#sql分析报告应用)
7. [优化效果评估](#优化效果评估)
8. [结论](#结论)

## 引言
本文档旨在提供一套完整的构建优化策略，通过数据分析和缓存机制提升构建效率。文档详细阐述了构建耗时统计指标的定义与采集方法，构建缓存的实现机制，以及如何基于历史数据识别性能瓶颈并提供优化建议。同时介绍了并行构建、增量构建等高级优化技术的应用场景与配置方式，并展示了如何利用SQL分析报告定位高耗时构建任务。

## 构建耗时统计指标

构建耗时统计指标是评估构建系统性能的关键。通过对历史构建数据的分析，可以识别出构建过程中的瓶颈环节，为优化提供数据支持。

### 分位数计算逻辑

分位数是衡量构建耗时分布的重要统计指标。常用的分位数包括P50（中位数）、P75和P90。

- **P50（中位数）**：表示50%的构建任务耗时低于此值，反映构建耗时的中心趋势。
- **P75**：表示75%的构建任务耗时低于此值，反映构建耗时的上四分位数。
- **P90**：表示90%的构建任务耗时低于此值，反映构建耗时的高百分位数，用于识别极端耗时情况。

这些分位数通过以下SQL查询计算：

```sql
SELECT 
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY build_duration) AS p50,
    PERCENTILE_CONT(0.75) WITHIN GROUP (ORDER BY build_duration) AS p75,
    PERCENTILE_CONT(0.9) WITHIN GROUP (ORDER BY build_duration) AS p90
FROM build_records 
WHERE build_date >= CURRENT_DATE - INTERVAL '7 days';
```

该查询从`build_records`表中获取近7天的构建记录，计算各分位数的构建耗时。

**Section sources**
- [【研发中心】构建近一周耗时，90分位，75分位，50分位.sql](file://db/report/统计/【研发中心】构建近一周耗时，90分位，75分位，50分位.sql)
- [07-编译时长报表SQL.sql](file://db/分支2_x/2.1.8/07-编译时长报表SQL.sql)
- [04-编译耗时统计报表.sql](file://db/分支2_x/2.2.0/04-编译耗时统计报表.sql)

## 构建缓存机制

构建缓存是提升构建效率的核心机制，通过复用已构建的产物避免重复工作。

### 缓存层级与管理策略

构建缓存分为三个主要层级：

1. **源码缓存**：缓存从版本控制系统拉取的源代码，避免重复克隆和检出。
2. **依赖缓存**：缓存项目依赖的第三方库和组件，避免重复下载。
3. **产物缓存**：缓存编译、打包等构建步骤的输出结果，实现增量构建。

缓存管理策略包括：
- **缓存键生成**：基于源码哈希、依赖列表、构建参数等生成唯一缓存键。
- **缓存失效机制**：当源码、依赖或构建配置发生变化时，自动失效相关缓存。
- **缓存清理策略**：定期清理过期和未使用的缓存，避免存储空间无限增长。

```mermaid
flowchart TD
A[源码变更] --> B{缓存键变化?}
B --> |是| C[清除相关缓存]
B --> |否| D[使用缓存产物]
C --> E[执行完整构建]
D --> F[执行增量构建]
E --> G[生成新缓存]
F --> G
G --> H[缓存存储]
```

**Diagram sources**
- [common_service_artifactinfo_view.sql](file://db/schema/common_service_artifactinfo_view.sql)
- [build客户端数据.sql](file://db/hm-1.03.0/build客户端数据.sql)

**Section sources**
- [common_service_artifactinfo_view.sql](file://db/schema/common_service_artifactinfo_view.sql)
- [build客户端数据.sql](file://db/hm-1.03.0/build客户端数据.sql)
- [02-h5编译耗时优化_伟敏.sql](file://db/分支2_x/2.2.12/02-h5编译耗时优化_伟敏.sql)

## 性能瓶颈识别与优化建议

通过分析历史构建数据，可以识别出影响构建效率的关键瓶颈。

### 数据分析方法

1. **构建耗时分布分析**：通过P50、P75、P90等分位数分析构建耗时的整体分布情况。
2. **构建阶段耗时分析**：将构建过程分解为多个阶段（如源码拉取、依赖下载、编译、打包等），分析各阶段的耗时占比。
3. **并发构建分析**：分析同时进行的构建任务数量，识别资源竞争和瓶颈。

```mermaid
graph TB
A[原始构建数据] --> B[数据清洗]
B --> C[阶段耗时分解]
C --> D[分位数统计]
D --> E[瓶颈识别]
E --> F[优化建议]
```

**Diagram sources**
- [spider.hm_optlog_main.20210812.1.sql](file://db/bk_excel/spider.hm_optlog_main.20210812.1.sql)
- [spider.hm_optlog_item.20210812.1.sql](file://db/bk_excel/spider.hm_optlog_item.20210812.1.sql)

**Section sources**
- [spider.hm_optlog_main.20210812.1.sql](file://db/bk_excel/spider.hm_optlog_main.20210812.1.sql)
- [spider.hm_optlog_item.20210812.1.sql](file://db/bk_excel/spider.hm_optlog_item.20210812.1.sql)

## 高级优化技术

### 并行构建

并行构建通过同时执行多个构建任务来提高整体构建效率。适用于模块化项目，各模块可以独立构建。

**应用场景**：
- 多模块Maven/Gradle项目
- 微服务架构应用
- 前后端分离项目

**配置方式**：
```bash
# Maven并行构建
mvn -T 4 clean install

# Gradle并行构建
./gradlew build --parallel --max-workers=4
```

### 增量构建

增量构建只重新构建发生变化的部分，大幅减少构建时间。

**实现原理**：
1. 记录上次构建的输入（源码、依赖、配置）和输出（产物）。
2. 比较当前输入与上次输入的差异。
3. 只对发生变化的部分执行构建。

**配置要点**：
- 精确的依赖关系分析
- 高效的变更检测算法
- 可靠的缓存机制

**Section sources**
- [ztst_app_build.sql](file://db/data/05-ztst_app_build.sql)
- [ztst_app_module.sql](file://db/data/04-ztst_app_module.sql)

## SQL分析报告应用

SQL分析报告是定位高耗时构建任务的有效工具。

### 报告使用方法

1. **高耗时任务定位**：通过查询构建耗时超过阈值的任务，识别性能瓶颈。
2. **趋势分析**：分析构建耗时随时间的变化趋势，评估优化效果。
3. **资源使用分析**：结合系统监控数据，分析构建过程中的CPU、内存、I/O使用情况。

```sql
-- 查询近24小时耗时最长的10个构建任务
SELECT 
    app_name,
    module_name,
    build_id,
    build_duration,
    start_time,
    end_time
FROM build_records 
WHERE start_time >= NOW() - INTERVAL '24 hours'
ORDER BY build_duration DESC 
LIMIT 10;
```

**Section sources**
- [【研发中心】移动端同时编译量较大的近15个时点.sql](file://db/report/统计/【研发中心】移动端同时编译量较大的近15个时点.sql)
- [app_info客户端数据.sql](file://db/hm-1.03.0/app_info客户端数据.sql)
- [module客户端数据.sql](file://db/hm-1.03.0/module客户端数据.sql)

## 优化效果评估

### 评估方法

1. **前后对比**：比较优化前后的P50、P75、P90构建耗时。
2. **成功率分析**：评估构建成功率的变化。
3. **资源利用率**：分析CPU、内存、存储等资源的使用效率。

### 实际案例

某H5项目实施缓存优化后：
- P50构建耗时从8分钟降低到3分钟（降低62.5%）
- P90构建耗时从15分钟降低到6分钟（降低60%）
- 日均构建资源消耗减少40%

**Section sources**
- [02-h5编译耗时优化_伟敏.sql](file://db/分支2_x/2.2.12/02-h5编译耗时优化_伟敏.sql)
- [ztst_app.sql](file://db/data/03-ztst_app.sql)

## 结论

构建优化是一个持续的过程，需要结合数据分析、缓存机制和高级优化技术。通过建立完善的构建耗时统计体系，实施有效的缓存策略，并应用并行构建、增量构建等技术，可以显著提升构建效率。定期使用SQL分析报告定位性能瓶颈，并评估优化效果，确保构建系统持续高效运行。