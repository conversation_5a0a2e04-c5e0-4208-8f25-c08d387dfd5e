# 应用管理包 zt@2020-05-07
import json

from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication

from rest_framework import response
from rest_framework import viewsets
from app_mgt import app_mgt_ser
from app_mgt.app_mgt_ser import get_had_batch_publish_app_list
from app_mgt.models import AppInfo, AppModule, AppBuild, AppTeam
from publish_mgt.models import PublishMgtRebootApp
from spider.settings import ApiResult
from tool_mgt.models import TeamInfo


def dict_fetchall(cursor):
    return [dict(zip([col[0] for col in cursor.description], row)) for row in cursor.fetchall()]


class AppMgtApi(viewsets.ViewSet):
    """
    获取「应用」列表 zt@2020-05-07
    """
    env = 'uat'

    def list(self, request):
        pipeline_id = request.GET.get('pipeline_id')
        if pipeline_id:
            print('>>>> env = ', self.env)
            print('>>>> pipeline_id = ', pipeline_id)

            json_result = json.loads(pipeline_id)
            module_name = json_result['module_name']
            page_num = json_result['pageNum']
            page_total = json_result['pageTotal']
            page_size = json_result['pageSize']
        else:
            page = {'num': 1, 'size': 10, 'total': 1080}
            return response.Response(data=ApiResult.success_dict(
                msg="获取应用列表为空",
                data={
                    "page": page
                }
            ))
        if page_num:
            page_num = int(page_num)
        else:
            page_num = 1

        if page_size:
            page_size = int(page_size)
        else:
            page_size = 10

        if page_total:
            page_total = int(page_total)
        else:
            page_total = 0

        page, data_list = app_mgt_ser.get_app_info(
            module_name=module_name,
            page_num=page_num,
            page_size=page_size,
            page_total=page_total,
        )
        return response.Response(data=ApiResult.success_dict(
            msg="获取应用列表成功",
            data={
                "page": page,
                "data_list": data_list
            }
        ))

    def create(self, request, *args, **kwargs):
        module_name = request.data['module_name']
        need_online = request.data['need_online']
        need_check = request.data['need_check']
        lib_repo = request.data['lib_repo']
        container_name = request.data['container_name']
        # module_jdk_version = request.data['module_jdk_version']
        app_port = request.data['app_port']
        create_path = request.data['create_path']
        # isgitorsvn = request.data['isgitorsvn']
        url = request.data['url']
        path = request.data['path']
        package_name = request.data['package_name']
        team_name = request.data['team_name']
        platform_type = request.data['platform_type']
        AppModule.objects.filter(module_name=module_name).update(lib_repo=lib_repo, container_name=container_name,
                                                                 need_online=need_online, need_check=need_check,
                                                                 app_port=app_port, create_path=create_path, )
        id = AppModule.objects.filter(module_name=module_name).values('app_id')[0]['app_id']
        build_id = AppBuild.objects.filter(app_id=id).update(package_name=package_name)
        Appteam_id_data = AppTeam.objects.filter(app_id=id).values('team_id')
        team_id = list(Appteam_id_data)[0]['team_id']
        Appteam_name_data = TeamInfo.objects.filter(id=team_id).update(team_name=team_name)
        if platform_type == 1:
            AppInfo.objects.filter(id=id).update(git_url=url, git_path=path)
        elif platform_type == 0:
            AppInfo.objects.filter(id=id).update(svn_url=url, svn_path=path)
        return response.Response(data=ApiResult.success_dict(
            msg="编辑应用信息成功",
            data={}
        ))

    def update(self, request, *args, **kwargs):
        pass

    def destroy(self, request, *args, **kwargs):
        pass


class AppModuleApi(viewsets.ViewSet):
    """
    获取「应用」zt@2020-05-21
    """

    def list(self, request):
        module_name = request.GET.get('module_name')
        node_ip = request.GET.get('node_ip')
        page_size = request.GET.get('pageSize')
        page_num = request.GET.get('pageNum')

        dict_list = dict_fetchall(app_mgt_ser.get_app_module(
            module_name=module_name,
            node_ip=node_ip,
            page_size=page_size,
            page_num=page_num,
        ))
        return response.Response(data=ApiResult.success_dict(
            msg="获取「应用」列表成功",
            data={"data_list": dict_list}
        ))

    def create(self, request, *args, **kwargs):
        pass

    def update(self, request, *args, **kwargs):
        pass

    def destroy(self, request, *args, **kwargs):
        pass


class AppInfoApiForMock(viewsets.ViewSet):
    """
    提供给mock系统查询应用的接口
    """
    authentication_classes = []

    def list(self, request):
        return_data = dict_fetchall(app_mgt_ser.get_app_info_for_mock())
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=return_data
        ))


class JavaAppInfo(viewsets.ViewSet):
    """
    所有的java类型的应用
    """

    def list(self, request):
        had_batch_publish = request.GET.get("had_batch_publish", False)
        return_data = app_mgt_ser.get_java_app_info(had_batch_publish)
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=return_data
        ))


class AppInfoView(viewsets.ViewSet):
    """
    所有的java类型的应用
    """

    def retrieve(self, request, pk=None):
        param = request.query_params
        return_data = []
        if param.get("app_type")[0] in ['1', '2']:
            return_data = app_mgt_ser.get_app_infos()
        elif param.get("app_type")[0] == '3':
            return_data = app_mgt_ser.get_pa_app_infos()
        elif param.get("app_type")[0] == '4':
            return_data = app_mgt_ser.get_3_app_infos()
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=return_data
        ))

    def list(self, request):
        return_data = app_mgt_ser.get_app_infos()
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=return_data
        ))


class BatchPublishAppInfo(viewsets.ViewSet):
    """
    批量发布应用
    """
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]

    def list(self, request):
        restart_mode = 2
        # 全局模式：1、团队模式：2
        req_restart_mode = request.GET.get('restart_mode')
        if req_restart_mode:
            restart_mode = int(req_restart_mode)

        req_user = None
        if restart_mode == 2:
            if isinstance(request.user, str):
                req_user = request.user
            else:
                req_user = request.user.username

        dict_list = dict_fetchall(app_mgt_ser.get_restart_app_list(req_user))

        return response.Response(data=ApiResult.success_dict(
            msg="获取「一键得重启全量应用」列表成功",
            data={"app_list": dict_list}
        ))

    def create(self, request, *args, **kwargs):
        print("request.data:{}".format(request.data))
        if isinstance(request.user, str):
            operator = request.user
        else:
            operator = request.user.username
        module_name = request.data.get("module_name")
        suite_code = request.data.get("suite_code")
        team_name = request.data.get("team_name")
        department_name = request.data.get("department_name")
        page_num = request.data.get("page_num", 1)
        page_size = request.data.get("page_size", 10)
        restart_app_list = PublishMgtRebootApp.objects.filter(operator=operator, status=1)
        page, app_list = get_had_batch_publish_app_list(module_name, suite_code, team_name, department_name, page_num,
                                                        page_size, restart_app_list)
        return response.Response(data=ApiResult.success_dict(data={"page": page, "app_list": app_list}))


class ValidAppInfoApi(viewsets.ViewSet):
    """
    查询有效应用的接口
    """
    authentication_classes = []

    def list(self, request):
        return_data = dict_fetchall(app_mgt_ser.get_valid_app_info())
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=return_data
        ))


class AppEncodeFormatInfo(viewsets.ViewSet):
    """
    给java服务用，参数名用驼峰
    查询应用的编码格式接口, 默认UTF-8
    """
    authentication_classes = []

    def list(self, request):
        module_name = request.GET.get("app_name")
        suite_code = request.GET.get("suite_code")
        return_data = dict_fetchall(app_mgt_ser.get_app_encode(module_name, suite_code))
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=return_data
        ))


class PeopleAndTeamInfo(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        team_name = request.GET.get("team_name")
        team_info = app_mgt_ser.get_pepole_team_info(team_name)
        lead_info = app_mgt_ser.get_pepole_team_lead_info(team_name)
        team_info.extend(lead_info)
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=team_info
        ))


'''
查询小团队负责人信息
'''
class SmallTeamInfo(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        team_info = app_mgt_ser.get_small_team_info()
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=team_info
        ))


class ModuleAndTeamInfo(viewsets.ViewSet):
    authentication_classes = []

    def list(self, request):
        team_name = request.GET.get("team_name")
        module_info = app_mgt_ser.get_module_team_info(team_name)
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=module_info
        ))


class GetAlarmTeamInfo(viewsets.ViewSet):
    def list(self, request):
        team_name = request.GET.get("team_name")
        alarm_team_info = app_mgt_ser.get_alarm_team_info(team_name)
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=alarm_team_info
        ))


class GetAliasInfoByModule(viewsets.ViewSet):

    def list(self, request):
        module_name = request.GET.get("module_name")
        alias_info = app_mgt_ser.get_db_alias_by_module_name(module_name)
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data=alias_info
        ))


class GetAppInOrExInfoByModule(viewsets.ViewSet):

    def list(self, request):
        module_name = request.GET.get("module_name")
        in_or_ex_info = app_mgt_ser.get_db_in_or_ex_by_module_name(module_name)
        if in_or_ex_info:
            need_online = in_or_ex_info.get("need_online")
            need_ops = in_or_ex_info.get("need_ops")
            if need_online == 1:
                if need_ops == 1:
                    app_type = 1
                else:
                    app_type = 0
            else:
                return response.Response(data=ApiResult.failed_dict(
                    msg="该应用为依赖应用"
                ))
        return response.Response(data=ApiResult.success_dict(
            msg="success",
            data={"app_type": app_type}
        ))
