# ES 初始化接口设计文档

## 1. ES 初始化接口

### 1.1. 功能描述

通过调用 Jenkins 流水线 `test_es_init` 来触发 ES 的初始化过程。该接口会接收业务参数，并创建一条记录到 `jenkins_mgt_test_es_init_job` 表中来跟踪初始化任务的状态。

### 1.2. 接口设计

- **HTTP 方法**: `POST`
- **URL**: `/spider/es_mgt/init/` (此为建议, 具体路径需在 `es_mgt/urls.py` 中定义)
- **所属模块**: `es_mgt`

### 1.3. 请求体 (Body)

```json
{
    "job_name": "test_es_init",
    "bis_pipeline_id": "MIDDLEWARE-SCHEDULE_dev",
    "suite_code": "it29",
    "biz_br_name": "dev",
    "es_dump_name": "your_dump_name_here"
}
```
*(注：根据 `jenkins_mgt_test_es_init_job` 表结构，`es_dump_name` 是关键字段，也应包含在请求中)*

### 1.4. 实现逻辑

1.  接口接收到请求后，解析 `suite_code`, `biz_br_name`, `es_dump_name` 等参数。
2.  准备调用 Jenkins 流水线所需的参数。
3.  在 `jenkins_mgt_test_es_init_job` 表中创建一条新的记录，初始状态设置为 `running`。参考 `db_iter_mgt_pipeline_view.py` 中 `DbIterMgtPipelineView.create()` 方法的实现逻辑，将相关信息（如 `biz_code`, `job_name`, `job_param` 等）存入数据库。
4.  触发 `test_es_init` Jenkins 流水线。
5.  向前端返回成功或失败的响应。

### 1.5. 返回数据示例

```json
{
  "code": 0,
  "message": "ES 初始化任务已成功触发",
  "data": {
    "job_id": 1,
    "status": "running"
  }
}
```