# 生成知识图谱PRP

## 功能描述: $ARGUMENTS

为知识图谱更新和内存管理生成完整的KG-PRP，进行全面的研究和规划。这个命令专门用于在代码或业务变更后抽取新知识并更新知识图谱。

KG-PRP将指导AI智能体如何：
1. 分析代码变更和新增功能
2. 识别需要更新的知识实体和关系
3. 设计知识抽取和更新策略
4. 实现内存/知识图谱的增量更新

## 研究过程

### 1. 变更分析和知识识别
   - 分析最近的代码提交和变更内容
   - 识别新增的类、方法、配置和业务概念
   - 确定需要在知识图谱中表示的实体类型
   - 分析实体间的新关系和依赖

### 2. 现有知识图谱状态分析
   - 检查当前知识图谱的实体和关系结构
   - 分析现有的知识抽取模式和更新机制
   - 识别需要扩展或修改的图谱结构
   - 了解现有的内存管理和持久化策略

### 3. 知识抽取策略研究
   - 研究代码AST解析和静态分析技术
   - 分析文档和注释的自然语言处理方法
   - 了解配置文件和元数据的结构化抽取
   - 研究增量更新和版本管理策略

### 4. 技术栈和工具研究
   - 图数据库更新操作最佳实践
   - Java代码解析库（JavaParser, ASM等）
   - 知识抽取和NLP工具
   - 内存管理和缓存策略

### 5. 用户澄清（如需要）
   - 需要抽取的知识类型和粒度？
   - 知识图谱的更新频率和触发条件？
   - 内存管理的性能和容量要求？
   - 与现有系统的集成方式？

## KG-PRP生成指南

使用PRPs/templates/kg_prp_base.md作为专用模板：

### 知识图谱更新的关键上下文
- **变更检测**: Git diff分析、文件变更监控
- **代码解析**: AST解析、静态分析工具集成
- **知识抽取**: 实体识别、关系抽取算法
- **图谱更新**: 增量更新策略、冲突解决机制
- **内存管理**: 缓存策略、持久化机制
- **性能优化**: 批量操作、索引策略

### KG-PRP实现蓝图
1. **变更检测模块**
   - Git hooks集成
   - 文件变更监控
   - 增量分析逻辑

2. **知识抽取引擎**
   - 代码结构解析
   - 注释和文档处理
   - 配置文件解析
   - 业务逻辑理解

3. **图谱更新管理**
   - 实体CRUD操作
   - 关系更新逻辑
   - 版本控制和回滚
   - 数据一致性保证

4. **内存优化**
   - 缓存层设计
   - 热数据管理
   - 内存使用监控
   - 垃圾回收优化

### 验证关卡（针对Java项目）
```bash
# 编译检查
mvn clean compile

# 代码解析测试
java -cp target/classes com.howbuy.dfile.kg.CodeAnalyzer --test

# 知识抽取验证
java -cp target/classes com.howbuy.dfile.kg.KnowledgeExtractor --validate

# 图谱更新测试
# 运行知识图谱更新测试套件

# 内存使用分析
jstat -gc -t [PID] 1s 10

# 性能基准测试
# 运行知识抽取和更新的性能测试
```

### 知识图谱专用质量检查
- **数据质量**: 实体完整性、关系准确性、重复检测
- **增量更新**: 变更检测准确性、更新一致性
- **性能指标**: 抽取速度、更新效率、内存使用
- **集成测试**: 与现有系统的兼容性验证

## 实现策略考虑

### 知识抽取优先级
1. **高优先级**: 核心业务类、API接口、配置实体
2. **中优先级**: 工具类、辅助方法、测试代码结构
3. **低优先级**: 临时变量、内部实现细节

### 更新策略模式
- **实时更新**: 代码提交触发的即时更新
- **批量更新**: 定期的全量知识重建
- **智能更新**: 基于变更影响度的选择性更新

## 版本化输出管理

### 自动版本目录创建
- **自动检测Git分支**: 基于当前Git分支创建版本目录
- **标准目录结构**: 创建 `PRPs/{branch-version}/requirements/` 和 `PRPs/{branch-version}/prps/`
- **知识需求管理**: 自动复制KG-INITIAL.md到 `requirements/` 目录
- **版本元数据**: 更新 `metadata.json` 记录知识图谱文件生成历史

### 输出路径规范
```bash
# 版本目录结构
PRPs/{current-git-branch}/
├── requirements/
│   ├── INITIAL.md              # 功能需求文档
│   └── KG-INITIAL.md           # 复制的知识图谱需求文档
├── prps/
│   ├── {feature-name}.md       # 功能实现PRP
│   └── KG-{feature-name}.md    # 生成的知识图谱PRP
└── metadata.json               # 版本元数据（自动更新）

# 输出示例
保存为: PRPs/1.18.2/prps/KG-{描述}-memory-update.md
需求备份: PRPs/1.18.2/requirements/KG-INITIAL.md
```

### 与功能PRP的协调
- **同版本管理**: 与功能PRP存储在同一版本目录下
- **关联追踪**: metadata.json记录功能PRP和知识图谱PRP的关联关系
- **依赖管理**: 知识图谱PRP可以引用同版本的功能PRP内容

## KG-PRP质量检查清单
- [ ] 变更检测机制完整可靠
- [ ] 知识抽取算法准确高效
- [ ] 图谱更新策略安全一致
- [ ] 内存管理优化合理
- [ ] 验证测试全面可执行
- [ ] 性能指标明确可测量
- [ ] 与现有系统集成顺畅
- [ ] 错误处理和恢复机制完善
- [ ] 监控和调试工具齐全
- [ ] 文档和使用指南清晰

按1-10评分KG-PRP的实施信心水平（Claude Code一次性成功实现的预期）

**目标**: 构建一个智能的、高效的知识图谱更新系统，能够自动感知代码变更并将新知识无缝集成到内存和持久化存储中。