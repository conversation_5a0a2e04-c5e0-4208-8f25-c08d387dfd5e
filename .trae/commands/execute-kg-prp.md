# 执行知识图谱PRP

## KG-PRP文件: $ARGUMENTS

执行知识图谱PRP以实现内存更新和知识图谱同步。这个命令专门用于将代码变更抽取的知识集成到系统内存和持久化存储中。

## 执行流程

### 1. **加载KG-PRP**
   - 读取指定的KG-PRP文件
   - 理解所有上下文和知识抽取要求
   - 遵循PRP中的所有指令，必要时扩展研究
   - 确保具备完整实施知识更新的上下文
   - 根据需要进行额外的代码库探索和在线研究

### 2. **智能分析阶段 (ULTRATHINK for KG)**
   - 深度思考知识抽取和更新策略
   - 分析当前代码库状态和最近变更
   - 识别需要抽取的知识实体和关系
   - 评估对现有知识图谱的影响
   - 规划增量更新路径和回滚策略
   - 使用TodoWrite工具创建详细的实施计划

### 3. **变更检测与分析**
   - 分析Git变更历史和差异
   - 识别新增、修改、删除的代码元素
   - 解析代码结构变化（类、方法、配置等）
   - 提取注释和文档中的业务知识
   - 分析依赖关系和调用图变化

### 4. **知识抽取执行**
   - 实施PRP中定义的知识抽取算法
   - 解析代码AST获取结构信息
   - 提取业务概念和实体定义
   - 识别实体间的关系和依赖
   - 处理配置文件和元数据信息

### 5. **本地存储和图谱更新**
   - 将抽取的知识保存到本地存储结构 (KGs/storage/)
   - 按实体和关系类型组织本地文件存储
   - 创建版本快照和更新历史记录
   - 更新本地索引和元数据信息
   - 执行数据验证和一致性检查

### 6. **Neo4j数据库同步**
   - **优先使用neo4j-mcp工具**: 默认使用neo4j-mcp提供的工具进行数据库同步
   - **自动连接管理**: neo4j-mcp自动处理数据库连接和认证
   - **批量数据同步**: 使用neo4j-mcp批量导入本地JSON数据到Neo4j
   - **实时状态验证**: 通过neo4j-mcp查询验证同步结果和数据一致性
   - **错误自动处理**: neo4j-mcp提供智能的错误处理和重试机制

### 7. **验证测试**
   - 运行PRP中定义的每个验证命令
   - 验证知识抽取的准确性和完整性
   - 测试图谱更新的一致性
   - 检查内存使用和性能指标
   - 修复任何失败的测试用例
   - 重复运行直到所有验证通过

### 8. **质量保证**
   - 确保所有检查清单项目完成
   - 运行最终的综合验证套件
   - 验证与现有系统的集成
   - 检查知识图谱的查询性能
   - 确认内存优化效果

### 9. **完成报告**
   - 生成知识更新报告
   - 统计抽取的实体和关系数量
   - 记录性能指标和优化效果
   - 报告完成状态和任何注意事项
   - 重新审阅KG-PRP确保所有要求已实现

### 10. **持续监控设置**
   - 配置知识变更监控机制
   - 设置性能和内存使用告警
   - 建立知识质量评估流程
   - 准备下次增量更新的基础

## 执行重点

### 知识抽取精度
- 准确识别业务概念和技术概念
- 正确建立实体间的关系映射
- 保持知识的语义一致性
- 避免重复和冗余信息

### 更新策略
- **增量更新**: 仅处理变更的部分，提高效率
- **版本控制**: 维护知识的历史版本和变更轨迹
- **冲突解决**: 处理新旧知识的矛盾和覆盖
- **一致性保证**: 确保更新过程中的数据完整性

### 性能优化
- **批量操作**: 合并小的更新操作提高效率
- **缓存策略**: 优化热点知识的内存访问
- **异步处理**: 大量数据的后台处理机制
- **资源管理**: 控制内存使用和垃圾回收

### 错误处理
- **回滚机制**: 更新失败时的恢复策略
- **部分成功**: 处理部分更新成功的情况
- **重试逻辑**: 临时失败的自动重试
- **告警通知**: 严重错误的及时通知

## 验证检查点

### 技术验证
```bash
# 编译和语法检查
mvn clean compile

# 知识抽取单元测试
mvn test -Dtest=KnowledgeExtractionTest

# 图谱更新集成测试
mvn test -Dtest=KnowledgeGraphUpdateTest

# 内存使用分析
jstat -gc -t [PID] 1s 5
```

### 业务验证
- 关键业务概念是否正确抽取
- 实体关系映射是否准确
- 知识查询结果是否符合预期
- 系统响应性能是否满足要求

### Neo4j同步验证
```bash
# 使用neo4j-mcp工具进行数据库同步验证
# AI会自动调用neo4j-mcp提供的工具：

# 1. 测试Neo4j连接
neo4j-mcp connection-test

# 2. 验证数据同步状态  
neo4j-mcp sync-status

# 3. 执行数据一致性检查
neo4j-mcp consistency-check

# 4. 运行图谱查询测试
neo4j-mcp query-test --business-queries
```

### 数据验证
- 数据完整性检查（无丢失、无损坏）
- 本地存储与Neo4j数据一致性验证
- 重复数据检测和清理
- 知识图谱结构合理性验证
- Neo4j查询性能和准确性测试

## 成功标准

- [ ] 所有新增和变更的代码元素成功抽取
- [ ] 知识图谱结构正确更新
- [ ] 内存缓存有效刷新
- [ ] 查询性能满足预期
- [ ] 数据一致性完整性验证通过
- [ ] 系统集成功能正常
- [ ] 监控和告警机制工作正常

## 注意事项

**如果验证失败**: 
- 使用PRP中的错误模式进行诊断
- 检查知识抽取算法的准确性
- 验证图谱更新操作的原子性
- 确认内存管理策略的有效性
- 必要时回滚到上一个稳定状态

**性能关注点**:
- 监控内存使用峰值和增长趋势
- 跟踪知识抽取和更新的耗时
- 观察图谱查询的响应时间变化
- 注意系统整体性能的影响

记住：知识图谱的准确性和一致性比速度更重要，确保每次更新都是可靠和可验证的。