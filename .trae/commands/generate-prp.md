# 创建PRP

## 功能文件: $ARGUMENTS

为通用功能实现生成完整的PRP，进行全面的研究。确保上下文传递给AI智能体以启用自我验证和迭代改进。首先阅读功能文件以了解需要创建什么、提供的示例如何帮助以及任何其他考虑因素。

AI智能体只获得您附加到PRP的上下文和训练数据。假设AI智能体可以访问代码库并具有与您相同的知识截止点，因此将您的研究结果包含或引用在PRP中很重要。智能体具有网络搜索功能，因此请传递文档和示例的URL。

## 研究过程

1. **代码库分析**
   - 在代码库中搜索类似的功能/模式
   - 识别在PRP中引用的文件
   - 注意要遵循的现有约定
   - 检查验证方法的测试模式

2. **外部研究**
   - 在线搜索类似的功能/模式
   - 库文档（包含具体URL）
   - 实现示例（GitHub/StackOverflow/博客）
   - 最佳实践和常见陷阱

3. **用户澄清**（如需要）
   - 要镜像的特定模式以及在哪里找到它们？
   - 集成要求以及在哪里找到它们？

## PRP生成

使用PRPs/templates/prp_base.md作为模板：

### 需要包含并传递给AI智能体的关键上下文作为PRP的一部分
- **文档**: 包含特定章节的URL
- **代码示例**: 来自代码库的真实片段
- **注意事项**: 库的特殊问题、版本问题
- **模式**: 要遵循的现有方法

### 实现蓝图
- 从显示方法的伪代码开始
- 引用真实文件的模式
- 包含错误处理策略
- 列出完成PRP需要完成的任务，按应完成的顺序排列

### 验证关卡（必须可执行）例如对于python
```bash
# 语法/样式
ruff check --fix && mypy .

# 单元测试
uv run pytest tests/ -v

```

*** 关键 - 在您完成研究和探索代码库之后，在开始编写PRP之前 ***

*** 深度思考PRP并规划您的方法，然后开始编写PRP ***

## 版本化输出管理

### 自动版本目录创建
- **自动检测Git分支**: 基于当前Git分支创建版本目录
- **标准目录结构**: 创建 `PRPs/{branch-version}/requirements/` 和 `PRPs/{branch-version}/prps/`
- **需求文档管理**: 自动复制INITIAL.md到 `requirements/` 目录
- **版本元数据**: 更新 `metadata.json` 记录文件生成历史

### 输出路径规范
```bash
# 版本目录结构
PRPs/{current-git-branch}/
├── requirements/
│   └── INITIAL.md           # 复制的原始需求文档
├── prps/
│   └── {feature-name}.md    # 生成的PRP文件
└── metadata.json            # 版本元数据（自动更新）

# 输出示例
保存为: PRPs/1.18.2/prps/{功能名称}.md
需求备份: PRPs/1.18.2/requirements/INITIAL.md
```

## 质量检查清单
- [ ] 包含所有必要的上下文
- [ ] 验证关卡可由AI执行
- [ ] 引用现有模式
- [ ] 清晰的实现路径
- [ ] 错误处理已记录

按1-10的尺度为PRP评分（使用claude codes成功进行一次性实现的信心水平）

记住: 目标是通过全面的上下文实现一次性实现成功。