# 代码审查 (Code Reviewer)

基于知识图谱中的质量标准审查代码PRP执行结果，确保符合PRP要求，并将改进反馈到PRP和知识图谱中。

## 执行结果路径: $ARGUMENTS

## 审查流程

### 1. **加载审查上下文**
   - 读取PRP执行结果和相关代码变更
   - 从知识图谱中获取质量标准和审查清单
   - 识别相关的PRP文档和业务需求
   - 加载常见问题库和最佳实践

### 2. **全面质量审查**
   - **代码质量检查**
     - 代码规范和风格一致性
     - 架构设计模式遵循情况
     - 异常处理和边界条件覆盖
     - 性能和安全考量
   
   - **PRP合规性验证**
     - 功能需求完整实现
     - 验收标准逐项检查
     - 配置和集成要求验证
     - 文档和示例完整性

   - **知识图谱标准对照**
     - 接口设计规范
     - 版本兼容性要求
     - 数据流和依赖关系
     - 测试覆盖率标准

### 3. **问题识别与分类**
   - **严重问题**: 功能缺失、安全漏洞、架构违背
   - **一般问题**: 代码规范、性能优化、文档不足
   - **改进建议**: 最佳实践、重构机会、扩展性考虑

### 4. **反馈处理**
   - **执行结果不合规时**
     - 参考知识图谱生成具体改进建议
     - 提供代码示例和修复方案
     - 标记需要重新执行的PRP部分

   - **PRP设计缺陷但代码已修复时**
     - 提取修复逻辑和设计模式
     - 反向更新原则和要求等到原始PRP文档
     - 同步更新知识图谱的PRP模式库

   - **审查通过时**
     - 记录执行经验和质量反馈
     - 提取改进建议和最佳实践
     - 更新知识图谱的质量标准

### 5. **知识图谱更新**
   - **新问题模式发现**
     - 分析问题根因和解决方案
     - 更新质量检查清单
     - 丰富常见问题库

   - **成功经验积累**
     - 记录优秀实现模式
     - 更新最佳实践库
     - 改进审查标准

### 6. **生成审查报告**
   - **执行摘要**: 整体质量评估和合规状态
   - **详细发现**: 问题清单、改进建议、参考资料
   - **行动建议**: 优先级排序的改进计划
   - **知识更新**: 对PRP和知识图谱的更新内容

## 质量标准框架

### **代码层面**
- [ ] 遵循Java 8编码规范
- [ ] 正确使用SPI设计模式
- [ ] 异常处理完整且恰当
- [ ] 配置管理符合架构要求
- [ ] 路径处理使用工具类方法
- [ ] 测试覆盖关键业务逻辑

### **架构层面**
- [ ] 模块职责清晰，依赖合理
- [ ] 接口设计符合facade模式
- [ ] 配置和业务逻辑正确分离
- [ ] 扩展点设计符合开闭原则

### **集成层面**
- [ ] Maven构建配置正确
- [ ] 依赖版本兼容
- [ ] 部署配置完整
- [ ] 文档和示例准确

## 工具集成
- **构建验证**: `mvn clean compile && mvn package`
- **代码分析**: 静态代码检查工具
- **知识图谱查询**: 使用mcp-neo4j-memory工具
- **PRP更新**: 直接修改PRP文档文件

## 输出交付物
1. **审查报告**: 详细的质量评估和改进建议
2. **更新的PRP**: 基于发现问题改进的PRP文档
3. **知识图谱更新**: 新的质量标准和问题模式
4. **修复代码**: 针对发现问题的代码修复（如需要）

## 成功标准
- 所有质量检查点通过
- 关键问题得到修复或标记
- PRP和知识图谱得到有效更新
- 生成可执行的改进计划