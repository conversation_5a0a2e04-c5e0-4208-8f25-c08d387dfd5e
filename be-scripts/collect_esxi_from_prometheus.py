# 采集esxi物理机的使用率 zt@2025-09-22
# 第1版 zt@2025-09-22
# 简写说明：
#   FMT：format
#   TGT：target
# 特别注意：
#   dev: export SCM_BEE_PATH=/Users/<USER>/ztst/workspaces/zt-work/howbuy-py/be-scripts/be-scripts
#   test:
#   prod:
#       export PYTHONPATH=/Users/<USER>/ztst/workspaces/zt-work/howbuy-py/be-scripts/be-scripts
# 日志目录：
#   mkdir -p /data/ztst_logs/collect_esxi_from_prometheus/

# ==== 1、环境变量 ====
import os
import sys
from typing import Any, Dict

print("=================== 环境变量打印（开始）===================")
print(">>>> PATH(os): {}".format(os.getenv('PATH')))
print(">>>> SCM_BEE_PATH: {}".format(os.getenv('SCM_BEE_PATH')))
print(">>>> SCM_PATH: {}".format(os.getenv('SCM_PATH')))
print(">>>> PYTHONPATH: {}".format(os.getenv('PYTHONPATH')))
print(">>>> sys.path: {}".format(sys.path))
print("=================== 环境变量打印（结束）===================")
# ==== 2、日志处理 ====
import logging
from logging.handlers import TimedRotatingFileHandler

FMT_DATE_STR = '%Y-%m-%d'
FMT_TIME_STR = '%Y-%m-%dT%H:%M:%S'
FMT_UTC_STR = '%Y-%m-%dT%H:%M:%SZ'
# ======== 自定义日志（开始） ========
# 1、日志文件
LOG_TGT = "collect_esxi_from_prometheus"
LOG_PATH = "/data/ztst_logs/" + LOG_TGT
LOG_NAME = "collect_esxi_from_prometheus.log"
LOG_FILE = os.path.join(LOG_PATH, LOG_NAME)
# 2、日志格式
FMT_CONSOLE_STR = "[%(levelname)s]: %(message)s"
FMT_TRF_STR = "%(asctime)s (%(name)-12s) %(filename)s[line:%(lineno)d] [%(levelname)-8s]: %(message)s"
# 3、logging初始化
# 3-1、日志等级
log = logging.getLogger(__name__)
log.setLevel(level=logging.INFO)
# 3-2、日志目标
# 3-2-1、控制台
console_handler = logging.StreamHandler(stream=sys.stdout)
console_handler.setLevel(logging.INFO)
console_fmt = logging.Formatter(fmt=FMT_CONSOLE_STR, datefmt="%H:%M:%S")
console_handler.setFormatter(console_fmt)
# 3-2-2、文件
trf_handler = TimedRotatingFileHandler(LOG_FILE, when='H', backupCount=24 * 30, encoding='utf-8')
trf_handler.setLevel(logging.INFO)
trf_fmt = logging.Formatter(FMT_TRF_STR)
trf_handler.setFormatter(trf_fmt)
# 3-3、双日志输出
# log.addHandler(console_handler)
log.addHandler(trf_handler)
# ======== 自定义日志（结束） ========

# ==== 3、业务功能 ====
from datetime import datetime, timedelta, timezone
from enum import unique, Enum
import requests
import pymysql
from pymysql.cursors import DictCursor
import json
from settings import DATABASES, CMDBDATABASES, MIANBAO_API

CREATE_USER = "scm_sync"
UPDATE_USER = "scm_sync"
CREATE_STAMP = 0
# cache
SPIDER_GROUP_ID_DICT = None
SPIDER_MODULE_NAME_DICT = None
ZONE_CACHE_SUITE_DICT = {}
# domain
DOMAIN_NAME = "http://prometheus.intelnal.howbuy.com"
METRIC_PREFIX = "vmware_host"


def get_cost_time(st, et=None):
    """获取耗时，单位「秒」zt@2022-11-09"""
    ct = 0
    if st:
        if not et:
            et = datetime.now()
        ct = round((et - st).total_seconds(), 2)
    return ct


def parse_start_end_time(st_str, et_str=None, curr_time=None):
    """解析起始时间 zt@2025-09-22"""
    if not curr_time:
        curr_time = datetime.now()

    if not st_str:
        st_time = curr_time - timedelta(hours=1)
        et_time = curr_time - timedelta(hours=1)
    elif not et_str:
        st_time = datetime.strptime(st_str, FMT_TIME_STR)
        et_time = st_time
    else:
        st_time = datetime.strptime(st_str, FMT_TIME_STR)
        et_time = datetime.strptime(et_str, FMT_TIME_STR)

    st_time = st_time.replace(minute=0, second=0, microsecond=0)
    et_time = et_time.replace(minute=59, second=59, microsecond=999999)

    return st_time, et_time


@unique
class SyncTypeEnum(Enum):
    SYNC_TYPE_ALL = ('sync_all', "同步所有信息")
    SYNC_TYPE_DAY = ('sync_day', "同步部分信息")

    def __init__(self, type_name, type_desc):
        self.type_name = type_name
        self.type_desc = type_desc


@unique
class ProviderEnum(Enum):
    PROVIDER_ALL = ('ALL', '全部', 'pa/vvhosts')

    # PROVIDER_VSPHERE = ('vsphere', 'VMware', 'vsphere/vms')
    # PROVIDER_UCLOUD = ('ucloud', '优刻得', 'ucloud/uhost')
    # PROVIDER_QQCLOUD = ('qqcloud', '腾讯云', 'qqcloud/instance')

    def __init__(self, provider_code, provider_name, provider_url):
        self.provider_code = provider_code
        self.provider_name = provider_name
        self.provider_url = provider_url


@unique
class ErrTypeEnum(Enum):
    ERR_TYPE_NODE_NONE = ('err_node_none', "cmdb中无此节点信息（minion_id）。")
    ERR_TYPE_BIND_NONE = ('err_bind_none', "cmdb中无分组、无订单，不知道绑哪个应用。")
    ERR_TYPE_BIND_MODULE = ('err_bind_module', "cmdb中的res_code，在spider找不到对应的模块名，无法绑定。")
    ERR_TYPE_BIND_SUITE = ('err_bind_suite', "spider中没找到对应的环境套映射信息。")
    ERR_TYPE_GROUP = ('err_group', "spider缺少分组数据")

    def __init__(self, err_name, err_desc):
        self.err_name = err_name
        self.err_desc = err_desc


def connect_scm_mysql():
    """获取scm的mysql数据库连接"""
    conn = pymysql.connect(host=DATABASES['IP'],
                           port=DATABASES['PORT'],
                           database=DATABASES['DB'],
                           charset=DATABASES['CHARSET'],
                           user=DATABASES['USER'],
                           password=DATABASES['PASSWORD'])

    return conn

def connect_cmdb_mysql():
    """获取cmdb的mysql数据库连接"""
    conn = pymysql.connect(host=CMDBDATABASES['IP'],
                           port=CMDBDATABASES['PORT'],
                           database=CMDBDATABASES['DB'],
                           charset=CMDBDATABASES['CHARSET'],
                           user=CMDBDATABASES['USER'],
                           password=CMDBDATABASES['PASSWORD'])

    return conn


def __get_batch_code(conn):
    """获取spider中的分组信息"""
    spider_new_code_sql = '''select @DATE_STR:=DATE_FORMAT(sysdate(), '%y%m%d') as date_str,
       CONCAT('C', @DATE_STR, IF(v.max_code is null, '0001', LPAD(SUBSTRING(v.max_code, -4)+1, 4, 0))) as new_code
    from (
        select max(check_batch_code) as max_code
        from node_check_batch
        where check_batch_code like CONCAT('C', DATE_FORMAT(sysdate(), '%y%m%d'), '%')
             ) v, (select @DATE_STR:='')T1
    where 1=1;'''

    with conn.cursor(cursor=DictCursor) as cursor:
        cursor.execute(spider_new_code_sql)
        result = cursor.fetchone()

    return result


def __ins_node_check_batch(conn, ins_dict, ins_time):
    ins_sql = """INSERT INTO node_check_batch(
    create_user,
    create_time,
    update_user,
    update_time,
    stamp,
    provider_code,
    check_batch_code,
    node_check_status,
    node_check_desc,
    node_check_req_ct,
    node_check_ins_ct
    )VALUES(
    %s, %s, %s, %s, %s,
    %s, %s, %s, %s, %s, %s
    );"""

    if not ins_time:
        ins_time = datetime.now()

    ins_tuple = (CREATE_USER, ins_time, UPDATE_USER, ins_time, CREATE_STAMP,
                 ins_dict.get('provider_code'),
                 ins_dict.get('check_batch_code'),
                 ins_dict.get('node_check_status'),
                 ins_dict.get('node_check_desc'),
                 ins_dict.get('node_check_req_ct'),
                 None)

    with conn.cursor(cursor=DictCursor) as cursor:
        ins_count = cursor.execute(ins_sql, ins_tuple)
        # 单条插入，只要成功肯定是1笔。
        log.info(">>>> 插入节点对账主表信息：{}笔。".format(ins_count))
        last_rowid = cursor.lastrowid

    return last_rowid


def __upd_node_check_batch(conn, upd_dict, upd_time):
    upd_sql = '''UPDATE
    node_check_batch
    SET
        update_user = %s,
        update_time = %s,
        node_check_status = %s,
        node_check_ins_ct = %s,
        node_ins_count = %s
    WHERE id = %s;'''

    if not upd_time:
        upd_time = datetime.now()

    upd_tuple = (UPDATE_USER, upd_time,
                 upd_dict.get('node_check_status'),
                 upd_dict.get('node_check_ins_ct'),
                 upd_dict.get('node_ins_count'),
                 upd_dict.get('pid'))

    with conn.cursor(cursor=DictCursor) as cursor:
        # 基于主键ID更新，如果成果只会有1笔。
        upd_count = cursor.execute(upd_sql, upd_tuple)
        log.info(">>>> 插入节点对账主表信息：{}笔。".format(upd_count))

    return upd_count


def __parse_ins_node_tuple_list(batch_id, batch_code, res_results, ins_time):
    ins_tuple_list = []

    if res_results:
        if not ins_time:
            ins_time = datetime.now()

    node_dict_list = sorted(res_results, key=lambda x: (x['IpAddress'] if x['IpAddress'] else json.dumps(x)))

    for node_dict in node_dict_list:
        # mianbao返回的字典映射
        node_ip = node_dict.get('IpAddress')
        node_cpu = node_dict.get('NumCpu')
        node_mem = node_dict.get('MemorySize')
        node_name = node_dict.get('Name')
        node_zone = node_dict.get('Prz')
        node_json = json.dumps(node_dict, sort_keys=True)

        ins_node_tuple = (
            CREATE_USER,
            ins_time,
            UPDATE_USER,
            ins_time,
            CREATE_STAMP,
            batch_id,
            batch_code,
            node_ip,
            node_cpu,
            node_mem,
            node_name,
            node_zone,
            node_json,
        )
        ins_tuple_list.append(ins_node_tuple)

    return ins_tuple_list


def __ins_node_check_detail(conn, batch_id, batch_code, res_results, ins_time):
    if res_results:
        ins_node_tuple_list = __parse_ins_node_tuple_list(batch_id, batch_code, res_results, ins_time)

        ins_sql = """
            INSERT INTO node_check_batch_detail(
                create_user, 
                create_time, 
                update_user, 
                update_time, 
                stamp,
                check_batch_id,
                check_batch_code,
                node_ip,
                node_cpu,
                node_mem,
                node_name,
                node_zone,
                node_json
                )VALUES(
                    %s, %s, %s, %s, %s, 
                    %s, %s, %s, %s, %s, %s, %s, %s
                );"""
        with conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_sql, ins_node_tuple_list)
        log.info(">>>> ins_node_count: {}".format(ins_count))
    return ins_count


def __get_api_url(provider_enum):
    mb_ip = MIANBAO_API['ip']
    mb_port = MIANBAO_API['port']
    mb_url = MIANBAO_API['url']
    mb_ver = MIANBAO_API['version']
    mb_limit = MIANBAO_API['limit']
    provider_code = provider_enum.provider_code
    provider_name = provider_enum.provider_name
    provider_url = provider_enum.provider_url

    url = 'http://{}:{}/{}/{}/{}?limit={}'.format(mb_ip, mb_port, mb_url, mb_ver, provider_url, mb_limit)
    log.info(">>>> 获取{}({})的接口地址：{}".format(provider_name, provider_code, url))
    return url


def __ins_node_by_provider(provider_enum):
    """ 根据供应商「批量」插入节点信息 zt@2022-07-25 """
    curr_time = datetime.now()
    # 1、数据解析
    req_url = __get_api_url(provider_enum)
    req_st = datetime.now()
    res = requests.get(req_url)
    req_et = datetime.now()
    req_timedelta = req_et - req_st
    req_cost_time = req_timedelta.seconds + req_timedelta.microseconds / 1000000

    json_dict = res.json()
    # 如果无返回，则打印错误日志后继续
    if not json_dict:
        raise ValueError("供应商「{}」接口返回异常：返回数据空（结构为空，非无数据）！".format(provider_enum.provider_code))

    res_count = json_dict.get('count')
    res_previous = json_dict.get('previous')
    res_next = json_dict.get('next')
    res_results = json_dict.get('results')

    # 打印耗时
    res_msg = ">>>> {}({})节点数据「耗时：{}秒」：count:{}, previous:{}, next:{}".format(provider_enum.provider_name,
                                                                              provider_enum.provider_code,
                                                                              req_cost_time,
                                                                              res_count,
                                                                              res_previous,
                                                                              res_next)
    if not res_results:
        res_msg = "{}, results（无数据）:{}".format(res_msg, res_results)
    log.info(res_msg)
    # 2、数据插入
    ins_st = datetime.now()
    scm_conn = connect_scm_mysql()
    try:
        # 2-1、获取批次号
        batch_code_dict = __get_batch_code(scm_conn)
        if not batch_code_dict:
            raise ValueError("供应商「{}」获取节点对账批次号异常：没有获取到返回字典！".format(provider_enum.provider_code))
        new_code = batch_code_dict.get('new_code')
        if not new_code:
            raise ValueError("供应商「{}」获取节点对账批次号异常：没有获取到新的批次号！".format(provider_enum.provider_code))
        log.info(">>>> new_code = {}".format(new_code))
        # 2-2、插入主表数据
        ins_dict = {
            "provider_code": provider_enum.provider_code,
            "check_batch_code": new_code,
            "node_check_status": 2,
            "node_check_desc": res_msg,
            "node_check_req_ct": req_cost_time,
        }

        pid = __ins_node_check_batch(scm_conn, ins_dict, curr_time)
        log.info(">>>> pid: {}".format(pid))
        # 2-3、批量插入子表数据
        ins_count = __ins_node_check_detail(scm_conn, pid, new_code, res_results, curr_time)

        ins_et = datetime.now()
        ins_timedelta = ins_et - ins_st
        ins_cost_time = ins_timedelta.seconds + ins_timedelta.microseconds / 1000000
        # 2-4、更新完成状态
        upd_time = datetime.now()
        upd_dict = {
            "pid": pid,
            "node_check_status": 1,
            "node_check_ins_ct": ins_cost_time,
            "node_ins_count": ins_count,
        }
        __upd_node_check_batch(scm_conn, upd_dict, upd_time)
        # 2-5、关闭数据库连接
        scm_conn.commit()
    except Exception as e:
        log.error(">>>> 数据插入异常：{}".format(e))
        scm_conn.rollback()

    scm_conn.close()


def __sync_all_node():
    """ 同步所有节点信息至DevOps平台 zt@2022-07-25 """
    sync_all_node_st = datetime.now()
    for provider_enum in ProviderEnum:
        __ins_node_by_provider(provider_enum)

    sync_all_node_et = datetime.now()
    sync_all_node_timedelta = sync_all_node_et - sync_all_node_st
    sync_all_node_cost_time = sync_all_node_timedelta.seconds + sync_all_node_timedelta.microseconds / 1000000
    log.info(">>>> 同步所有节点总耗时：{}秒。".format(sync_all_node_cost_time))


def __get_esxi_base_info() -> Dict[str, Any]:
    hardware_info_map = __get_hardware_info()
    num_cpu_map = __get_num_cpu()
    cpu_max_map = __get_cpu_max()
    mem_max_map = __get_mem_max()
    for k, v in hardware_info_map.items():
        num_cpu_val = num_cpu_map[k]["num_cpu_val"]
        cpu_max_val = cpu_max_map[k]["cpu_max_val"]
        mem_max_val = mem_max_map[k]["mem_max_val"]
        mem_max_int = 0
        if mem_max_val:
            mem_max_int = int(float(mem_max_val) / 1024 // 16 + 1) * 16

        v["num_cpu_val"] = num_cpu_val
        v["cpu_max_val"] = cpu_max_val
        v["mem_max_int"] = mem_max_int

    # log.info(">>>> hardware_info_map = {}".format(json.dumps(hardware_info_map, ensure_ascii=False, indent=4)))
    return hardware_info_map

def __get_esxi_more_info(param_st_time, param_et_time) -> Dict[str, Any]:
    esxi_more_info = __get_cpu_usage(param_st_time, param_et_time)
    mem_usage_map = __get_mem_usage(param_st_time, param_et_time)
    disk_read_map = __get_disk_read(param_st_time, param_et_time)
    disk_write_map = __get_disk_write(param_st_time, param_et_time)
    for k, v in esxi_more_info.items():
        mem_usage = mem_usage_map.get(k).get("memory_usage")
        disk_read = disk_read_map.get(k).get("disk_read_average")
        disk_write = disk_write_map.get(k).get("disk_write_average")
        v["mem_usage"] = mem_usage
        v["disk_read"] = disk_read
        v["disk_write"] = disk_write

    # log.info(">>>> esxi_more_info = {}".format(json.dumps(cpu_usage_map, ensure_ascii=False, indent=4)))
    return esxi_more_info

def __get_hardware_info():
    params = {
        "query": "vmware_host_hardware_info",
    }
    rst = get_prometheus_query_data(params)
    host_map = {}
    for item in rst:
        metric = item.get("metric")
        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")
        hardware_model = metric.get("hardware_model")
        hardware_cpu_model = metric.get("hardware_cpu_model")
        host_map[host_name] = {
            "dc_name": dc_name,
            "env": env,
            "hardware_model": hardware_model,
            "hardware_cpu_model": hardware_cpu_model,
        }
    # log.info(">>>> host_map = {}".format(json.dumps(host_map, ensure_ascii=False, indent=4)))
    return host_map

def __get_num_cpu():
    params = {
        "query": "vmware_host_num_cpu",
    }
    rst = get_prometheus_query_data(params)
    host_map = {}
    for item in rst:
        metric = item.get("metric")
        value = item.get("value")
        num_cpu_val = value[1]

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        host_map[host_name] = {
            "dc_name": dc_name,
            "env": env,
            "num_cpu_val": num_cpu_val,
        }
    # log.info(">>>> host_map = {}".format(json.dumps(host_map, ensure_ascii=False, indent=4)))
    return host_map

def __get_cpu_max():
    params = {
        "query": "vmware_host_cpu_max",
    }
    rst = get_prometheus_query_data(params)
    host_map = {}
    for item in rst:
        metric = item.get("metric")
        value = item.get("value")
        cpu_max_val = value[1]

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        host_map[host_name] = {
            "dc_name": dc_name,
            "env": env,
            "cpu_max_val": cpu_max_val,
        }
    # log.info(">>>> host_map = {}".format(json.dumps(host_map, ensure_ascii=False, indent=4)))
    return host_map

def __get_mem_max():
    params = {
        "query": "vmware_host_memory_max",
    }
    rst = get_prometheus_query_data(params)
    host_map = {}
    for item in rst:
        metric = item.get("metric")
        value = item.get("value")
        mem_max_val = value[1]

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        host_map[host_name] = {
            "dc_name": dc_name,
            "env": env,
            "mem_max_val": mem_max_val,
        }
    # log.info(">>>> host_map = {}".format(json.dumps(host_map, ensure_ascii=False, indent=4)))
    return host_map

def __get_cpu_usage(param_st_time : datetime, param_et_time : datetime) -> Dict[str, Dict[str, Any]]:
    metric_suffix = "cpu_usage"

    query_key = f"{METRIC_PREFIX}_{metric_suffix}"
    rst = get_prometheus_query_range_data(query_key, param_st_time, param_et_time)

    # values
    values_map = {}
    for item in rst:
        metric = item.get("metric")
        values = item.get("values")

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        for value in values:
            value_seconds = value[0]
            value_str = value[1]
            dt = datetime.fromtimestamp(value_seconds)
            dt_str = dt.strftime(FMT_TIME_STR)

            values_key = f"{dc_name}-{host_name}-{value_seconds}"

            values_map[values_key] = {
                "host_name": host_name,
                "dc_name": dc_name,
                "env": env,
                "dt_seconds": value_seconds,
                "dt_str": dt_str,
                metric_suffix: value_str,
            }
    # log.info(">>>> values_map = {}".format(json.dumps(values_map, ensure_ascii=False, indent=4)))
    return values_map

def __get_mem_usage(param_st_time : datetime, param_et_time : datetime) -> Dict[str, Dict[str, Any]]:
    metric_suffix = "memory_usage"

    query_key = f"{METRIC_PREFIX}_{metric_suffix}"
    rst = get_prometheus_query_range_data(query_key, param_st_time, param_et_time)

    # values
    values_map = {}
    for item in rst:
        metric = item.get("metric")
        values = item.get("values")

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        for value in values:
            value_seconds = value[0]
            value_str = value[1]
            dt = datetime.fromtimestamp(value_seconds)
            dt_str = dt.strftime(FMT_TIME_STR)

            values_key = f"{dc_name}-{host_name}-{value_seconds}"

            values_map[values_key] = {
                "host_name": host_name,
                "dc_name": dc_name,
                "env": env,
                "dt_seconds": value_seconds,
                "dt_str": dt_str,
                metric_suffix: value_str,
            }
    # log.info(">>>> values_map = {}".format(json.dumps(values_map, ensure_ascii=False, indent=4)))
    return values_map

def __get_disk_read(param_st_time : datetime, param_et_time : datetime) -> Dict[str, Dict[str, Any]]:
    metric_suffix = "disk_read_average"

    query_key = f"{METRIC_PREFIX}_{metric_suffix}"
    rst = get_prometheus_query_range_data(query_key, param_st_time, param_et_time)

    # values
    values_map = {}
    for item in rst:
        metric = item.get("metric")
        values = item.get("values")

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        for value in values:
            value_seconds = value[0]
            value_str = value[1]
            dt = datetime.fromtimestamp(value_seconds)
            dt_str = dt.strftime(FMT_TIME_STR)

            values_key = f"{dc_name}-{host_name}-{value_seconds}"

            values_map[values_key] = {
                "host_name": host_name,
                "dc_name": dc_name,
                "env": env,
                "dt_seconds": value_seconds,
                "dt_str": dt_str,
                metric_suffix: value_str,
            }
    # log.info(">>>> values_map = {}".format(json.dumps(values_map, ensure_ascii=False, indent=4)))
    return values_map

def __get_disk_write(param_st_time : datetime, param_et_time : datetime) -> Dict[str, Dict[str, Any]]:
    metric_suffix = "disk_write_average"

    query_key = f"{METRIC_PREFIX}_{metric_suffix}"
    rst = get_prometheus_query_range_data(query_key, param_st_time, param_et_time)

    # values
    values_map = {}
    for item in rst:
        metric = item.get("metric")
        values = item.get("values")

        host_name = metric.get("host_name")
        dc_name = metric.get("dc_name")
        env = metric.get("env")

        for value in values:
            value_seconds = value[0]
            value_str = value[1]
            dt = datetime.fromtimestamp(value_seconds)
            dt_str = dt.strftime(FMT_TIME_STR)

            values_key = f"{dc_name}-{host_name}-{value_seconds}"

            values_map[values_key] = {
                "host_name": host_name,
                "dc_name": dc_name,
                "env": env,
                "dt_seconds": value_seconds,
                "dt_str": dt_str,
                metric_suffix: value_str,
            }
    # log.info(">>>> values_map = {}".format(json.dumps(values_map, ensure_ascii=False, indent=4)))
    return values_map

def get_prometheus_query_data(params):
    req_url = f"{DOMAIN_NAME}/api/v1/query"
    res = requests.get(req_url, params, timeout=5)
    res.raise_for_status()
    json_dict = res.json()
    if json_dict:
        json_status = json_dict.get("status")
        if json_status and json_status == "success":
            json_data = json_dict.get("data")
            if json_data:
                json_rst_type = json_data.get("resultType")
                log.info(f">>>> data.resultType = {json_rst_type}")
                json_rst_list = json_data.get("result")
                return json_rst_list

def get_prometheus_query_range_data(query, st_time, et_time, step="10m"):
    utc_st_time = st_time.astimezone(timezone.utc)
    utc_et_time = et_time.astimezone(timezone.utc)
    utc_st_time_str = utc_st_time.strftime(FMT_UTC_STR)
    utc_et_time_str = utc_et_time.strftime(FMT_UTC_STR)
    params = {
        "query": query,
        "start": utc_st_time_str,
        "end": utc_et_time_str,
        "step": step
    }

    req_url = f"{DOMAIN_NAME}/api/v1/query_range"
    res = requests.get(req_url, params, timeout=5)
    res.raise_for_status()
    json_dict = res.json()
    if json_dict:
        json_status = json_dict.get("status")
        if json_status and json_status == "success":
            json_data = json_dict.get("data")
            if json_data:
                json_rst_type = json_data.get("resultType")
                log.info(f">>>> data.resultType({query}) = {json_rst_type}")
                json_rst_list = json_data.get("result")
                return json_rst_list

def insert_esxi_batch_record(req_st_time, req_et_time):
    cmdb_conn = connect_cmdb_mysql()

    try:
        with cmdb_conn.cursor(cursor=DictCursor) as cursor:
            batch_sql = """
            SELECT 
                v.current_time_batch AS time_batch, 
                COALESCE(MAX(batch.batch_number) + 1, 1) AS next_batch_number 
            FROM ( 
                SELECT CONCAT( 
                    DATE_FORMAT(NOW(), '%Y%m%d%H'), 
                    LPAD(MINUTE(NOW()) - MINUTE(NOW()) % 10, 2, '0'), '00') AS current_time_batch 
            ) AS v 
                LEFT JOIN ztst_cmdb_esxi_usage_batch batch ON batch.time_batch = v.current_time_batch 
            GROUP BY v.current_time_batch
            """
            cursor.execute(batch_sql)
            rt = cursor.fetchone()
            time_batch = rt["time_batch"]
            batch_number = rt["next_batch_number"]


            ins_sql = """INSERT INTO ztst_cmdb_esxi_usage_batch (
                create_user,
                create_time,
                update_user,
                update_time,
                stamp,
                time_batch,
                batch_number,
                req_st_time,
                req_et_time
                ) VALUES (
                %s, %s, %s, %s, %s,
                %s, %s, %s, %s
                );"""

            ins_time = datetime.now()
            cursor.execute(ins_sql, (
                CREATE_USER,  # create_user
                ins_time,  # create_time
                UPDATE_USER,  # update_user
                ins_time,  # update_time
                CREATE_STAMP,  # stamp
                time_batch,  # time_batch
                batch_number,  # batch_number
                req_st_time,  # req_st_time
                req_et_time  # req_et_time
            ))

            cmdb_conn.commit()
            record_id = cursor.lastrowid

            return {
                'batch_id': record_id,
                'time_batch': time_batch,
                'batch_number': batch_number,
                'ins_time': ins_time
            }
    except Exception as e:
        log.error(">>>> 数据插入异常：{}".format(e))
        cmdb_conn.rollback()
    finally:
        cmdb_conn.close()

def insert_esxi_info_record(time_batch, batch_number, esxi_base_map:Dict[str, Any], ins_time=None):

    if not ins_time:
        ins_time = datetime.now()

    ins_sql = """INSERT INTO ztst_cmdb_esxi_usage_info (
        create_user,
        create_time,
        update_user,
        update_time,
        stamp,
        time_batch,
        batch_number,
        host_name,
        dc_name,
        dc_env,
        hardware_model,
        cpu_model,
        num_cpu,
        cpu_max,
        mem_max,
        esxi_info_desc
        ) VALUES (
        %s, %s, %s, %s, %s,
        %s, %s, 
        %s, %s, %s, %s, %s, %s, %s, %s, %s
        );"""
    ins_tuple_list = []
    for key, value in sorted(esxi_base_map.items()):
        host_name = key
        dc_name = value.get("dc_name")
        dc_env = value.get("env")
        hardware_model = value.get("hardware_model")
        cpu_model = value.get("hardware_cpu_model")
        num_cpu = int(value.get("num_cpu_val"))
        cpu_max = int(value.get("cpu_max_val"))
        mem_max = int(value.get("mem_max_int"))

        ins_tuple = (CREATE_USER, ins_time, UPDATE_USER, ins_time, CREATE_STAMP,
                     time_batch,
                     batch_number,
                     host_name,
                     dc_name,
                     dc_env,
                     hardware_model,
                     cpu_model,
                     num_cpu,
                     cpu_max,
                     mem_max,
                     None)
        ins_tuple_list.append(ins_tuple)

    cmdb_conn = connect_cmdb_mysql()

    try:
        with cmdb_conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_sql, ins_tuple_list)
            # log.info(">>>> ins_esxi_info_count: {}".format(ins_count))
            cmdb_conn.commit()
        return ins_count
    except Exception as e:
        log.error(">>>> 数据插入异常：{}".format(e))
        cmdb_conn.rollback()
    finally:
        cmdb_conn.close()

def insert_esxi_detail_record(time_batch, batch_number, esxi_detail_map:Dict[str, Any], ins_time=None):

    if not ins_time:
        ins_time = datetime.now()

    ins_sql = """INSERT INTO ztst_cmdb_esxi_usage_detail (
        create_user,
        create_time,
        update_user,
        update_time,
        stamp,
        time_batch,
        batch_number,
        host_name,
        dc_name,
        dc_env,
        collect_time,
        cpu_usage,
        mem_usage,
        disk_read,
        disk_write,
        esxi_detail_desc
        ) VALUES (
        %s, %s, %s, %s, %s,
        %s, %s, 
        %s, %s, %s, %s, %s, %s, %s, %s, %s
        );"""

    ins_tuple_list = []
    for key, value in sorted(esxi_detail_map.items()):
        host_name = value.get("host_name")
        dc_name = value.get("dc_name")
        dc_env = value.get("env")
        dt_seconds = value.get("dt_seconds")
        collect_time = datetime.fromtimestamp(dt_seconds)
        cpu_usage = int(value.get("cpu_usage"))
        mem_usage = round(int(value.get("mem_usage")) / 1024, 2)
        disk_read = int(value.get("disk_read"))
        disk_write = int(value.get("disk_write"))

        ins_tuple = (CREATE_USER, ins_time, UPDATE_USER, ins_time, CREATE_STAMP,
                     time_batch,
                     batch_number,
                     host_name,
                     dc_name,
                     dc_env,
                     collect_time,
                     cpu_usage,
                     mem_usage,
                     disk_read,
                     disk_write,
                     None)
        ins_tuple_list.append(ins_tuple)

    cmdb_conn = connect_cmdb_mysql()

    try:
        with cmdb_conn.cursor(cursor=DictCursor) as cursor:
            ins_count = cursor.executemany(ins_sql, ins_tuple_list)
            # log.info(">>>> ins_esxi_info_count: {}".format(ins_count))
            cmdb_conn.commit()
        return ins_count
    except Exception as e:
        log.error(">>>> 数据插入异常：{}".format(e))
        cmdb_conn.rollback()
    finally:
        cmdb_conn.close()


def __write_to_db(req_st_time, req_et_time, esxi_base_map, esxi_more_map):

    batch_map = insert_esxi_batch_record(req_st_time, req_et_time)

    if batch_map:
        batch_id = batch_map.get("batch_id")
        time_batch = batch_map.get("time_batch")
        batch_number = batch_map.get("batch_number")
        ins_time = batch_map.get("ins_time")

        log.info(f">>>> batch_id = {batch_id}")
        log.info(f">>>> time_batch = {time_batch}")
        log.info(f">>>> batch_number = {batch_number}")

        ins_esxi_info_count = insert_esxi_info_record(time_batch, batch_number, esxi_base_map, ins_time)
        log.info(f">>>> ins_esxi_info_count = {ins_esxi_info_count}")

        ins_esxi_detail_count = insert_esxi_detail_record(time_batch, batch_number, esxi_more_map, ins_time)
        log.info(f">>>> ins_esxi_detail_count = {ins_esxi_detail_count}")

def __main(start_str, end_str, curr_time):
    """「节点对账」主方法 zt@2022-07-21"""
    req_st_time, req_et_time = parse_start_end_time(start_str, end_str, curr_time)
    log.info(f">>>> req_st_time = {req_st_time.strftime(FMT_TIME_STR)}")
    log.info(f">>>> req_et_time = {req_et_time.strftime(FMT_TIME_STR)}")

    esxi_base_map = __get_esxi_base_info()
    esxi_more_map = __get_esxi_more_info(req_st_time, req_et_time)
    log.info(">>>> esxi_base_map = {}".format(json.dumps(esxi_base_map, ensure_ascii=False, indent=4)))
    log.info(">>>> esxi_more_map = {}".format(json.dumps(esxi_more_map, ensure_ascii=False, indent=4)))

    __write_to_db(req_st_time, req_et_time, esxi_base_map, esxi_more_map)


if __name__ == '__main__':
    """主入口，先判断参数"""
    curr_t = datetime.now()

    try:
        req_start_str = None
        req_end_str = None
        if len(sys.argv) > 1:
            req_start_str = sys.argv[1]
        if len(sys.argv) > 2:
            req_end_str = sys.argv[2]
        if len(sys.argv) > 3:
            raise Exception(">>>> 参数异常: 只接受起始日期。")
        log.info(f">>>> req_start_str = {req_start_str}")
        log.info(f">>>> req_end_str = {req_end_str}")
        req_sync_type_enum = SyncTypeEnum.SYNC_TYPE_ALL
        __main(req_start_str, req_end_str, curr_t)
        log.info("==== ！！！！执行成功！！！！ ====")
        exit(0)
    except ValueError as err:
        log.warning("==== ！！！！执行出错！！！！ ====")
        err_msg = ">>>> 执行出错(ValueError) {}".format(err)
        log.error(err_msg)
        exit(1)
    except Exception as ex:
        log.error("==== ！！！！执行异常！！！！ ====")
        err_msg = ">>>> 执行异常(Exception) {}".format(ex)
        log.error(err_msg)
        raise ex
        exit(1)
    finally:
        e_time = datetime.now()
        cost_time = get_cost_time(curr_t, e_time)
        log.info("== 执行最终耗时（秒）：{}".format(cost_time))
        log.info("====================")
        log.info("====================\n\n")
