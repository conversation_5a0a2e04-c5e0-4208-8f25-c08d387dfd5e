import datetime
import os
import sys

# 设置项目目录 解决依赖问题
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(PROJECT_DIR)
from app_mgt.app_apidoc.model.app_mgt_apidoc_scan_log import AppMgtApidocScanLog

from app_mgt.app_apidoc.bo.apidoc_bo import ApidocBO
import json
from app_mgt.app_apidoc.bo.app_mgt_apidoc_info_temp_bo import AppMgtApidocInfoTempBO
from app_mgt.app_apidoc.bo.app_mgt_apidoc_param_info_temp_bo import AppMgtApidocParamInfoTempBO
from app_mgt.app_apidoc.util.do_utils import to_formatted_table
from app_mgt.app_apidoc.scan_api_doc_base import ScanApiDocBase
import logging
from utils.test_env.test_env_lib import step_desc
from app_mgt.app_apidoc.service.app_mgt_api_service import persist_api_info, save_scan_log


# @Time : 20230407
# <AUTHOR> yang.zhan
class ApiDocParse(ScanApiDocBase):

    def __init__(self, app_name, iter_branch, jenkins_work_dir, pipeline_id):
        super(ApiDocParse, self).__init__(app_name, iter_branch, jenkins_work_dir, pipeline_id)

    @step_desc("解析apidoc")
    def parse_api_doc(self):
        error_apidoc_list = []
        apidoc_list = []
        dumplicate_apidoc_list = []
        apidoc_file_path = self.get_api_doc_file_path()
        api_list = list()
        api_bo_list = list([ApidocBO])
        scan_log = AppMgtApidocScanLog(
            module_name=self.module_name,
            pipeline_id=self.pipeline_id,
            phase='解析apidoc',
            status='success',
            create_time=datetime.datetime.now(),
            create_user='pa'
        )
        try:
            with open(apidoc_file_path, 'r') as apidoc_file_content:
                api_bo_list.clear()
                apidocs = json.load(apidoc_file_content)
                logging.info("生成的apidoc数量:{}".format(len(apidocs)))
                # logging.info("生成的apidoc：{}".format(apidocs))
                for apidoc in apidocs:
                    try:
                        apidoc_list.append(apidoc["url"])
                        if str(apidoc["type"]).upper() in ('POST', 'GET', 'PUT', 'DELETE'):
                            api_type = "http"
                            api_method = str(apidoc["type"]).upper()
                        elif str(apidoc["type"]).upper() == 'DUBBO':
                            api_type: str = "dubbo"
                            # 截取方法名
                            api_method: str = apidoc["url"].split('.')[-1]
                            api_method: str = api_method[:api_method.index("(")]
                        elif str(apidoc["type"]).upper() == 'METHOD':
                            api_type: str = "method"
                            # 截取方法名，处理格式与dubbo一致
                            api_method: str = apidoc["url"].split('.')[-1]
                            api_method: str = api_method[:api_method.index("(")]
                        elif str(apidoc["type"]).upper() == 'MQ':
                            api_type: str = 'mq'
                            api_method: str = apidoc["name"]
                        else:
                            logging.error("不支持的接口类型:{},该接口路径是:{},所在文件是:{}".format(apidoc["type"],
                                                                                                     apidoc["url"],
                                                                                                     apidoc[
                                                                                                         "filename"]))
                            error_apidoc_list.append(apidoc["url"])
                            continue
                        if not api_type:
                            logging.error("接口类型不支持为空,该接口路径是:{},所在文件是:{}".format(apidoc["url"],
                                                                                                    apidoc["filename"]))
                            error_apidoc_list.append(apidoc["url"])
                            continue
                        # 解析request_param参数
                        api_method_signature_value, method_param_define_list, request_examples = self.request_param_handler(
                            api_method,
                            api_type,
                            apidoc)
                        # logging.info('解析request_param参数如下:')
                        # print(to_formatted_table(method_param_define_list))
                        # 解析response_param参数
                        response_param_define_list, response_examples = self.response_param_handler(api_method,
                                                                                                    api_type,
                                                                                                    api_method_signature_value,
                                                                                                    apidoc)
                        # logging.info('解析response_param参数如下:')
                        # print(to_formatted_table(response_param_define_list))
                        method_param_define_list.extend(response_param_define_list)
                        if api_type[0].upper() in 'HTTP':
                            # 解析header参数
                            header_param_define_list = self.http_header_param_handler(api_method, api_type,
                                                                                      api_method_signature_value,
                                                                                      apidoc)
                            # logging.info('解析header_param参数如下:')
                            # print(to_formatted_table(header_param_define_list))
                            method_param_define_list.extend(header_param_define_list)

                        apidoc_info_temp_bo = AppMgtApidocInfoTempBO(module_name=self.module_name,
                                                                  iter_branch=self.iter_branch,
                                                                  api_type=api_type,
                                                                  api_path=apidoc["url"],
                                                                  api_name=apidoc["name"],
                                                                  api_method=api_method,
                                                                  api_method_signature=api_method_signature_value,
                                                                  api_request_sample=request_examples,
                                                                  api_response_sample=response_examples,
                                                                  create_user="pa",
                                                                  api_group=apidoc.get('group'),
                                                                  api_description=apidoc.get('description'),
                                                                  api_doc_module_name=apidoc.get("module_name")
                                                                  )
                        if str(apidoc["type"]).upper() == 'DUBBO':
                            apidoc_info_temp_bo.api_path = ".".join(apidoc["url"].split(".")[:-1])
                        elif str(apidoc["type"]).upper() == 'METHOD':
                            apidoc_info_temp_bo.api_path = ".".join(apidoc["url"].split(".")[:-1])
                        # logging.info('解析得到api信息如下:')
                        # print(to_formatted_table(list([api_info_temp_bo])))
                        exist = False
                        for api_bo in api_bo_list:
                            if api_bo.apidoc_info_temp_bo == apidoc_info_temp_bo:
                                exist = True
                        if apidoc_info_temp_bo is not None and not exist:
                            api_bo_list.append(ApidocBO(apidoc_info_temp_bo, method_param_define_list))
                            api_list.append(apidoc_info_temp_bo)
                        else:
                            dumplicate_apidoc_list.append(apidoc["url"])
                    except Exception as ex:
                        error_apidoc_list.append(apidoc["url"])
                        logging.error("apidoc解析异常,该接口路径是:{},所在文件是:{}".format(apidoc["url"],
                                                                                            apidoc["filename"]))
            logging.error("解析出错的api 列表长度:{}".format(len(error_apidoc_list)))
            if len(error_apidoc_list) > 0:
                scan_log.status = 'error'
            logging.info("解析到的api 列表长度:{}".format(len(apidoc_list)))
            logging.info("解析成功到但是重复的api 列表长度:{}".format(len(dumplicate_apidoc_list)))

            logging.info("解析成功到的需要入库的api 列表长度:{}".format(len(api_bo_list)))

            persist_api_info(self.module_name, self.iter_branch, api_bo_list, self.pipeline_id)
            # 解析api文档 完成
            logging.info("parse api doc end for %s %s", self.module_name, self.iter_branch)
        except Exception as ex:
            scan_log.status = 'error'
            logging.error("==== ！！！！api doc执行异常！！！！ ====")
            logging.error(ex)
            exit(0)
        finally:
            save_scan_log(scan_log)
        return api_list

    def request_param_handler(self, api_method, api_type, apidoc):
        parameters_define_dict = dict()
        method_param_define_list = list()
        if 'parameter' not in apidoc or 'fields' not in apidoc['parameter']:
            logging.debug("不存在parameter参数")
            return apidoc["url"], method_param_define_list, None
        parameters = apidoc['parameter']['fields']
        examples = None
        if 'examples' in apidoc['parameter']:
            examples = apidoc['parameter']['examples'][0].get("content", None)
        for key in parameters:
            method_param_name = key
            method_param_type = None
            for param_define in parameters[method_param_name]:
                if param_define['field'] == method_param_name:
                    method_param_type = param_define['type']
                param_allowed_values = param_define.get('allowedValues', None)
                if param_allowed_values:
                    param_allowed_values = str(param_allowed_values)
                if 'defaultValue' in param_define:
                    default_value = param_define['defaultValue']
                else:
                    default_value = None
                method_param_define_list.append(AppMgtApidocParamInfoTempBO(param_type='request_param',
                                                                            param_name=param_define['field'],
                                                                            param_desc=param_define[
                                                                             'description'],
                                                                            param_value_type=param_define.get('type',
                                                                                                           "Object"),
                                                                            param_size=param_define.get('size', None),
                                                                            param_default_value=default_value,
                                                                            param_allowed_values=param_allowed_values,
                                                                            optional=param_define['optional'],
                                                                            create_user="pa"
                                                                            ))

            parameters_define_dict[method_param_name] = method_param_type
        api_method_signature_value = apidoc["url"]
        # 处理api_method_signature
        for param in parameters_define_dict:
            if param and parameters_define_dict[param]:
                api_method_signature_value = api_method_signature_value.replace(param,
                                                                                parameters_define_dict[param])
        return api_method_signature_value, method_param_define_list, examples

    def response_param_handler(self, api_method, api_type, api_method_signature_value, apidoc):
        method_param_define_list = list()
        if 'success' not in apidoc or 'fields' not in apidoc['success']:
            logging.debug("不存在响应参数")
            return list(), None
        logging.debug("正在解析响应参数")
        examples = None
        if 'examples' in apidoc['success']:
            examples = apidoc['success']['examples'][0].get("content", None)
        parameters = apidoc['success']['fields']
        for key in parameters:
            method_param_name = key
            for param_define in parameters[method_param_name]:
                param_allowed_values = param_define.get('allowedValues', None)
                if param_allowed_values:
                    param_allowed_values = str(param_allowed_values)
                if 'defaultValue' in param_define:
                    default_value = param_define['defaultValue']
                else:
                    default_value = None
                temp_bo = AppMgtApidocParamInfoTempBO(param_type='response_param',
                                                      param_name=param_define['field'],
                                                      param_desc=param_define[
                                                       'description'],
                                                      param_value_type=param_define.get('type', "Object"),
                                                      param_size=param_define.get('size', None),
                                                      param_allowed_values=param_allowed_values,
                                                      param_default_value=default_value,
                                                      optional=param_define['optional'],
                                                      create_user="pa"
                                                      )
                if temp_bo not in method_param_define_list:
                    method_param_define_list.append(temp_bo)
        return method_param_define_list, examples

    def http_header_param_handler(self, api_method, api_type, api_method_signature_value, apidoc):
        http_header_param_define_list = list()
        if 'header' not in apidoc:
            logging.debug("不存在header参数")
            return list()
        logging.debug("正在解析header参数")
        parameters = apidoc['header']['fields']
        for key in parameters:
            method_param_name = key
            for param_define in parameters[method_param_name]:
                if param_define['field'] != method_param_name:
                    if 'defaultValue' in param_define:
                        default_value = param_define['defaultValue']
                    else:
                        default_value = None
                    http_header_param_define_list.append(AppMgtApidocParamInfoTempBO(param_type='header_param',
                                                                                     param_name=(param_define[
                                                                                                  'field'] + ""),
                                                                                     param_desc=param_define[
                                                                                      'description'],
                                                                                     param_default_value=default_value,
                                                                                     param_value_type=param_define[
                                                                                      'type'],
                                                                                     optional=param_define['optional'],
                                                                                     create_user="pa"
                                                                                     ))
        method_param_define_list_result = list(set(http_header_param_define_list))
        return method_param_define_list_result


if __name__ == '__main__':
    params = sys.argv[1:]
    logging.info("参数列表 {}".format(params))
    logging.info("job_name is {}".format(params[1]))
    logging.info("jenkins_work_dir is {}".format(params[2]))
    param_list = params[1].split("_")
    module_name = param_list[2]
    branch_name = param_list[1]
    pipeline_id = params[1]
    logging.info("module_name is {}".format(module_name))
    logging.info("branch_name is {}".format(branch_name))
    apiDocParse = ApiDocParse(module_name, branch_name, params[2], pipeline_id)
    apiDocParse.check_api_doc_file()
    try:
        apis = apiDocParse.parse_api_doc()
        logging.info("本次扫描接口数量:{}".format(len(apis)))
    except Exception as ex:
        logging.error(ex)
    finally:
        logging.info("排错文档链接:{url}".format(
            url="http://dms.intelnal.howbuy.com/pages/viewpage.action?pageId=70488092"))
