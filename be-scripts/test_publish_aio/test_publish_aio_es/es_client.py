import requests
import json
import logging
from typing import Dict, List, Optional
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

log = logging.getLogger(__name__)

class EsClient:
    """Elasticsearch客户端封装（多线程优化版本）"""
    
    def __init__(self, es_url: str, timeout: int = 30, max_retries: int = 3, pool_connections: int = 10, pool_maxsize: int = 20):
        self.es_url = es_url.rstrip('/')
        self.timeout = timeout
        
        # 创建会话并配置连接池
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        
        # 配置重试策略
        retry_strategy = Retry(
            total=max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "PUT", "DELETE", "OPTIONS", "TRACE", "POST"],
            backoff_factor=1
        )
        
        # 配置HTTP适配器，支持连接池
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=pool_connections,
            pool_maxsize=pool_maxsize
        )
        
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
        
        log.info(f"ES客户端初始化完成，连接池大小: {pool_maxsize}, 连接数: {pool_connections}")
    
    def health_check(self) -> Dict:
        """ES健康检查"""
        try:
            url = f"{self.es_url}/_cluster/health"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            log.error(f"ES健康检查失败: {e}")
            raise
    
    def get_indices(self) -> List[str]:
        """获取所有索引列表"""
        try:
            url = f"{self.es_url}/_cat/indices?format=json"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            indices = response.json()
            return [idx['index'] for idx in indices]
        except Exception as e:
            log.error(f"获取索引列表失败: {e}")
            raise
    
    def delete_all_indices(self) -> Dict:
        """删除所有索引"""
        try:
            url = f"{self.es_url}/_all"
            response = self.session.delete(url, timeout=self.timeout)
            response.raise_for_status()
            return {'acknowledged': True}
        except Exception as e:
            log.error(f"删除索引失败: {e}")
            raise
    
    def verify_snapshot(self, snapshot_name: str) -> Dict:
        """验证快照状态"""
        try:
            url = f"{self.es_url}/_snapshot/my_s3_backup/{snapshot_name}/_status"
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            log.error(f"验证快照失败: {e}")
            raise
    
    def restore_snapshot(self, snapshot_name: str, indices: str = "*") -> Dict:
        """恢复快照"""
        try:
            url = f"{self.es_url}/_snapshot/my_s3_backup/{snapshot_name}/_restore"
            data = {
                "indices": indices,
                "ignore_unavailable": True,
                "include_global_state": True
            }
            response = self.session.post(url, json=data, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            log.error(f"恢复快照失败: {e}")
            raise
    
    def create_index(self, index_name: str, settings: Dict) -> Dict:
        """创建索引"""
        response = None
        try:
            url = f"{self.es_url}/{index_name}"
            response = self.session.put(url, json=settings, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            # 检查是否为资源已存在异常
            error_msg = str(e).lower()
            is_already_exists = (
                "resource_already_exists_exception" in error_msg or
                "already exists" in error_msg or
                (response and response.status_code == 400 and "already exists" in error_msg)
            )
            
            if is_already_exists:
                log.warning(f"索引 {index_name} 已存在，跳过创建: {e}")
                return {"acknowledged": True, "shards_acknowledged": True, "index": index_name}
            
            log.error(f"创建索引失败: {e}")
            raise
    
    def verify_repository(self, repo_name: str = "my_s3_backup") -> Dict:
        """验证快照仓库是否存在"""
        try:
            url = f"{self.es_url}/_snapshot/{repo_name}/_verify"
            response = self.session.post(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            log.error(f"验证快照仓库失败: {e}")
            raise
    
    def create_repository(self, repo_name: str = "my_s3_backup", repo_config: Dict = None) -> Dict:
        """创建快照仓库"""
        if repo_config is None:
            repo_config = {
                "type": "s3",
                "settings": {
                    "bucket": "elastic"
                }
            }
        
        try:
            url = f"{self.es_url}/_snapshot/{repo_name}"
            response = self.session.put(url, json=repo_config, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            log.error(f"创建快照仓库失败: {e}")
            raise
    
    def ensure_repository_exists(self, repo_name: str = "my_s3_backup") -> bool:
        """确保快照仓库存在，如果不存在则创建"""
        try:
            # 先尝试验证仓库是否存在
            self.verify_repository(repo_name)
            log.info(f"快照仓库 {repo_name} 已存在")
            return True
        except Exception as e:
            # 如果验证失败，尝试创建仓库
            log.warning(f"快照仓库 {repo_name} 不存在或验证失败: {e}，尝试创建")
            try:
                result = self.create_repository(repo_name)
                log.info(f"快照仓库 {repo_name} 创建成功: {result}")
                return True
            except Exception as create_error:
                log.error(f"创建快照仓库 {repo_name} 失败: {create_error}")
                raise

    def put_mapping(self, index_name: str, mapping: Dict) -> Dict:
        """设置索引映射"""
        try:
            url = f"{self.es_url}/{index_name}/_mapping/_doc"
            response = self.session.put(url, json=mapping, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            log.error(f"设置索引映射失败: {e}")
            raise