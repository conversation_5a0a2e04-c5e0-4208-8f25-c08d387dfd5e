import glob
import json
import os
import re
import subprocess
from settings import logger as log
from typing import List


class EsScriptExecutor:
    """ES脚本执行器"""

    def __init__(self, workspace: str):
        self.workspace = workspace
        self.script_base_dir = os.path.join(workspace, 'es_scripts')

    def clone_repository(self, repo_name: str, repo_url: str) -> str:
        """克隆脚本仓库"""
        try:
            script_dir = os.path.join(self.script_base_dir, repo_name)

            if os.path.exists(script_dir):
                # 如果目录已存在，执行git pull
                subprocess.run(['git', 'pull'], cwd=script_dir, check=True)
            else:
                # 克隆仓库
                os.makedirs(os.path.dirname(script_dir), exist_ok=True)
                subprocess.run(['git', 'clone', repo_url, script_dir], check=True)

            return script_dir
        except Exception as e:
            log.error(f"克隆脚本仓库失败: {e}")
            raise

    def scan_script_files(self, repo_path: str) -> List[str]:
        """扫描脚本文件"""
        try:

            # 查找所有.txt文件
            pattern = os.path.join(repo_path, '**', '*.txt')
            script_files = glob.glob(pattern, recursive=True)

            # 按文件名排序，确保执行顺序
            script_files.sort()

            log.info(f"发现 {len(script_files)} 个脚本文件")
            return script_files
        except Exception as e:
            log.error(f"扫描脚本文件失败: {e}")
            raise

    def execute_script_file(self, es_client, script_file: str):
        """执行单个脚本文件"""
        try:

            log.info(f"执行脚本文件: {os.path.basename(script_file)}")

            with open(script_file, 'r', encoding='utf-8') as f:
                script_content = f.read()

            # 解析脚本内容
            commands = self._parse_script_content(script_content)

            # 执行每个命令
            failed_commands = []
            for command in commands:
                try:
                    self._execute_es_command(es_client, command)
                except Exception as e:
                    # 检查是否为资源已存在异常
                    error_msg = str(e).lower()
                    is_already_exists = (
                        "resource_already_exists_exception" in error_msg or
                        "already exists" in error_msg or
                        "400 client error" in error_msg
                    )
                    
                    if is_already_exists:
                        log.warning(f"资源已存在，跳过命令: {command['method']} {command['path']}, 警告: {e}")
                        continue
                    
                    log.error(f"执行命令失败: {command['method']} {command['path']}, 错误: {e}")
                    failed_commands.append({"command": command, "error": str(e)})
                    # 对于非资源已存在的错误，记录但继续执行其他命令
                    continue
            
            # 如果有失败的命令，记录警告但不中断整个流程
            if failed_commands:
                log.warning(f"脚本文件 {script_file} 中有 {len(failed_commands)} 个命令执行失败，但已跳过继续执行")
                for failed in failed_commands:
                    log.warning(f"失败命令: {failed['command']['method']} {failed['command']['path']}, 错误: {failed['error']}")
        
        except Exception as e:
            log.error(f"执行脚本文件失败: {script_file}, 错误: {e}")
            # 改为警告而不是抛出异常，避免中断整个流程
            log.warning(f"脚本文件执行遇到问题，但继续执行后续脚本: {e}")

    def _parse_script_content(self, script_content: str) -> list:
        """解析脚本内容"""
        commands = []
        lines = script_content.strip().split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            # 跳过空行和注释
            if not line or line.startswith('#'):
                i += 1
                continue

            # 解析HTTP方法和路径
            match = re.match(r'^(PUT|POST|GET|DELETE)\s+(.+)$', line)
            if match:
                method = match.group(1)
                path = match.group(2)

                # 查找请求体
                body = None
                i += 1
                if i < len(lines) and lines[i].strip().startswith('{'):
                    # 解析JSON请求体
                    json_lines = []
                    brace_count = 0

                    while i < len(lines):
                        json_line = lines[i].strip()
                        json_lines.append(json_line)

                        brace_count += json_line.count('{') - json_line.count('}')
                        i += 1

                        if brace_count == 0:
                            break

                    try:
                        body = json.loads('\n'.join(json_lines))
                    except json.JSONDecodeError as e:
                        log.warning(f"解析JSON失败: {e}")
                        body = None

                commands.append({
                    'method': method,
                    'path': path,
                    'body': body
                })
            else:
                i += 1

        return commands

    def _execute_es_command(self, es_client, command: dict):
        """执行ES命令"""
        method = command['method']
        path = command['path']
        body = command['body']

        if method == 'PUT':
            if '/_mapping' in path:
                # 设置映射
                index_name = path.split('/')[0]
                result = es_client.put_mapping(index_name, body)
            else:
                # 创建索引
                result = es_client.create_index(path, body or {})
        else:
            # 其他方法的通用处理
            url = f"{es_client.es_url}/{path.lstrip('/')}"

            try:
                if method == 'POST':
                    response = es_client.session.post(url, json=body, timeout=es_client.timeout)
                elif method == 'GET':
                    response = es_client.session.get(url, timeout=es_client.timeout)
                elif method == 'DELETE':
                    response = es_client.session.delete(url, timeout=es_client.timeout)
                else:
                    raise Exception(f"不支持的HTTP方法: {method}")

                response.raise_for_status()
                result = response.json()
            except Exception as e:
                # 检查是否为资源已存在异常
                if "resource_already_exists_exception" in str(e).lower() or "already exists" in str(e).lower():
                    log.warning(f"资源已存在，跳过操作: {method} {path}, 警告: {e}")
                    return {"acknowledged": True, "message": "Resource already exists, skipped"}
                raise

        log.info(f"命令执行成功: {method} {path}")
        return result
