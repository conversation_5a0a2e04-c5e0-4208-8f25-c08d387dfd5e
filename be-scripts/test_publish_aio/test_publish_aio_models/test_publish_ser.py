import importlib
import os
import json
import datetime
import sys

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from dao.connect.mysql import DBConnectionManager
from settings import logger, PIPELINE_TEST_PUBLISH, TEST_PUBLISH_AIO, CONFIG_DIR_FLAG, MOCK_SYSTEM_INTERFACE
from test_pipeline.test_pipeline_models.test_pipeline_models import TestMgtPublishApp
from test_publish_aio.test_suite_init_utils import BaseBo
from test_publish_aio.test_suite_init_constants import TypeEnum
from test_mgt.test_mgt_ser import get_db_info_by_app
from dao.get.mysql import db_mgt_bind_view, app_info
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from job.jenkins.test_data_dev.models import BizBaseInfo
from sqlalchemy import text
from dao.connect.oracle_sqlalchemy import DBConnectionManagerForSqlalchemyOracle


class AppObject(BaseBo):
    def __init__(self, app_info_dict, *args, **kwargs):
        """
        应用信息对象
        :param app_info_dict:
        :param args:
        :param kwargs:
        """
        self.module_name = app_info_dict["module_name"]
        self.lib_repo_path = self.__get_lib_repo(app_info_dict["lib_repo"])
        self.platform_type = app_info_dict["platform_type"]
        self.zeus_type = app_info_dict["zeus_type"]
        self.package_name = app_info_dict["package_name"]
        self.third_party_middleware = app_info_dict["third_party_middleware"]
        self.package_type = app_info_dict["package_type"]
        self.package_full = app_info_dict["package_full"]
        self.platform_type = app_info_dict["platform_type"]
        self.gitlab_path = app_info_dict["gitlab_path"]
        self.node_docker = app_info_dict["node_docker"]
        self.selected_br_name = app_info_dict["selected_br_name"]
        self.selected_zeus_type = app_info_dict["selected_zeus_type"]
        self.online_br_name = app_info_dict["online_br_name"]
        self.online_zeus_type = app_info_dict["online_zeus_type"]
        self.archive_br_name = app_info_dict["archive_br_name"]
        self.archive_zeus_type = app_info_dict["archive_zeus_type"]
        self.deploy_br_name = app_info_dict["deploy_br_name"]
        self.deploy_zeus_type = app_info_dict["deploy_zeus_type"]
        self.archive_or_online = app_info_dict["archive_or_online"]
        self.container_name = self.__get_container_name(app_info_dict["container_name"])
        self.tomcat_name = self.__get_tomcat_name(app_info_dict["tomcat_name"])

    def __get_container_name(self, container_name):
        """
        容器名为 None时候 用 模块名称代替
        :param container_name:
        :return:
        """
        if container_name is None:
            return self.module_name
        else:
            return container_name

    def __get_tomcat_name(self, tomcat_name):
        if tomcat_name is None:
            return self.module_name
        else:
            return tomcat_name

    @staticmethod
    def __get_lib_repo(lib_repo):
        """
        拼接出来完整的gitlab 仓库url
        :param lib_repo:
        :return:
        """
        if lib_repo is None:
            return lib_repo
        return os.path.join(PIPELINE_TEST_PUBLISH["lib_git_url"], lib_repo + '.git')


class SuiteObject(BaseBo):
    def __init__(self, app_info_dict, *args, **kwargs):
        """
        环境信息
        :param app_info_dict:
        :param args:
        :param kwargs:
        """

        self.active_node_ip = app_info_dict["active_node_ip"]
        self.suite_code = app_info_dict["suite_code"]


class AppSuiteObject(AppObject, SuiteObject, BaseBo):
    def __init__(self, app_info_dict, *args, **kwargs):
        """
        应用环境套信息
        :param app_info_dict:
        :param args:
        :param kwargs:
        """
        self.deploy_type = app_info_dict["deploy_type"]
        super().__init__(app_info_dict)
        super(AppObject, self).__init__(app_info_dict)

        self.deploy_path = self._get_deploy_path(app_info_dict["deploy_path"])
        if self.deploy_path and not self.deploy_path.endswith("/"):
            self.deploy_path = self.deploy_path + "/"
        self.start_level = app_info_dict["start_level"]
        self.deploy_config_path = self._get_deploy_config_path(app_info_dict["config_path"])
        if self.deploy_config_path and not self.deploy_config_path.endswith("/"):
            self.deploy_config_path = self.deploy_config_path + "/"
        self.lib_cache_repo_path = self.get_lib_cache_repo_path(app_info_dict["lib_repo"],
                                                                app_info_dict["suite_code"],
                                                                app_info_dict["lib_push_dir"])
        if self.lib_cache_repo_path and not self.lib_cache_repo_path.endswith("/"):
            self.lib_cache_repo_path = self.lib_cache_repo_path + "/"
        self.src_cache_config_path = self.get_src_cache_config_path(app_info_dict["suite_code"],
                                                                    app_info_dict["lib_repo_config_path"])
        if self.src_cache_config_path and not self.src_cache_config_path.endswith("/"):
            self.src_cache_config_path = self.src_cache_config_path + "/"
        self.selected_src_cache_config_path = self.get_selected_src_cache_config_path(app_info_dict["suite_code"])
        self.target_cache_config_path = self.get_target_cache_config_path(app_info_dict["suite_code"])
        if self.target_cache_config_path and not self.target_cache_config_path.endswith("/"):
            self.target_cache_config_path = self.target_cache_config_path + "/"

        # 20211026 获取并替换启动脚本 数据 帅
        self.workspace = app_info_dict["workspace"]
        self.script_name = app_info_dict["script_name"]
        self.start_script_template_url = PIPELINE_TEST_PUBLISH["start_script_template_url"]
        start_script_repos_name = self.start_script_template_url.split("/")[-1].replace(".git", "")
        # logger.info(self.script_name)
        if self.script_name is None:
            if self.package_type == "jar":
                self.script_name = PIPELINE_TEST_PUBLISH['def_script_name']
            else:
                self.script_name = PIPELINE_TEST_PUBLISH['def_war_script_name']

        # if self.package_type == "war":
        #     self.start_file_local_path = os.path.join(self.workspace, start_script_repos_name, self.module_name,
        #                                               "catalina.sh")
        # else:
        self.start_file_local_path = os.path.join(self.workspace, start_script_repos_name, self.module_name,
                                                  self.script_name)

        self.start_script_path_dir, self.start_script_path = self._get_start_script_path(app_info_dict["script_path"],
                                                                                         self.script_name)

        # 添加 svn_container_path zt@2020-11-18
        # self.svn_container_path = app_info_dict["svn_container_path"]
        # 添加 target_base_path zt@2020-12-24
        self.target_base_path = app_info_dict["target_base_path"]
        # 添加lib_repo_config_path zt@2020-12-31
        self.lib_repo_config_path = app_info_dict["lib_repo_config_path"]
        # 添加 nacos 的相关字段 zt@2021-04-26
        self.nacos_namespace = app_info_dict["nacos_namespace"]
        self.nacos_conf_name = app_info_dict["nacos_conf_name"]
        # 添加agent 的信息 20220803 by fwm
        self.agent_info_list = app_info_dict.get("agent_info_list")
        # 添加应用db 信息 20230228 by fwm
        self.db_info_list = app_info_dict.get("db_info_list")
        # 添加ES相关信息 20241220 by fwm
        self.es_info_list = app_info_dict.get("es_info_list", [])
        self.has_es_dependency = len(self.es_info_list) > 0

    def __str__(self):
        """
        返回易查看的文件字符串 zt@2020-10-19
        :return:
        """
        return json.dumps(self, default=lambda o: o.__dict__, sort_keys=True, indent=4)

    def get_es_modules(self):
        """获取关联的ES模块列表"""
        return [es_info['es_module_name'] for es_info in self.es_info_list]

    def get_es_urls(self):
        """获取ES服务URL列表"""
        return [es_info['es_url'] for es_info in self.es_info_list]

    def get_es_git_repos(self):
        """获取ES脚本仓库列表"""
        return [es_info['es_lib_git_repo'] for es_info in self.es_info_list]

    def get_lib_cache_repo_path(self, lib_repo, suite_code, lib_push_dir):
        """
        拼接出来本地临时工作空间
        :param lib_repo:
        :return:
        """
        if lib_repo is None:
            return lib_repo
        if lib_push_dir is None:
            return os.path.join(TEST_PUBLISH_AIO["root_path"], suite_code, "app", self.module_name)
        else:
            return os.path.join(TEST_PUBLISH_AIO["root_path"], suite_code, "app", lib_push_dir)

    def get_target_cache_config_path(self, suite_code):
        """
        拼接出来本地临时工作空间

        :return:
        """

        return os.path.join(TEST_PUBLISH_AIO["nfs_root_path"], suite_code, "config", self.module_name)

    def get_src_cache_config_path(self, suite_code, lib_repo_config_path):
        """获取配置缓存目录 zt@2020-12-25"""
        src_cache_config_path = None
        if self.third_party_middleware == 1 and self.zeus_type == 0:
            return src_cache_config_path

        # 虚机+非宙斯 test_app_resource zt@2020-12-25
        if lib_repo_config_path is None:
            if (self.deploy_type == 1 and self.zeus_type != 1) or self.package_type in ['static', 'remote']:
                src_cache_config_path = os.path.join(TEST_PUBLISH_AIO["root_path"],
                                                     suite_code,
                                                     TEST_PUBLISH_AIO["test_app_resource_gitlab_name"],
                                                     self.module_name)
            else:
                src_cache_config_path = os.path.join(TEST_PUBLISH_AIO["nfs_root_path"],
                                                     suite_code,
                                                     TEST_PUBLISH_AIO["app_resource_gitlab_name"],
                                                     "ec",
                                                     self.module_name,
                                                     CONFIG_DIR_FLAG["bs-prod"])
        else:
            # 兼容一个应用支持部署虚机和部署容器（在不同的环境）两种方式，另外test_app_resource仓库下的目录都是用module_name命名
            if (self.deploy_type == 1 and self.zeus_type != 1) or self.package_type in ['static', 'remote']:
                src_cache_config_path = os.path.join(TEST_PUBLISH_AIO["root_path"],
                                                     suite_code,
                                                     TEST_PUBLISH_AIO["test_app_resource_gitlab_name"],
                                                     self.module_name)
            else:
                src_cache_config_path = os.path.join(TEST_PUBLISH_AIO["nfs_root_path"],
                                                     suite_code,
                                                     TEST_PUBLISH_AIO["app_resource_gitlab_name"],
                                                     lib_repo_config_path)

        return src_cache_config_path

    def get_selected_src_cache_config_path(self, suite_code):
        src_cache_config_path = None
        if self.selected_zeus_type == 1:
            src_cache_config_path = os.path.join(TEST_PUBLISH_AIO["root_path"],
                                                 suite_code,
                                                 TEST_PUBLISH_AIO["test_app_resource_gitlab_name"],
                                                 self.module_name)
            src_cache_config_path = src_cache_config_path + '/'
        return src_cache_config_path

    def _get_start_script_path(self, script_path, script_name):
        if script_path is None:
            if self.deploy_type == 1:
                if self.package_type == "jar":
                    return os.path.join(PIPELINE_TEST_PUBLISH["def_vm_jar_lib_path"],
                                        self.tomcat_name), \
                        os.path.join(PIPELINE_TEST_PUBLISH["def_vm_jar_lib_path"],
                                     self.tomcat_name,
                                     script_name)
                else:
                    return os.path.join(PIPELINE_TEST_PUBLISH["def_vm_tomcat_lib_path"],
                                        self.tomcat_name, 'bin'), \
                        os.path.join(PIPELINE_TEST_PUBLISH["def_vm_tomcat_lib_path"],
                                     self.tomcat_name,
                                     'bin',
                                     script_name)
            elif self.deploy_type == 2:
                if self.package_type == "jar":
                    return os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                        self.node_docker,
                                        self.container_name,
                                        self.container_name,
                                        'bin'), \
                        os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                     self.node_docker,
                                     self.container_name,
                                     self.container_name,
                                     'bin',
                                     script_name)
                else:
                    return os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                        self.node_docker,
                                        self.container_name), \
                        os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                     self.node_docker,
                                     self.container_name,
                                     script_name)
            return "部署类型不明确", "部署类型不明确"
        else:
            return script_path, os.path.join(script_path, script_name)

    def _get_deploy_config_path(self, deploy_config_path):

        if deploy_config_path is None:
            if self.deploy_type == 1:
                if self.package_type == "jar":
                    return os.path.join(PIPELINE_TEST_PUBLISH["def_vm_jar_conf_path"],
                                        self.tomcat_name)
                else:
                    return os.path.join(PIPELINE_TEST_PUBLISH['def_vm_tomcat_conf_path'],
                                        self.tomcat_name)
            elif self.deploy_type == 2:
                if self.package_type == "jar":
                    return os.path.join(PIPELINE_TEST_PUBLISH['config_map_conf_path'],
                                        self.node_docker,
                                        self.module_name, "conf")
                if self.package_type == "static" or self.package_type == "remote":
                    return os.path.join(PIPELINE_TEST_PUBLISH['config_map_conf_path'],
                                        self.node_docker,
                                        self.container_name,
                                        self.module_name)
                else:
                    # 适配占大部分比例的已经接入宙斯的应用 20220125 by fwm
                    return os.path.join(PIPELINE_TEST_PUBLISH['config_map_conf_path'],
                                        self.node_docker,
                                        self.module_name)
        return deploy_config_path.format(self.node_docker)

    def _get_deploy_path(self, deploy_path):
        """
        测试环境 部署路径
        deploy_type 1 代表虚拟机 deploy_type 2 代表容器
        :param deploy_path:
        :return:
        """
        if deploy_path is None:
            if self.deploy_type == 1:
                if self.package_type == "jar":
                    return os.path.join(PIPELINE_TEST_PUBLISH["def_vm_jar_lib_path"], self.tomcat_name)
                else:
                    return os.path.join(PIPELINE_TEST_PUBLISH["def_vm_tomcat_lib_path"],
                                        self.tomcat_name,
                                        'webapps',
                                        self.package_name.replace(".war", "").replace(".tar", ""))
            elif self.deploy_type == 2:
                if self.package_type == "jar":
                    return os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                        self.node_docker,
                                        self.container_name,
                                        self.module_name,
                                        'lib')
                elif self.package_type == "static" or self.package_type == "remote":
                    return os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                        self.node_docker,
                                        self.container_name,
                                        self.module_name)
                else:
                    return os.path.join(PIPELINE_TEST_PUBLISH["mirror_factory_root_path"],
                                        self.node_docker,
                                        self.container_name,
                                        self.module_name)

        return deploy_path.format(self.node_docker)


class EnvInitObject(AppSuiteObject):
    pass


class TestPublishObject(AppSuiteObject):
    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        self.start_script_template_src = self.__config_info["start_script_template_src"]
        super().__init__(app_info_dict)

    def get_lib_cache_repo_path(self, lib_repo, suite_code, lib_push_dir):
        """
        拼接出来本地临时工作空间
        :param lib_repo:
        :return:
        """
        if lib_repo is None:
            return lib_repo
        if lib_push_dir is None:
            return os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"], self.module_name, self.br_name)
        else:
            return os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"],
                                self.module_name,
                                self.br_name,
                                "/".join(lib_push_dir.split("/")[1:]))

    def get_src_cache_config_path(self, suite_code, lib_repo_config_path):
        """
        拼接出来本地临时工作空间

        :return:
        """
        if self.third_party_middleware == 1 and self.zeus_type == 0:
            return None
        if self.deploy_type == 1 or self.package_type == "static" or self.package_type == "remote" or self.zeus_type == 1:
            return os.path.join(TEST_PUBLISH_AIO["root_path"],
                                suite_code,
                                TEST_PUBLISH_AIO["test_app_resource_gitlab_name"],
                                self.module_name)
        else:
            return os.path.join(TEST_PUBLISH_AIO["root_path"],
                                suite_code,
                                TEST_PUBLISH_AIO["app_resource_gitlab_name"],
                                "ec",
                                self.module_name,
                                CONFIG_DIR_FLAG["bs-prod"])
        return None

    def get_target_cache_config_path(self, suite_code):
        """
        拼接出来本地临时工作空间

        :return:
        """

        return os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"], self.module_name, "config", self.br_name)


class PublishTestRepos:
    def __init__(self, env_suite, workspace, cache_data_code=None, type_enum=TypeEnum.INIT, br_name=None,
                 app_dict_list=None):
        """
        环境初始化数据仓库
        :param env_suite: 环境套
        :param workspace: 工作空间
        :param cache_data_code: 缓存数据id
        business_name "env_init" or "test_publish"
        app_list: .list
        br_name: .str
        """

        self.cache_data_code = cache_data_code
        self.env_suite = env_suite
        self.workspace = workspace
        self.type_enum = type_enum

        self.br_name = br_name
        logger.info('----------app_dict_list:{}----------'.format(app_dict_list))
        self.app_node_data = self.get_app_node_data(cache_data_code, env_suite, app_dict_list)

    app_suite_object = {"env_init": EnvInitObject,
                        "test_publish": TestPublishObject}

    def __iter__(self):
        return self.app_node_data

    def get_app_node_data(self, cache_data_code, env_suite, app_dict_list):
        """
        获取环境初始化需要的 应用和节点信息
        1、没有缓存编码，从数据库中取数据
        2、有缓存编码，从文件中取数据
        :param app_dict_list:
        :param cache_data_code:
        :param env_suite:
        :return:
        """
        if cache_data_code is None:
            app_node_data = self.get_app_node_data_db(env_suite, app_dict_list)
            self.cache_data_code = self.data_ser(app_node_data)
        else:
            app_node_data = self.data_de_ser(cache_data_code)
        for row in app_node_data:
            row["br_name"] = self.br_name
            # 原来的字典找，修改成动态生成 zt@2020-11-23
            m = importlib.import_module("be-scripts.test_publish_aio.test_publish_aio_models.test_publish_ser")
            # logger.info("m={}".format(m))
            cls_name = self.type_enum.cls_name
            # logger.info("cls_name={}".format(cls_name))
            clz = getattr(m, cls_name)
            # logger.info("clz={}".format(clz))
            tp_bo = clz(row)
            # app_suite_object = self.app_suite_object[self.business_name](row)
            yield tp_bo

    def data_de_ser(self, cache_data_code):
        """
        取出来 持久化后的数据
        :param cache_data_code:
        :return:
        """
        with open(os.path.join(self.workspace, cache_data_code) + ".json", "r", encoding='utf-8') as f:
            app_node_data = json.loads(f.read())
        return app_node_data

    def data_ser(self, test_publish_data_list):
        """
        将数据持久化到文件
        :param test_publish_data_list:
        :return:
        """
        cache_data_code = "publish_test_data_{}".format(datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f"))
        # logger.info(json.dumps(test_publish_data_list))
        if not os.path.isdir(self.workspace):
            os.system("mkdir -p {}".format(self.workspace))
        with open(os.path.join(self.workspace, cache_data_code + ".json"), "w", encoding='utf-8') as f:
            f.write(json.dumps(test_publish_data_list))
        return cache_data_code

    def __get_publish_app_list(self, suite_code):
        sql = """
                select t.module_name from 
                test_mgt_publish_app t
                where t.suite_code = '{}'
            """.format(suite_code)
        get_publish_app_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            get_publish_app_list.append(row.get("module_name"))
        return get_publish_app_list

    def __get_test_publish_data(self, env_suite, app_list, app_dict_list=None):
        """
        获取应用的归档版本及其他部署信息
        从app_br_cache中获取应用的归档分支和在线分支  20230803 by fwm
        """
        sql = """
            SELECT DISTINCT
                m.module_name,
            IF
                ( pdi.nacos_namespace IS NULL, m.module_name, pdi.nacos_namespace ) AS nacos_namespace,
                pdi.nacos_conf_name,
            IF
                ( eb.container_name IS NULL, pdi.container_name, eb.container_name ) AS container_name,
            IF
                ( eb.tomcat_name IS NULL, pdi.tomcat_name, eb.tomcat_name ) AS tomcat_name,
                m.lib_repo,
                pdi.lib_repo_config_path,
                a.platform_type,
            IF
                ( eb.deploy_path IS NULL, pdi.deploy_path, eb.deploy_path ) AS deploy_path,
                m.zeus_type,
                m.is_agent,
                a.third_party_middleware,
                b.package_type,
                b.package_full,
                pdi.lib_push_dir,
                pdi.start_level,
            IF
                ( b.package_name IS NULL, m.module_name, b.package_name ) AS package_name,
                CONCAT( a.git_url, a.git_path ) AS gitlab_path,
            IF
                ( eb.config_path IS NULL, pdi.config_path, eb.config_path ) AS config_path,
            IF
                ( eb.script_path IS NULL, pdi.script_path, eb.script_path ) AS script_path,
            IF
                ( eb.script_name IS NULL, pdi.script_name, eb.script_name ) AS script_name,
            IF
                ( eb.target_base_path IS NULL, pdi.target_base_path, eb.target_base_path ) AS target_base_path,
            IF
                ( n.node_status = 0, n.node_ip, NULL ) AS active_node_ip,
                n.node_ip,
                s.suite_code,
                eb.node_docker,
                eb.deploy_type,
                n.node_status,
                pp.archive_br_name AS archive_br_name,
                pp.online_br_name AS online_br_name,
                vv.br_end_date 
            FROM
                app_mgt_app_module m
                LEFT JOIN app_mgt_app_build b ON b.module_name = m.module_name
                LEFT JOIN publish_deploy_info pdi ON pdi.module_name = m.module_name
                LEFT JOIN app_mgt_app_info a ON a.id = m.app_id
                LEFT JOIN env_mgt_node_bind eb ON eb.module_name = m.module_name
                LEFT JOIN env_mgt_node n ON n.id = eb.node_id
                LEFT JOIN env_mgt_suite s ON s.id = eb.suite_id
                LEFT JOIN (
                                SELECT c.app_module_name AS module_name,
                   c.archive_br AS archive_br_name,
                   c.online_br AS online_br_name
                    FROM app_br_cache c
                    INNER JOIN (
                            SELECT
                                         MAX(CONCAT(time_batch, batch_number)) AS max_batch_num
                            FROM app_br_cache
                            )v_max ON v_max.max_batch_num = CONCAT(c.time_batch, c.batch_number)
                ) pp ON pp.module_name = m.module_name
                left join (
                    select imii.br_end_date, imii.br_name, imia.appName as module_name from iter_mgt_iter_info imii
                    inner join iter_mgt_iter_app_info imia on imii.pipeline_id = imia.pipeline_id
                ) vv on vv.br_name = pp.archive_br_name and vv.module_name = m.module_name
            WHERE
                m.need_online = 1 
                AND m.need_check = 1 
                AND s.suite_code = '{}'""".format(env_suite)

        if app_list is None:
            pass
        else:
            sql = sql + ' and m.module_name in ("{}")'.format('","'.join(app_list))
        logger.info(sql)
        test_publish_data_list = self.__get_test_publish_data_list_from_db(sql, env_suite, app_dict_list)
        return test_publish_data_list

    def __get_test_publish_data_list_from_db(self, sql, env_suite, app_dict_list=None) -> list:
        test_publish_data_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            results = db.cur.fetchall()
            
            # 获取所有模块名称用于ES信息查询
            app_list = [row.get("module_name") for row in results]
            
            # 查询ES信息
            es_info_dict = self.__get_es_info_dict(app_list, env_suite)
            logger.info(f"es_info_dict===={es_info_dict}")
            
        for row in results:
            # app_suite_object = AppSuiteObject(row)
            row["workspace"] = self.workspace
            branch = None
            for app in app_dict_list:
                if app.get(row.get("module_name")):
                    branch = app.get(row.get("module_name"))
            row["agent_info_list"] = self.__get_agent_info_list(row.get("module_name"), env_suite, branch=branch)
            row["db_info_list"] = self.__get_app_db_info(row.get("module_name"), env_suite)
            # 添加ES信息
            module_name = row.get("module_name")
            row["es_info_list"] = es_info_dict.get(module_name, [])
            test_publish_data_list.append(row)
        return test_publish_data_list

    def get_app_online_br(self, module_name):
        sql = '''
            select c.online_br
            from app_br_cache c
            inner join(
                select time_batch,
                       max(batch_number) as max_batch_num
                from app_br_cache
                where time_batch = (select max(time_batch) from app_br_cache)
            )v_max on (v_max.time_batch, v_max.max_batch_num) = (c.time_batch, c.batch_number)
            WHERE c.app_module_name = '{}';
        '''.format(module_name)

        with DBConnectionManager() as db:
            db.cur.execute(sql)
            for row in db.cur.fetchall():
                return row.get("online_br")

    def __get_agent_info_list(self, module_name, suite_code, branch=None):
        # 找到所在环境和应用绑定的可用agent
        app_env_available_agent_list = self.__get_app_env_available_agent_list(module_name, suite_code)
        # 平台觉得应用必须要部署的agent
        platform_need_deploy_agent_list = self.get_platform_need_deploy_agent_list(module_name, suite_code)
        if not branch:
            get_publish_app_list = self.__get_publish_app_list(suite_code)
            if module_name in get_publish_app_list:
                branch = self.get_app_online_br(module_name)
            else:
                branch = app_info.get_latest_archive_version(module_name)
        if branch == 'archive_br':
            branch = app_info.get_latest_archive_version(module_name)
        elif branch == 'online_br':
            branch = self.get_app_online_br(module_name)
        platform_need_deploy_shard_agent_list = self.get_platform_need_deploy_shard_agent_list(module_name, suite_code,
                                                                                               branch)
        logger.info("app_env_available_agent_list:{}".format(app_env_available_agent_list))
        logger.info("platform_need_deploy_agent_list:{}".format(platform_need_deploy_agent_list))
        logger.info("get_platform_need_deploy_shard_agent_list:{}".format(platform_need_deploy_shard_agent_list))
        # 取并集
        app_env_available_agent_list.extend(platform_need_deploy_shard_agent_list)
        app_env_available_agent_list.extend(platform_need_deploy_agent_list)
        new_app_env_available_agent_list = []
        new_app_env_available_agent_list_key = []
        for agent in app_env_available_agent_list:
            if agent.get('agent_module_name') not in new_app_env_available_agent_list_key:
                new_app_env_available_agent_list.append(agent)
                new_app_env_available_agent_list_key.append(agent.get('agent_module_name'))
        return new_app_env_available_agent_list

    def __get_app_env_available_agent_list(self, module_name, suite_code):
        sql = """SELECT ab.agent_module_name ,a.agent_deploy_path,a.agent_cmd, c.package_name,i.third_party_middleware,pio.agent_deploy_ip, am.arex_app_id
                 FROM agent_mgt_env_app_bind ab 
                 JOIN agent_mgt_agent_info a  ON ab.agent_module_name = a.agent_module_name
                 LEFT JOIN app_mgt_app_build c ON ab.agent_module_name = c.module_name
                 LEFT JOIN app_mgt_app_module m ON ab.agent_module_name = m.module_name
                 LEFT JOIN app_mgt_app_info i ON m.app_id = i.id
                 LEFT JOIN agent_mgt_publish_info pio ON pio.agent_module_name = a.agent_module_name AND pio.suite_code = ab.suite_code
                 LEFT JOIN agent_mgt_arex_app_map am ON ab.app_module_name = am.module_name
                 WHERE ab.app_module_name = '{}' AND ab.suite_code = '{}' AND ab.is_use = '1';
            """.format(module_name, suite_code)
        agent_info_list = []
        # logger.info(sql)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            agent_info_list.append(
                {"agent_module_name": row.get("agent_module_name"), "agent_deploy_path": row.get("agent_deploy_path"),
                 "agent_cmd": row.get("agent_cmd"), "agent_package_name": row.get("package_name"),
                 "third_party_middleware": row.get("third_party_middleware"),
                 "agent_deploy_ip": row.get("agent_deploy_ip") if row.get("agent_deploy_ip") else MOCK_SYSTEM_INTERFACE[
                     'agent_deploy_path_ip'], "arex_service_name": row.get("arex_app_id"),
                 "pinpoint_agent_id": f"{module_name.upper().replace('-', '_')}_SERVICE_HOST"})
        return agent_info_list

    @staticmethod
    def get_platform_need_deploy_agent_list(module_name, suite_code):
        sql = """
              SELECT v.agent_module_name, v.agent_deploy_path, v.agent_cmd, v.package_name, v.third_party_middleware, pio.agent_deploy_ip, v.arex_app_id
	          FROM 
              (
                  SELECT a.agent_module_name, a.agent_deploy_path, a.agent_cmd, c.package_name, i.third_party_middleware, itx.suite_code, am.arex_app_id    
                  FROM agent_mgt_agent_info a 
                  LEFT JOIN app_mgt_app_build c ON a.agent_module_name = c.module_name
                  LEFT JOIN app_mgt_app_module m ON a.agent_module_name = m.module_name
                  LEFT JOIN app_mgt_app_info i ON m.app_id = i.id
                  LEFT JOIN agent_mgt_arex_app_map am ON m.module_name = am.module_name
                  JOIN (SELECT env_mgt_suite.suite_code AS suite_code
                        FROM env_mgt_suite
                        WHERE env_mgt_suite.suite_is_active = TRUE) itx
                  WHERE a.forced_mount = 1 
              ) v
              LEFT JOIN agent_mgt_publish_info pio ON pio.agent_module_name = v.agent_module_name 
                                                      AND pio.suite_code = v.suite_code
              WHERE v.suite_code = '{}';
              """.format(suite_code)
        agent_info_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            agent_info_list.append(
                {"agent_module_name": row.get("agent_module_name"), "agent_deploy_path": row.get("agent_deploy_path"),
                 "agent_cmd": row.get("agent_cmd"), "agent_package_name": row.get("package_name"),
                 "third_party_middleware": row.get("third_party_middleware"),
                 "agent_deploy_ip": row.get("agent_deploy_ip") if row.get("agent_deploy_ip") else MOCK_SYSTEM_INTERFACE[
                     'agent_deploy_path_ip'], "arex_service_name": row.get("arex_app_id"),
                 "pinpoint_agent_id": f"{module_name.upper().replace('-', '_')}_SERVICE_HOST"})
        return agent_info_list

    @staticmethod
    def get_active_agent_by_type(agent_type):
        sql = """
            SELECT
                agent_module_name,
                agent_cmd,
                agent_deploy_path,
                IFNULL(forced_mount, 0) as forced_mount
            FROM
                agent_mgt_agent_info 
            WHERE
                is_active = 1 
                AND agent_type = {}
                """.format(agent_type)
        agent_info_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            agent_info_list.append(
                {"agent_name": row.get("agent_module_name"), "agent_deploy_path": row.get("agent_deploy_path"),
                 "agent_cmd": row.get("agent_cmd"), "forced_mount": row.get("forced_mount")})
        return agent_info_list

    @staticmethod
    def get_agent_info_list(app_list, suite_list):
        sql = """
            SELECT ai.agent_module_name,
            ai.agent_cmd,
            ai.agent_deploy_path
            FROM agent_mgt_env_app_bind ab 
            INNER JOIN agent_mgt_agent_info ai ON ab.agent_module_name = ai.agent_module_name
            WHERE ab.app_module_name IN ('{app_str}')
            AND ab.suite_code IN ('{suite_str}')
            AND ab.is_use = 1
            AND ai.is_active = 1
            AND ai.agent_type = 2
        """.format(app_str= "','".join(app_list), suite_str= "','".join(suite_list))
        agent_info_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
            for row in db.cur.fetchall():
                agent_info_list.append(
                    {"agent_name": row.get("agent_module_name"), "agent_deploy_path": row.get("agent_deploy_path"),
                     "agent_cmd": row.get("agent_cmd")})
        return agent_info_list

    @staticmethod
    def get_platform_need_deploy_shard_agent_list(module_name, suite_code, branch):
        sql = """
             select  a.sharding_agent_module_name as 'agent_module_name',b.agent_deploy_path, b.agent_cmd,c.package_name,0 as 'third_party_middleware','' as 'agent_deploy_ip',a.sharding_agent_branch_name,a.sharding_agent_param
             
             from app_mgt_sharding_config  a
	JOIN agent_mgt_agent_info b on b.agent_module_name=a.sharding_agent_module_name
  LEFT JOIN app_mgt_app_build c ON c.module_name = a.sharding_agent_module_name
	LEFT JOIN agent_mgt_publish_info pio ON pio.agent_module_name = a.sharding_agent_module_name 
                                                      AND pio.suite_code = '{suite_code}'
	where a.module_name='{module_name}' and a.branch_name='{branch}' ;
              """.format(module_name=module_name, suite_code=suite_code, branch=branch)
        agent_info_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            agent_info_list.append(
                {"agent_module_name": row.get("agent_module_name"), "agent_deploy_path": row.get("agent_deploy_path"),
                 "agent_cmd": row.get("agent_cmd"), "agent_package_name": row.get("package_name"),
                 "third_party_middleware": row.get("third_party_middleware"),
                 "agent_type": "shard",
                 "agent_version": row.get(
                     "sharding_agent_branch_name"),
                 "sharding_agent_param": row.get(
                     "sharding_agent_param"),
                 "agent_deploy_ip": row.get("agent_deploy_ip") if row.get("agent_deploy_ip") else MOCK_SYSTEM_INTERFACE[
                     'agent_deploy_path_ip'
                 ]})
        return agent_info_list

    def __get_use_decide_not_to_deploy_agent_list(self, module_name, suite_code):
        sql = """select ab.agent_module_name from agent_mgt_env_app_bind ab 
                 left join agent_mgt_agent_info ai on ab.agent_module_name = ai.agent_module_name
                 where ab.app_module_name = '{}' and ab.suite_code = '{}' and ab.is_use = '0' and ai.agent_type = '1';
              """.format(module_name, suite_code)
        agent_info_list = []
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            agent_info_list.append(row.get("agent_module_name"))
        return agent_info_list

    def __get_agent_info(self, agent_module_name):
        sql = """select ai.agent_module_name, ai.agent_type, b.package_name as agent_package_name, ai.agent_cmd, ai.agent_deploy_path, i.third_party_middleware 
                    from agent_mgt_agent_info ai 
                    left join app_mgt_app_module m on ai.agent_module_name = m.module_name
                    left join app_mgt_app_info i on m.app_id = i.id
                    left join app_mgt_app_build b on m.module_name = b.module_name
                    where ai.agent_module_name = '{}';
                  """.format(agent_module_name)
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row
        return {}

    def __get_app_db_info(self, module_name, suite_code):
        db_info_result = db_mgt_bind_view.get(module_name=module_name, suite_code=suite_code)
        db_info_list = db_info_reset(db_info_result)
        return db_info_list

    def get_actual_selected_br_name(self, publish_data, selected_br_name):
        if selected_br_name == "online_br":
            selected_br_name = publish_data.get("online_br_name")
        elif selected_br_name == "archive_br":
            selected_br_name = publish_data.get("archive_br_name")
        return selected_br_name

    def __join_app_deploy_info(self, data_list, app_dict_list):
        new_data_list = []
        logger.info("type_enum==={}".format(self.type_enum))
        logger.info("type(type_enum)==={}".format(type(self.type_enum)))
        for publish_data in data_list:
            for app_dict in app_dict_list:
                k = list(app_dict.keys())[0]
                if publish_data.get("module_name") == k:
                    selected_br_name = app_dict.get(k)
                    selected_br_name = self.get_actual_selected_br_name(publish_data, selected_br_name)
                    publish_data.update({"origin_selected_br_name": app_dict.get(k)})
                    publish_data.update({"selected_br_name": selected_br_name})
                    selected_zeus_type = self.__get_zeus_type_by_branch(k, selected_br_name)
                    publish_data.update({"selected_zeus_type": selected_zeus_type})
                    archive_zeus_type = self.__get_zeus_type_by_branch(k, publish_data.get("archive_br_name"))
                    publish_data.update({"archive_zeus_type": archive_zeus_type})
                    online_zeus_type = self.__get_zeus_type_by_branch(k, publish_data.get("online_br_name"))
                    publish_data.update({"online_zeus_type": online_zeus_type})

                    # 根据 type_enum 和 suite_code 来选出本次应该部署的 版本和zeus_type
                    archive_or_online = None
                    if self.type_enum in (TypeEnum.TEST_PUSH, TypeEnum.MULTI_PUSH, TypeEnum.BUILD):
                        publish_data.update({"deploy_br_name": selected_br_name})
                        publish_data.update({"deploy_zeus_type": selected_zeus_type})
                    elif self.type_enum in (TypeEnum.INIT, TypeEnum.HISTORY, TypeEnum.CRON, TypeEnum.UPDATE):
                        if TestMgtPublishApp.select().where(TestMgtPublishApp.suite_code == self.env_suite,
                                                            TestMgtPublishApp.module_name == k):
                            publish_data.update({"deploy_br_name": publish_data.get("online_br_name")})
                            publish_data.update({"deploy_zeus_type": online_zeus_type})
                            archive_or_online = 'online'
                        else:
                            publish_data.update({"deploy_br_name": publish_data.get("archive_br_name")})
                            publish_data.update({"deploy_zeus_type": archive_zeus_type})
                            archive_or_online = 'archive'
                    else:
                        publish_data.update({"deploy_br_name": self.br_name})
                        publish_data.update({"deploy_zeus_type": 0})
                    publish_data.update({"archive_or_online": archive_or_online})
                    new_data_list.append(publish_data)
        logger.info("new_data_list==={}".format(json.dumps(new_data_list, indent=4)))
        return new_data_list

    def __get_zeus_type_by_branch(self, module_name, branch_name):
        sql = '''
                SELECT IFNULL(ai.zeus_type, m.zeus_type) AS zeus_type 
                FROM iter_mgt_iter_app_info ai 
                LEFT JOIN  iter_mgt_iter_info ii ON ai.pipeline_id = ii.pipeline_id
                LEFT JOIN app_mgt_app_module m ON ai.appName = m.module_name
                WHERE ai.appName = '{}' AND ii.br_name = '{}';
              '''.format(module_name, branch_name)

        with DBConnectionManager() as db:
            db.cur.execute(sql)
        for row in db.cur.fetchall():
            return row.get("zeus_type")
        return 1

    def get_app_node_data_db(self, env_suite, app_dict_list):
        """获取应用和节点的信息
        :param env_suite: string.环境套
        :param app_dict_list: list.环境套
        :param type_enum:
        :return:
        """
        app_list = []
        if app_dict_list:
            app_list = [list(app_dict.keys())[0] for app_dict in app_dict_list]

        test_publish_data_list = self.__get_test_publish_data(env_suite, app_list, app_dict_list)
        test_publish_data_list = self.__join_app_deploy_info(test_publish_data_list, app_dict_list)
        return test_publish_data_list

    def __get_es_info_dict(self, app_list, env_suite):
        """获取ES信息字典
        :param app_list: 应用列表
        :param env_suite: 环境套
        :return: ES信息字典
        """
        es_info_dict = {}
        if not app_list:
            return es_info_dict
            
        # ES信息查询SQL
        es_info_sql = """
        SELECT DISTINCT 
            bi.es_module_name,
            ab.module_name,
            ai.git_url as es_lib_git_repo,
            bd.node_docker_service_hosts as es_url
        FROM es_mgt_index_info ii
        INNER JOIN es_mgt_biz_info bi ON ii.es_biz_code = bi.es_biz_code
        INNER JOIN es_mgt_app_bind ab ON ab.es_biz_code = bi.es_biz_code
        INNER JOIN app_mgt_app_module m ON ab.module_name = m.module_name
        INNER JOIN app_mgt_app_info ai ON m.app_id = ai.id
        INNER JOIN env_mgt_node_bind b ON b.module_name = bi.es_module_name
        INNER JOIN env_mgt_suite s ON b.suite_id = s.id
        INNER JOIN env_mgt_node_bind_dynamic bd ON b.id = bd.bind_id
        WHERE ab.module_name IN ({}) AND s.suite_code = %s
        """
        
        try:
            placeholders = ','.join(['%s'] * len(app_list))
            es_sql = es_info_sql.format(placeholders)
            
            # 打印最终执行的SQL语句和查询条件
            query_params = app_list + [env_suite]
            logger.info(f"执行ES信息查询SQL: {es_sql}")
            logger.info(f"查询参数: {query_params}")
            
            with DBConnectionManager() as db:
                db.cur.execute(es_sql, query_params)
                es_results = db.cur.fetchall()
                
            for es_row in es_results:
                module_name = es_row['module_name']
                if module_name not in es_info_dict:
                    es_info_dict[module_name] = []
                
                es_info_dict[module_name].append({
                    'es_module_name': es_row['es_module_name'],
                    'es_lib_git_repo': es_row['es_lib_git_repo'],
                    'es_url': es_row['es_url']
                })
                
        except Exception as e:
            logger.error(f"查询ES信息失败: {e}")
            
        return es_info_dict

    def get_es_backup_info(self, biz_code, suite_code):
        """获取ES备份信息
        :param biz_code: 业务代码
        :param suite_code: 环境套代码
        :return: ES备份信息列表
        """
        sql = """
        SELECT 
            di.es_dump_name,
            di.source_es_module_name,
            di.dump_file_path,
            di.dump_size,
            di.index_count,
            di.dump_status
        FROM es_mgt_dump_info di
        INNER JOIN es_mgt_dump_biz_bind bb ON di.es_dump_name = bb.es_dump_name
        WHERE bb.biz_code = %s AND di.suite_code = %s
        ORDER BY bb.priority ASC
        """
        
        try:
            with DBConnectionManager() as db:
                db.cur.execute(sql, [biz_code, suite_code])
                return db.cur.fetchall()
        except Exception as e:
            logger.error(f"查询ES备份信息失败: {e}")
            return []

    def get_es_backup_info_by_biz_test_iter_id(self, biz_test_iter_id):
        """根据业务测试迭代ID获取ES备份信息
        :param biz_test_iter_id: 业务测试迭代ID
        :return: ES备份信息列表
        """
        sql = """
        SELECT 
            di.es_dump_name,
            di.source_es_module_name,
            di.file_path,
            di.file_size,
            di.index_count,
            di.status
        FROM es_mgt_dump_info di
        INNER JOIN es_mgt_dump_biz_bind bb ON di.es_dump_name = bb.es_dump_name
        WHERE bb.biz_test_iter_id = %s
        ORDER BY di.create_time ASC
        """
        
        try:
            with DBConnectionManager() as db:
                db.cur.execute(sql, [biz_test_iter_id])
                return db.cur.fetchall()
        except Exception as e:
            logger.error(f"查询ES备份信息失败: {e}")
            return []


class TpInitBo(AppSuiteObject):
    """初始化(init) zt@2020-12-25"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpBuildBo(AppSuiteObject):
    """构建(build) zt@2020-12-28"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)

    def get_lib_cache_repo_path(self, lib_repo, suite_code, lib_push_dir):
        if lib_repo is None:
            return lib_repo
        if lib_push_dir is None:
            return os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"], self.module_name, self.br_name)
        else:
            return os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"],
                                self.module_name,
                                self.br_name,
                                "/".join(lib_push_dir.split("/")[1:]))

    def get_src_cache_config_path(self, suite_code, lib_repo_config_path):
        if self.third_party_middleware == 1 and self.zeus_type == 0:
            return None

        if self.deploy_type == 1 or self.package_type == "static" or self.package_type == "remote" or self.zeus_type == 1:
            return os.path.join(TEST_PUBLISH_AIO["root_path"],
                                suite_code,
                                TEST_PUBLISH_AIO["test_app_resource_gitlab_name"],
                                self.module_name)
        else:
            return os.path.join(TEST_PUBLISH_AIO["root_path"],
                                suite_code,
                                TEST_PUBLISH_AIO["app_resource_gitlab_name"],
                                "ec",
                                self.module_name,
                                CONFIG_DIR_FLAG["bs-prod"])
        return None

    def get_target_cache_config_path(self, suite_code):
        return os.path.join(PIPELINE_TEST_PUBLISH["pipeline_local_path"], self.module_name, "config", self.br_name)


class TpMockBo(AppSuiteObject):
    """模拟(mock) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpHistoryBo(AppSuiteObject):
    """历史版本发布(history) zt@2020-12-25"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpTestPublishBo(AppSuiteObject):
    """测试单推(test_push) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)

    def get_src_cache_config_path(self, suite_code, lib_repo_config_path):
        src_cache_config_path = None
        if self.third_party_middleware == 1 and self.zeus_type == 0:
            return src_cache_config_path

        src_cache_config_path = os.path.join(TEST_PUBLISH_AIO["root_path"],
                                             suite_code,
                                             TEST_PUBLISH_AIO["test_app_resource_gitlab_name"],
                                             self.module_name)

        return src_cache_config_path


class TpCheckBo(AppSuiteObject):
    """环境预检(check) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpCronBo(AppSuiteObject):
    """定时发布(cron) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpOtherBo(AppSuiteObject):
    """其它(other) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpTestMultiPublishBo(AppSuiteObject):
    """多推(multi_push) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


class TpUpdateBo(AppSuiteObject):
    """测试环境更新(update) zt@2020-11-23"""

    def __init__(self, app_info_dict):
        self.br_name = app_info_dict["br_name"]
        super().__init__(app_info_dict)


def db_info_reset(db_info_result):
    db_info_list = []
    for db_info in db_info_result:
        result = {"db_logic_id": db_info.get("db_logic_info_id"), "db_info_id": db_info.get("db_info_id"),
                  "db_group_name": db_info.get("db_group_name"),
                  "db_srv_type": db_info.get("db_srv_type"),
                  "db_srv_hosts": db_info.get("db_srv_hosts"), "db_srv_port": db_info.get("db_srv_port"),
                  "db_srv_name": db_info.get("db_srv_name"), "db_alias": db_info.get("db_alias"),
                  "suite_db_name": db_info.get("suite_db_name"), "username": db_info.get("username"),
                  "password": db_info.get("password"), "conn_url": db_info.get("conn_url"),
                  "db_vcs_type": db_info.get("db_vcs_type"), "vcs_url": db_info.get("vcs_url"),
                  "db_name": db_info.get("db_name"), "db_srv_username": db_info.get("db_srv_username"),
                  "db_srv_password": db_info.get("db_srv_password"),
                  "db_srv_bash_profile": db_info.get("db_srv_bash_profile"),
                  "db_srv_socket_path": db_info.get("db_srv_socket_path"),
                  "db_table_space": db_info.get("db_table_space"),
                  "data_dump_dir": db_info.get("data_dump_dir"), "module_name": db_info.get("app_module_name")}
        db_info_list.append(result)
    return db_info_list


def query_diff_info(db_srv_type=None, workspace=None):
    pass
    # sql = """
    # SELECT DISTINCT


#       iter.biz_test_iter_id,
#       db_srv.id AS db_srv_id,
#       db_info.id AS db_info_id,
#       restore_his.db_name AS suite_db_name,
#       db_group.db_group_name,
#       li.logic_db_name AS db_info_suffix_name,
#       db_srv.db_srv_username,
#       db_srv.db_srv_password,
#       biz_base.biz_base_db_code,
#       iter.biz_code,
#       db_srv.db_srv_hosts,
#       db_srv.db_srv_port,
#       restore_his.suite_code,
#       restore_his.restore_datetime,
#       CONCAT( "{diff_sql_root_path}",'/',biz_base.biz_base_db_code,'/',iter.biz_test_iter_id,'/',restore_his.suite_code,'/', li.logic_db_name ) AS diff_sql_root_path,
#       cdc_info.binlog_name,
#       cdc_info.log_pos,
#       db_srv.db_srv_type,
#       db_srv.db_srv_url,
#       db_srv.db_srv_name,
#       cdc_info.cdc_position ,
#       restore_his.id AS restore_his_id
# FROM
#     db_mgt_db_restore_his restore_his
#     JOIN db_mgt_cdc_info cdc_info ON restore_his.id = cdc_info.restore_his_id
#     JOIN biz_test_iter iter ON restore_his.opt_pipeline_id = iter.biz_test_iter_id
#     AND iter.br_status = 'open'
#     JOIN db_mgt_info db_info ON restore_his.db_info_id = db_info.id
#     JOIN db_mgt_suite_bind sb ON sb.db_info_id = db_info.id
#     JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id
#     JOIN db_mgt_domain dmd ON li.db_domain_id = dmd.id
#     JOIN db_mgt_group db_group ON dmd.db_group_id= db_group.id
#     JOIN db_mgt_srv db_srv ON db_info.db_srv_id = db_srv.id
#     JOIN biz_base_db_bind biz_base ON iter.biz_code = biz_base.biz_code
#     WHERE db_srv.db_srv_type = '{db_srv_type}'
#     AND cdc_info.cdc_flag=1
#     """.format(db_srv_type=db_srv_type, diff_sql_root_path=workspace)
# with DBConnectionManager() as db:
#     db.cur.execute(sql)
# return db.cur.fetchall()


def get_lib_repo_path(app_name):
    sql = """
            select lib_repo from app_mgt_app_module where module_name = '{}'
        """.format(app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return os.path.join(PIPELINE_TEST_PUBLISH["lib_git_url"], row.get("lib_repo") + '.git')


def get_agent_latest_archive_version(app_name):
    sql = """select m.br_name from iter_mgt_iter_info m
            left join iter_mgt_iter_app_info i on i.pipeline_id = m.pipeline_id
            where br_end_date = 
            (
            select MAX(m1.br_end_date) from iter_mgt_iter_info m1
            INNER JOIN iter_mgt_iter_app_info i1 ON i1.pipeline_id = m1.pipeline_id
            WHERE m1.br_status = 'close' and i1.appName = '{app_name}'
            ) 
            and i.appName = '{app_name}';
        """.format(app_name=app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row.get("br_name")


def is_third_party_middleware(app_name):
    sql = """
            select t.third_party_middleware from app_mgt_app_info t
            left join app_mgt_app_module m on t.id = m.app_id
            where m.module_name = '{}';
        """.format(app_name)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        if row.get("third_party_middleware") == 1:
            return True
        else:
            return False


def get_flyway_iteration_info(app_name, iteration_id, suite_code):
    sql_infos = get_db_info_by_app(app_name, suite_code)
    sql_ver_db_list = []
    for sql_info in sql_infos:
        sql_ver_db_list.append(sql_info['db_name_info'])
    sql = """SELECT * FROM db_mgt_sql WHERE sql_ver_db in ('{}') AND iteration_id = '{}'""".format(
        "', '".join(sql_ver_db_list),
        iteration_id)

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    iter_sql_info = db.cur.fetchall()
    for item in iter_sql_info:
        for sql_info in sql_infos:
            if item['sql_ver_db'] == sql_info['db_name_info']:
                item['db_user'] = sql_info['db_user']
                item['db_passwd'] = sql_info['db_passwd']
                item['conn_url'] = sql_info['conn_url']

    return iter_sql_info


def is_app_deploy_developing_version(app_name, suite_code):
    sql = '''
            SELECT b.module_name, b.lib_repo_info_id, p.iteration_id, i.br_status FROM env_mgt_node_bind b
            LEFT JOIN env_mgt_suite s ON b.suite_id = s.id
            LEFT JOIN product_mgt_product_info p ON b.lib_repo_info_id = p.id
            LEFT JOIN iter_mgt_iter_info i ON p.iteration_id = i.pipeline_id
            WHERE b.module_name = '{}' AND s.suite_code = '{}'
           '''.format(app_name, suite_code)

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        if row.get("br_status") == "open":
            return True
        else:
            return False


def get_db_branch_by_suite_code_and_db_group_name(suite_code, db_group_name):
    sql = '''
            SELECT DISTINCT li.logic_db_name, i.br_name, t.copy_br_name_record, t.db_info_id
            FROM env_mgt_db_deploy_info t
            LEFT JOIN iter_mgt_iter_info i ON t.pipeline_id = i.pipeline_id
            LEFT JOIN db_mgt_info db ON db.id = t.db_info_id
            LEFT JOIN db_mgt_suite_bind sb ON sb.db_info_id = t.db_info_id
            LEFT JOIN db_mgt_logic_info li ON sb.db_logic_id = li.id
            LEFT JOIN db_mgt_domain dmd ON dmd.id = li.db_domain_id
            LEFT JOIN db_mgt_app_bind db_bind ON db_bind.db_domain_id = dmd.id
            LEFT JOIN db_mgt_group g ON dmd.db_group_id = g.id
            WHERE t.suite_code = '{}' AND g.db_group_name = '{}';
           '''.format(suite_code, db_group_name)

    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return (row.get("br_name") if row.get("br_name") else '',
                row.get("copy_br_name_record") if row.get("copy_br_name_record") else '',
                row.get("db_info_id") if row.get("db_info_id") else '')
    return '', '', ''


def get_db_archive_branch_list(db_group_name, db_deploy_branch=None):
    sql1 = '''
            SELECT vv.br_name, vv.br_end_date
            FROM (SELECT DISTINCT m.br_name AS br_name,
                                  m.br_end_date
                  FROM iter_mgt_iter_info m
                      INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                      INNER JOIN db_mgt_app_bind db_bind ON i.appName = db_bind.app_module_name
                      INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
                      INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id AND g.db_group_name = '{db_group_name}'
                  WHERE m.br_status = 'close'
            
                  UNION
            
                  SELECT DISTINCT t.br_name, DATE_FORMAT(t.br_end_time, '%Y年%m月%d日 %H时%i分') AS br_end_time
                  FROM iter_mgt_sql_iter_info t
                       INNER JOIN iter_mgt_iter_info m ON t.pipeline_id = m.pipeline_id
                       INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                       INNER JOIN db_mgt_app_bind db_bind ON i.appName = db_bind.app_module_name
                       INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
                       INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id AND g.db_group_name = '{db_group_name}'
                  WHERE t.br_status = 'close') vv '''.format(db_group_name=db_group_name)
    sql2 = ''' WHERE vv.br_end_date > (SELECT DISTINCT m.br_end_date
                                        FROM iter_mgt_iter_info m
                                             INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                                             INNER JOIN db_mgt_app_bind db_bind ON i.appName = db_bind.app_module_name AND db_bind.read_or_write = 1
                                             INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
                                             INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id
                                        AND g.db_group_name = '{db_group_name}'
                                    WHERE m.br_name = '{db_deploy_branch}') '''.format(db_group_name=db_group_name,
                                                                                       db_deploy_branch=db_deploy_branch)
    sql3 = ''' ORDER BY vv.br_end_date ASC '''

    if db_deploy_branch:
        sql = sql1 + sql2 + sql3
    else:
        sql = sql1 + sql3
    logger.info(sql)
    branch_list = []
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        branch_list.append(row.get("br_name"))
    return branch_list


def get_last_db_archive_branch_list(db_group_name):
    sql = '''
            SELECT vv.pipeline_id
            FROM (SELECT DISTINCT m.pipeline_id AS pipeline_id,
                                  m.br_end_date
                  FROM iter_mgt_iter_info m
                      INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                      INNER JOIN db_mgt_app_bind db_bind ON i.appName = db_bind.app_module_name
                      INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
                      INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id AND g.db_group_name = '{db_group_name}'
                  WHERE m.br_status = 'close'
                  UNION
                  SELECT DISTINCT t.pipeline_id, DATE_FORMAT(t.br_end_time, '%Y年%m月%d日 %H时%i分') AS br_end_time
                  FROM iter_mgt_sql_iter_info t
                       INNER JOIN iter_mgt_iter_info m ON t.pipeline_id = m.pipeline_id
                       INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                       INNER JOIN db_mgt_app_bind db_bind ON i.appName = db_bind.app_module_name
                       INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
                       INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id AND g.db_group_name = '{db_group_name}'
                  WHERE t.br_status = 'close') vv  ORDER BY vv.br_end_date DESC LIMIT 1'''.format(
        db_group_name=db_group_name)

    logger.info(sql)
    pipeline_id = None
    with DBConnectionManager() as db:
        db.cur.execute(sql)
        result = db.cur.fetchone()
        pipeline_id = result['pipeline_id']
    return pipeline_id


def get_db_deploy_info(suite_code, db_info_id):
    sql = '''
            SELECT t.db_info_id, t.suite_code, t.pipeline_id FROM env_mgt_db_deploy_info t
            WHERE t.db_info_id = '{}' AND t.suite_code='{}';
          '''.format(db_info_id, suite_code)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    db_deploy_info_list = []
    for row in db.cur.fetchall():
        db_deploy_info_list.append({"db_info_id": row.get("db_info_id"), "suite_code": row.get("suite_code"),
                                    "pipeline_id": row.get("pipeline_id")})
    return db_deploy_info_list


def get_latest_pipeline_id_by_db_group_name(db_group_name):
    sql = '''
            SELECT DISTINCT
                m.br_name AS br_name,
                m.br_end_date,
                m.pipeline_id
            FROM
                iter_mgt_iter_info m
                INNER JOIN iter_mgt_iter_app_info i ON i.pipeline_id = m.pipeline_id
                INNER JOIN db_mgt_app_bind db_bind ON i.appName = db_bind.app_module_name
                INNER JOIN db_mgt_domain dmd ON db_bind.db_domain_id = dmd.id
		        INNER JOIN db_mgt_group g ON dmd.db_group_id = g.id
                AND g.db_group_name = '{}' 
            WHERE
                    m.br_status = 'close' 
             ORDER BY br_end_date DESC;
    '''.format(db_group_name)
    logger.info(sql)
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    for row in db.cur.fetchall():
        return row.get("pipeline_id")


def get_biz_bind_app_list(biz_code):
    sql = '''
            SELECT t.app_module_name 
            FROM biz_app_bind t 
            WHERE t.biz_code = '{biz_code}' AND t.biz_app_bind_is_active = 1;
            '''.format(biz_code=biz_code)

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    app_list = []
    for row in db.cur.fetchall():
        app_list.append(row.get("app_module_name"))
    return app_list


def get_app_by_start_level(app_list):
    sql = '''
            SELECT DISTINCT start_level, IFNULL(container_name, module_name) AS module_name
            FROM publish_deploy_info t 
            WHERE t.module_name IN ('{}')
          '''.format("','".join(app_list))
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    app_list_dict = {}
    for row in db.cur.fetchall():
        if row.get("start_level") not in app_list_dict:
            app_list_dict[row.get("start_level")] = [row.get("module_name")]
        else:
            app_list_dict[row.get("start_level")].append(row.get("module_name"))
    app_list_dict = dict(sorted(app_list_dict.items(), key=lambda x: x[0]))
    return app_list_dict


def get_app_container_list(app_list):
    sql = '''
                SELECT DISTINCT start_level, IFNULL(container_name, module_name) AS module_name
                FROM publish_deploy_info t 
                WHERE t.module_name IN ('{}')
              '''.format("','".join(app_list))
    with DBConnectionManager() as db:
        db.cur.execute(sql)
    app_container_list = []
    for row in db.cur.fetchall():
        app_container_list.append(row.get("module_name"))
    return app_container_list


def is_derivative_business(biz_code):
    # 业务是否为衍生业务
    with DBConnectionManagerForSqlalchemy() as db:
        biz_base_info = db.session.query(BizBaseInfo).filter(
            BizBaseInfo.biz_code == biz_code).one()
        if biz_base_info.biz_category == 2:
            return True
    return False


if __name__ == "__main__":
    db_info_result = db_mgt_bind_view.get(module_name='param-server', suite_code='it29')
    db_info_list = db_info_reset(db_info_result)
    print(db_info_list)

    # dict_list = [{"acc-center-server": "1.0.0"}, {"fbs-online-server": "2.0.0"}, {"fbs-online-server": "3.0.0"}]
    #
    # pas = PublishTestRepos("it10", "D:\\test", type_enum=TypeEnum.BUILD, br_name="1.0.1",
    #                        app_dict_list=[{"acc-center-server": "1.0.1"}])

    # pas = PublishTestRepos("tms18", "E:\\test")

    # logger.info(pas.cache_data_code)
    # for row in pas:
    #     logger.info(row)
    # logger.info("模块名： {}".format(row.module_name))
    # logger.info("制品库路径： {}".format(row.lib_repo_path))
    # logger.info("配置类型： {}".format(row.zeus_type))
    # logger.info("容器名： {}".format(row.container_name))
    # logger.info("是否第三方： {}".format(row.third_party_middleware))
    # logger.info("包类型： {}".format(row.package_type))
    # logger.info("是否整包： {}".format(row.package_full))
    # logger.info("是否接入平台： {}".format(row.platform_type))
    # #logger.info(row.gitlab_path)
    # logger.info("线上版本： {}".format(row.online_br_name))
    # logger.info("本地制品临时目录： {}".format(row.lib_cache_repo_path))
    # logger.info("部署目录： {}".format(row.deploy_path))
    # logger.info("部署IP： {}".format(row.active_node_ip))
    # logger.info("部署方式： {}".format(row.deploy_type))
    # logger.info("容器节点： {}".format(row.node_docker))
    # if row.third_party_middleware == 1:

    # logger.info("模块名： {}".format(row.module_name))
    # logger.info("部署目录： {}".format(row.start_script_path))
    # logger.info("启动级别： {}".format(row.start_level))
    # logger.info("部署IP： {}".format(row.active_node_ip))

    # if row.zeus_type == 1 and row.platform_type == 1:
    #     logger.info("缓存制品： {}".format(row.lib_cache_repo_path))
    #     logger.info("配置源： {}".format(row.src_cache_config_path))
    #     logger.info("配置目标： {}".format(row.target_cache_config_path))

    # logger.info("本地配置目录： {}".format(row.lib_cache_config_path))
    # if row.zeus_type == 0:
    # #     logger.info(row.module_name)
    # if row.third_party_middleware == 0:
    #     # if row.zeus_type == 0:
    #     #     print('update app_mgt_app_module set zeus_type = 5 where module_name = "{}";'.format(row.module_name))
    #
    #     if os.path.isdir(row.src_cache_config_path):
    #         logger.info(row.src_cache_config_path)
    #         continue
    #     else:
    #         if row.zeus_type != 0 :
    #             logger.info(row.module_name)
    #             #logger.info(row.zeus_type)
    #             logger.info("源配置目录：{}".format(row.src_cache_config_path))
    #        # logger.info("目标目录：{}".format(row.target_cache_config_path))
    # #logger.info(row.package_type)
    # if row.module_name in ("tms-counter-console","tomcat-quartz"):
    #     logger.info(row.deploy_path)
    #     logger.info("模块名： {}".format(row.module_name))
    #     logger.info("制品库路径： {}".format(row.lib_repo_path))
    #     logger.info("配置类型： {}".format(row.zeus_type))
    #     logger.info("容器名： {}".format(row.container_name))
    #     logger.info("是否第三方： {}".format(row.third_party_middleware))
    #     logger.info("包类型： {}".format(row.package_type))
    #     logger.info("是否整包： {}".format(row.package_full))
    #     logger.info("是否接入平台： {}".format(row.platform_type))
    #     #logger.info(row.gitlab_path)
    #     logger.info("线上版本： {}".format(row.online_br_name))
    #     logger.info("本地制品临时目录： {}".format(row.lib_cache_repo_path))
    #     logger.info("部署目录： {}".format(row.deploy_path))
    #     logger.info("部署IP： {}".format(row.active_node_ip))
    #     logger.info("部署方式： {}".format(row.deploy_type))
    #     logger.info("容器节点： {}".format(row.node_docker))
    #     logger.info("发布配置路径： {}".format(row.deploy_config_path))
    #     logger.info("启动脚本路径： {}".format(row.start_script_path))
    #     logger.info("本地配置目录： {}".format(row.lib_cache_config_path))
    # # if row.deploy_type == 2 and row.package_type == "h5":
    # #     logger.info('INSERT publish_deploy_info (module_name,container_name) VALUES ("{}","{}");'.format(row.module_name,row.container_name))
    # #break
