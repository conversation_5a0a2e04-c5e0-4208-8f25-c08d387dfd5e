# 测试环境一键初始化：实现--其它
# 第1版 zt@2020-10-22
import json
import os
import sys
import threading
import time
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)
from datetime import datetime, timedelta
from db_mgt.db_dump_mgt import DbDumpMgt
from common.oracle.models import DbMgtOptScript
from db_mgt.creat_sql_migrate.models import DbMgtExecSqlFileHistory, EnvMgtDbDeployInfo
from dao.get.mysql.app_info import query_archived_applications_and_versions, get_db_info_by_app_name, \
    get_db_in_transit_br_names
from common.mysql.mysql import MysqlHandler
from common.oracle.oracle import OracleHandler
from settings import logger as log, PRODUCT_STORE_SQL, TEST_DATA_INIT, MYSQL_BASIC_USERNAME, \
    ORACLE_BASIC_PASSWORD, ORACLE_BASIC_CONN_URL, MYSQL_BASIC_PASSWORD, MYSQL_BASIC_CONN_URL
from settings import TEST_PUBLISH_AIO as TP_AIO
from test_publish_aio.test_suite_init_impl import ParentHandler
from test_publish_aio.test_suite_init_bo import is_not_tpm
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd, sort_sql_common, \
    exec_flyway_command, checkout_repo, biz_sql_sort, exec_remote_cmd_by_username_password, increment_sort_sql_common
from db_mgt.db_dump_ser import get_dump_archive_pipeline_id
from abc import abstractmethod
from test_publish_aio.test_suite_init_constants import TypeEnum
from test_publish_aio.test_publish_aio_models.test_publish_ser import get_db_branch_by_suite_code_and_db_group_name, \
    get_db_archive_branch_list, get_db_deploy_info, get_latest_pipeline_id_by_db_group_name, get_biz_bind_app_list

from concurrent.futures import ThreadPoolExecutor, as_completed


class DbHandler(ParentHandler):
    """db zt@2020-10-23"""
    filter_func_list = [is_not_tpm]
    filter_key_list = ['container_name']

    @abstractmethod
    def exec(self, *args, **kwargs):
        """逻辑处理 zt@2020-10-23"""
        step_en_name = self.step_enum.en_name
        step_cn_name = self.step_enum.cn_name
        log.info(">>>> parent「{}: {}」， job_business_id = {}".format(step_en_name, step_cn_name, self.job_business_id))

        bo_list = kwargs.get('bo_list')
        bo_dict = kwargs.get('bo_dict')
        bo_list_dict = kwargs.get('bo_list_dict')
        log.info(">>>> parent「{}: {}」， len(bo_list) = {}".format(step_en_name, step_cn_name,
                                                                 len(bo_list) if bo_list else None))
        log.info(">>>> parent「{}: {}」， len(bo_dict) = {}".format(step_en_name, step_cn_name,
                                                                 len(bo_dict) if bo_dict else None))
        log.info(">>>> parent「{}: {}」， len(bo_list_dict) = {}".format(step_en_name, step_cn_name,
                                                                      len(bo_list_dict) if bo_list_dict else None))


class DbInitHandler(DbHandler):
    """51-数据库初始化 zt@2020-10-22"""
    filter_key_list = ['db_info_list']

    def exec(self, *args, **kwargs):
        """逻辑处理 zt@2020-10-22"""
        super().exec(*args, **kwargs)

        bo_dict = kwargs.get('bo_dict')

        log.info("biz_db_init_flag:{}".format(self.biz_db_init_flag))
        if self.biz_db_init_flag == "false" or self.type_enum == TypeEnum.UPDATE:
            log.info("如果是{}类型，不初始化数据库".format(TypeEnum.UPDATE))
            self.biz_db_init_flag = False

        if self.type_enum in [TypeEnum.INIT, TypeEnum.CRON]:
            log.info("type_enum={}，执行老的数据库初始化方式".format(self.type_enum))
            script_name = TP_AIO['db_py_script']
            db_py_script = 'python3.x {} {} {}'.format(script_name, self.db_env, self.suite_code)
            exec_local_cmd(db_py_script, 3600)

        if self.biz_db_init_flag:
            log.info("biz_db_init_flag开关打开，执行新的数据库初始化方式")
            log.info("需要执行的业务测试迭代为: {}".format(self.bis_pipeline_id))
            if self.bis_pipeline_id:
                biz_code = self.bis_pipeline_id.split('_')[0]
                biz_bind_app_list = get_biz_bind_app_list(biz_code)
                log.info("需要执行的业务为: {}, 关联的应用列表为：{}".format(biz_code, biz_bind_app_list))

                new_bo_dict = {}
                for k, v in bo_dict.items():
                    if k in biz_bind_app_list:
                        new_bo_dict[k] = v
                bo_dict = new_bo_dict
                log.info("过滤后的bo_dict=={}".format(bo_dict))
            if bo_dict:
                oracle_db_info_list = []
                mysql_db_info_list = []
                # basic_db_code = self.biz_base_db_code if self.biz_base_db_code else self.db_env
                # log.info("需要执行的业务的基础库集为: {}".format(basic_db_code))
                log.info("开始执行dump恢复")
                exist_db_info = []
                for k, v in bo_dict.items():
                    for db_info in v.get('db_info_list'):
                        if db_info.get("db_srv_type") == 'oracle':
                            db_info['module_name'] = k
                            if (db_info.get("db_srv_type") + db_info.get(
                                    "suite_db_name")) not in exist_db_info:
                                oracle_db_info_list.append(db_info)
                                exist_db_info.append(db_info.get("db_srv_type") + db_info.get(
                                    "suite_db_name"))
                        elif db_info.get("db_srv_type") == 'mysql':
                            db_info['module_name'] = k
                            if (db_info.get("db_srv_type") + db_info.get(
                                    "suite_db_name")) not in exist_db_info:
                                mysql_db_info_list.append(db_info)
                                exist_db_info.append(db_info.get("db_srv_type") + db_info.get(
                                    "suite_db_name"))

                oracle_db_info_list_dict = {}
                for db_info in oracle_db_info_list:
                    if oracle_db_info_list_dict.get(db_info.get("module_name")):
                        oracle_db_info_list_dict[db_info.get("module_name")].append(db_info)
                    else:
                        oracle_db_info_list_dict[db_info.get("module_name")] = [db_info]
                log.info("待恢复的oracle数据库信息：{}".format(oracle_db_info_list_dict.items()))
                # In the place where you have the for loop:
                # 创建一个共享变量来存储异常信息
                self.shared_exception = []
                oracle_threads = []
                for module_name, db_info_list in oracle_db_info_list_dict.items():
                    log.info("正在恢复的oracle数据库信息：{}".format(db_info_list))
                    t = threading.Thread(target=self.handle_oracle_db_info,
                                         args=(module_name, self.suite_code, db_info_list, self.bis_pipeline_id))
                    t.start()
                    oracle_threads.append(t)
                # Wait for all threads to finish
                for t in oracle_threads:
                    t.join()
                # 检查异常信息，并抛出异常
                if self.shared_exception:
                    raise Exception("执行oracle数据库初始化失败，异常信息为：{}".format(self.shared_exception[0]))
                mysql_db_info_list_dict = {}
                for db_info in mysql_db_info_list:
                    if mysql_db_info_list_dict.get(db_info.get("module_name")):
                        mysql_db_info_list_dict[db_info.get("module_name")].append(db_info)
                    else:
                        mysql_db_info_list_dict[db_info.get("module_name")] = [db_info]
                log.info("待恢复的mysql数据库信息：{}".format(mysql_db_info_list_dict.items()))
                # In the place where you have the for loop:
                mysql_threads = []
                for module_name, db_info_list in mysql_db_info_list_dict.items():
                    t = threading.Thread(target=self.handle_db_info,
                                         args=(module_name, self.suite_code, db_info_list, self.bis_pipeline_id))
                    t.start()
                    mysql_threads.append(t)
                for t in mysql_threads:
                    t.join()
            else:
                log.info("无数据，跳过此步骤")

    def handle_db_info(self, module_name, suite_code, db_info_list, bis_pipeline_id):
        log.info("开始mysql数据库初始化：{}".format(db_info_list))
        start_time = datetime.now()

        mh = MysqlHandler(module_name, suite_code, db_info_list, bis_pipeline_id)
        mh._check_dump_exist('mysql')
        mh._db_drop()
        mh._db_create()
        mh._db_restore()

        end_time = datetime.now()
        log.info("执行mysql数据库初始化总耗时：{}s".format(round((end_time - start_time).total_seconds(), 2)))

    def handle_oracle_db_info(self, module_name, suite_code, db_info_list, bis_pipeline_id):
        try:
            log.info("开始oracle数据库初始化：{}".format(db_info_list))
            start_time = datetime.now()

            if not self.shared_exception:
                self.shared_exception = []
            oh = OracleHandler(module_name, suite_code, db_info_list, bis_pipeline_id)
            oh._check_dump_exist('oracle')
            # 经验证 可以不杀session 直接恢复，暂时先实行该方案，持续观测 20240924 by fwm
            # 不分库不清理有问题，导致测试库不过，先恢复
            # oh._db_lock()
            # oh._kill_session()
            oh._db_unlock()
            oh._drop_ddl()
            # oh._db_drop()
            oh._db_restore()
            # oh._db_unlock()
            oh._db_set_user_and_password()
            oh._db_update_synonym()

            end_time = datetime.now()
            log.info("执行oracle数据库初始化总耗时：{}s".format(round((end_time - start_time).total_seconds(), 2)))
        except Exception as e:
            # 存储异常信息到共享变量中
            self.shared_exception.append(e)
            traceback.print_exc()


class DbPullSqlHandler(DbHandler):
    """52-数据库SQL制品准备 fwm@2023-06-16"""
    filter_key_list = ['db_info_list', 'archive_br_name', 'online_br_name', 'selected_br_name']

    def exec(self, *args, **kwargs):
        super().exec(*args, **kwargs)
        bo_dict = kwargs.get('bo_dict')
        log.info("biz_db_init_flag:{}".format(self.biz_db_init_flag))

        if self.biz_db_init_flag == "false":
            self.biz_db_init_flag = False

        if self.biz_db_init_flag or self.type_enum == TypeEnum.UPDATE:
            log.info("is_db_init_new开关打开，执行新的数据库初始化方式")
            log.info("需要执行的业务测试迭代为: {}".format(self.bis_pipeline_id))
            # basic_db_code = self.biz_base_db_code if self.biz_base_db_code else self.db_env
            # log.info("需要执行的业务的基础库集为: {}".format(basic_db_code))
            log.info("开始准备SQL制品")

            # 1、创建环境对应数据库制品目录
            db_cache_path = os.path.join(TP_AIO['root_path'], self.suite_code, "db")
            if not os.path.exists(db_cache_path):
                cmd = "mkdir -p {}".format(db_cache_path)
                exec_local_cmd(cmd)

            if self.bis_pipeline_id:
                biz_code = self.bis_pipeline_id.split('_')[0]
                biz_bind_app_list = get_biz_bind_app_list(biz_code)
                log.info("需要执行的业务为: {}, 关联的应用列表为：{}".format(biz_code, biz_bind_app_list))

                new_bo_dict = {}
                for k, v in bo_dict.items():
                    if k in biz_bind_app_list:
                        new_bo_dict[k] = v
                bo_dict = new_bo_dict
                log.info("过滤后的bo_dict=={}".format(bo_dict))

            if bo_dict:
                # 拉feature sql
                db_info_dict = handle_db_group_info(bo_dict)
                log.info("处理后的数据库信息：{}".format(json.dumps(db_info_dict, indent=4, sort_keys=True)))
                for db_group_name, db_info in db_info_dict.items():
                    db_group_repo_local_path = os.path.join(db_cache_path, db_group_name)
                    db_group_repo_master_local_path = os.path.join(db_cache_path, 'master', db_group_name)
                    clean_dir([db_group_repo_local_path, db_group_repo_master_local_path])
                    db_group_repo_master_archive_local_path = os.path.join(db_cache_path, 'master', db_group_name,
                                                                           'archive')
                    sql_repo_url = db_info.get('sql_repo_url')
                    log.info("in_transit_br_name:{}".format(db_info.get('in_transit_br_name')))
                    if db_info.get('in_transit_br_name'):
                        sql_br_name = db_info.get("in_transit_br_name")
                        checkout_repo(db_cache_path, db_group_repo_local_path, sql_repo_url, sql_br_name)
                        # 替换分支中的archive目录为master中的
                        db_group_repo_archive_local_path = os.path.join(db_group_repo_local_path,
                                                                        'archive')
                        clean_dir([db_group_repo_archive_local_path])
                        checkout_repo(db_cache_path, db_group_repo_master_local_path, sql_repo_url, 'master')
                        if os.path.exists(db_group_repo_master_archive_local_path):
                            cmd = 'mv {} {}'.format(db_group_repo_master_archive_local_path, db_group_repo_local_path)
                            exec_local_cmd(cmd)
                    else:
                        sql_br_name = db_info.get("archived_br_name")
                        checkout_repo(db_cache_path, db_group_repo_local_path, sql_repo_url, 'master')
                        # 删除master中在sql_br_name后的分支
                        remove_behind_of_br_branches_from_archive(db_group_repo_local_path, sql_br_name,
                                                                  db_info.get("asc_sorted_archive_branch_list"))
            else:
                log.info("无数据，跳过此步骤")


class DbSortSqlHandler(DbHandler):
    """53-数据库SQL分拣 fwm@2023-06-16"""
    filter_key_list = ['db_info_list']

    def exec(self, *args, **kwargs):
        super().exec(*args, **kwargs)
        bo_dict = kwargs.get('bo_dict')
        log.info("biz_db_init_flag:{}".format(self.biz_db_init_flag))
        if self.biz_db_init_flag == "false":
            self.biz_db_init_flag = False
        if self.biz_db_init_flag or self.type_enum == TypeEnum.UPDATE:
            if self.bis_pipeline_id:
                biz_code = self.bis_pipeline_id.split('_')[0]

                biz_bind_app_list = get_biz_bind_app_list(biz_code)
                log.info("需要执行的业务为: {}, 关联的应用列表为：{}".format(biz_code, biz_bind_app_list))

                new_bo_dict = {}
                for k, v in bo_dict.items():
                    if k in biz_bind_app_list:
                        new_bo_dict[k] = v
                bo_dict = new_bo_dict
                log.info("过滤后的bo_dict=={}".format(bo_dict))

            if bo_dict:
                log.info("is_db_init_new开关打开，执行新的数据库初始化方式")
                log.info("需要执行的业务测试迭代为: {}".format(self.bis_pipeline_id))
                # basic_db_code = self.biz_base_db_code if self.biz_base_db_code else self.db_env
                # log.info("需要执行的业务的基础库集为: {}".format(basic_db_code))
                flyway_cache_path = os.path.join(TP_AIO['root_path'], self.suite_code, "db",
                                                 TEST_DATA_INIT.get('flyway_cache_path'))
                db_cache_path = os.path.join(TP_AIO['root_path'], self.suite_code, "db")

                if not os.path.isdir(flyway_cache_path):
                    cmd = 'mkdir -p {}'.format(flyway_cache_path)
                    exec_local_cmd(cmd)
                else:
                    if len(flyway_cache_path.split('/')) > 2:
                        log.info("清空flyway缓存目录：{}".format(flyway_cache_path))
                        cmd = 'rm -rf {}'.format(os.path.join(flyway_cache_path, '*'))
                        exec_local_cmd(cmd)

                db_group_name_list = []
                db_name_list = []
                for k, v in bo_dict.items():
                    for db_info in v.get('db_info_list'):
                        db_name_list.append(db_info.get('db_name'))
                        if db_info.get('db_group_name') not in db_group_name_list:
                            db_group_name_list.append(db_info.get('db_group_name'))
                log.info("db_group_name_list:{}".format(db_group_name_list))

                if self.type_enum == TypeEnum.UPDATE:
                    # 增量copy DDL数据
                    try:
                        self.increment_flyway_sql_copy(db_group_name_list, db_cache_path, flyway_cache_path)
                    except Exception as e:
                        traceback.print_exc()
                        log.error("增量copy DDL数据失败:{}".format(str(e)))
                        raise Exception("增量copy DDL数据失败:{}".format(str(e)))
                # copy feature sql
                else:
                    dump_pipeline_id_list = get_dump_archive_pipeline_id(self.bis_pipeline_id)

                    available_dev_br = DbDumpMgt.get_new_after_dump_file_dev_iter_2(dump_pipeline_id_list,
                                                                                    self.bis_pipeline_id,
                                                                                    biz_bind_app_list)
                    # available_dev_br = DbDumpMgt.get_after_dump_file_dev_iter(self.biz_base_db_code, biz_bind_app_list)
                    for db_group_name in db_group_name_list:
                        feature_sql_path = os.path.join(db_cache_path, db_group_name)
                        feature_sql_db_list = [f.path for f in os.scandir(feature_sql_path) if
                                               f.is_dir() and not f.name.startswith('.')]
                        log.info('{}的子目录列表为: {}'.format(feature_sql_path, feature_sql_db_list))
                        for feature_sql_db in feature_sql_db_list:
                            if 'archive' in feature_sql_db:
                                # 多分支目录copy
                                log.info("")
                                archive_dir = os.path.join(feature_sql_path, 'archive')
                                # todo  加参数
                                sort_sql_common(archive_dir, flyway_cache_path, available_dev_br)
                            else:
                                # copy 库
                                cmd = 'cp -r {} {}'.format(feature_sql_db, flyway_cache_path)
                                exec_local_cmd(cmd)
            else:
                log.info("无数据，跳过此步骤")

    def increment_flyway_sql_copy(self, db_group_name_list, db_cache_path, flyway_cache_path):
        for db_group_name in db_group_name_list:
            # 获取数据库组在该环境下的部署的分支版本
            db_deploy_branch, copy_br_name_record, db_info_id = get_db_branch_by_suite_code_and_db_group_name(
                self.suite_code, db_group_name)
            log.info(
                "数据库分组{}在{}环境下的部署的分支版本为: {}".format(db_group_name, self.suite_code, db_deploy_branch))

            if db_deploy_branch:
                # 算增量版本
                increment_branches_list = get_db_archive_branch_list(db_group_name, db_deploy_branch)
                if copy_br_name_record:
                    copy_list = copy_br_name_record.split(',')
                    # 把copy_list中的元素加入到increment_branches_list中
                    increment_branches_list = list(set(increment_branches_list + copy_list))
                log.info("数据库分组{}在{}环境下的增量版本列表为: {}".format(db_group_name, self.suite_code,
                                                                             increment_branches_list))

                emd, created = EnvMgtDbDeployInfo.get_or_create(db_info_id=db_info_id,
                                                                suite_code=self.suite_code,
                                                                defaults={"copy_br_name_record": ','.join(
                                                                    increment_branches_list),
                                                                    "create_user": 'howbuyscm',
                                                                    "create_time": datetime.now()})
                if not created:
                    EnvMgtDbDeployInfo.update(copy_br_name_record=','.join(increment_branches_list),
                                              update_time=datetime.now(),
                                              update_user='howbuyscm').where(
                        EnvMgtDbDeployInfo.db_info_id == db_info_id,
                        EnvMgtDbDeployInfo.suite_code == self.suite_code).execute()
                feature_sql_path = os.path.join(db_cache_path, db_group_name)
                feature_sql_db_list = [f.path for f in os.scandir(feature_sql_path) if
                                       f.is_dir() and not f.name.startswith('.')]
                log.info('{}的子目录列表为: {}'.format(feature_sql_path, feature_sql_db_list))
                for feature_sql_db in feature_sql_db_list:
                    if 'archive' in feature_sql_db:
                        # 多分支目录copy
                        log.info("")
                        archive_dir = os.path.join(feature_sql_path, 'archive')
                        increment_sort_sql_common(archive_dir, flyway_cache_path, increment_branches_list)
            else:
                log.warn("数据库分组{}对应的数据库在{}环境下没有指定部署版本，跳过分拣SQL".format(db_group_name,
                                                                                                 self.suite_code))
                continue


class DbExecuteSqlHandler(DbHandler):
    """54-数据库SQL执行 fwm@2023-06-16"""
    filter_key_list = ['db_info_list']

    def exec(self, *args, **kwargs):
        super().exec(*args, **kwargs)

        bo_dict = kwargs.get('bo_dict')
        log.info("biz_db_init_flag:{}".format(self.biz_db_init_flag))
        if self.biz_db_init_flag == "false":
            self.biz_db_init_flag = False
        if self.biz_db_init_flag or self.type_enum == TypeEnum.UPDATE:
            log.info("is_db_init_new开关打开，执行新的数据库初始化方式")
            log.info("需要执行的业务测试迭代为: {}".format(self.bis_pipeline_id))
            # basic_db_code = self.biz_base_db_code if self.biz_base_db_code else self.db_env
            # log.info("需要执行的业务的基础库集为: {}".format(basic_db_code))
            log.info("bo_dict: {}".format(bo_dict))

            if self.bis_pipeline_id:
                biz_code = self.bis_pipeline_id.split('_')[0]
                # if is_derivative_business(biz_code):
                #     log.info("衍生业务，无需执行sql")
                #     return
                biz_bind_app_list = get_biz_bind_app_list(biz_code)
                log.info("需要执行的业务为: {}, 关联的应用列表为：{}".format(biz_code, biz_bind_app_list))

                new_bo_dict = {}
                for k, v in bo_dict.items():
                    if k in biz_bind_app_list:
                        new_bo_dict[k] = v
                bo_dict = new_bo_dict
                log.info("过滤后的bo_dict=={}".format(bo_dict))

            if bo_dict:
                new_db_info_list = []
                for k, v in bo_dict.items():
                    new_db_info_list.extend(v.get('db_info_list'))
                # 列表去重
                unique_dict_set = set(frozenset(d.items()) for d in new_db_info_list)
                new_db_info_list = [dict(s) for s in unique_dict_set]
                flyway_unique_db_info_list = []
                unique_db_info_list = []
                for new_db_info in new_db_info_list:
                    if (new_db_info.get("conn_url") + new_db_info.get("db_name")) not in flyway_unique_db_info_list:
                        flyway_unique_db_info_list.append(new_db_info.get("conn_url") + new_db_info.get("db_name"))
                        unique_db_info_list.append(new_db_info)
                new_db_info_list = unique_db_info_list
                log.info("待执行SQL的数据库列表: {}".format(new_db_info_list))
                if self.type_enum == TypeEnum.UPDATE:
                    for db_info in new_db_info_list:
                        if not get_db_deploy_info(self.suite_code, db_info.get("db_info_id")):
                            log.warn("数据库{}没有部署信息，跳过执行SQL".format(db_info.get("db_name")))
                            new_db_info_list.remove(db_info)
                # 优化使用线程池执行dml。zt@2024-08-12
                # 使用共享变量来捕获子线程中的异常
                self.thread_exceptions = []

                def thread_wrapper(target_func, *args):
                    """线程包装函数，用于捕获子线程中的异常"""
                    try:
                        target_func(*args)
                    except Exception as e:
                        self.thread_exceptions.append(str(e))
                        log.error(f"子线程执行异常: {str(e)}")

                threads = []
                ot = threading.Thread(target=thread_wrapper,
                                      args=(self.exec_sql_use_pool_with_type, new_db_info_list, 'oracle'))
                ot.start()
                threads.append(ot)
                mt = threading.Thread(target=thread_wrapper,
                                      args=(self.exec_sql_use_pool_with_type, new_db_info_list, 'mysql'))
                mt.start()
                threads.append(mt)
                for t in threads:
                    t.join()

                # 检查子线程中是否有异常，如果有则中断流程
                if self.thread_exceptions:
                    error_summary = '\n'.join(self.thread_exceptions)
                    log.error(f"数据库SQL执行过程中出现异常：\n{error_summary}")
                    raise Exception(f"数据库SQL执行过程中出现异常：{error_summary}")
                # self.exec_dml_sql_use_pool(new_db_info_list)
            else:
                log.info("无数据，跳过此步骤")

    def execute(self, new_db_info_list, db_type):
        threads = []
        for new_db_info in new_db_info_list:
            if new_db_info.get("db_srv_type") == db_type:
                t = threading.Thread(target=self.handle_db_info, args=(new_db_info, db_type, self.suite_code))
                t.start()
                threads.append(t)
        # Wait for all threads to finish
        for t in threads:
            t.join()
        log.info("所有{}数据库初始化完成".format(db_type))

    def exec_sql_use_pool_with_type(self, new_db_info_list, db_type):
        """分别使用MySQL和Oracle两个线程池执行dml。zt@2024-08-12"""
        """原方法名exec_sql_use_pool_with_type有误导，改名exec_sql_use_pool_with_type 20250530 by fwm"""
        type_db_info_list = [tmp_db_info for tmp_db_info in new_db_info_list
                             if tmp_db_info.get("db_srv_type") == db_type]

        if type_db_info_list:
            exceptions = []
            with ThreadPoolExecutor(max_workers=5) as sql_thread_executor:
                sql_futures = {
                    sql_thread_executor.submit(self.handle_db_info,
                                               sql_db_info,
                                               db_type,
                                               self.suite_code): sql_db_info
                    for sql_db_info in type_db_info_list
                }

            # Wait for all threads to finish
            for sql_future in as_completed(sql_futures, timeout=60 * 60 * 2):
                try:
                    data = sql_future.result()
                    log.info('>>>>{}线程池执行，完成1个: {}'.format(db_type, data))
                except Exception as exc:
                    # 获取对应的sql_db_info信息
                    sql_db_info = sql_futures[sql_future]
                    suite_db_name = sql_db_info.get("suite_db_name", "未知数据库")
                    error_msg = '数据库[{}] - %r generated an exception: %s' % (suite_db_name, sql_future, exc)
                    log.error(error_msg)
                    exceptions.append(error_msg)

            log.info('>>>>{}线程池执行，全部完成。'.format(db_type))

            # 如果有异常，打印所有异常信息并中断流水线
            if exceptions:
                error_summary = '\n'.join(exceptions)
                log.error(f"执行过程中出现以下异常：\n{error_summary}")
                # sys.exit(1)
                raise Exception(f"{db_type}数据库SQL执行过程中出现异常，请查看上方详细错误信息")

    # 方法没有用到，先注释掉  20250530 by fwm
    # def exec_dml_sql_use_pool(self, new_db_info_list):
    #     """分别使用MySQL和Oracle两个线程池执行dml。zt@2024-08-12"""
    #     mysql_db_info_list = [tmp_mysql_db_info for tmp_mysql_db_info in new_db_info_list
    #                           if tmp_mysql_db_info.get("db_srv_type") == 'mysql']
    #     oracle_db_info_list = [tmp_oracle_db_info for tmp_oracle_db_info in new_db_info_list
    #                            if tmp_oracle_db_info.get("db_srv_type") == 'oracle']
    #
    #     if mysql_db_info_list:
    #         with ThreadPoolExecutor(max_workers=5) as mysql_thread_executor:
    #             mysql_futures = {
    #                 mysql_thread_executor.submit(self.handle_db_info,
    #                                              mysql_db_info,
    #                                              'mysql',
    #                                              self.suite_code): mysql_db_info
    #                 for mysql_db_info in mysql_db_info_list
    #             }
    #
    #     if oracle_db_info_list:
    #         with ThreadPoolExecutor(max_workers=5) as oracle_thread_executor:
    #             oracle_futures = {
    #                 oracle_thread_executor.submit(self.handle_db_info,
    #                                               oracle_db_info,
    #                                               'oracle',
    #                                               self.suite_code): oracle_db_info
    #                 for oracle_db_info in oracle_db_info_list
    #             }
    #
    #     # 等优化部分：MySQL和Oracle是同时开始执行没有问题，但打印信息会先打印mysql的，然后打印oracle的。
    #     # Wait for mysql threads to finish
    #     if mysql_futures:
    #         for mysql_future in as_completed(mysql_futures, timeout=60 * 60 * 2):
    #             try:
    #                 data = mysql_future.result()
    #             except Exception as exc:
    #                 log.error('%r generated an exception: %s' % (mysql_future, exc))
    #             else:
    #                 log.info('>>>>mysql线程池执行，完成1个: %s' % data)
    #
    #     # Wait for oracle threads to finish
    #     if oracle_futures:
    #         for oracle_future in as_completed(oracle_futures, timeout=60 * 60 * 2):
    #             try:
    #                 data = oracle_future.result()
    #             except Exception as exc:
    #                 log.error('%r generated an exception: %s' % (oracle_future, exc))
    #             else:
    #                 log.info('>>>>oracle线程池执行，完成1个: %s' % data)

    def handle_db_info(self, new_db_info, db_type, suite_code):
        start_time = time.time()
        st = datetime.now()
        if new_db_info.get("db_srv_type") != db_type:
            return
        flyway_db_info = {}
        flyway_db_info['db_user'] = new_db_info.get("username")
        flyway_db_info['db_passwd'] = new_db_info.get("password")
        flyway_db_info['conn_url'] = new_db_info.get("conn_url")
        db_dir = os.path.join(TP_AIO['root_path'], suite_code, "db",
                              TEST_DATA_INIT.get('flyway_cache_path'), new_db_info.get("db_name"))
        try:
            ora_dml_root_dir = os.path.join(db_dir, 'DML')
            mv_seq_to_ddl = os.path.join(ora_dml_root_dir, 'seq')
            db_dir = os.path.join(db_dir, 'DDL')
            if os.path.exists(mv_seq_to_ddl):
                try:
                    log.info("mv_seq_to_ddl: {}".format(mv_seq_to_ddl))
                    cmd = 'mv {}/** {}/'.format(mv_seq_to_ddl, db_dir)
                    exec_local_cmd(cmd)
                    cmd = 'rm -rf {}'.format(mv_seq_to_ddl)
                    exec_local_cmd(cmd)
                except Exception as e:
                    log.error('执行rm -rf 异常：{}'.format(e))

            exec_flyway_command(flyway_db_info, 'repair', db_dir)
            try:
                exec_flyway_command(flyway_db_info, 'migrate', db_dir)
                pipeline_id = get_latest_pipeline_id_by_db_group_name(new_db_info.get("db_group_name"))
                log.info("需要记录的pipeline_id:{}".format(pipeline_id))
                emd, created = EnvMgtDbDeployInfo.get_or_create(db_info_id=new_db_info.get("db_info_id"),
                                                                suite_code=self.suite_code,
                                                                defaults={"pipeline_id": pipeline_id,
                                                                          "create_user": 'howbuyscm',
                                                                          "create_time": datetime.now()})
                if not created:
                    EnvMgtDbDeployInfo.update(pipeline_id=pipeline_id, update_time=datetime.now(),
                                              update_user='howbuyscm').where(
                        EnvMgtDbDeployInfo.db_info_id == new_db_info.get("db_info_id"),
                        EnvMgtDbDeployInfo.suite_code == self.suite_code).execute()
            except Exception as ex:
                traceback.print_exc()
                log.error("flyway migrate执行失败")
                raise ex

            if self.type_enum == TypeEnum.UPDATE:
                log.info("{}类型的部署，不执行DML!".format(self.type_enum))
            else:
                if new_db_info.get("db_srv_type") == 'oracle':
                    exec_dml(ora_dml_root_dir, new_db_info)
                else:
                    exec_mysql_dml(ora_dml_root_dir, new_db_info)
            log.info("{}:数据库初始化完成".format(new_db_info.get("db_name")))
        except Exception as ex:
            traceback.print_exc()
            raise Exception(ex)
        finally:
            end_time = time.time()
            et = datetime.now()
            execution_time = end_time - start_time
            log.info("{}: 数据库初始化完成，开始时间：{}，结束时间：{}，执行耗时：{}秒".format(
                new_db_info.get("db_name"),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time)),
                time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time)),
                execution_time
            ))
            dml_path = os.path.join(TP_AIO['root_path'],
                                    suite_code,
                                    "db",
                                    TEST_DATA_INIT.get('flyway_cache_path'),
                                    new_db_info.get("db_name"),
                                    "DML")
            # 数据库初始化耗时日志增加。zt@2024-08-12
            db_init_cost_time_for_log(dml_path, new_db_info, st, et)


def db_init_cost_time_for_log(dml_path, db_info_map, start_time, end_time) -> None:
    """数据库初始化日志增加。zt@2024-08-12"""
    if not end_time:
        end_time = datetime.now()
    # 1、数据库信息
    db_srv_hosts = db_info_map.get("db_srv_hosts")
    db_srv_type = db_info_map.get("db_srv_type")
    suite_db_name = db_info_map.get("suite_db_name")
    # 2、耗时信息
    start_time_str = start_time.strftime("%Y-%m-%d %H:%M:%S")
    end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")
    ct = round((end_time - start_time).total_seconds(), 2)
    # 3、sql文件信息
    dml_sql_file_count = 0
    dml_sql_file_size = 0
    # 目录存在
    if os.path.exists(dml_path):
        dml_sql_dir_list = os.listdir(dml_path)
        # 文件列表
        if dml_sql_dir_list:
            sql_count = 0
            sql_size = 0
            for tmp_file in dml_sql_dir_list:
                if tmp_file.endswith(".sql"):
                    sql_file_path = os.path.join(dml_path, tmp_file)
                    sql_count = sql_count + 1
                    # bytes --> KB
                    sql_size = sql_size + os.path.getsize(sql_file_path) / 1024
            # sql文件信息汇总
            dml_sql_file_count = sql_count
            dml_sql_file_size = round(sql_size, 2)

    # 4、汇总信息
    log_map = {
        "db_hosts": db_srv_hosts,
        "db_type": db_srv_type,
        "db_name": suite_db_name,
        "start_time": start_time_str,
        "end_time": end_time_str,
        "cost_time": ct,
        "sql_file_count": dml_sql_file_count,
        "sql_file_size": dml_sql_file_size,
    }
    msg = ">>>>数据库初始化耗时(秒，KB)：{}".format(json.dumps(log_map, indent=None, ensure_ascii=False))
    log.info(msg)


# 按照既定逻辑过滤数据库版本
def handle_db_group_info(bo_dict):
    batch_db_info_dict = {}
    try:
        for k, v in bo_dict.items():
            db_info_list = v.get('db_info_list')
            selected_br_name = v.get("selected_br_name")
            archive_br_name = v.get("archive_br_name")
            for db_info in db_info_list:
                db_group_name = db_info.get("db_group_name")
                db_name = db_info.get("db_name")
                if batch_db_info_dict.get(db_group_name):
                    batch_db_info_dict.get(db_group_name)['selected_br_names'].append(selected_br_name)
                    batch_db_info_dict.get(db_group_name)['archive_br_names'].append(archive_br_name)
                    batch_db_info_dict.get(db_group_name)['db_names'].append(db_name)
                else:
                    db_info_dict = {
                        'db_group_name': db_group_name,
                        'db_names': [db_name],
                        'selected_br_names': [selected_br_name],
                        'archive_br_names': [archive_br_name],
                        'sql_repo_url': os.path.join(PRODUCT_STORE_SQL.get('url'), db_group_name + '.git')
                    }
                    batch_db_info_dict[db_group_name] = db_info_dict
        # 如果选了多个分支，优先选用在途迭代的，如果没有，则选用最新归档迭代;
        for k, v in batch_db_info_dict.items():
            if v.get('selected_br_names'):
                br_names = v.get('selected_br_names')
                log.info("=========br_names:{}".format(br_names))
                db_names = list(set(v.get('db_names'))) if v.get('db_names') else []
                log.info("=========v['db_names']:{}".format(db_names))
                in_transit_branch_list = []
                if db_names:
                    in_transit_branch_list.extend(get_db_in_transit_br_names(db_names))
                in_transit_branch_list = list(set(in_transit_branch_list))
                log.info("=========in_transit_branch_list:{}".format(in_transit_branch_list))
                in_transits = []
                if in_transit_branch_list:
                    in_transits = list(set(br_names) & set(in_transit_branch_list))
                if in_transits:
                    # 随机一个在途迭代（流程上不允许选多个在途迭代）
                    v['in_transit_br_name'] = in_transits[0]
                else:
                    archive_branch_list = get_db_archive_branch_list(v.get("db_group_name"))
                    log.info("v['db_names']:archive_branch_list:{}".format(archive_branch_list))
                    v['asc_sorted_archive_branch_list'] = archive_branch_list
                    # 确保选中的分支也在归档分支列表中
                    common_branches = list(set(br_names) & set(archive_branch_list))
                    # 选中的版本按照最近归档版本的顺序排序（升序）
                    sorted_branches = sorted(common_branches, key=lambda x: archive_branch_list.index(x))
                    # 取最后归档的迭代
                    log.info("选中的版本按照最近归档版本的顺序排序（升序）: {}".format(sorted_branches))
                    v['archived_br_name'] = sorted_branches[-1]
                    log.info("取最后归档的迭代: {}".format(v['archived_br_name']))
            else:
                if v.get('archive_br_names'):
                    archive_branch_list = get_db_archive_branch_list(v.get("db_group_name"))
                    # 选中的版本按照最近归档版本的顺序排序（升序）
                    sorted_branches = sorted(v.get('archive_br_names'), key=lambda x: archive_branch_list.index(x))
                    # 取最后归档的迭代
                    log.info("选中的版本按照最近归档版本的顺序排序（升序）: {}".format(sorted_branches))
                    v['archived_br_name'] = sorted_branches[-1]
                    log.info("取最后归档的迭代: {}".format(v['archived_br_name']))
    except Exception as ex:
        traceback.print_exc()
        raise ex
    return batch_db_info_dict


def clean_dir(params):
    for dir in params:
        cmd = "rm -rf {}".format(dir)
        exec_local_cmd(cmd)


def remove_behind_of_br_branches_from_archive(db_group_repo_local_path, sql_br_name, asc_sorted_archive_branch_list):
    db_group_repo_local_archive_path = os.path.join(db_group_repo_local_path, "archive")
    asc_sorted_archive_branch_list_distinct = []
    # asc_sorted_archive_branch_list 去重
    for item in asc_sorted_archive_branch_list:
        if item not in asc_sorted_archive_branch_list_distinct:
            asc_sorted_archive_branch_list_distinct.append(item)
    removed_branches = asc_sorted_archive_branch_list_distinct[
                       asc_sorted_archive_branch_list_distinct.index(sql_br_name) + 1:]
    for removed_branch in removed_branches:
        removed_branch_path = os.path.join(db_group_repo_local_archive_path, removed_branch)
        if os.path.exists(removed_branch_path):
            cmd = "rm -rf {}".format(removed_branch_path)
            exec_local_cmd(cmd)
        else:
            log.info("db branch {} not exist".format(removed_branch))


def batch_log_exec_sql_file(wait_log_sql_files, iteration_id, batch_no):
    if not iteration_id:
        return
    if not batch_no:
        return
    if not wait_log_sql_files:
        log.info("无 sql执行需要记录")
        return
    for sql_file in wait_log_sql_files:
        DbMgtExecSqlFileHistory.get_or_create(iteration_id=iteration_id,
                                              batch_no=batch_no,
                                              suite_code=sql_file['suite_code'],
                                              db_name=sql_file['db_name'],
                                              sql_ver_name=sql_file['sql_ver_name'],
                                              sql_file_hash=sql_file['sql_file_hash'],
                                              defaults={'create_user': "scm",
                                                        'create_time': datetime.now(),
                                                        'update_user': "scm",
                                                        'update_time': datetime.now(),
                                                        })


def exec_dml(ora_dml_root_dir, db_info, wait_log_sql_dml_files=None, iteration_id=None,
             batch_no=None):
    flag = os.path.exists(ora_dml_root_dir)
    if not flag:
        log.error('路径不存在：{}'.format(ora_dml_root_dir))
    else:
        dir_list = os.listdir(ora_dml_root_dir)
        if not dir_list:
            log.warn('路径下无文件或文件夹：{}'.format(ora_dml_root_dir))
            return
        dml_files = [f for f in dir_list if f.endswith(".sql")]
        log.info("dml总数:{}".format(len(dml_files)))
        exec_dml_fc(ora_dml_root_dir=ora_dml_root_dir,
                    db_info=db_info)
        batch_log_exec_sql_file(wait_log_sql_dml_files, iteration_id=iteration_id, batch_no=batch_no)
        log.info("dml执行完成")


def exec_mysql_dml(ora_dml_root_dir, db_info, wait_log_sql_dml_files=None, iteration_id=None,
                   batch_no=None):
    flag = os.path.exists(ora_dml_root_dir)
    if not flag:
        log.error('路径不存在：{}'.format(ora_dml_root_dir))
    else:
        dir_list = os.listdir(ora_dml_root_dir)
        if not dir_list:
            log.warn('路径下无文件或文件夹：{}'.format(ora_dml_root_dir))
            return
        dml_files = [f for f in dir_list if f.endswith(".sql")]
        log.info("dml总数:{}".format(len(dml_files)))
        exec_mysql_dml_fc(ora_dml_root_dir=ora_dml_root_dir,
                          db_info=db_info)
        batch_log_exec_sql_file(wait_log_sql_dml_files, iteration_id=iteration_id, batch_no=batch_no)
        log.info("dml执行完成")


def exec_dml_fc(files=None, ora_dml_root_dir=None, db_info=None, wait_log_sql_dml_files=None):
    exec_dml_script = generate_exec_oracle_dml_script(db_info, ora_dml_root_dir)
    log.info("开始执行exec_dml_script:{}".format(exec_dml_script))
    exec_local_cmd("sh {exec_dml_script}".format(exec_dml_script=exec_dml_script), timeout=1800)


def generate_exec_oracle_dml_script(db_info, ora_dml_root_dir):
    host = None
    sid = None
    db_user = None
    db_passwd = None
    if db_info.get("db_srv_hosts"):
        host = db_info.get("db_srv_hosts")
        sid = db_info.get("db_srv_name")
        db_user = db_info.get("username")
        db_passwd = db_info.get("password")
    else:
        host = db_info.get("db_hosts")
        sid = db_info.get("db_name")
        db_user = db_info.get("db_user")
        db_passwd = db_info.get("db_passwd")
    exec_sql_script = '''
#!/bin/bash
start_time_total=$(date +%s.%N)
echo "当前执行用户是: $(whoami)"
source ~/.bash_profile
# Define the directory containing the files
directory="{ora_dml_root_dir}"
cd $directory
# Iterate over each file in the directory
for file in $directory/*
do
  # Execute the MySQL command for the current file
  #echo "begin;" > temp.sql
  cat "$file" > temp.sql
  #echo "commit;" >> temp.sql
  # 执行SQL并计时
  start_time=$(date +%s.%N) # 获取更精确的时间戳（包括毫秒）
  (echo set arraysize 5000; echo set autocommit off; echo set define off; echo @temp.sql;echo commit; echo exit) |  sqlplus {db_user}/{db_pass}@//{db_host}:1521/{db_sid} &>> "$file"_log.txt
  end_time=$(date +%s.%N)
  # 计算并打印执行时间
  execution_time=$(echo "$end_time - $start_time" | bc)
  # 如果需要转换为毫秒
  execution_time_ms=$(echo "$execution_time * 1000" | bc)
  echo "sql:$file Execution time in milliseconds: $execution_time_ms ms"
  # Remove temporary sql file
  rm temp.sql
done
# Iterate over each file in the directory
# Record the end time
end_time_total=$(date +%s.%N)

# Calculate and print the total execution time
execution_time_total=$(echo "$end_time_total - $start_time_total" | bc)
execution_time_total_ms=$(echo "$execution_time_total * 1000" | bc)
echo "Total execution time in milliseconds: $execution_time_total_ms ms"
        '''.format(ora_dml_root_dir=ora_dml_root_dir, db_host=host,
                   db_sid=sid,
                   db_user=db_user, db_pass=db_passwd)
    cmd = '''cd {exec_sql_script_file_path};echo '{sql_script}'>{suite_db_name}.sql'''.format(
        exec_sql_script_file_path="/data/app/env/sql_script",
        sql_script=exec_sql_script,
        suite_db_name=db_user)
    exec_local_cmd(cmd)
    return "/data/app/env/sql_script/{}.sql".format(db_user)


def exec_mysql_dml_fc(ora_dml_root_dir=None, db_info=None):
    exec_dml_script = generate_exec_mysql_dml_script(db_info, ora_dml_root_dir)
    log.info("开始执行exec_dml_script:{}".format(exec_dml_script))
    exec_local_cmd("sh {exec_dml_script}".format(exec_dml_script=exec_dml_script), timeout=1500)


def generate_exec_mysql_dml_script(db_info, ora_dml_root_dir):
    exec_sql_script = '''
#!/bin/bash
# Define the directory containing the files
# Record the start time
start_time_total=$(date +%s.%N)
directory="{ora_dml_root_dir}"
cd $directory
# Iterate over each file in the directory
for file in $directory/*
do
  # Execute the MySQL command for the current file
  判断$file是不是以dump_dml.sql结尾文件
  if [[ $file == *dump_dml.sql ]]; then
      cat "$file" > temp.sql
  else
      echo "begin;" > temp.sql
      cat "$file" >> temp.sql
      echo "commit;" >> temp.sql
  fi
  # 执行SQL并计时
  start_time=$(date +%s.%N) # 获取更精确的时间戳（包括毫秒）
  result=`mysql -h {db_host} -P{db_port} -u{db_user}  -p"{db_pass}" -N {db} < temp.sql 2>&1`
  # mysql -h {db_host} -P{db_port} -u{db_user}  -p{db_pass} -N {db} < temp.sql
  end_time=$(date +%s.%N)
  echo "mysql executed is: $result "
  # 计算并打印执行时间
  execution_time=$(echo "$end_time - $start_time" | bc)
  # 如果需要转换为毫秒
  execution_time_ms=$(echo "$execution_time * 1000" | bc)
  echo "sql:$file Execution time in milliseconds: $execution_time_ms ms"
  # Remove temporary sql file
  rm temp.sql
done
# Iterate over each file in the directory
# Record the end time
end_time_total=$(date +%s.%N)

# Calculate and print the total execution time
execution_time_total=$(echo "$end_time_total - $start_time_total" | bc)
execution_time_total_ms=$(echo "$execution_time_total * 1000" | bc)
echo "Total execution time in milliseconds: $execution_time_total_ms ms"
        '''.format(ora_dml_root_dir=ora_dml_root_dir, db_host=db_info.get("db_srv_hosts"),
                   db_port=3306,
                   db_user=db_info.get("username"), db_pass=db_info.get("password"), db=db_info.get("suite_db_name"))
    cmd = '''cd {exec_sql_script_file_path};echo '{sql_script}'>{suite_db_name}.sql'''.format(
        exec_sql_script_file_path="/data/app/env/sql_script",
        sql_script=exec_sql_script,
        suite_db_name=db_info.get("suite_db_name"))
    exec_local_cmd(cmd)
    return "/data/app/env/sql_script/{}.sql".format(db_info.get("suite_db_name"))


class DbInitDbMasterSqlHandler:
    def exec(self, *args):
        new_db_info_list = args[0]
        log.info("数据库列表: {}".format(new_db_info_list))
        suite_db_list = []
        suite_code = 'it999'
        for new_db_info in new_db_info_list:
            try:
                if new_db_info.get("db_srv_type") != 'oracle':
                    self._mysql_restore(new_db_info.get("suite_db_name"), new_db_info.get("suite_db_name") + '.sql', 10)
                else:
                    suite_db_list.append(new_db_info.get("suite_db_name"))
                    # oracle_opt_list = [14, 13, 11, 12, 9, 15]
                    # for opt_type in oracle_opt_list:
                    #     try:
                    #         self._oracle_opt(new_db_info, opt_type)
                    #     except Exception as e:
                    #         log.error(traceback.format_exc())
                    # self._oracle_restore_opt(new_db_info.get("suite_db_name"))
            except Exception as ex:
                traceback.print_exc()
                log.error(str(ex))
                log.error("恢复数据库：{}:失败".format(new_db_info.get("suite_db_name")))
        opt_type = 16
        dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)
        for suite_db_name in suite_db_list:
            try:
                oh = OracleHandler("spider", suite_code, [], "PA-TEST_master")
                log.info("======开始执行数据库，数据库名：{}======".format(suite_db_name))
                oh._execute_sql_cmd(dump_file_path="/data/dml/",
                                    sql_script=dos.script.format(username=suite_db_name),
                                    username=suite_db_name, db_srv_hosts="***************",
                                    db_srv_username="oracle",
                                    db_srv_password="oracle123",
                                    db_srv_bash_profile="~/.bash_profile",
                                    opt_type=opt_type, is_not_sys=True, db_srv_name=None,
                                    password="howbuy2015")
                log.info("======结束执行数据库，数据库名：{}======".format(new_db_info.get("db_name")))
                self._oracle_restore_opt(suite_db_name)
            except Exception as e:
                traceback.print_exc()
                log.error(str(e))

    def _mysql_restore(self, db_name, dump_file_name, opt_type):
        log.info("======开始执行数据库恢复，数据库名：{}======".format(db_name))
        dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)
        log.info('开始生成sql文件')
        cmd = '''cd {dump_file_path};echo "{sql_script}">{db_name}.sql'''.format(
            dump_file_path="/data/dmp/mysqlbak/restore",
            sql_script=dos.script.format(db_name=db_name), db_name=db_name)
        log.info("生成sql文件命令：{}".format(cmd))
        res1 = exec_remote_cmd_by_username_password("***************", cmd, "root", "howbuy1!")

        log.info(res1)
        cmd = '''mysql --socket={db_srv_socket_path} -P3306 -u{username} -p{password} -N  < {dump_file_path}/{dump_file_name}
                                      '''.format(db_srv_socket_path="/data/mysql/data/mysql.sock",
                                                 username="admin", password="admin",
                                                 suite_db_name=db_name,
                                                 dump_file_path="/data/dmp/mysqlbak/restore",
                                                 dump_file_name=dump_file_name)
        log.info("恢复数据库命令为：{}".format(cmd))

        res = exec_remote_cmd_by_username_password("***************", cmd, "root", "howbuy1!")
        log.info(res)
        log.info("======结束执行恢复数据库，数据库名：{}======".format(db_name))

    # def _oracle_opt(self, db_name, opt_type):
    #     log.info("======开始执行数据库恢复，数据库名：{}======".format(db_name))
    #     dos = DbMgtOptScript.get(DbMgtOptScript.type == opt_type)
    #     log.info(dos.script)
    #     log.info('开始生成sql文件')
    #     cmd = '''"echo '{sql_script}'>{dump_file_path}{username}.sql"'''.format(dump_file_path="/data/dml/",
    #                                                                             sql_script=dos.script.format(
    #                                                                                 username=db_name),
    #                                                                             username=db_name)
    #     log.info("生成sql文件命令：{}".format(cmd))
    #     sshpass_cmd = "sshpass -p {} ssh oracle@{} {}".format('oracle123', "***************", cmd)
    #     res = exec_local_cmd(sshpass_cmd)
    #     log.info(res)
    #     log.info('开始执行sql文件')
    #     cmd = '''"source {db_srv_bash_profile};sqlplus / as sysdba @{dump_file_path}/{username}.sql
    #                       "'''.format(db_srv_bash_profile="~/.bash_profile", dump_file_path="/data/dml",
    #                                   username=db_name,
    #                                   username_prefix=db_name)
    #     log.info(cmd)
    #     sshpass_cmd = "sshpass -p {} ssh oracle@{} {}".format('oracle123', "***************", cmd)
    #     res2 = exec_local_cmd(sshpass_cmd)
    #     log.info(res2)
    #     log.info('移除生成的sql文件')
    #     cur_time = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    #     cmd = '''mv {dump_file_path}/{username}.sql /home/<USER>/bak/{username}.sql.{opt_type}.bak{cur_time}
    #                           '''.format(dump_file_path="/data/dml", username=db_name, opt_type=opt_type,
    #                                      cur_time=cur_time)
    #     log.info(cmd)
    #     sshpass_cmd = "sshpass -p {} ssh oracle@{} {}".format('oracle123', "***************", cmd)
    #     res3 = exec_local_cmd(sshpass_cmd)
    #     log.info(res3)
    #     log.info("======结束执行恢复数据库，数据库名：{}======".format(db_name))

    def _oracle_restore_opt(self, db_name):
        cmd = '''source {db_srv_bash_profile}; impdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile={dump_file_name}.dmp remap_schema={db_name}:{username} table_exists_action=replace transform=oid:n
              '''.format(db_srv_bash_profile="~/.bash_profile",
                         dump_file_name=db_name,
                         dump_bis_code=db_name.upper(),
                         db_name=db_name,
                         username=db_name)
        res = exec_remote_cmd_by_username_password("***************", cmd, "oracle", "oracle123")

        log.info("======结束执行恢复数据库，数据库名：{}======,res:{}".format(db_name, res))


class DbPullMasterSqlHandler:
    """52-数据库SQL制品准备 fwm@2023-06-16"""

    def exec(self, *args):
        log.info("开始准备SQL制品")
        # 创建环境对应数据库制品目录
        db_cache_path = os.path.join(TP_AIO['root_path'], 'basic', "db")
        if not os.path.exists(db_cache_path):
            cmd = "mkdir -p {}".format(db_cache_path)
            exec_local_cmd(cmd)
        # 拉feature sql
        db_group_name_list = []
        db_info_list = args[0]
        log.info("db_info_list:{}".format(db_info_list))
        if not db_info_list:
            return
        sql_br_name = 'master'
        for db_info in db_info_list:
            db_group_name = db_info.get("db_group_name")
            # 一个数据库组的仓库只拉一次
            if db_group_name not in db_group_name_list:
                db_group_name_list.append(db_group_name)
                db_group_repo_local_path = os.path.join(db_cache_path, db_group_name)
                sql_repo_url = os.path.join(PRODUCT_STORE_SQL.get('url'), db_group_name + '.git')
                checkout_repo(db_cache_path, db_group_repo_local_path, sql_repo_url, sql_br_name)

        # # 拉testing sql
        # basic_db_code = 'basic'
        # bis_type_git = basic_db_code + '.git'
        # bis_sql_repo_url = os.path.join(PRODUCT_STORE_SQL.get('bis_url'), bis_type_git)
        # # 克隆基础库的master分支
        # bis_sql_repo_local_path = os.path.join(db_cache_path, basic_db_code)
        # checkout_repo(db_cache_path, bis_sql_repo_local_path, bis_sql_repo_url, sql_br_name)


class DbSortMasterSqlHandler:
    """53-数据库SQL分拣 fwm@2023-06-16"""

    def exec(self, *args):
        flyway_cache_path = os.path.join(TP_AIO['root_path'], 'basic', "db",
                                         TEST_DATA_INIT.get('flyway_cache_path'))
        db_cache_path = os.path.join(TP_AIO['root_path'], 'basic', "db")

        if not os.path.isdir(flyway_cache_path):
            cmd = 'mkdir -p {}'.format(flyway_cache_path)
            exec_local_cmd(cmd)
        else:
            if len(flyway_cache_path.split('/')) > 2:
                log.info("清空flyway缓存目录：{}".format(flyway_cache_path))
                cmd = 'rm -rf {}'.format(os.path.join(flyway_cache_path, '*'))
                exec_local_cmd(cmd)

        db_group_name_list = []
        db_info_list = args[0]
        log.info("db_info_list:{}".format(db_info_list))
        if not db_info_list:
            return
        for db_info in db_info_list:
            if db_info.get('db_group_name') not in db_group_name_list:
                db_group_name_list.append(db_info.get('db_group_name'))
        # copy feature sql
        for db_group_name in db_group_name_list:
            feature_sql_path = os.path.join(db_cache_path, db_group_name)
            feature_sql_db_list = [f.path for f in os.scandir(feature_sql_path) if
                                   f.is_dir() and not f.name.startswith('.')]
            log.info('{}的子目录列表为: {}'.format(feature_sql_path, feature_sql_db_list))
            for feature_sql_db in feature_sql_db_list:
                if 'archive' in feature_sql_db:
                    # 多分支目录copy
                    archive_dir = os.path.join(feature_sql_path, 'archive')
                    sort_sql_common(archive_dir, flyway_cache_path, {})
                else:
                    # copy 库
                    cmd = 'cp -r {} {}'.format(feature_sql_db, flyway_cache_path)
                    exec_local_cmd(cmd)

        # copy 基础库
        basic_db_code = 'basic'
        biz_dir = os.path.join(db_cache_path, basic_db_code)
        biz_sql_sort(biz_dir, db_group_name_list, flyway_cache_path)


class DbPullBranchSqlFromMasterHandler:
    """52-数据库SQL制品准备 fwm@2023-06-16"""

    def exec(self, *args):
        # 创建环境对应数据库制品目录
        db_cache_path = os.path.join(TP_AIO['root_path'], 'feature_basic', "db")
        if not os.path.exists(db_cache_path):
            cmd = "mkdir -p {}".format(db_cache_path)
            exec_local_cmd(cmd)
        # 拉feature sql
        db_group_name_list = []
        db_info_list = args[0]
        log.info("db_info_list:{}".format(db_info_list))
        if not db_info_list:
            return
        sql_br_name = 'master'
        for db_info in db_info_list:
            db_group_name = db_info.get("db_group_name")
            # 一个数据库组的仓库只拉一次
            if db_group_name not in db_group_name_list:
                time.sleep(1)
                db_group_name_list.append(db_group_name)
                db_group_repo_local_path = os.path.join(db_cache_path, db_group_name, sql_br_name)
                sql_repo_url = os.path.join(PRODUCT_STORE_SQL.get('url'), db_group_name + '.git')
                checkout_repo(db_cache_path, db_group_repo_local_path, sql_repo_url, sql_br_name)


class DbSortBranchSqlFromMasterHandler:
    """53-数据库SQL分拣 fwm@2023-06-16"""

    def exec(self, *args):
        now = datetime.now()
        flyway_cache_path = os.path.join(TP_AIO['root_path'], 'feature_basic', "db",
                                         TEST_DATA_INIT.get('flyway_cache_path'))
        db_cache_path = os.path.join(TP_AIO['root_path'], 'feature_basic', "db")

        if not os.path.isdir(flyway_cache_path):
            cmd = 'mkdir -p {}'.format(flyway_cache_path)
            exec_local_cmd(cmd)
        else:
            if len(flyway_cache_path.split('/')) > 2:
                log.info("清空flyway缓存目录：{}".format(flyway_cache_path))
                cmd = 'rm -rf {}'.format(os.path.join(flyway_cache_path, '*'))
                exec_local_cmd(cmd)

        db_group_name_list = []
        db_info_list = args[0]
        log.info("db_info_list:{}".format(db_info_list))
        if not db_info_list:
            return
        for db_info in db_info_list:
            sql_br_name = db_info.get("br_name")
            db_name = db_info.get("db_name")
            if sql_br_name and os.path.join(db_info.get('db_group_name'), sql_br_name) not in db_group_name_list:
                db_group_name_list.append(
                    (os.path.join(db_info.get('db_group_name'), 'master', 'archive', sql_br_name), db_name))
        # copy feature sql
        for db_group_name in db_group_name_list:
            feature_sql_path = os.path.join(db_cache_path, db_group_name[0])
            target_sql_path = os.path.join(flyway_cache_path, db_group_name[1])
            if not os.path.exists(feature_sql_path):
                continue
            if not os.path.exists(target_sql_path):
                cmd = 'mkdir -p {}'.format(target_sql_path)
                exec_local_cmd(cmd)
            cmd = 'cp -r {} {}'.format(feature_sql_path, target_sql_path)
            exec_local_cmd(cmd)


class DbSyncBranchSqlToTestingHandler:
    """53-数据库SQL分拣 fwm@2023-06-16"""

    def exec(self, *args):
        now = datetime.now()
        db_info_list = args[0]
        log.info("db_info_list:{}".format(db_info_list))
        flyway_cache_path = os.path.join(TP_AIO['root_path'], 'feature_basic', "db",
                                         TEST_DATA_INIT.get('flyway_cache_path'))
        if not db_info_list:
            return
        for db_info in db_info_list:
            db_name = db_info.get("db_name")
            sql_br_name = db_info.get("br_name")
            remote_dir = os.path.join("/data/db_script", db_name, sql_br_name)
            local_dir = os.path.join(flyway_cache_path, sql_br_name, db_name)
            if not os.path.exists(local_dir):
                log.info("不存在:{},跳过同步".format(local_dir))
                continue
            local_dir = os.path.join(local_dir, "/")
            # copy 库
            remote_cmd = "sshpass -p howbuy1! scp -r {flyway_cache_path} root@***************:{remote_dir}".format(
                flyway_cache_path=local_dir,
                remote_dir=remote_dir)
            # 隐藏密码信息
            remote_cmd_log = remote_cmd.replace("howbuy1!", "****")
            log.info("执行远程命令：{}".format(remote_cmd_log))
            exec_local_cmd(remote_cmd)


class DbExecBranchSqlFromMasterHandler:
    def exec(self, *args):
        db_info_list = args[0]
        log.info("数据库列表: {}".format(db_info_list))
        db_group_name_list = []
        new_db_info_list = []
        for db_info in db_info_list:
            sql_br_name = db_info.get("br_name")
            if os.path.join(db_info.get('db_group_name'), sql_br_name) not in db_group_name_list:
                db_group_name_list.append(os.path.join(db_info.get('db_group_name'), sql_br_name))
                new_db_info_list.append(db_info)
        log.info("去重后数据库列表: {}".format(new_db_info_list))
        for new_db_info in new_db_info_list:
            flyway_db_info = {}
            db_dir = os.path.join(TP_AIO['root_path'], 'feature_basic', "db",
                                  TEST_DATA_INIT.get('flyway_cache_path'), new_db_info.get("db_name"))
            ora_dml_root_dir = os.path.join(db_dir, 'DML')
            try:
                if new_db_info.get("db_srv_type") != 'oracle':
                    flyway_db_info['db_user'] = new_db_info.get("db_user")
                    flyway_db_info['db_passwd'] = new_db_info.get("db_passwd")
                    flyway_db_info['conn_url'] = new_db_info.get("conn_url")
                    log.info("flyway_db_info:{},clean,db_dir:{}".format(flyway_db_info, db_dir))
                    exec_flyway_command(flyway_db_info, 'clean', db_dir)
                else:
                    flyway_db_info['db_user'] = new_db_info.get("db_user")
                    flyway_db_info['db_passwd'] = new_db_info.get("db_passwd")
                    flyway_db_info['conn_url'] = new_db_info.get("conn_url")
                    db_dir = os.path.join(db_dir, 'DDL')
                    log.info("flyway_db_info:{},repair,db_dir:{}".format(flyway_db_info, db_dir))
                    exec_flyway_command(flyway_db_info, 'repair', db_dir)
                exec_flyway_command(flyway_db_info, 'migrate', db_dir)
                if new_db_info.get("db_srv_type") == 'oracle':
                    new_db_info['db_srv_hosts'] = new_db_info.get("db_hosts")
                    new_db_info['db_srv_port'] = new_db_info.get("db_port")
                    new_db_info['username'] = new_db_info.get("db_user")
                    new_db_info['password'] = new_db_info.get("db_passwd")
                    new_db_info['db_srv_name'] = new_db_info.get("db_srv_name")
                    log.info("flyway_db_info:{},exec_dml,ora_dml_root_dir:{}".format(flyway_db_info, ora_dml_root_dir))
                    log.info("new_db_info:{}".format(flyway_db_info, new_db_info))
                    exec_dml(ora_dml_root_dir, new_db_info)
            except Exception as e:
                traceback.print_exc()
                log.error("flyway migrate执行失败")
                raise e


class DbExecuteMasterSqlHandler:
    def exec(self, *args):
        new_db_info_list = args[0]
        log.info("数据库列表: {}".format(new_db_info_list))
        exit_flag = False
        error_db_name_list = []
        for new_db_info in new_db_info_list:
            flyway_db_info = {}
            db_dir = os.path.join(TP_AIO['root_path'], 'basic', "db",
                                  TEST_DATA_INIT.get('flyway_cache_path'), new_db_info.get("db_name"))
            ora_dml_root_dir = os.path.join(db_dir, 'DML')
            try:
                if new_db_info.get("db_srv_type") != 'oracle':
                    new_db_info['username'] = MYSQL_BASIC_USERNAME
                    new_db_info['password'] = MYSQL_BASIC_PASSWORD
                    new_db_info['conn_url'] = MYSQL_BASIC_CONN_URL.format(new_db_info.get("suite_db_name"))
                    flyway_db_info['db_user'] = new_db_info.get("username")
                    flyway_db_info['db_passwd'] = new_db_info.get("password")
                    flyway_db_info['conn_url'] = new_db_info.get("conn_url")
                    log.info("flyway_db_info:{},clean,db_dir:{}".format(flyway_db_info, db_dir))
                    exec_flyway_command(flyway_db_info, 'clean', db_dir)
                    exec_flyway_command(flyway_db_info, 'migrate', db_dir)
                else:
                    new_db_info['username'] = new_db_info.get("suite_db_name").upper()
                    new_db_info['password'] = ORACLE_BASIC_PASSWORD
                    new_db_info['conn_url'] = ORACLE_BASIC_CONN_URL
                    flyway_db_info['db_user'] = new_db_info.get("username")
                    flyway_db_info['db_passwd'] = new_db_info.get("password")
                    flyway_db_info['conn_url'] = new_db_info.get("conn_url")
                    db_dir = os.path.join(db_dir, 'DDL')
                    log.info("flyway_db_info:{},repair,db_dir:{}".format(flyway_db_info, db_dir))
                    exec_flyway_command(flyway_db_info, 'repair', db_dir)
                    exec_flyway_command(flyway_db_info, 'migrate', db_dir)
                if new_db_info.get("db_srv_type") == 'oracle':
                    new_db_info['db_srv_hosts'] = '***************'
                    new_db_info['db_srv_port'] = '1521'
                    new_db_info['username'] = new_db_info.get("username")
                    new_db_info['password'] = 'howbuy2015'
                    new_db_info['db_srv_name'] = 'ptrade'
                    log.info("flyway_db_info:{},exec_dml,ora_dml_root_dir:{}".format(flyway_db_info, ora_dml_root_dir))
                    log.info("new_db_info:{}".format(flyway_db_info, new_db_info))
                    # exec_dml(ora_dml_root_dir, new_db_info)
            except Exception as e:
                traceback.print_exc()
                log.error("flyway migrate执行失败")
                # 如果是DOCKER_IT999_CUST数据库，仅打印警告信息，不中断流水线
                if new_db_info.get("suite_db_name") == "DOCKER_IT999_CUST":
                    log.warning("数据库DOCKER_IT999_CUST恢复失败，但不中断流水线执行: {}".format(str(e)))
                else:
                    exit_flag = True
                    if new_db_info.get("suite_db_name") not in error_db_name_list:
                        error_db_name_list.append(new_db_info.get("suite_db_name"))
        if exit_flag:
            raise Exception("有数据库{}恢复失败了！！".format(",".join(error_db_name_list)))


if __name__ == '__main__':
    param_list = sys.argv
    err_msg = None
    try:
        log.info(param_list)
        if len(param_list) < 3:
            raise ValueError("缺少必要参数：{}".format(param_list))
        class_name = param_list[1]
        method_name = "exec"
        if param_list[2].upper() == 'ALL':
            # 查一天前所有归档的迭代 迭代相关的应用 应用相关的库
            # 获取当前日期
            current_date = datetime.now()
            formatted_current_date = current_date.strftime("%Y年%m月%d日")
            print("当前日期:", formatted_current_date)
            # 获取昨天的日期
            yesterday = current_date - timedelta(days=1)
            formatted_yesterday = yesterday.strftime("%Y年%m月%d日")
            print("昨天的日期:", formatted_yesterday)
        else:
            db_info_list = json.loads(param_list[2])
            log.info("db_info_list:{}".format(db_info_list))
            # 实例化类
            handler = globals()[class_name]()
            # 调用方法
            getattr(handler, method_name)((db_info_list))
            # 正常返回
            sys.exit()
        app_info_list = query_archived_applications_and_versions(formatted_yesterday, formatted_current_date)

        log.info("app_info_list:{}".format(app_info_list))
        if not app_info_list:
            log.info("无归档应用！跳过恢复数据库")
            # 正常返回
            sys.exit()
        apps_str = ', '.join("'" + item[0] + "'" for item in app_info_list)
        log.info("apps_str:{}".format(apps_str))
        # apps_str = "'app_module_name','adviser-batch-center-remote','asset-center-remote','asset-batch-center-remote','doctor','center-member-service','member-server-remote','center-platform-boot','coop-admin-remote','coop-promotion-remote','coupon-center-remote','ftx-order-remote','ftx-batch-remote','message-center-remote','message-manage-server','message-remote','message-xbox-service','order-center-new-remote','param-data-sync','param-server','param-console','order-plan-center-remote','product-center-remote','howbuy-interlayer-console','tms-counter-console','tms-mock-remote','acc-center-server','acc-console-web','crm-asset','crm-td-server','crm-trade-server','crm-asset-remote','crm-cgi-remote','hbdoc-webapp','crm-inst-admin','crm-inst-data','crm-inst-message','crm-wechat-remote','calc-offline-fund','calc-offline-high','calc-offline-portfolio','fpc-manage-admin','fpc-manage-data','howbuy-fpc-monitor','howbuy-fpc-scheduler','howbuy-fpc-submit','fpc-manage-console','howbuy-fpc-reptile','howbuy-fpc-quotation','howbuy-activiti-server','howbuy-ams-cgi','howbuy-ams-server','howbuy-simu-server','howbuy-fund-server','fpc-manage-report','howbuy-data-ants','howbuy-web-server','howbuy-es-data','howbuy-content-server','howbuy-cms-server','pension-order-remote','pension-batch-remote','howbuy-act-remote','activity-center-remote','hk-acc-online-service','howbuy-grayscale-cms','dtms-order-remote','dtms-product-remote','trade-mock','howbuy-qa-info-remote','howbuy-document-service','howbuy-fpc-das','howbuy-fpc-data','howbuy-fpc-notice','howbuy-fpc-web'"
        db_info_list = get_db_info_by_app_name(app_str=apps_str, suite_code='it999')
        # db_info_list = [{'db_srv_type': 'oracle', 'db_name': 'fin', 'db_group_name': 'FIN'},{'db_srv_type': 'oracle', 'db_name': 'trade', 'db_group_name': 'TRADE'}]
        log.info("db_info_list:{}".format(db_info_list))
        # 实例化类
        handler = globals()[class_name]()
        # 调用方法
        getattr(handler, method_name)((db_info_list))
        # 正常返回
        sys.exit()
    except Exception as e:
        # 打印错误信息
        traceback.print_exc()
        err_msg = "基础库初始化，ValueError：{}".format(e)
        log.error(err_msg)
        # 异常返回
        sys.exit(-1)
