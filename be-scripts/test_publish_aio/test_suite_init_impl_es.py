# ES流水线处理器实现
# 第1版 fwm@2025-09-10

import time
import os
import sys
import threading
import concurrent.futures
from functools import wraps

from test_publish_aio.test_suite_init_constants import TypeEnum

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(PROJECT_DIR)
from settings import logger as log
from test_publish_aio.test_suite_init_impl import <PERSON><PERSON><PERSON><PERSON><PERSON>
from test_publish_aio.test_publish_aio_es.es_client import EsClient
from test_publish_aio.test_publish_aio_es.es_script_executor import EsScriptExecutor
from ci_pipeline.ci_pipeline_utils.publish_utils import <PERSON><PERSON>StatusChecker
from test_publish_aio.test_publish_aio_models.test_publish_ser import PublishTestRepos


# 常量定义
class EsConstants:
    """ES相关常量定义"""
    DEFAULT_BACKUP_REPOSITORY = "my_s3_backup"
    DEFAULT_ES_URL = ""
    MAX_RESTORE_WAIT_TIME = 300  # 最大等待5分钟
    RESTORE_CHECK_INTERVAL = 10  # 每10秒检查一次


def require_multi_push_type(func):
    """装饰器：检查类型枚举，只有MULTI_PUSH类型才执行ES相关操作"""

    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if self.type_enum != TypeEnum.MULTI_PUSH:
            log.info(f"当前类型为 {self.type_enum}，跳过ES脚本执行操作")
            return
        return func(self, *args, **kwargs)

    return wrapper


class EsThreadConfig:
    """ES多线程执行配置
    
    支持从环境变量和配置文件读取配置，提高灵活性
    环境变量优先级：环境变量 > 配置文件 > 默认值
    """
    # 默认配置值
    _DEFAULT_MAX_MODULE_WORKERS = 2
    _DEFAULT_MAX_SCRIPT_WORKERS = 4
    _DEFAULT_ES_POOL_CONNECTIONS = 10
    _DEFAULT_ES_POOL_MAXSIZE = 20
    _DEFAULT_ES_MAX_RETRIES = 3

    @classmethod
    def _get_env_or_default(cls, env_key: str, default_value: int) -> int:
        """从环境变量获取配置值，如果不存在则使用默认值"""
        try:
            env_value = os.environ.get(env_key)
            return int(env_value) if env_value else default_value
        except (ValueError, TypeError):
            log.warning(f"环境变量 {env_key} 值无效，使用默认值 {default_value}")
            return default_value

    @classmethod
    def MAX_MODULE_WORKERS(cls) -> int:
        """模块级别并发数（同时执行的ES模块数量）"""
        return cls._get_env_or_default('ES_MAX_MODULE_WORKERS', cls._DEFAULT_MAX_MODULE_WORKERS)

    @classmethod
    def MAX_SCRIPT_WORKERS(cls) -> int:
        """脚本级别并发数（单个模块内同时执行的脚本文件数量）"""
        return cls._get_env_or_default('ES_MAX_SCRIPT_WORKERS', cls._DEFAULT_MAX_SCRIPT_WORKERS)

    @classmethod
    def ES_POOL_CONNECTIONS(cls) -> int:
        """ES客户端连接池连接数"""
        return cls._get_env_or_default('ES_POOL_CONNECTIONS', cls._DEFAULT_ES_POOL_CONNECTIONS)

    @classmethod
    def ES_POOL_MAXSIZE(cls) -> int:
        """ES客户端连接池最大大小"""
        return cls._get_env_or_default('ES_POOL_MAXSIZE', cls._DEFAULT_ES_POOL_MAXSIZE)

    @classmethod
    def ES_MAX_RETRIES(cls) -> int:
        """ES客户端最大重试次数"""
        return cls._get_env_or_default('ES_MAX_RETRIES', cls._DEFAULT_ES_MAX_RETRIES)

    @classmethod
    def get_module_workers(cls, module_count: int) -> int:
        """根据模块数量动态调整并发数"""
        return min(cls.MAX_MODULE_WORKERS(), module_count)

    @classmethod
    def get_script_workers(cls, script_count: int) -> int:
        """根据脚本数量动态调整并发数"""
        return min(cls.MAX_SCRIPT_WORKERS(), script_count)

    @classmethod
    def get_config_summary(cls) -> dict:
        """获取当前配置摘要"""
        return {
            'MAX_MODULE_WORKERS': cls.MAX_MODULE_WORKERS(),
            'MAX_SCRIPT_WORKERS': cls.MAX_SCRIPT_WORKERS(),
            'ES_POOL_CONNECTIONS': cls.ES_POOL_CONNECTIONS(),
            'ES_POOL_MAXSIZE': cls.ES_POOL_MAXSIZE(),
            'ES_MAX_RETRIES': cls.ES_MAX_RETRIES()
        }


class EsHealthCheckHandler(ParentHandler):
    """ES健康检查处理器"""
    filter_key_list = ['es_info_list']

    @require_multi_push_type
    def exec(self, *args, **kwargs):
        """执行ES健康检查"""
        super().exec(*args, **kwargs)

        backup_info_list = _get_backup_info_list(self.bis_pipeline_id, self.suite_code)

        if not backup_info_list:
            log.info("未发现ES备份，跳过执行ES健康检查")
            return

        bo_dict = kwargs.get('bo_dict', {})

        # 收集所有ES模块名称
        es_module_names = set()
        for bo in bo_dict.values():
            if 'es_info_list' in bo and bo['es_info_list']:
                for es_info in bo['es_info_list']:
                    es_module_names.add(es_info['es_module_name'])

        if not es_module_names:
            log.info("未发现ES依赖，跳过ES健康检查")
            return

        # 转换为列表并执行批量健康检查
        es_module_name_list = list(es_module_names)
        log.info(f"开始检查ES模块健康状态: {es_module_name_list}")

        try:
            # 使用batch_check_node_status方法进行批量检查
            checker = PublishStatusChecker(self.suite_code, "")
            result = checker.batch_check_node_status(es_module_name_list)

            if result:
                log.info("所有ES模块健康检查通过")
            else:
                raise Exception("ES模块健康检查失败")

        except Exception as e:
            log.error(f"ES模块健康检查失败: {e}")
            raise


class EsBackupVerifyHandler(ParentHandler):
    """ES备份验证处理器
    
    验证ES快照备份的完整性和可用性，确保备份数据可以正常恢复
    """
    filter_key_list = ['es_info_list']

    @require_multi_push_type
    def exec(self, *args, **kwargs):
        """执行ES备份验证"""
        super().exec(*args, **kwargs)

        bo_dict = kwargs.get('bo_dict', {})

        log.info(f"ES备份验证开始: bis_pipeline_id={self.bis_pipeline_id}, suite_code={self.suite_code}")

        # 获取需要验证的备份信息
        backup_info_list = _get_backup_info_list(self.bis_pipeline_id, self.suite_code)

        if not backup_info_list:
            log.info("未发现ES备份，跳过备份验证")
            return

        # 验证每个备份
        failed_backups = []
        for backup_info in backup_info_list:
            try:
                self._verify_backup_integrity(backup_info, bo_dict)
                log.info(f"ES备份验证通过: {backup_info['es_dump_name']}")
            except Exception as e:
                log.error(f"ES备份验证失败: {backup_info['es_dump_name']}, 错误: {e}")
                failed_backups.append(backup_info['es_dump_name'])

        if failed_backups:
            raise Exception(f"以下ES备份验证失败: {failed_backups}")

        log.info("所有ES备份验证通过")

    def _verify_backup_integrity(self, backup_info: dict, bo_dict: dict = None):
        """验证备份完整性"""
        es_url = _get_es_url_by_module(backup_info['source_es_module_name'], bo_dict)
        es_client = EsClient(f"http://{es_url}")

        # 首先确保快照仓库存在
        try:
            es_client.ensure_repository_exists(EsConstants.DEFAULT_BACKUP_REPOSITORY)
        except Exception as e:
            log.error(f"确保快照仓库存在失败: {e}")
            raise Exception(f"快照仓库{EsConstants.DEFAULT_BACKUP_REPOSITORY}不可用: {e}")

        # 验证快照状态
        snapshot_name = backup_info['es_dump_name']
        snapshot_status = es_client.verify_snapshot(snapshot_name)

        # 检查快照状态
        snapshots = snapshot_status.get('snapshots', [])
        if not snapshots:
            raise Exception(f"快照不存在: {snapshot_name}")

        snapshot = snapshots[0]
        if snapshot.get('state') != 'SUCCESS':
            raise Exception(f"快照状态异常: {snapshot.get('state')}")

        # 验证索引数量
        expected_count = backup_info.get('index_count', 0)
        actual_count = len(snapshot.get('indices', []))

        if expected_count != actual_count:
            raise Exception(f"索引数量不匹配，期望: {expected_count}, 实际: {actual_count}")


def _get_es_url_by_module(module_name: str, bo_dict: dict = None) -> str:
    """根据模块名称获取ES URL的公共方法"""
    if bo_dict:
        # 从bo_dict中查找对应模块的ES URL
        for bo in bo_dict.values():
            if bo.get('es_info_list'):
                for es_info in bo['es_info_list']:
                    if es_info['es_module_name'] == module_name:
                        return es_info['es_url']

    # 如果未找到，返回默认值
    return EsConstants.DEFAULT_ES_URL


def _get_backup_info_list(bis_pipeline_id: str, suite_code: str) -> list:
    """获取备份信息列表的公共方法"""
    log.info(f"查询ES备份信息: bis_pipeline_id={bis_pipeline_id}, suite_code={suite_code}")

    # 直接调用数据库查询方法
    repos = PublishTestRepos(suite_code, "")
    backup_info = repos.get_es_backup_info_by_biz_test_iter_id(bis_pipeline_id)

    log.info(f"查询到的ES备份信息数量: {len(backup_info) if backup_info else 0}")
    return backup_info


class EsDataClearHandler(ParentHandler):
    """ES数据清空处理器
    
    清空ES实例中的所有用户数据，保留系统索引
    """
    filter_key_list = ['es_info_list']

    @require_multi_push_type
    def exec(self, *args, **kwargs):
        """执行ES数据清理"""
        super().exec(*args, **kwargs)

        backup_info_list = _get_backup_info_list(self.bis_pipeline_id, self.suite_code)

        if not backup_info_list:
            log.info("未发现ES备份，跳过ES数据清空")
            return

        bo_dict = kwargs.get('bo_dict', {})

        # 收集所有ES URL
        es_urls = self._collect_es_urls(bo_dict)

        if not es_urls:
            log.info("未发现ES依赖，跳过ES数据清空")
            return

        # 清空每个ES实例的数据
        for es_url in es_urls:
            try:
                self._clear_all_indices(es_url)
                log.info(f"ES数据清空完成: {es_url}")
            except Exception as e:
                log.error(f"ES数据清空失败: {es_url}, 错误: {e}")
                raise

        log.info("所有ES数据清空完成")

    def _collect_es_urls(self, bo_dict: dict) -> set:
        """收集ES URL列表"""
        es_urls = set()
        for bo in bo_dict.values():
            if bo.get('es_info_list'):
                for es_info in bo['es_info_list']:
                    es_urls.add(es_info['es_url'])
        return es_urls

    def _clear_all_indices(self, es_url: str):
        """清空所有索引"""
        es_client = EsClient(f"http://{es_url}")

        # 获取当前索引列表
        indices_before = es_client.get_indices()
        log.info(f"清空前索引数量: {len(indices_before)}")

        # 删除所有索引
        result = es_client.delete_all_indices()

        if not result.get('acknowledged'):
            raise Exception("删除索引操作未被确认")

        # 验证清空结果
        indices_after = es_client.get_indices()
        log.info(f"清空后索引数量: {len(indices_after)}")

        if len(indices_after) > 0:
            log.warning(f"仍有 {len(indices_after)} 个索引未被删除，可能为系统保护索引")


class EsBackupRestoreHandler(ParentHandler):
    """ES备份恢复处理器
    
    从快照备份中恢复ES数据，支持增量和全量恢复
    """
    filter_key_list = ['es_info_list']

    @require_multi_push_type
    def exec(self, *args, **kwargs):
        """执行ES备份恢复"""
        super().exec(*args, **kwargs)

        bo_dict = kwargs.get('bo_dict', {})

        log.info(f"ES备份恢复开始: bis_pipeline_id={self.bis_pipeline_id}, suite_code={self.suite_code}")

        # 获取需要恢复的备份信息
        backup_info_list = _get_backup_info_list(self.bis_pipeline_id, self.suite_code)

        if not backup_info_list:
            log.info("未发现ES备份，跳过ES备份恢复")
            return

        # 恢复每个备份
        for backup_info in backup_info_list:
            try:
                self._restore_from_backup(backup_info, bo_dict)
                log.info(f"ES备份恢复完成: {backup_info['es_dump_name']}")
            except Exception as e:
                log.error(f"ES备份恢复失败: {backup_info['es_dump_name']}, 错误: {e}")
                raise

        log.info("所有ES备份恢复完成")

    def _restore_from_backup(self, backup_info: dict, bo_dict: dict = None):
        """从备份恢复数据"""
        es_url = _get_es_url_by_module(backup_info['source_es_module_name'], bo_dict)
        es_client = EsClient(f"http://{es_url}")

        # 首先确保快照仓库存在
        try:
            es_client.ensure_repository_exists(EsConstants.DEFAULT_BACKUP_REPOSITORY)
        except Exception as e:
            log.error(f"确保快照仓库存在失败: {e}")
            raise Exception(f"快照仓库{EsConstants.DEFAULT_BACKUP_REPOSITORY}不可用: {e}")

        snapshot_name = backup_info['es_dump_name']

        # 执行恢复操作
        result = es_client.restore_snapshot(snapshot_name)

        if not result.get('accepted'):
            raise Exception(f"恢复操作未被接受: {result}")

        # 等待恢复完成并验证
        self._wait_for_restore_completion(es_client, snapshot_name, backup_info)

    def _wait_for_restore_completion(self, es_client: EsClient, snapshot_name: str, backup_info: dict):
        """等待恢复完成并验证"""
        max_wait_time = 300  # 最大等待5分钟
        check_interval = 10  # 每10秒检查一次
        waited_time = 0

        while waited_time < max_wait_time:
            try:
                # 检查恢复状态
                indices = es_client.get_indices()
                non_system_indices = [idx for idx in indices if not idx.startswith('.')]

                expected_count = backup_info.get('index_count', 0)
                if len(non_system_indices) >= expected_count:
                    log.info(f"恢复完成，索引数量: {len(non_system_indices)}")
                    return

                log.info(f"恢复进行中，当前索引数量: {len(non_system_indices)}/{expected_count}")
                time.sleep(check_interval)
                waited_time += check_interval

            except Exception as e:
                log.warning(f"检查恢复状态时出错: {e}")
                time.sleep(check_interval)
                waited_time += check_interval

        raise Exception(f"恢复超时，等待时间超过 {max_wait_time} 秒")


class EsScriptExecHandler(ParentHandler):
    """ES脚本执行处理器
    
    执行ES索引创建和配置脚本，支持从Git仓库拉取并执行脚本文件
    """
    filter_key_list = ['es_info_list']

    @require_multi_push_type
    def exec(self, *args, **kwargs):
        """执行ES脚本（多线程优化版本）"""
        super().exec(*args, **kwargs)
        start_time = time.time()

        # skip = 1
        # if skip == 1:
        #     return

        backup_info_list = _get_backup_info_list(self.bis_pipeline_id, self.suite_code)

        if not backup_info_list:
            log.info("未发现ES备份，跳过ES脚本执行")
            return

        bo_dict = kwargs.get('bo_dict', {})
        job_workspace = kwargs.get('job_workspace') or self.job_workspace

        if not job_workspace:
            log.error("job_workspace参数为空，无法执行ES脚本")
            raise ValueError("job_workspace参数不能为空")

        # 收集需要执行脚本的ES信息
        es_script_info_list = self._collect_es_script_info(bo_dict)

        if not es_script_info_list:
            log.info("未发现需要执行的ES脚本，跳过脚本执行")
            return

        log.info(
            f"ES脚本执行开始，配置信息: 模块并发数={EsThreadConfig.MAX_MODULE_WORKERS}, 脚本并发数={EsThreadConfig.MAX_SCRIPT_WORKERS}")

        # 多线程执行每个ES模块的脚本
        failed_modules = []
        success_modules = []
        module_lock = threading.Lock()

        def execute_single_module(es_info):
            """执行单个ES模块脚本的线程函数"""
            try:
                self._execute_index_scripts(es_info, job_workspace)
                with module_lock:
                    success_modules.append(es_info['es_module_name'])
                log.info(f"ES模块脚本执行完成: {es_info['es_module_name']}")
                return {"status": "success", "module": es_info['es_module_name']}
            except Exception as e:
                with module_lock:
                    failed_modules.append({"module": es_info['es_module_name'], "error": str(e)})
                log.error(f"ES模块脚本执行失败: {es_info['es_module_name']}, 错误: {e}")
                return {"status": "failed", "module": es_info['es_module_name'], "error": str(e)}

        # 使用线程池执行ES模块，根据配置动态调整并发数
        max_module_workers = EsThreadConfig.get_module_workers(len(es_script_info_list))
        log.info(f"开始并发执行 {len(es_script_info_list)} 个ES模块，使用 {max_module_workers} 个线程")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_module_workers) as executor:
            # 提交所有模块任务
            future_to_module = {executor.submit(execute_single_module, es_info): es_info['es_module_name']
                                for es_info in es_script_info_list}

            # 等待所有模块任务完成
            for future in concurrent.futures.as_completed(future_to_module):
                module_name = future_to_module[future]
                try:
                    result = future.result()
                    # 结果已在execute_single_module中处理
                except Exception as e:
                    log.error(f"模块线程执行异常: {module_name}, 错误: {e}")
                    with module_lock:
                        failed_modules.append({"module": module_name, "error": str(e)})

        # 总结执行结果
        end_time = time.time()
        total_time = end_time - start_time
        total_modules = len(es_script_info_list)
        success_count = len(success_modules)
        failed_count = len(failed_modules)

        log.info(
            f"ES脚本执行完成: 总计 {total_modules} 个模块，成功 {success_count} 个，失败 {failed_count} 个，总耗时 {total_time:.2f} 秒")
        log.info(
            f"性能统计: 平均每个模块耗时 {total_time / total_modules:.2f} 秒，并发效率提升约 {EsThreadConfig.MAX_MODULE_WORKERS() * EsThreadConfig.MAX_SCRIPT_WORKERS()}x")

        if failed_modules:
            log.warning(f"ES脚本执行完成，但有 {failed_count} 个模块执行失败:")
            for failed in failed_modules:
                log.warning(f"失败模块: {failed['module']}, 错误: {failed['error']}")
        else:
            log.info("所有ES脚本执行完成")

    def _collect_es_script_info(self, bo_dict: dict) -> list:
        """收集ES脚本信息"""
        es_script_info_map = {}

        for bo in bo_dict.values():
            if bo.get('es_info_list'):
                for es_info in bo['es_info_list']:
                    key = es_info['es_module_name']
                    if key not in es_script_info_map:
                        es_script_info_map[key] = es_info

        return list(es_script_info_map.values())

    def _execute_index_scripts(self, es_info: dict, job_workspace: str):
        """执行索引创建脚本（多线程优化版本）"""
        # 1. 克隆脚本仓库
        script_executor = EsScriptExecutor(job_workspace)
        repo_name = es_info['es_lib_git_repo']
        repo_url = f"************************:ES-SCRIPT/{repo_name}.git"

        script_repo_path = script_executor.clone_repository(repo_name, repo_url)

        # 2. 扫描脚本文件
        script_files = script_executor.scan_script_files(script_repo_path)

        if not script_files:
            log.info(f"模块 {es_info['es_module_name']} 未发现脚本文件")
            return

        # 3. 多线程执行脚本
        # 创建优化的ES客户端，支持连接池
        es_client = EsClient(f"http://{es_info['es_url']}", pool_connections=EsThreadConfig.ES_POOL_CONNECTIONS(),
                             pool_maxsize=EsThreadConfig.ES_POOL_MAXSIZE(), max_retries=EsThreadConfig.ES_MAX_RETRIES()
                             )

        # 线程安全的结果收集
        failed_scripts = []
        success_scripts = []
        lock = threading.Lock()

        def execute_single_script(script_file):
            """执行单个脚本文件的线程函数"""
            try:
                script_executor.execute_script_file(es_client, script_file)
                with lock:
                    success_scripts.append(script_file)
                log.info(f"脚本文件执行成功: {script_file}")
                return {"status": "success", "file": script_file}
            except Exception as e:
                with lock:
                    failed_scripts.append({"file": script_file, "error": str(e)})
                log.error(f"脚本文件执行失败: {script_file}, 错误: {e}")
                return {"status": "failed", "file": script_file, "error": str(e)}

        # 使用线程池执行脚本，根据配置动态调整并发数
        max_workers = EsThreadConfig.get_script_workers(len(script_files))
        log.info(f"开始并发执行 {len(script_files)} 个脚本文件，使用 {max_workers} 个线程")

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_script = {executor.submit(execute_single_script, script_file): script_file
                                for script_file in script_files}

            # 等待所有任务完成
            for future in concurrent.futures.as_completed(future_to_script):
                script_file = future_to_script[future]
                try:
                    result = future.result()
                    # 结果已在execute_single_script中处理
                except Exception as e:
                    log.error(f"线程执行异常: {script_file}, 错误: {e}")
                    with lock:
                        failed_scripts.append({"file": script_file, "error": str(e)})

        # 总结脚本执行结果
        total_scripts = len(script_files)
        success_count = len(success_scripts)
        failed_count = len(failed_scripts)

        log.info(
            f"模块 {es_info['es_module_name']} 脚本执行完成: 总计 {total_scripts} 个，成功 {success_count} 个，失败 {failed_count} 个")

        if failed_scripts:
            log.warning(f"模块 {es_info['es_module_name']} 有 {failed_count} 个脚本文件执行失败:")
            for failed in failed_scripts:
                log.warning(f"失败脚本: {failed['file']}, 错误: {failed['error']}")
        else:
            log.info(f"模块 {es_info['es_module_name']} 所有脚本文件执行成功")
