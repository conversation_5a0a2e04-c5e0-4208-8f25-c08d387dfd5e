# 一键部署（底层方法实现）
import datetime
import ftplib
import os
import pipes
import re
import subprocess
import sys
import functools
import time
import traceback
import urllib.parse
import urllib.request
import requests
import json
import paramiko

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from settings import logger as log, NACOS_KEY, INTERFACE_URL, CODE_STYLE, CONFIG_DIR_FLAG, TEST_DATA_INIT, DUMP, \
    CMD_EXECUTOR_FAIL
from settings import PIPELINE_TEST_PUBLISH as PTP, PRODUCT_STORE_SQL_URL
from settings import TEST_PUBLISH_AIO as TPA
from common.ext_cmd.svn.svn_cmd import SvnCmd
from common.files import search
from utils.test_env.test_env_lib import gen_signature, md5_factory, request
from utils.zeus.config_publish import ZeusAP<PERSON>
from common.ztst_utils.file_hash import file_md5
from dao.get.mysql.env_info import get_tomcat_password_by_node_ip, get_suite_code_by_region_group
from test_publish_aio.test_suite_init_constants import SVN_EXCLUDE_LIST, SVN_MAX_LEVEL, TypeEnum, PROCESS_CODE_PROD
from time import sleep
from test_pipeline.test_pipeline_models.test_pipeline_models import TestMgtPublishApp
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from db_mgt.creat_sql_migrate.models import IterMgtSqlHandleLog
from test_publish_aio.test_publish_aio_common.db_error_catch import DbErrorCollector

from dao.connect.mysql import DBConnectionManager


class CmdException(Exception):
    pass


def get_tomcat_password(node_ip, cmd=None, password=None):
    """查询节点tomcat用户连接密码"""
    if not password:
        password = get_tomcat_password_by_node_ip(node_ip)
        if password and password.strip() != '':
            msg = "使用「配置密码」连接服务器：{}，执行命令：{}".format(node_ip, cmd)
        else:
            password = PTP['def_tomcat_password']
            msg = "使用「默认密码」连接服务器：{}，执行命令：{}".format(node_ip, cmd)
    else:
        msg = "使用「传入密码」连接服务器：{}，执行命令：{}".format(node_ip, cmd)

    return password, msg


def shell_cmd(command, exit_conditions="", read_line=False, all_log=False):
    """
        用于执行需要在控制台输出日志的shell操作，如编译操作。

    :param command: 需要执行的命令 如：mvn install
    :param exit_conditions: 程序退出条件 如：exit_conditions="Build failure" ，
    那么当输出日志中存在Build failure程序将异常退出，默认无退出条件。
    :param read_line: 是否逐行输出日志。
    :return: 1 执行命令错误，输出中包含退出条件
    0 执行成功
    std_out_lines 调用外部命令的输出内容
    """

    std_out = subprocess.Popen(command, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    # error_log = std_out.stderr.read().decode(CODE_STYLE)
    # if error_log:
    #     logger.error(error_log)
    #     return 1, error_log
    # 逐行读取
    exit_conditions_flag = 0
    if read_line:
        std_out_list = []
        while True:
            std_out_line = std_out.stdout.readline().decode(CODE_STYLE)
            if std_out_line:
                log.info(std_out_line)
                std_out_list.append(std_out_line)
                # 如果没有退出条件，继续读取下一行输出
                if exit_conditions == "":
                    continue
                for exit_con in exit_conditions.split(","):
                    if exit_con in std_out_line:
                        exit_conditions_flag = 1
                        if not all_log:
                            return 1, "\n".join(std_out_list)
            else:
                break
        if exit_conditions_flag:
            return 1, "\n".join(std_out_list)
        else:
            return 0, "\n".join(std_out_list)
    else:
        std_out_lines = std_out.stdout.read().decode(CODE_STYLE)
        log.info(std_out_lines)
        # 如果没有退出条件,结束
        if exit_conditions == "":
            return 0, std_out_lines
        for exit_con in exit_conditions.split(","):
            if exit_con in std_out_lines:
                return 1, std_out_lines
        return 0, std_out_lines


def exec_local_cmd(cmd, timeout=None):
    """本地执行命令 zt@2020-09-08"""
    log.info("本地执行命令：{}".format(re.sub(r'^sshpass -p \S*', "sshpass -p ******", cmd)))
    try:
        if not timeout:
            timeout = int(PTP['exec_cmd_timeout'])
        completed_process_obj = subprocess.run(cmd, shell=True,
                                               timeout=timeout,
                                               capture_output=True, check=True)
        log.info("本地执行结果：{}".format(bytes.decode(completed_process_obj.stdout)))
    except subprocess.CalledProcessError as err:
        msg = "本地执行命令出错：{}".format(bytes.decode(err.stderr))
        log.error(msg)
        raise CmdException(msg)
    return completed_process_obj


def exec_local_popen(cmd):
    """本地Popen执行命令 zt@2020-09-11"""
    log.info("本地Popen执行命令：{}".format(re.sub(r'^sshpass -p \S*', "sshpass -p ******", cmd)))
    try:
        p = subprocess.Popen(cmd, shell=True,
                             stdout=subprocess.PIPE,
                             stderr=subprocess.PIPE, )

        stdout, stderr = p.communicate(timeout=int(PTP['exec_cmd_timeout']))
        code = p.returncode
        stdout_str = bytes.decode(stdout)
        if code == 0:

            if "执行结果有异常" in stdout_str:
                log.error("帅维脚本返回异常 {}".format(stdout_str))
                raise CmdException("帅维脚本返回异常 {}".format(stdout_str))
            log.info("本地Popen执行结果：{}".format(stdout_str))
        else:
            msg = "本地Popen执行命令出错：{}".format(bytes.decode(stderr))
            log.error(msg)
            raise CmdException(msg)
    except subprocess.CalledProcessError as err:
        msg = "本地Popen执行命令CalledProcessError：{}".format(bytes.decode(err.stderr))
        log.error(msg)
        raise CmdException(msg)
    return stdout


def exec_local_cmd_by_sshpass(node_ip, cmd, password=None):
    """利用sshpass执行命令 zt@2020-09-09"""
    password, msg = get_tomcat_password(node_ip, cmd, password)
    log.info(msg)
    sshpass_cmd = "sshpass -p '{}' {}".format(password, cmd)
    return exec_local_cmd(sshpass_cmd)


def exec_local_cmd_by_sshpass_tomcat(node_ip, cmd, password=None):
    """远程利用sshpass执行命令"""
    password, msg = get_tomcat_password(node_ip, cmd, password)
    log.info(msg)
    sshpass_cmd = "sshpass -p '{}' ssh tomcat@{} {}".format(password, node_ip, cmd)
    return exec_local_cmd(sshpass_cmd)


def exec_local_scp_from_remote(source_ip, source_path, target_path, password=None):
    """利用sshpass执行scp命令 zt@2020-09-09"""
    scp_cmd = "scp -r {}@{}:{} {}".format(PTP['def_ssh_user'], source_ip, source_path, target_path)
    exec_local_cmd_by_sshpass(source_ip, scp_cmd, password)


def exec_local_scp_to_remote(target_ip, source_path, target_path, password=None):
    """利用sshpass执行scp命令 zt@2020-09-09"""
    scp_cmd = "scp -r {} {}@{}:{}".format(source_path, PTP['def_ssh_user'], target_ip, target_path)
    exec_local_cmd_by_sshpass(target_ip, scp_cmd, password)


def exec_local_rsync_to_remote(target_ip, source_path, target_path, is_delete=None, password=None):
    """利用sshpass执行rsync命令 zt@2020-09-09"""
    rsync_cmd = "rsync{} " \
                "-LPa " \
                "--timeout={} " \
                "--exclude='*.git' " \
                "{} {}@{}:{}".format(' --delete' if is_delete else '',
                                     int(PTP['exec_cmd_timeout']),
                                     source_path,
                                     PTP['def_ssh_user'],
                                     target_ip,
                                     target_path)

    try:
        completed_process_obj1 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第1次rsync成功：{}".format(bytes.decode(completed_process_obj1.stdout)))
    except Exception as e1:
        log.error(">>>> 第1次rsync失败 >> msg：{}".format(e1))

    try:
        completed_process_obj2 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第2次rsync成功：{}".format(bytes.decode(completed_process_obj2.stdout)))
    except Exception as e2:
        log.error(">>>> 第2次rsync失败 >> msg：{}".format(e2))

    try:
        completed_process_obj3 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第3次rsync成功：{}".format(bytes.decode(completed_process_obj3.stdout)))
    except Exception as e3:
        log.error(">>>> 第3次rsync失败 >> msg：{}".format(e3))
        raise CmdException(">>>> 3次rsync全部失败: {}".format(e3))


def exec_local_rsync_to_local(source_path, target_path, is_delete=None):
    """本地执行rsync命令"""
    rsync_cmd = "rsync{} -LPa --timeout={} --exclude='*.git' {} {}".format(' --delete' if is_delete else '',
                                                                           int(PTP['exec_cmd_timeout']),
                                                                           source_path,
                                                                           target_path)

    try:
        completed_process_obj1 = exec_local_cmd(rsync_cmd)
        log.info("第1次本地rsync成功：{}".format(bytes.decode(completed_process_obj1.stdout)))
    except Exception as e1:
        log.error(">>>> 第1次本地rsync失败 >> msg：{}".format(e1))

    try:
        completed_process_obj2 = exec_local_cmd(rsync_cmd)
        log.info("第2次本地rsync成功：{}".format(bytes.decode(completed_process_obj2.stdout)))
    except Exception as e2:
        log.error(">>>> 第2次本地rsync失败 >> msg：{}".format(e2))

    try:
        completed_process_obj3 = exec_local_cmd(rsync_cmd)
        log.info("第3次本地rsync成功：{}".format(bytes.decode(completed_process_obj3.stdout)))
    except Exception as e3:
        log.error(">>>> 第3次本地rsync失败 >> msg：{}".format(e3))
        raise CmdException(">>>> 3次本地rsync全部失败: {}".format(e3))


def exec_remote_cmd(node_ip, cmd, password=None):
    """利用paramiko远程执行命令 zt@2020-09-09"""
    password, msg = get_tomcat_password(node_ip, cmd, password)
    log.info("『远程』{}".format(msg))

    try:
        client = paramiko.SSHClient()
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

        client.connect(node_ip, username=PTP['def_ssh_user'], password=password, timeout=int(PTP['exec_cmd_timeout']))

        stdin, stdout, stderr = client.exec_command(cmd)
        stdout_str = stdout.read()
        stderr_str = stderr.read()
        log.info('stdout_str：{}'.format(stdout_str.decode('utf-8')))
        log.info('stderr_str：{}'.format(stderr_str.decode('utf-8')))
        if stderr_str:
            if "JMX enabled by default" in stderr_str.decode("utf-8"):
                # 尝试解决特殊情况下报非 UTF-8 的问题 zt@2021-06-30
                stderr_msg = ">>>> 特殊输出警告：{}".format(stdout_str.decode("utf-8"))
                log.warning(stderr_msg)
            else:
                msg = "连接服务器：{}，paramiko远程执行命令出错：{}".format(node_ip, stdout_str.decode("utf-8"))
                raise ValueError(msg)

        log.info('执行结果为：{}'.format(stdout_str.decode("utf-8")))
        return stdout.read().decode('utf-8')
    except paramiko.SSHException as e:
        msg = "连接服务器：{}，paramiko远程执行命令发生SSHException：{}".format(node_ip, e)
        raise ValueError(msg)
    except Exception as e:
        msg = "paramiko远程执行命令出错：{}".format(e)
        raise CmdException(msg)
    finally:
        client.close()


def exec_remote_cmd_back_stdout(node_ip, cmd, password=None):
    """利用paramiko远程执行命令后返回标准执行结果"""
    password, msg = get_tomcat_password(node_ip, cmd, password)
    log.info("『远程』{}".format(msg))

    try:
        # 实例化SSHClient
        client = paramiko.SSHClient()
        # 自动添加策略，保存服务器的主机名和密钥信息，如果不添加，那么不再本地know_hosts文件中记录的主机将无法连接
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # 连接SSH服务端，以用户名和密码进行认证
        client.connect(node_ip, username=PTP['def_ssh_user'], password=password, timeout=int(PTP['exec_cmd_timeout']))

        # 打开一个Channel并执行命令
        # stdout 为正确输出，stderr为错误输出，同时是有1个变量有值
        stdin, stdout, stderr = client.exec_command(cmd)
        stderr_str = stderr.read()
        if stderr_str:
            if "JMX enabled by default" in stderr_str.decode("utf-8"):
                log.warning(stderr_str)

        return stdout.read().decode('utf-8')
    except paramiko.SSHException as e:
        msg = "连接服务器：{}，paramiko远程执行命令发生SSHException：{}".format(node_ip, e)
        raise ValueError(msg)
    finally:
        client.close()


@DbErrorCollector()
def exec_remote_cmd_by_username_password(node_ip, cmd, username, password, size=None):
    """利用paramiko远程执行命令后返回标准执行结果"""
    try:
        # 实例化SSHClient
        client = paramiko.SSHClient()
        # 尝试解决「paramiko.ssh_exception.SSHException: Error reading SSH protocol banner」问题。zt@2024-07-04
        client.banner_timeout = 300
        log.info(">>>> 设置「paramiko」的「banner_timeout」为：{}".format(client.banner_timeout))
        # 自动添加策略，保存服务器的主机名和密钥信息，如果不添加，那么不再本地know_hosts文件中记录的主机将无法连接
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # 连接SSH服务端，以用户名和密码进行认证
        client.connect(node_ip, username=username, password=password, timeout=int(PTP['exec_cmd_timeout']))

        # 打开一个Channel并执行命令
        # stdout 为正确输出，stderr为错误输出，同时是有1个变量有值
        # 隐藏命令中的密码信息用于日志记录
        masked_cmd = re.sub(r"-p'[^']*'", "-p'****'", cmd)
        masked_cmd = re.sub(r"-p[^\s]+", "-p****", masked_cmd)
        log.info("开始执行远程命令:{}".format(masked_cmd))
        stdin, stdout, stderr = client.exec_command(cmd, bufsize=1000)
        stderr_str = stderr.read()
        if stderr_str:
            std_res = stderr_str.decode('utf-8')
            log.warn('打印执行命令warn日志{}'.format(std_res))
            if "JMX enabled by default" in std_res:
                log.warn("Command execution failed:{}".format(stderr_str))
            error_keyword_str = CMD_EXECUTOR_FAIL['error_keyword']
            if error_keyword_str:
                keyword_list = eval(error_keyword_str)
                for keyword in keyword_list:
                    if keyword in std_res:
                        raise ValueError(std_res)

        if size:
            return stdout.read(size).decode('utf-8')
        return stdout.read().decode('utf-8')
    except paramiko.SSHException as e:
        msg = "连接服务器：{}，paramiko远程执行命令发生SSHException：{}".format(node_ip, e)
        raise ValueError(msg)
    finally:
        client.close()


@DbErrorCollector()
def exec_db_import_remote_cmd_by_username_password(node_ip, cmd, username, password, size=None):
    """利用paramiko远程执行命令后返回标准执行结果"""
    try:
        # 实例化SSHClient
        client = paramiko.SSHClient()
        # 尝试解决「paramiko.ssh_exception.SSHException: Error reading SSH protocol banner」问题。zt@2024-07-04
        client.banner_timeout = 300
        log.info(">>>> 设置「paramiko」的「banner_timeout」为：{}".format(client.banner_timeout))
        # 自动添加策略，保存服务器的主机名和密钥信息，如果不添加，那么不再本地know_hosts文件中记录的主机将无法连接
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        # 连接SSH服务端，以用户名和密码进行认证
        client.connect(node_ip, username=username, password=password, timeout=int(PTP['exec_cmd_timeout']))

        # 打开一个Channel并执行命令
        # stdout 为正确输出，stderr为错误输出，同时是有1个变量有值
        # 隐藏命令中的密码信息用于日志记录
        masked_cmd = re.sub(r"-p'[^']*'", "-p'****'", cmd)
        masked_cmd = re.sub(r"-p[^\s]+", "-p****", masked_cmd)
        log.info("开始执行远程命令:{}".format(masked_cmd))
        stdin, stdout, stderr = client.exec_command(cmd, bufsize=1000)
        stderr_str = stderr.read()
        if stderr_str:
            std_res = stderr_str.decode('utf-8')
            log.warn('打印执行命令warn日志{}'.format(std_res))
            if "JMX enabled by default" in std_res:
                log.warn("Command execution failed:{}".format(stderr_str))
            error_keyword_str = CMD_EXECUTOR_FAIL['db_import_error_keyword']
            if error_keyword_str:
                keyword_list = eval(error_keyword_str)
                for keyword in keyword_list:
                    if keyword in std_res:
                        raise ValueError(std_res)

        if size:
            return stdout.read(size).decode('utf-8')
        return stdout.read().decode('utf-8')
    except paramiko.SSHException as e:
        msg = "连接服务器：{}，paramiko远程执行命令发生SSHException：{}".format(node_ip, e)
        raise ValueError(msg)
    finally:
        client.close()


def exec_remote_cmd_by_sshpass(agent_ip, target_ip, cmd, agent_password=None, target_password=None):
    """远程执行sshpass命令 zt@2020-09-09"""
    target_password, target_msg = get_tomcat_password(target_ip, cmd, target_password)
    log.info("远程目标机，{}".format(target_msg))
    sshpass_cmd = "sshpass -p '{}' ssh -o StrictHostKeyChecking=no {}".format(target_password, cmd)
    exec_remote_cmd(agent_ip, sshpass_cmd, password=agent_password)


def vm_stop(node_ip, app_name):
    """停止虚机应用"""
    log.info("begin停止虚机{}的{}应用进程".format(node_ip, app_name))
    """判空处理，防止app_name为空时，杀机器上所有进程 20210621 by fwm"""
    if app_name and not app_name.isspace():
        cmd = "pkill -9 -f {}".format(app_name + "/")
        exec_remote_cmd(node_ip, cmd)
    else:
        log.info("容器名参数为空，跳过")
    log.info('end停止虚机应用')


def call_test_set_by_test_set_id(set_ids, env, execute_id=None, batch_no=None, testset_detail=None):
    """根据测试集ID调用测试集"""
    log.info("begin根据测试集ID调用测试集:{}".format(set_ids))
    if TPA['itest_domain'].startswith('http://'):
        url = TPA['itest_domain'] + TPA['itest_url']
    else:
        url = 'http://' + TPA['itest_domain'] + TPA['itest_url']

    try:
        for set_id in set_ids.split(','):
            data = {
                "targetId": int(set_id),
                "runEnv": env,
                "testSetDetail": testset_detail,
                "parallel": int(TPA['itest_run_type'])
            }
            if execute_id:
                data['executeId'] = execute_id

            if batch_no:
                data['batchNo'] = batch_no

            res = http_request_post(url, data)
            result = res.json()
            log.info('返回结果为:{}'.format(result))
            
            # 解析返回结果并更新数据库
            try:
                if 'data' in result and result['data']:
                    data_content = result['data']
                    execute_id_value = data_content.get('executeId')
                    app_list_value = data_content.get('appList', [])
                    
                    if execute_id_value and app_list_value:
                        # 将appList格式化为标准的JSON格式
                        app_list_json = {"appList": app_list_value}
                        app_list_json_str = json.dumps(app_list_json)
                        
                        update_sql = """
                            UPDATE jenkins_mgt_biz_job_run_testset 
                            SET testset_app_run_detail = %s, update_time = NOW() 
                            WHERE execute_id = %s
                        """
                        
                        with DBConnectionManager() as db:
                             db.cur.execute(update_sql, (app_list_json_str, execute_id_value))
                             db.connection.commit()
                        
                        log.info('成功更新测试集应用详情: execute_id={}, appList={}'.format(execute_id_value, app_list_json_str))
                    else:
                        log.warning('返回结果中缺少executeId或appList数据，跳过更新')
                else:
                    log.warning('返回结果中缺少data字段，跳过更新')
            except Exception as e:
                log.error('解析并更新测试集应用详情失败: {}'.format(str(e)))
            
            if TPA['itest_rst_code_key'] in result and result[TPA['itest_rst_code_key']] != TPA[
                'itest_rst_success_code']:
                log.info("end根据测试集ID:{} 调用QA-INFO".format(set_id))
            else:
                log.info("根据测试集ID:{},调用QA-INFO失败，{}".format(set_id, result[TPA['itest_rst_msg_key']]))
        return result
    except Exception as e:
        log.error(e)
        raise CmdException('根据测试集ID调用测试集失败')


def get_test_flow_app_deploy_info(batch_no_suite_tuple):
    sql = '''
            SELECT cd.batch_no, ld.job_bo_dict FROM test_env_mgt_test_suite_init_log_detail ld 
            LEFT JOIN test_env_mgt_test_suite_init_log il ON ld.parent_id = il.id 
            LEFT JOIN jenkins_mgt_job_composition_detail cd ON SUBSTRING_INDEX(SUBSTRING_INDEX(cd.job_url, '/', -3), '/', 1) = il.job_build_id AND cd.suite_code = il.job_http_suite_code
            WHERE il.job_http_job_name = 'test_suite_init' AND ld.job_step = 'APP_RESTART' AND cd.job_name = 'mult_init_job_type_环境初始化'
            AND (cd.batch_no, cd.suite_code) IN ({});
          '''.format(batch_no_suite_tuple)

    with DBConnectionManager() as db:
        db.cur.execute(sql)
    data_list = []
    for row in db.cur.fetchall():
        if row.get("job_bo_dict"):
            job_bo_dict = json.loads(row.get("job_bo_dict"))
            for k, v in job_bo_dict.items():
                data_list.append(
                    {"app_name": k, "app_deploy_branch": v[0].get("selected_br_name")})
    return data_list


def check_test_set_run_result(run_batch_id_list):
    log.info("begin根据测试集批次号:{}".format(run_batch_id_list))
    if TPA['itest_domain'].startswith('http://'):
        url = TPA['itest_domain'] + TPA['itest_check_url']
    else:
        url = 'http://' + TPA['itest_domain'] + TPA['itest_check_url']
    try:
        data = {"executeIds": run_batch_id_list}
        res = http_request_post(url, data)
        result = res.json()
        return result
    except Exception as e:
        log.error(e)
        raise CmdException('根据批次号查询测试集运行状态失败！')


def abolish_test_set(run_batch_id_list):
    log.info("begin取消测试集执行批次号:{}".format(run_batch_id_list))
    if run_batch_id_list:
        if TPA['itest_domain'].startswith('http://'):
            # 处理重复斜杠问题：当domain结尾是/且abolish_test_set开头也是/时，只保留一个/
            if TPA['itest_domain'].endswith('/') and TPA['abolish_test_set'].startswith('/'):
                url = TPA['itest_domain'] + TPA['abolish_test_set'][1:]
            else:
                url = TPA['itest_domain'] + TPA['abolish_test_set']
        else:
            # 处理重复斜杠问题：当domain结尾是/且abolish_test_set开头也是/时，只保留一个/
            if TPA['itest_domain'].endswith('/') and TPA['abolish_test_set'].startswith('/'):
                url = 'http://' + TPA['itest_domain'] + TPA['abolish_test_set'][1:]
            else:
                url = 'http://' + TPA['itest_domain'] + TPA['abolish_test_set']
        try:
            data = {"executeIds": run_batch_id_list}
            res = http_request_post(url, data)
            result = res.json()
            return result
        except Exception as e:
            log.error(e)
            raise CmdException('取消测试集执行失败！')


def call_collect_k8s_app_deploy_info(suite_code=None, app_name=None):
    if not suite_code:
        suite_code = ''
    if not app_name:
        app_name = ''
    host_url = INTERFACE_URL['spider']
    if not host_url.endswith('/'):
        host_url += '/'
    url = host_url + INTERFACE_URL['spider_context'] + TPA['collect_k8s_info_api']
    data = {
        "business_name": "test_info_collect",
        "suite_code": suite_code,
        "app_name": app_name
    }

    res = http_request_post(url, data)
    result = res.json()
    log.info('返回结果为:{}'.format(result))


def call_test_set_by_test_set_id_new(set_id, env):
    """根据测试集ID调用测试集，适配魔戒新接口"""
    log.info("begin根据测试集ID调用测试集:{}".format(set_id))
    if TPA['itest_domain'].startswith('http://'):
        url = TPA['itest_domain'] + TPA['itest_url']
    else:
        url = 'http://' + TPA['itest_domain'] + TPA['itest_url']

    data = {
        "targetId": int(set_id),
        "runEnv": env,
        "parallel": int(TPA['itest_run_type']),
        "receiveMail": TPA['itest_email_list']
    }
    res = http_request_post(url, data)
    result = res.json()
    log.info('返回结果为:{}'.format(result))
    if TPA['itest_rst_code_key'] in result and result[TPA['itest_rst_code_key']] != TPA['itest_rst_success_code']:
        log.info("end根据测试集ID调用测试集")
        return True
    else:
        log.info("根据测试集ID调用测试集失败，{}".format(result[TPA['itest_rst_msg_key']]))
        raise CmdException('根据测试集ID调用测试集失败')


def detection_service_is_normal(node_ip, tomcat_name):
    """检测服务是否正常启动"""
    log.info("begin检测服务是否启动")
    cmd = "ps -ef|grep '{}'|egrep -v grep".format(tomcat_name)
    cmd_info = exec_remote_cmd_back_stdout(node_ip, cmd)
    log.info("检测结果：{}".format(cmd_info))
    if cmd_info:
        log.info("end检测服务是否启动")
        return True
    else:
        log.info("end检测服务是否启动")
        return False


def clean_cache_by_cache_node(cache_node):
    """根据cache_node清理缓存"""
    log.info("begin根据cache_node清理缓存:{}".format(cache_node))
    url = 'http://' + TPA['cache_clear_host'] + ':' + TPA['cache_clear_port'] + TPA['cache_clear_url']
    headers = {'Content-Type': 'application/json; charset=UTF-8'}
    data = {
        "identity": TPA['cache_clear_identity'],
        "zkNode": cache_node,
        "connectKey": cache_node + TPA['cache_clear_identity'],
    }
    log.info('请求地址为：{}，请求参数为：{}'.format(url, json.dumps(data)))
    res = requests.post(url, data=json.dumps(data), headers=headers)
    result = res.json()
    log.info('返回结果为：{}'.format(result))
    if TPA['cache_clear_suc_code'] in result and result[TPA['cache_clear_suc_code']]:
        log.info('清理{}'.format(cache_node))
        log.info("end根据cache_node清理缓存")
    else:
        log.info("根据cache_node清理缓存失败：{}".format(result[TPA['cache_clear_msg_code']]))
        raise Exception(result[TPA['cache_clear_msg_code']])


def get_cache_node_by_suite_code(env):
    """根据环境套获取对应cache_node"""
    log.info("begin根据环境套获取对应cache_node:{}".format(env))
    export_srv_url = 'http://' + TPA['cache_node_host'] + ':' + TPA['cache_node_port'] + TPA['cache_node_url']
    export_data = {
        "env": env
    }
    res = http_request(export_srv_url, export_data)
    res_text = res.text
    log.info('返回结果为:{}'.format(res_text))
    if not res_text.startswith('error'):
        log.info("end根据环境套获取对应cache_node")
        return res_text
    else:
        log.info("根据环境套获取对应cache_node失败，{}".format(res_text))
        raise CmdException('根据环境套获取对应cache_node失败')


def ccms_config_download(env):
    """下载CCMS"""
    log.info("begin下载CCMS,传入环境{}".format(env))
    export_srv_url = 'http://' + TPA['ccms_exp_host'] + ':' + TPA['ccms_exp_port'] + TPA['ccms_exp_url']
    operate_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    export_security_code = gen_signature(md5_factory(operate_time, TPA['ccms_exp_salt'], TPA['ccms_exp_env'], ''))
    export_data = {
        "envFlag": TPA['ccms_exp_env'],
        "groupNames": '',  # 不传groupNames为所有组, 待万成开发, 十月后上线
        "operator_ip": '',
        "operator_name": '',
        "operator_time": operate_time,
        "security_code": export_security_code
    }
    res = http_request(export_srv_url, export_data)

    res_json = res.json()
    log.info('返回结果json化:{}'.format(res_json))
    if res_json[TPA['ccms_exp_res_code_key']] == 200:
        remote_file = res_json[TPA['ccms_exp_res_file_key']]
        local_ccms_download_path = os.path.join(TPA['nfs_root_path'], env, 'ccms', 'ccms_online.xls')
        cmd = "rsync -av --password-file=/home/<USER>/rsync.pass rsync@{}::{}{} {}".format(
            TPA['ccms_exp_host'],
            TPA['ccms_exp_file_path'],
            remote_file.split('/')[-1], local_ccms_download_path)
        log.info('Exec: {}'.format(cmd))
        local_cmd = exec_local_cmd(cmd)
        log.info(local_cmd)
        log.info("end下载CCMS")
    else:
        raise CmdException('请求CCMS下载接口失败')


def ccms_config_replace(suite_code, app_dict):
    """CCMS配置替换"""
    log.info("begin CCMS配置替换")
    request_address = INTERFACE_URL["zeus"] + "/zeus-service/apiForDeploy/envinit2"
    nfs_root_path = TPA['nfs_root_path']
    data_list = [{"module_name": 'ccms',
                  "src_path": "{}/{}/ccms/ccms_online.xls".format(nfs_root_path, suite_code),
                  "suite_code": suite_code,
                  "zeus_type": '1',
                  "target_path": "{}/{}/ccms/ccms_new_{}.xls".format(nfs_root_path, suite_code, suite_code)}]

    log.info("CCMS调用接口{}".format(request_address))
    log.info("CCMS调用参数{}".format(data_list))
    i = 0
    while i < 3:
        res = requests.post(url=request_address, data=json.dumps(data_list),
                            headers={'Content-Type': 'application/json'}, timeout=(3, 600))
        log.info("CCMS配置替换接口调用结果{}".format(res.text))
        if res.status_code == 200:
            if res.text == "success":
                log.info('end CCMS配置替换')
                return True
            else:
                i = i + 1
                sleep(3)
                log.error(res.text)
                # raise ValueError(res.text)
        else:
            i = i + 1
            sleep(3)
    raise ValueError("CCMS配置替换接口{}调用失败 {}".format(request_address, res))


def ccms_config_replace_after(suite_code, app_dict):
    """CCMS配置替换后校验接口"""
    log.info('begin CCMS配置替换后校验接口')
    request_address = INTERFACE_URL["zeus"] + "/zeus-service/apiForDeploy/setAndget_detail_info"
    param = {'env': suite_code, 'apps': 'ccms'}
    log.info('请求路经为：{},参数为：{}'.format(request_address, json.dumps(param)))
    res = request(INTERFACE_URL["zeus"],
                  '/zeus-service/apiForDeploy/setAndget_detail_info',
                  {'env': suite_code, 'apps': 'ccms', 'rule_type': ''})
    res_json = res.text
    log.info('CCMS配置替换后校验接口返回结果：{}'.format(res_json))
    log.info('end 调用CCMS配置替换后校验接口')


def ccms_config_upload_test(env):
    """导入测试CCMS"""
    log.info('begin 导入测试CCMS')
    local_ccms_upload_file = os.path.join(TPA['nfs_root_path'], env, 'ccms', TPA['ccms_imp_file_name'].format(env))
    remote_file_path = TPA['ccms_imp_file_path'] + TPA['ccms_imp_file_name'].format(env)
    result = False
    pattern = re.compile('192.168.*')
    ccms_imp_host = TPA['ccms_imp_host']
    pattern.search(ccms_imp_host)
    if not pattern.search(ccms_imp_host):
        raise CmdException("网段不合规：{}".format(ccms_imp_host))
    try:
        exec_local_rsync_to_remote(TPA['ccms_imp_host'], local_ccms_upload_file, remote_file_path)
        # push_local_to_other(TPA['ccms_imp_host'], local_ccms_upload_file, remote_file_path)
        result = True
    except Exception as e:
        log.error(str(e))
    if not result:
        raise Exception("CCMS文件导入失败")
    else:
        log.info("CCMS文件导入成功")
    import_env = 'docker-k8s-{}'.format(env)
    import_srv_url = 'http://' + TPA['ccms_imp_host'] + ':' + TPA['ccms_imp_port']
    operate_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    import_security_code = gen_signature(md5_factory(operate_time, TPA['ccms_imp_salt'], import_env))
    import_data = {
        'envFlag': import_env,
        'fileName': TPA['ccms_imp_file_name'].format(env),
        'needCall': 'no',
        'operator_ip': '',
        'operator_name': '',
        'operator_time': operate_time,
        'security_code': import_security_code
    }
    log.info("调用CCMS导入接口，地址:{}{}, 参数：{}".format(import_srv_url, TPA['ccms_imp_url'], import_data))
    res = request(import_srv_url, TPA['ccms_imp_url'], import_data)
    log.info('调用CCMS导入接口返回结果：{}'.format(res))
    res_json = res.json()
    if res_json[TPA['ccms_imp_res_code_key']] == 200:
        log.info("请求CCMS导入接口成功")
    else:
        raise Exception('请求CCMS导入接口失败: ' + str(res_json))


def upload_to_svn(env, svn_commit_msg=None, param_dict=None):
    """CCMS上传SVN"""
    log.info(">>>> 调用「svn上传」，参数：len(param_dict) = {}".format(len(param_dict) if param_dict else None))

    # 环境套地址
    svn_suite_path = TPA['svn_suite_path'].format(env)

    # svn远程根目录
    svn_ser_root_path = "svn://{}/{}/{}/{}".format(TPA['svn_server'], TPA['svn_repos_doc_path'], TPA['svn_config_path'],
                                                   svn_suite_path)
    log.info(">>>> svn_ser_root_path = {}".format(svn_ser_root_path))

    # 本机tms19下的svn缓存目录：svn_cache
    svn_cache_path = os.path.join(TPA['root_path'], env, TPA['svn_cache_path'])
    log.info(">>>> svn_cache_path = {}".format(svn_cache_path))

    # 本机svn_cache下的svn环境目录：docker-k8s-tms19
    svn_suite_cache_path = os.path.join(svn_cache_path, svn_suite_path)
    log.info(">>>> svn_suite_cache_path = {}".format(svn_suite_cache_path))

    # svn实例
    svn = SvnCmd()

    # 配置分发
    if param_dict:
        log.info('begin 配置文件上传SVN')

        svn_remote_cache_path = os.path.join(svn_suite_cache_path, TPA['svn_remote_cache_path'])
        svn_tomcat_cache_path = os.path.join(svn_suite_cache_path, TPA['svn_tomcat_cache_path'])
        # remote
        if os.path.exists(svn_remote_cache_path):
            cmd = 'rm -rf {}'.format(svn_remote_cache_path)
            exec_local_cmd(cmd)
        # tomcat
        if os.path.exists(svn_tomcat_cache_path):
            cmd = 'rm -rf {}'.format(svn_tomcat_cache_path)
            exec_local_cmd(cmd)

        svn_remote_path = os.path.join(svn_ser_root_path, TPA['svn_remote_cache_path'])
        svn_tomcat_path = os.path.join(svn_ser_root_path, TPA['svn_tomcat_cache_path'])
        # 整体拉remote
        svn.svn_checkout(svn_remote_path, svn_suite_cache_path)
        # 整体拉tomcat
        svn.svn_checkout(svn_tomcat_path, svn_suite_cache_path)

        # 循环应用
        for k, v in param_dict.items():
            log.info('======== 开始处理应用：{} ======='.format(k))
            # svn的容器目录
            svn_container_path = v.get('svn_container_path')

            # 配置过的svn才处理 zt@2020-12-11
            if svn_container_path:
                package_type = v.get('package_type')
                deploy_config_path = v.get('deploy_config_path')
                container_name = v.get('container_name')

                if package_type.lower() == 'jar':
                    svn_type_cache_path = svn_remote_cache_path
                    type_path = TPA['svn_remote_cache_path']
                elif package_type.lower() == 'remote' or package_type.lower() == 'static':
                    svn_type_cache_path = svn_tomcat_cache_path + '/tstatic/'
                    type_path = TPA['svn_tomcat_cache_path'] + '/tstatic/'
                else:
                    svn_type_cache_path = svn_tomcat_cache_path
                    type_path = TPA['svn_tomcat_cache_path']

                svn_ser_app_path = "{}/{}/{}".format(svn_ser_root_path, type_path, svn_container_path)

                # rm svn的本地缓存目录（基于应用的容器名）
                # 优化在清理时，不删除隐蔽文件 zt@2021-04-21
                svn_app_cache_path = os.path.join(svn_type_cache_path, svn_container_path)
                if os.path.exists(svn_app_cache_path):
                    cmd = 'rm -rf {}/*'.format(svn_app_cache_path)
                    exec_local_cmd(cmd)

                # 拉取
                # svn远端的ccms地址（单拉一个应用）
                # 优化支持多级目录拉取 zt@2021-04-21
                svn_ws = svn_type_cache_path
                svn_container_path_idx = svn_container_path.rfind("/")
                if svn_container_path_idx > 0:
                    svn_container_path__prefix = svn_container_path[:svn_container_path_idx]
                    svn_ws = os.path.join(svn_ws, svn_container_path__prefix)
                    log.info(">>>> svn多级目录，应用「{}」，工作空间「{}」。".format(k, svn_ws))
                svn.svn_checkout(svn_ser_app_path, svn_ws)

                # 清理文件
                if os.path.exists(svn_app_cache_path):
                    clear_svn_path(svn_type_cache_path, container_name, svn_container_path, deploy_config_path)
                else:
                    cmd = 'mkdir -p {}'.format(svn_app_cache_path)
                    exec_local_cmd(cmd)
                    log.warn(">>>>『警告』svn上没有配置目录：{}".format(svn_app_cache_path))

                # 复制覆盖
                # 目前写死为 config + 应用名，等数据化（表：env_mgt_svn_path.cache_path）
                app_conf_cache_path = os.path.join(TPA['root_path'], env, 'config', k)
                app_conf_copy_path = os.path.join(app_conf_cache_path, '.')

                re_conf_pattern = r'.*' + svn_container_path
                conf_path = re.sub(re_conf_pattern, svn_container_path, deploy_config_path)
                log.info(">>>> 应用「{}」配置后缀「{}」。".format(k, conf_path))
                svn_target_path = os.path.join(svn_type_cache_path, conf_path)
                if not os.path.exists(svn_target_path):
                    cmd = 'mkdir -p {}'.format(svn_target_path)
                    exec_local_cmd(cmd)

                if os.listdir(app_conf_cache_path):
                    cmd = '\\cp -a {} {}'.format(app_conf_copy_path, svn_target_path)
                    exec_local_cmd(cmd)
                    log.info(">>>> 应用「{}」配置从「{}」(cp -a)到「{}」。".format(k, app_conf_copy_path, svn_target_path))
                else:
                    log.warn(">>>> 应用「{}」配置为空目录。".format(k, app_conf_cache_path))

                # svn del操作
                svn.svn_del_files(workspace=svn_app_cache_path)
            else:
                log.warn(">>>> 应用「{}」没有配置svn_path，跳过『配置复制到svn缓存目录』。".format(k))
        # 循环结束，分别提交remote和tomcat
        # svn add
        cmd = 'cd {} && svn add * --no-ignore --force'.format(svn_remote_cache_path)
        exec_local_cmd(cmd)
        # svn commit
        svn.svn_commit(svn_commit_msg, svn_remote_cache_path)

        # svn add
        cmd = 'cd {} && svn add * --no-ignore --force'.format(svn_tomcat_cache_path)
        exec_local_cmd(cmd)
        # svn commit
        log.info("准备提交")
        svn.svn_commit(svn_commit_msg, svn_tomcat_cache_path)
        log.info("提交结束")
    else:  # ccms分发
        log.info('begin CCMS文件上传SVN')
        # 本机ccms的目录
        svn_ccms_cache_path = os.path.join(svn_suite_cache_path, TPA['svn_ccms_cache_path'])

        # ccms目录清理
        if os.path.exists(svn_ccms_cache_path):
            log.info(">>>> 清理「ccms」的svn缓存目录：{}".format(svn_ccms_cache_path))
            cmd = 'rm -rf {}'.format(svn_ccms_cache_path)
            exec_local_cmd(cmd)

        # 更新svn
        # svn远端的ccms地址
        svn_ser_ccms_path = "{}/{}".format(svn_ser_root_path, TPA['svn_ccms_cache_path'])
        svn.svn_checkout(svn_ser_ccms_path, svn_suite_cache_path)

        # 复制ccms文件
        ccms_cache_file = os.path.join(TPA['root_path'], env, TPA['ccms_cache_path'],
                                       TPA['ccms_cache_file'].format(env))
        if os.path.exists(ccms_cache_file):
            cmd = '\\cp -r {} {}'.format(ccms_cache_file, svn_ccms_cache_path)
            exec_local_cmd(cmd)
            log.info(">>>> ccms文件：「{}」，已复制到：「{}」。".format(ccms_cache_file, svn_ccms_cache_path))
        else:
            log.warn(">>>> 本地没找到ccms文件：「{}」，跳过『ccms文件复制。』。".format(ccms_cache_file))

        # ccms提交
        # svn add
        cmd = 'cd {} && svn add * --no-ignore --force'.format(svn_ccms_cache_path)
        exec_local_cmd(cmd)
        # svn commit
        svn.svn_commit(svn_commit_msg, svn_ccms_cache_path)


def upload_to_config_repo(suite_code, param_dict=None):
    config_repo_git_url = TPA['conf_repo_gitlab_ssh']
    config_repo_git_workspace = os.path.join(TPA['root_path'], suite_code, TPA['conf_repo_gitlab_name'])
    config_repo_branch_name = TPA['conf_repo_gitlab_branch']
    commit_desc = "update by be-script" + time.strftime('%Y-%m-%d %H:%M:%S')
    tstatic_container_name = 'tstatic'

    # push前先pull，防并发
    os.chdir(config_repo_git_workspace)
    cmd = 'git pull'
    exec_local_cmd(cmd, 1200)

    if param_dict:
        for k, v in param_dict.items():
            config_copy_source = os.path.join(TPA['nfs_root_path'], suite_code, 'config', k)
            config_copy_target = os.path.join(TPA['root_path'], suite_code, TPA['conf_repo_gitlab_name'], suite_code)
            if v.get("container_name") == tstatic_container_name:
                config_copy_target = os.path.join(TPA['root_path'], suite_code, TPA['conf_repo_gitlab_name'],
                                                  suite_code, tstatic_container_name)

            if not os.path.exists(config_copy_target):
                cmd = 'mkdir -p {}'.format(config_copy_target)
                exec_local_cmd(cmd)
            cmd = 'rm -rf {}'.format(os.path.join(config_copy_target, k))
            exec_local_cmd(cmd)
            cmd = '\\cp -r {} {}'.format(config_copy_source, config_copy_target)
            exec_local_cmd(cmd)

        push_info_git_for_config(config_repo_git_url, config_repo_git_workspace, config_repo_branch_name, commit_desc)


def get_conf_cache_path(type_enum, suite_code, module_name, zeus_type, app_dict):
    """采用和「全流程构建」一样的方式，解析配置的缓存目录 zt@2020-12-28"""
    # 缓存配置根目录取/data/devops_nfs/test_publish_aio_nfs 20221108 by fwm

    module_config = app_dict[module_name]
    deploy_type = module_config["deploy_type"]
    package_type = module_config["package_type"]
    lib_repo_config_path = module_config["lib_repo_config_path"]

    # 基础路径
    base_path = os.path.join(TPA["nfs_root_path"], suite_code)
    # 处理TEST_PUSH类型
    if type_enum == TypeEnum.TEST_PUSH:
        path = get_default_test_path(module_name)
        return os.path.join(base_path, path, '')

    # 处理非TEST_PUSH类型
    if (deploy_type == 1 and zeus_type != 1) or package_type in ['h5', 'static', 'remote']:
        path = get_default_test_path(module_name)
    else:
        path = get_production_path(module_name, lib_repo_config_path)

    # 处理MULTI_PUSH特殊情况
    if type_enum == TypeEnum.MULTI_PUSH and package_type in ['jar', 'war', 'tar']:
        selected_br_name = module_config.get("selected_br_name")
        archive_br_name = module_config.get("archive_br_name")
        online_br_name = module_config.get("online_br_name")

        if selected_br_name in (archive_br_name, online_br_name):
            path = get_production_path(module_name, lib_repo_config_path)
        else:
            path = get_default_test_path(module_name)

    return os.path.join(base_path, path, '')

def get_default_test_path(module_name: str) -> str:
    """获取默认测试路径"""
    return os.path.join(TPA["test_app_resource_gitlab_name"], module_name)

def get_production_path(module_name: str, lib_repo_config_path: str) -> str:
    """获取生产环境路径"""
    if lib_repo_config_path:
        return os.path.join(TPA["app_resource_gitlab_name"], lib_repo_config_path)
    return os.path.join(TPA["app_resource_gitlab_name"], "ec", module_name, CONFIG_DIR_FLAG["bs-prod"])


def http_request_for_zeus(suite_code, app_dict, type_enum):
    """调用宙斯配置替换http接口"""
    request_address = INTERFACE_URL["zeus"] + "/zeus-service/apiForDeploy/envinit2"
    log.info('begin 调用宙斯配置替换http接口，地址为：{}'.format(request_address))
    data_list = []
    for module_name in app_dict:
        # 不同操作类型对数据的差异处理 zt@2020-12-29
        # 部署类型
        deploy_type = app_dict[module_name]["deploy_type"]
        # 是否宙斯
        zeus_type = get_zeus_type(suite_code, module_name, app_dict, type_enum)
        # 包类型
        package_type = app_dict[module_name]["package_type"]
        # 自定义配置
        lib_repo_config_path = app_dict[module_name]["lib_repo_config_path"]
        # 获取「配置缓存路径」
        conf_cache_path = get_conf_cache_path(type_enum, suite_code, module_name, zeus_type, app_dict)
        # 数据源
        src_cache_config_path = app_dict[module_name]["src_cache_config_path"]
        # 不一致时会有一个警告
        if conf_cache_path != src_cache_config_path:
            log.warn(">>>> 配置缓存路径，采用新逻辑：conf_cache_path = " + conf_cache_path)
        target_cache_config_path = app_dict[module_name]["target_cache_config_path"]

        log.info(">>>> 「配置缓存源路径」conf_cache_path = {}".format(conf_cache_path))
        log.info(">>>> 「配置缓存目标路径」target_cache_config_path = {}".format(target_cache_config_path))

        if zeus_type == 0:
            continue
        elif zeus_type == 1:
            if os.path.isdir(target_cache_config_path):
                if len(target_cache_config_path.split("/")) > 3:
                    log.info("rm -rf {}".format(target_cache_config_path))
                    os.system("rm -rf {}/*".format(target_cache_config_path))
            else:
                os.system("mkdir -p {}".format(target_cache_config_path))

            cmd = 'cp -r {}. {}'.format(conf_cache_path, target_cache_config_path)
            exec_local_cmd(cmd)
        else:
            if type_enum == TypeEnum.TEST_PUSH:
                log.warn(">>>>『{}』操作时非宙斯应用「{}」，跳过『配置替换』。".format(type_enum.cn_name, module_name))
                continue

            data_list.append({"module_name": module_name,
                              "src_path": conf_cache_path,
                              "suite_code": suite_code,
                              "zeus_type": "{}".format(zeus_type),
                              "target_path": target_cache_config_path})
    if len(data_list) > 0:
        log.info('请求路经为：{},参数为：{}'.format(request_address, json.dumps(data_list)))
        i = 0
        while i < 3:
            res = requests.post(url=request_address, data=json.dumps(data_list),
                                headers={'Content-Type': 'application/json'}, timeout=(3, 600))
            log.info('请求返回结果为：{}'.format(res.text))
            if res.status_code == 200:
                if res.text == "success":
                    return True
                else:
                    i = i + 1
                    sleep(3)
                    log.error(res.text)
            else:
                i = i + 1
                sleep(3)
        raise ValueError("宙斯接口调用失败 {}".format(request_address))
    else:
        log.info('宙斯配置替换http接口无需调用')


def get_zeus_type(suite_code, module_name, app_dict, type_enum):
    if type_enum in (TypeEnum.INIT, TypeEnum.HISTORY, TypeEnum.CRON):
        if TestMgtPublishApp.select().where(TestMgtPublishApp.suite_code == suite_code,
                                            TestMgtPublishApp.module_name == module_name):
            log.info("本次zeus_type取online_zeus_type: {}".format(app_dict[module_name]["online_zeus_type"]))
            zeus_type = app_dict[module_name]["online_zeus_type"]
        else:
            log.info("本次zeus_type取archive_zeus_type: {}".format(app_dict[module_name]["archive_zeus_type"]))
            zeus_type = app_dict[module_name]["archive_zeus_type"]
    elif type_enum in (TypeEnum.MULTI_PUSH, TypeEnum.TEST_PUSH):
        log.info("本次zeus_type取selected_zeus_type: {}".format(app_dict[module_name]["selected_zeus_type"]))
        zeus_type = app_dict[module_name]["selected_zeus_type"]
    else:
        log.info("本次zeus_type取app_mgt_app_module表中的zeus_type: {}".format(app_dict[module_name]["zeus_type"]))
        zeus_type = app_dict[module_name]["zeus_type"]
    return zeus_type


def http_request_for_zeus_after(suite_code, app_dict):
    """调用宙斯配置替换后校验接口http接口"""
    request_address = INTERFACE_URL["zeus"] + "/zeus-service/apiForDeploy/setAndget_detail_info"
    log.info('begin 调用宙斯配置替换后校验接口http接口：{}'.format(request_address))
    app_list = []
    for module_name in app_dict:
        if app_dict[module_name]["zeus_type"] == 0 or app_dict[module_name]["zeus_type"] == 1:
            continue
        else:
            app_list.append(module_name)

    log.info('请求路经为：{},参数为：{}'.format(request_address, ','.join(app_list)))
    res = request(INTERFACE_URL["zeus"],
                  '/zeus-service/apiForDeploy/setAndget_detail_info',
                  {'env': suite_code, 'apps': ','.join(app_list), 'rule_type': ''})
    res_json = res.text
    log.info('调用宙斯配置替换后校验接口返回结果：{}'.format(res_json))
    log.info('end调用宙斯配置替换后校验接口')


def zeus_config_synchronos(app_name, br_name, suite_code):
    """zeus接口同步、发布"""
    zeus_work = ZeusAPI(app_name, br_name, suite_code)
    log.info('begin 宙斯配置同步发布，地址：{}'.format(zeus_work.ZEUS_URL))
    log.info('调用宙斯同步接口:')
    i = 0
    while i < 3:
        try:
            sync_log = zeus_work.env_sync()
            return_message = json.loads(sync_log)
            if return_message['code'] == 'success':
                log.info("调用同步接口第{}次".format(i + 1))
                break
            else:
                sleep(3)
                i += 1
        except Exception as err:
            log.info("宙斯服务异常，流程中止！请登录宙斯平台，排查同步问题。")
            sys.exit(1)
    log.info(sync_log)
    log.info('调用宙斯发布接口:')
    i = 0
    while i < 3:
        try:
            publish_lob = zeus_work.release()
            return_message = json.loads(publish_lob)
            if return_message['code'] == 'success':
                log.info("调用发布接口第{}次".format(i + 1))
                break
            else:
                sleep(3)
                i += 1
        except Exception as err:
            log.info("宙斯服务异常，流程中止！请登录宙斯平台，排查发布问题。")
            sys.exit(1)
    log.info(publish_lob)
    log.info('end 宙斯配置同步发布')


def zeus_config_replace(config_path, env, br_name):
    """宙斯配置文件替换"""
    log.info("begin宙斯配置文件替换，参数为：config_path:{}, env:{}, br_name:{}".format(config_path, env, br_name))
    if not os.path.isdir(config_path):
        log.error("{}目录不存在，请先将配置库clone到本地".format(config_path))
        raise CmdException("{}目录不存在，请先将配置库clone到本地".format(config_path))
    try:
        ignore_list = None
        ignore_list_str = TPA['zeus_replace_exclude_regex_list']
        log.info(">>>> 宙斯配置文件排除的正则列表，ignore_list_str = %s", ignore_list_str)
        if ignore_list_str:
            ignore_list = [x.strip() for x in ignore_list_str.split(",") if x]
            log.info(">>>> 宙斯配置文件排除的正则列表：%s", ignore_list)
        else:
            log.warn(">>>> 宙斯配置文件排除的正则列表：为空！！！")
        all_match_version_count = 0
        all_match_env_count = 0
        for file_path in search.file_search(config_path, ignore=ignore_list):
            match_version_count = 0
            match_env_count = 0
            log.info("修改的配置文件路径{}".format(file_path))
            # 临时解决方案，判断js文件后缀即为前端的配置文件方式，则跳过配置key的替换，注：js不是nacos连接的配置方式
            if file_path.endswith(".js"):
                match_version_count = 1
                match_env_count = 1
                all_match_version_count = all_match_version_count + match_version_count
                all_match_env_count = all_match_env_count + match_env_count
                log.info("js配置文件类型，为remote服务，无需配置替换")
                break
            with open(file_path, "r+", encoding='utf-8', errors="ignore") as f:
                file_content = f.read()
                for key_type in ['version', 'env']:
                    if key_type == 'version':
                        replace_str = br_name
                    else:
                        replace_str = env
                    for version_key in NACOS_KEY[key_type]:
                        """判断是否能找到系统配置的key，如果找不到，说明外移文件中的key不是系统认识的key，修改必然不成功"""
                        if re.search("({}=).*".format(version_key), file_content):
                            if key_type == 'version':
                                match_version_count = match_version_count + 1
                            else:
                                match_env_count = match_env_count + 1
                            file_content = re.sub("({}=).*".format(version_key), "\g<1>{}".format(replace_str),
                                                  file_content)

                            file_content = nacos_config_replace(file_content)

                f.seek(0, 0)
                f.truncate()
                f.write(file_content)
            log.info("{}中的应用版本号被改次数为{}".format(file_path, match_version_count))
            log.info("{}中的环境被改次数为{}".format(file_path, match_env_count))
            all_match_version_count = all_match_version_count + match_version_count
            all_match_env_count = all_match_env_count + match_env_count
            log.info("{}内容为:".format(file_path))
            exec_local_cmd(cmd=f'cat {file_path}')
        if not all_match_version_count or not all_match_env_count:
            raise CmdException(
                "外移文件改失败啦，没找到要替换的key，请确认外移文件中的key与部署平台的配置[NACOS_KEY.ENV,NACOS_KEY.VERSION]是否一致！") from Exception
    except Exception as err:
        log.info("宙斯配置文件{},值{},修改异常：{}".format(config_path, env, err))
        raise CmdException("宙斯配置文件修改异常：{}".format(err))


def nacos_config_replace(file_content):
    if re.search("({}=).*".format('nacos.config.username'), file_content):
        file_content = re.sub("({}=).*".format('nacos.config.username'),
                              "\g<1>{}".format(NACOS_KEY['nacos_config_username']),
                              file_content)
    else:
        file_content += '\r\n' + 'nacos.config.username={}'.format(
            NACOS_KEY['nacos_config_username']) + '\r\n'
    if re.search("({}=).*".format('nacos.config.password'), file_content):
        file_content = re.sub("({}=).*".format('nacos.config.password'),
                              "\g<1>{}".format(NACOS_KEY['nacos_config_password']),
                              file_content)
    else:
        file_content += '\r\n' + 'nacos.config.password={}'.format(
            NACOS_KEY['nacos_config_password']) + '\r\n'

    return file_content


def zeus_config_replace_after(config_path, env):
    """宙斯配置文件替换后校验"""
    pass


def ssh_path_mkdir(ip, path):
    """远程创建目录"""
    password, msg = get_tomcat_password(ip)
    log.info("begin在远程服务器{}上创建目录{}".format(ip, path))
    log.info(msg)
    exec_local_cmd('''
                    sshpass -p '{}' ssh tomcat@{} "mkdir -p {}"
                   '''.format(password, ip, path))


def ssh_path_whether_exist(ip, path, is_file=False):
    """判断远程目录或文件是否存在"""
    # chk_status = None
    # chk_msg = None
    password, msg = get_tomcat_password(ip)
    if is_file:
        log.info("begin检测远程服务器{}上文件{}是否存在".format(ip, path))
        log.info(msg)
        status = exec_local_cmd("sshpass -p '{}' ssh tomcat@{} [ -f {} ] && echo yes || echo no".
                                format(password, ip, path))
        status = bytes.decode(status.stdout).replace('\n', '')
        if str(status) == 'yes':
            chk_status = True
            chk_msg = "OK"
            # return True
        elif str(status) == 'no':
            chk_status = False
            chk_msg = "远程服务器{}上文件{}不存在".format(ip, path)
            log.info(chk_msg)
            # return False
        else:
            chk_status = False
            chk_msg = '未知返回状态：{}'.format(status)
            log.info(chk_msg)
    else:
        log.info("begin检测远程服务器{}上目录{}是否存在".format(ip, path))
        log.info(msg)
        status = exec_local_cmd("sshpass -p '{}' ssh tomcat@{} [ -d {} ] && echo yes || echo no".
                                format(password, ip, path))
        status = bytes.decode(status.stdout).replace('\n', '')
        if str(status) == 'yes':
            chk_status = True
            chk_msg = "OK"
            # return True
        elif str(status) == 'no':
            chk_status = False
            chk_msg = "远程服务器{}上目录{}不存在".format(ip, path)
            log.info(chk_msg)
            # return False
        else:
            chk_status = False
            chk_msg = '未知返回状态：{}'.format(status)
            log.info(chk_msg)

    return chk_status, chk_msg


def local_path_whether_exist(path):
    """判断本地目录或文件是否存在"""
    log.info("检测本地目录{}是否存在".format(path))
    if os.path.exists(path):
        return True
    else:
        log.info("目录{}不存在".format(path))
        return False


def push_local_to_other_rsync(target_ip, source_path, target_path, is_delete=None, password=None):
    """rsync从执行机推送到目标目录begin"""
    if not source_path.endswith("/"):
        source_path = source_path + '/'
    log.info(
        "begin rsync从执行机推送到目标目录:target_ip:{}、source_path:{}、target_path:{}".format(target_ip,
                                                                                              source_path, target_path))
    if len(str(target_path).split('/')) < 5:
        raise CmdException("目录长度检测：异常！至少三层以上!")
    else:
        print("目录长度检测：通过！")

    is_dir = False
    count = 0
    file_path = ""
    file_name = ""
    for file in os.listdir(source_path):
        if file == ".git":
            continue
        if os.path.isfile(os.path.join(source_path, file)):
            count = count + 1
            file_path = os.path.join(source_path, file)
            file_name = file
        else:
            is_dir = True
            break
    # 判断条件应该是count > 0 20220329 by fwm
    if is_dir or count > 0:
        log.warn("**************source_path:{} is dir or count {}***************".format(source_path, count))
        pass
    else:
        del_cmd = "rm -rf {}".format(os.path.join(target_path, '*.jar'))
        exec_remote_cmd_back_stdout(target_ip, del_cmd)
        source_path = file_path
    rsync_cmd = "ionice -c3 rsync{} -LPa --timeout={} --exclude='*.git' {} {}@{}:{}".format(
        ' --delete' if is_delete else '',
        int(PTP['exec_cmd_timeout']),
        source_path,
        PTP['def_ssh_user'],
        target_ip,
        target_path)

    log.info("执行rsync命令:{}".format(rsync_cmd))
    chk_status, chk_msg = ssh_path_whether_exist(target_ip, target_path, False)
    if not chk_status and '目录' in chk_msg:
        # 远端目录不存在，去创建一次 20220803 by fwm
        ssh_path_mkdir(target_ip, target_path)
        chk_status, chk_msg = ssh_path_whether_exist(target_ip, target_path, False)
        if not chk_status and '目录' in chk_msg:
            raise ValueError(chk_msg)
    # if not ssh_path_whether_exist(target_ip, target_path, False):
    #     raise CmdException("执行目录不存在")
    try:
        completed_process_obj1 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第1次rsync成功：{}".format(bytes.decode(completed_process_obj1.stdout)))
    except Exception as e1:
        log.error(">>>> 第1次rsync失败 >> msg：{}".format(e1))

    try:
        completed_process_obj2 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第2次rsync成功：{}".format(bytes.decode(completed_process_obj2.stdout)))
    except Exception as e2:
        log.error(">>>> 第2次rsync失败 >> msg：{}".format(e2))

    try:
        completed_process_obj3 = exec_local_cmd_by_sshpass(target_ip, rsync_cmd, password)
        log.info("第3次rsync成功：{}".format(bytes.decode(completed_process_obj3.stdout)))
    except Exception as e3:
        log.error(">>>> 第3次rsync失败 >> msg：{}".format(e3))
        raise CmdException(">>>> 3次rsync全部失败: {}".format(e3))
    if is_dir or count > 1:
        pass
    else:
        target_path = os.path.join(target_path, file_name)
    check_cmd = "find {} -type f ! -name '*.git' ! -path '*/.git/*' | xargs -d '\n' du -sh -b | ".format(source_path)
    source_size = exec_local_cmd(check_cmd + "awk '{sum1+= $1}END{print sum1}'")
    check_cmd = "find {} -type f ! -name '*.git' ! -path '*/.git/*' | xargs -d '\n' du -sh -b | ".format(target_path)
    target_size = exec_remote_cmd_back_stdout(target_ip, check_cmd + "awk '{sum1+= $1}END{print sum1}'")
    source_size = bytes.decode(source_size.stdout).replace('\n', '')
    log.info('执行机目录文件大小{}'.format(source_size))
    log.info('远程目录文件大小{}'.format(target_size))
    log.info('source_size类型为：{}'.format(type(source_size)))
    log.info('target_size类型为：{}'.format(type(target_size)))
    log.info('转换后source_size大小为：{}'.format(float(source_size)))
    log.info('转换后target_size大小为：{}'.format(float(target_size)))
    if float(source_size) != float(target_size):
        log.error("推送过程中发生包丢失情况")
        raise CmdException("推送过程中发生包丢失情况")
    log.info("end rsync从执行机推送到目标目录！")


def http_request(url, params, headers=None):
    """调用外部http接口"""
    log.info("begin调用外部http接口:url:{}, params:{}".format(url, params))
    if headers:
        response = requests.get(url + '?' + urllib.parse.urlencode(params), headers=headers)
    else:
        response = requests.get(url + '?' + urllib.parse.urlencode(params))
    # response = urllib.request.urlopen(url, data=data)
    log.info("调用结果：{}".format(response))
    log.info("end调用外部http接口！")
    return response


def http_request_post(url, params, headers={'Content-Type': 'application/json'}):
    """调用外部http接口"""
    log.info("begin调用外部http接口:url:{}, params:{}".format(url, params))
    response = requests.post(url, headers=headers, data=json.dumps(params))
    # response = urllib.request.urlopen(url, data=data)
    log.info("调用结果：{}".format(response))
    log.info("end调用外部http接口！")
    return response


def push_local_to_other(target_ip, source_path, target_path, password=None):
    """scp从执行机推送到目标目录"""
    log.info(
        "begin scp从执行机推送到目标目录:target_ip:{}、source_path:{}、target_path:{}".format(target_ip, source_path,
                                                                                            target_path))
    scp_cmd = "scp -r {} {}@{}:{}".format(source_path, PTP['def_ssh_user'], target_ip, target_path)
    log.info("执行命令:{}".format(scp_cmd))
    sshpass = exec_local_cmd_by_sshpass(target_ip, scp_cmd, password)
    log.info("执行结果:{}".format(sshpass))
    log.info("end从执行机推送！")


def push_info_git_for_static(git_path, workspace, branch_name, msg, app_name=None):
    """
    git 创建远程分支
    """

    log.info('begin git push代码参数分别为：workspace：{}, branch_name：{}, msg：{}, app_name:{}'.format
             (workspace, branch_name, msg, app_name))
    os.chdir(workspace)

    app_cache_path = os.path.join(workspace, app_name)
    if not os.path.exists(app_cache_path):
        cmd = "mkdir -p {}".format(app_name)
        exec_local_cmd(cmd)
        log.info("执行clone的命令为：git clone {} {}".format(git_path, app_name))
        # cmd = "git clone --depth 1 -b {} {} {}".format('master', git_path, app_name)
        # 只有git 1.7.1的版本 用深度1克隆master，能把所有分支克隆下来
        # 新版本的git ，比如 1.9.5 ，用深度1克隆master分支，只能克隆master一个分支 20211205 by fwm
        cmd = "git clone {} {}".format(git_path, app_name)
        exec_local_cmd(cmd)

    os.chdir(app_name)
    log.info('当前工作目录为：{}'.format(os.getcwd()))
    # 适配git 新版本 20221027 by fwm
    # log.info("git pull")
    # os.system("git pull")
    cmd = 'git remote set-branches origin {}'.format(branch_name)
    log.info('切换远端分支： {}'.format(cmd))
    os.system(cmd)
    cmd = 'git fetch --depth 1 origin {}'.format(branch_name)
    log.info('fetch远程分支至工作区，采用「--depth 1」浅克隆： {}'.format(cmd))
    os.system(cmd)
    cmd = 'git checkout {}'.format(branch_name)
    log.info('切换工作区至目标分支： {}'.format(cmd))
    os.system(cmd)

    log.info('执行的检测命令为：git branch -a |grep "{}" && echo yes || echo no'.format(branch_name))
    result = subprocess.Popen('git branch -a |grep "{}" && echo yes || echo no'.format(branch_name),
                              shell=True,
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE)
    status = bytes.decode(result.stdout.read()).replace('\n', '')
    log.info('检测分支是否存在结果为：{}'.format(status))
    if str(status) == 'no':
        raise CmdException("{}分支{}不存在,请联系创建!".format(app_name, branch_name))
    else:
        log.info('分支已存在')
        os.chdir(workspace)
        log.info("rm -rf {}".format(app_name))
        os.system("rm -rf {}".format(app_name))
        log.info("git clone --depth 1 -b {} {} {}".format(branch_name, git_path, app_name))
        os.system("git clone --depth 1 -b {} {} {}".
                  format(branch_name, git_path, app_name))

    # os.chdir(workspace)
    # if app_name:
    #     pull_info_git(git_path, branch_name, workspace, app_name)
    # # 制品库分支不存在则创建，并切换到该分支
    # result = subprocess.Popen('git branch |grep "* {}"'.format(branch_name), shell=True, stdout=subprocess.PIPE,
    #                           stderr=subprocess.PIPE)
    # log.info('检测制品库是否存在结果为{}'.format(result.stdout.read()))
    # if not result.stdout.read():
    #     log.info("创建分支，git checkout -b {}".format(branch_name))
    #     os.system('git checkout -b {}'.format(branch_name))
    # os.system("git add .")
    # os.system('git commit -m "{}"'.format(msg))
    # os.system("git push origin {}".format(branch_name))

    log.info('end git push代码')


def push_info_git_for_config(git_path, workspace, branch_name, msg, app_name=None):
    """
    git 创建远程分支
    """
    log.info('begin git push代码参数分别为：workspace：{}, branch_name：{}, msg：{}, app_name:{}'.format
             (workspace, branch_name, msg, app_name))
    os.chdir(workspace)
    if app_name:
        os.chdir(app_name)
    log.info('当前工作目录为：{}'.format(os.getcwd()))
    os.system("git add -A")
    os.system('git commit -m "{}"'.format(msg))
    os.system("git push origin {}".format(branch_name))
    log.info('end git push代码')


def pull_info_git(git_path, br_version, target_path, app_name):
    """从git拉东西放到执行机begin！"""
    log.info("begin从git拉东西放到执行机:git_path:{}、br_version:{},、target_path:{}、app_name:{}".format
             (git_path, br_version, target_path, app_name))
    # 切换到应用对应的制品库目录
    if not os.path.exists(target_path):
        cmd = 'mkdir -p {}'.format(target_path)
        log.info("执行命令:{}".format(cmd))
        exec_local_cmd(cmd)
    os.chdir(target_path)
    # 拉取
    # 拉取分支制品
    if not os.path.exists(app_name):
        cmd = 'git clone -b {} --depth 1 {} {}'.format(br_version, git_path, app_name)
        log.info("执行命令:{}".format(cmd))
        exec_local_cmd(cmd, 1200)
        log.info("从git拉东西end!")
    else:
        # 切换至分支目录
        os.chdir(app_name)
        # 放弃本地修改
        cmd = 'git checkout .'
        exec_local_cmd(cmd, 1200)
        # 更新制品
        cmd = 'git pull'
        exec_local_cmd(cmd, 1200)
        # log.info('切换分支到{}'.format(br_version))
        # cmd = "git checkout {}".format(br_version)
        # exec_local_cmd(cmd, 600)
        # cmd = 'git pull origin {}'.format(br_version)
        # exec_local_cmd(cmd, 1200)
        cmd = 'git remote set-branches origin {}'.format(br_version)
        log.info('切换远端分支： {}'.format(cmd))
        exec_local_cmd(cmd, 600)
        cmd = 'git fetch --depth 1 origin {}'.format(br_version)
        log.info('fetch远程分支至工作区，采用「--depth 1」浅克隆： {}'.format(cmd))
        exec_local_cmd(cmd, 1200)
        cmd = 'git checkout {}'.format(br_version)
        log.info('切换工作区至目标分支： {}'.format(cmd))
        exec_local_cmd(cmd, 600)
    log.info("end从git拉东西放到执行机！")


def step_decorator(name):
    def decorator(func):
        @functools.wraps(func)
        def des(*args, **kwargs):
            try:
                func_name = func.__name__
                func_doc = func.__doc__
                kwargs['func_name'] = func_name
                kwargs['func_doc'] = func_doc
                log.info("====================================")
                log.info("Step({}): {}start".format(func_name, name))
                func(*args, **kwargs)
                log.info("Step({}): {}end".format(func_name, name))
                log.info("====================================")
            except Exception as ex:
                log.error("{}fail: {}".format(name, str(ex)))
                raise ValueError("{}fail: {}".format(name, str(ex)))

        return des

    return decorator


def get_all_files_by_tree(path):
    cmd = r'tree -fip {} | grep "^\[" | cut -c 15-'.format(path)
    tmp_file_bytes = exec_local_popen(cmd)
    return [bytes.decode(byte_obj) for byte_obj in tmp_file_bytes.split(b'\n') if byte_obj]


def get_only_files_by_tree(path):
    cmd = r'tree -fip {} | grep "^\[-" | cut -c 15-'.format(path)
    tmp_file_bytes = exec_local_popen(cmd)
    return [bytes.decode(byte_obj) for byte_obj in tmp_file_bytes.split(b'\n') if byte_obj]


def recur_svn_del_files(res_file_list, curr_path, conf_path, level=0):
    # 解析Svn缓存目录的文件列表
    curr_file_list = os.listdir(curr_path)
    # 排序
    curr_file_list.sort()

    re_pattern = r'^/?(.*?/)(.*)$'
    re_match = re.match(re_pattern, conf_path)
    # 当前目录，直接扫所有文件
    if conf_path == '' or conf_path == '/':
        tmp_file_list = get_only_files_by_tree(curr_path)
        res_file_list += tmp_file_list
    else:
        if re_match:
            s_path = re_match.group(1)
            e_path = re_match.group(2)
            if s_path.endswith('/'):
                s_path = s_path[:-1]

            log.info(">>>> s_path({}) = {}".format(level, s_path))
            log.info(">>>> e_path({}) = {}".format(level, e_path))
            # 直接在配置目录下，也只解析文件 zt@2021-04-22
            if s_path in curr_file_list:
                for tmp_file in curr_file_list:
                    if tmp_file in SVN_EXCLUDE_LIST:
                        continue

                    abs_file = os.path.join(curr_path, tmp_file)
                    if os.path.isfile(abs_file):
                        res_file_list.append(abs_file)
                        continue

                    if s_path == tmp_file:
                        if s_path and e_path:
                            if level < SVN_MAX_LEVEL:
                                recur_svn_del_files(res_file_list, abs_file, e_path, level + 1)
                            else:
                                log.warn(">>>> 警告：超过递归解析的最大深度「{}」，直接获取所有「文件」".format(level))
                                conf_root_path = os.path.join(curr_path, tmp_file)
                                tmp_file_list = get_only_files_by_tree(conf_root_path)
                                res_file_list += tmp_file_list
                        else:
                            conf_root_path = os.path.join(curr_path, tmp_file)
                            tmp_file_list = get_only_files_by_tree(conf_root_path)
                            res_file_list += tmp_file_list
                    else:
                        res_file_list.append(abs_file)
            else:
                tmp_file_list = get_all_files_by_tree(curr_path)
                res_file_list += tmp_file_list
        else:
            for tmp_file in curr_file_list:
                # 主目录下的 .svn 文件夹也不能删除 zt@2021-04-22
                if tmp_file in SVN_EXCLUDE_LIST:
                    continue

                abs_file = os.path.join(curr_path, tmp_file)
                if os.path.isfile(abs_file):
                    res_file_list.append(abs_file)
                    continue
                if tmp_file == conf_path:
                    tmp_file_list = get_only_files_by_tree(abs_file)
                    res_file_list += tmp_file_list
                else:
                    res_file_list.append(abs_file)


def clear_svn_path(root_path, container_name, svn_container_name, target_path):
    # 根据配置部署路径和容器名，获取部署前缀：
    # remote -> xx/conf/
    # tomcat -> xx/WEB-INF/classes/
    # tstatic -> tmsXX/tomcat/tstatic/xxx（新增）
    # 新增tstatic目录解析的支持，暂不考虑容器名和Svn目录不致的情况 zt@2021-04-22
    re_conf_pattern = r'.*' + svn_container_name
    conf_path = re.sub(re_conf_pattern, '', target_path)

    file_list = []
    s_time = datetime.datetime.now()
    recur_svn_del_files(file_list, os.path.join(root_path, svn_container_name), conf_path, 0)
    e_time = datetime.datetime.now()
    timedelta = e_time - s_time
    cost_time = timedelta.microseconds
    log.info(">>>> 目录解析耗时，cost_time(秒) = {}".format(cost_time / 1000000))

    if file_list:
        files_str = ' '.join(x for x in file_list)
        cmd = 'rm -rf {}'.format(files_str)
        exec_local_cmd(cmd)


def prepare_start_script(package_type, root_path, suite_code, start_script_template_gitlab_name, app_name,
                         script_name, start_script_cache_path):
    if package_type == "jar":
        start_script_source = os.path.join(root_path, suite_code, start_script_template_gitlab_name, app_name,
                                           script_name)
    if package_type == "war":
        start_script_source = os.path.join(root_path, suite_code, start_script_template_gitlab_name, 'tomcat',
                                           script_name)
    cmd = "cp -r {} {}".format(start_script_source, start_script_cache_path)
    exec_local_cmd(cmd)


def pull_sql_lib_repo(path, br_version, db_group, git_url, is_delete=True):
    git_path = git_url + db_group + '.git'
    if os.path.exists(path) and is_delete:
        if len(path.split('/')) > 2:
            exec_local_cmd("rm -rf {}".format(path))
    cmd = 'git clone -b {} --depth 1 {} {}'.format(br_version, git_path, path)
    exec_local_cmd(cmd)


def exec_flyway_command(db_info, cmd, filesystem):
    """
    flyway执行
     """
    configFiles = '/data/flyway-commandline/conf/flyway.conf'
    # filesystem = '/data/migrate_flyway/{}/{}/{}'.format(branch_info.get_iteration_id(self.app_name, self.br_name),db_info['db_group_name'], db_info['iter_version'],
    #                                                     db_info['db_name'])
    flyway_command = '''/data/flyway-commandline/flyway -user={} -password='{}' -url="{}" -configFiles={} -locations=filesystem:{} {}'''. \
        format(db_info['db_user'], db_info['db_passwd'], db_info['conn_url'], configFiles, filesystem, cmd)
    exec_local_cmd(flyway_command)


def sort_sql_common(sql_src_path, flyway_cache_path, available_br_dict):
    log.info("sql源路径：{}".format(sql_src_path))
    branch_dir_list = [f.path for f in os.scandir(sql_src_path) if f.is_dir()]
    log.info("一级子目录列表：{}".format(branch_dir_list))
    # branch_dir_list = ['/data/test_publish_aio/it15/db/TRADE/archive/1.0.0', '/data/test_publish_aio/it15/db/TRADE/archive/3.11.3']
    group_name = os.path.basename(os.path.dirname(sql_src_path))
    log.info("group_name：{}".format(group_name))
    # group_name = TRADE
    for branch_dir in branch_dir_list:
        log.info("branch_dir：{}".format(branch_dir))
        # branch_dir = /data/test_publish_aio/it15/db/TRADE/archive/1.0.0
        branch_name = os.path.basename(branch_dir)
        log.info("branch_name：{}".format(branch_name))
        # branch_name = 1.0.0
        available_branch_list = []
        if available_br_dict:
            # available_br_dict = {'TRADE': ['3.35.1']}
            available_branch_list = available_br_dict.get(group_name)

        db_name_dir_list = [f.path for f in os.scandir(branch_dir) if f.is_dir()]
        log.info("二级子目录列表：{}".format(db_name_dir_list))
        for db_name_dir in db_name_dir_list:
            db_name = os.path.basename(db_name_dir)
            log.info("db_name_dir：{} 目录的db_name是：{}".format(db_name_dir, db_name))
            ddl_dir = os.path.join(db_name_dir, "DDL")
            dml_dir = os.path.join(db_name_dir, "DML")
            ddl_flyway_path = os.path.join(flyway_cache_path, db_name + "/DDL")
            dml_flyway_path = os.path.join(flyway_cache_path, db_name + "/DML")
            log.info("ddl_flyway_path：{}".format(ddl_flyway_path))
            log.info("dml_flyway_path：{}".format(dml_flyway_path))
            if not os.path.exists(ddl_flyway_path):
                cmd = 'mkdir -p {}'.format(ddl_flyway_path)
                exec_local_cmd(cmd)
            if not os.path.exists(dml_flyway_path):
                cmd = 'mkdir -p {}'.format(dml_flyway_path)
                exec_local_cmd(cmd)
            if os.path.exists(ddl_dir):
                # 分拣全量的DDL
                cmd = 'cp -r {}/* {}'.format(ddl_dir, ddl_flyway_path)
                log.info("======DDL======拷贝ddl sql，按每个数据库来分拣sql：{}".format(cmd))
                exec_local_cmd(cmd)
            log.info("available_branch_list:{}".format(available_branch_list))
            log.info("branch_name:{}".format(branch_name))
            log.info("os.path.exists(dml_dir):{}".format(os.path.exists(dml_dir)))
            if available_branch_list and branch_name in available_branch_list and os.path.exists(dml_dir):
                cmd = 'cp -r {}/* {}'.format(dml_dir, dml_flyway_path)
                log.info("======DML======拷贝分支{}的dml sql，按每个数据库来分拣sql：{}".format(branch_name, cmd))
                exec_local_cmd(cmd)


def sort_sql_common_second_step(sql_src_path, flyway_cache_path, available_br_dict):
    """SQL分拣通用方法第二步 - 排除DML的SQL"""
    log.info("sql源路径：{}".format(sql_src_path))
    branch_dir_list = [f.path for f in os.scandir(sql_src_path) if f.is_dir()]
    log.info("一级子目录列表：{}".format(branch_dir_list))
    # branch_dir_list = ['/data/test_publish_aio/it15/db/TRADE/archive/1.0.0', '/data/test_publish_aio/it15/db/TRADE/archive/3.11.3']
    group_name = os.path.basename(os.path.dirname(sql_src_path))
    log.info("group_name：{}".format(group_name))
    # group_name = TRADE
    for branch_dir in branch_dir_list:
        log.info("branch_dir：{}".format(branch_dir))
        # branch_dir = /data/test_publish_aio/it15/db/TRADE/archive/1.0.0
        branch_name = os.path.basename(branch_dir)
        log.info("branch_name：{}".format(branch_name))
        # branch_name = 1.0.0
        available_branch_list = []
        if available_br_dict:
            # available_br_dict = {'TRADE': ['3.35.1']}
            available_branch_list = available_br_dict.get(group_name)

        db_name_dir_list = [f.path for f in os.scandir(branch_dir) if f.is_dir()]
        log.info("二级子目录列表：{}".format(db_name_dir_list))
        for db_name_dir in db_name_dir_list:
            db_name = os.path.basename(db_name_dir)
            log.info("db_name_dir：{} 目录的db_name是：{}".format(db_name_dir, db_name))
            ddl_dir = os.path.join(db_name_dir, "DDL")
            dml_dir = os.path.join(db_name_dir, "DML")
            ddl_flyway_path = os.path.join(flyway_cache_path, db_name + "/DDL")
            dml_flyway_path = os.path.join(flyway_cache_path, db_name + "/DML")
            if not os.path.exists(ddl_flyway_path):
                cmd = 'mkdir -p {}'.format(ddl_flyway_path)
                exec_local_cmd(cmd)
            if not os.path.exists(dml_flyway_path):
                cmd = 'mkdir -p {}'.format(dml_flyway_path)
                exec_local_cmd(cmd)
            if os.path.exists(ddl_dir):
                # 分拣全量的DDL
                cmd = 'cp -r {}/* {}'.format(ddl_dir, ddl_flyway_path)
                log.info("======DDL======拷贝ddl sql，按每个数据库来分拣sql：{}".format(cmd))
                exec_local_cmd(cmd)

            # 排除DML的SQL处理 - 注释掉原有的DML拷贝逻辑
            # if available_branch_list and branch_name in available_branch_list and os.path.exists(dml_dir):
            #     cmd = 'cp -r {}/* {}'.format(dml_dir, dml_flyway_path)
            #     log.info("======DML======拷贝分支{}的dml sql，按每个数据库来分拣sql：{}".format(branch_name, cmd))
            #     exec_local_cmd(cmd)
            log.info("======排除DML======跳过分支{}的dml sql拷贝".format(branch_name))


def update_data_base_config(opt_type, db_info):
    """
    处理制作dump和检查dump时，数据库配置处理
    """
    log.info('替换前的数据库配置如下：{}'.format(json.dumps(db_info)))
    db_type = db_info.get("db_srv_type")
    log.info('opt_type = {}'.format(opt_type))
    if opt_type == 1:
        log.info('opt_type = 1')
    elif opt_type == 2:
        parameter = None
        if db_type == 'mysql':
            dump_create_config = DUMP.get('mysql_dump_create_config')
            parameter = json.loads(dump_create_config)
            replace_mysql_db_config(db_info, parameter)
        elif db_type == 'oracle':
            dump_create_config = DUMP.get('oracle_dump_create_config')
            parameter = replace_oracle_db_config(db_info, dump_create_config)
        replace_db_config(db_info, parameter)

    elif opt_type == 3:
        parameter = None
        if db_type == 'mysql':
            dump_check_config = DUMP.get('mysql_dump_check_config')
            parameter = json.loads(dump_check_config)
            replace_mysql_db_config(db_info, parameter)
        elif db_type == 'oracle':
            dump_check_config = DUMP.get('oracle_dump_check_config')
            parameter = replace_oracle_db_config(db_info, dump_check_config)
        replace_db_config(db_info, parameter)
    else:
        raise Exception('错误的操作类型:{}'.format(opt_type))
    log.info('替换后的数据库配置如下：{}'.format(json.dumps(db_info)))


def replace_db_config(db_info, parameter):
    if parameter:
        db_info['db_srv_username'] = parameter.get('db_srv_username')
        db_info['db_srv_password'] = parameter.get('db_srv_password')
        db_info['db_srv_bash_profile'] = parameter.get("db_srv_bash_profile")
        db_info['data_dump_dir'] = parameter.get("data_dump_dir")
        db_info['db_srv_hosts'] = parameter.get('db_srv_hosts')


def replace_oracle_db_config(db_info, dump_check_config):
    parameter = json.loads(dump_check_config)
    data_base_prefix = parameter.get('data_base_prefix')
    suite_db_name = data_base_prefix + db_info.get('suite_db_name')
    db_info['conn_url'] = parameter.get('conn_url')
    db_info['suite_db_name'] = suite_db_name
    db_info['db_srv_name'] = parameter.get('db_srv_name')
    db_info['username'] = suite_db_name
    return parameter


def replace_mysql_db_config(db_info, parameter):
    db_info['username'] = parameter.get('db_username')
    db_info['password'] = parameter.get('db_password')
    db_info['db_srv_socket_path'] = parameter.get('db_srv_socket_path')
    data_base_prefix = parameter.get('data_base_prefix')
    suite_db_name = data_base_prefix + db_info.get('suite_db_name')
    conn_url = db_info.get('conn_url').replace(db_info.get('db_srv_hosts'), parameter.get('db_srv_hosts'))
    conn_url = conn_url.replace(db_info.get('suite_db_name'), suite_db_name)
    db_info['conn_url'] = conn_url
    db_info['suite_db_name'] = suite_db_name


def increment_sort_sql_common(sql_src_path, flyway_cache_path, include_branches_list):
    log.info("sql源路径：{}".format(sql_src_path))
    first_child_dir_list = [f.path for f in os.scandir(sql_src_path) if f.is_dir()]
    log.info("一级子目录列表：{}".format(first_child_dir_list))
    for first_child_dir in first_child_dir_list:
        for branch in include_branches_list:
            # 只copy需要传入的SQL目录
            if branch in first_child_dir:
                cmd = 'cp -r {} {}'.format(first_child_dir + '/*', flyway_cache_path)
                log.info("拷贝sql，按每个数据库来分拣sql：{}".format(cmd))
                exec_local_cmd(cmd)


def archery_login(request_address, username, password):
    param = {"username": username, "password": password}
    curr_time = datetime.datetime.now()
    headers = {'Content-type': 'application/json'}
    log.info('http================')
    s = requests.session()
    i = 0
    while i < 3:
        try:
            response = s.post(request_address, headers=headers, data=json.dumps(param))
            with DBConnectionManagerForSqlalchemy() as db:
                iterMgtSqlHandleLog = IterMgtSqlHandleLog(
                    archery_url=request_address,
                    request_type="login",
                    request_header=headers,
                    request_param=param,
                    response_json=response.json(),
                    create_user=username,
                    create_time=curr_time)
                db.session.add(iterMgtSqlHandleLog)
                db.session.commit()
        except Exception as ex:
            i = i + 1
            log.info('sleep')
            sleep(3)
            log.info(ex)
            continue
        log.info('请求返回结果为：{}'.format(response.json().get('access')))
        access = response.json().get('access')
        if access:
            return access
        else:
            i = i + 1
            log.info('sleep')
            sleep(3)
    log.info('response================{}'.format(response.json().get('access')))
    return response.json().get('access')


def sql_check(param_data):
    log.info('====================sql检测的请求参数:{}'.format(param_data))

    # prod_env_list = ['prod', 'bs-prod', 'pd-prod', 'tcloud-prod']
    prod_env_list = get_suite_code_by_region_group([PROCESS_CODE_PROD])

    s = requests.session()
    param = {"db_name": 'docker_{}_{}'.format(param_data.get("suite_code"), param_data.get("db_name")),
             "full_sql": param_data.get("sql_content"),
             "instance_id": param_data.get("instance_id")}

    if param_data.get("suite_code") in prod_env_list:
        param = {"db_name": param_data.get("db_name"),
                 "full_sql": param_data.get("sql_content"),
                 "instance_id": param_data.get("instance_id")}

    header = {"Authorization": "Bearer {}".format(param_data.get("access_token")), 'Content-Type': 'application/json'}
    log.info('====================header:{}'.format(header))
    response = s.post(param_data.get("request_address"), headers=header, data=json.dumps(param))
    log.info('===========sql_check_response:{}'.format(response.json()))

    with DBConnectionManagerForSqlalchemy() as db:
        iterMgtSqlHandleLog = IterMgtSqlHandleLog(
            archery_bind_id=param_data.get("archery_bind_id"),
            module_name=param_data.get("module_name"),
            suite_code=param_data.get("suite_code"),
            archery_url=param_data.get("request_address"),
            request_type="check",
            request_header=header,
            request_param=param,
            response_json=response.json(),
            iteration_id=param_data.get("iteration_id"),
            create_user=param_data.get("opt_user"),
            create_time=datetime.datetime.now())
        db.session.add(iterMgtSqlHandleLog)
        db.session.commit()
    if response.json().get("detail") == "您没有执行该操作的权限。":
        raise ValueError("{}没有archery执行该操作的权限。请联系dba解决！".format(param_data.get("opt_user")))

    return response.status_code, response.json()


def sql_commit(param_data):
    s = requests.session()
    param = {
        "sql_content": param_data.get("sql_content"),
        "workflow": {
            "workflow_name": param_data.get("workflow_name"),
            "demand_url": "",
            "group_id": param_data.get("group_id"),
            "db_name": param_data.get("db_name"),
            "is_backup": False,
            "engineer": param_data.get("opt_user"),
            "run_date_start": "",
            "run_date_end": "",
            "instance": param_data.get("instance_id")
        }
    }

    headers = {"Authorization": "Bearer {}".format(param_data.get("access_token")), "Content-Type": "application/json"}

    response = s.post(param_data.get("request_address"), headers=headers, data=json.dumps(param))
    log.info('===========sql_commit_response{}'.format(response.json()))
    log.info(response.status_code)

    with DBConnectionManagerForSqlalchemy() as db:
        iterMgtSqlHandleLog = IterMgtSqlHandleLog(
            archery_bind_id=param_data.get("archery_bind_id"),
            module_name=param_data.get("module_name"),
            suite_code=param_data.get("suite_code"),
            archery_url=param_data.get("request_address"),
            request_type="commit",
            request_header=headers,
            request_param=param,
            response_json=response.json(),
            iteration_id=param_data.get("iteration_id"),
            create_user=param_data.get("opt_user"),
            create_time=datetime.datetime.now())
        db.session.add(iterMgtSqlHandleLog)
        db.session.commit()

    return response.status_code, response.json()


def checkout_repo(db_cache_path, local_path, repo_url, br_name):
    result = True
    if not os.path.exists(local_path):
        cmd = 'cd {} && git clone {} {}'.format(db_cache_path, repo_url, local_path)
        exec_local_cmd(cmd)
    else:
        cmd = 'cd {} && git pull'.format(local_path)
        exec_local_cmd(cmd)

    cmd = 'cd {} && git branch --show-current'.format(local_path)
    current_br = bytes.decode(exec_local_cmd(cmd).stdout).strip()
    log.info("当前分支为: {}".format(current_br))
    log.info("需要切换到的分支为: {}".format(br_name))
    if current_br != br_name:
        try:
            cmd = 'cd {} && git checkout {}'.format(local_path, br_name)
            exec_local_cmd(cmd)
        except Exception as err:
            log.info("仓库{}的分支{}不存在".format(repo_url, br_name))
            result = False

    return result


# 过滤掉拆基础库之前的 dml
def ignore_f_dml(basic_db_code, db_name_list, flyway_cache_path):
    log.info("ignore_f_dml: {}".format(basic_db_code))
    biz_sql_init_ver = json.loads(TEST_DATA_INIT["biz_sql_init_ver"])
    # if not biz_sql_init_ver.get(basic_db_code):
    #     return
    log.info("基础库的版本为: {}".format(biz_sql_init_ver.get(basic_db_code)))
    log.info("db_name_list: {}".format(db_name_list))
    for db_nm in db_name_list:
        version = biz_sql_init_ver.get((basic_db_code + '_' + db_nm).lower())
        if version:
            for root, dirs, files in os.walk(os.path.join(flyway_cache_path, db_nm, "DML")):
                for file in files:
                    log.info(file)
                    if file.endswith('.sql') and file.split('__')[0] < version:
                        log.info("删除{}文件".format(os.path.join(root, file)))
                        os.remove(os.path.join(root, file))


def biz_sql_sort(bis_dir, db_group_name_list, flyway_cache_path, available_branches_dict=None):
    # 数据
    # bis_dir_list = [os.path.join(db_cache_path, basic_db_code + '_master'),
    #                 os.path.join(db_cache_path, basic_db_code + '_' + bis_br_name)]
    # bis_dir_list = [os.path.join(db_cache_path, basic_db_code)]
    # for bis_dir in bis_dir_list:

    if os.path.exists(bis_dir):
        bis_sql_db_group_list = [f.path for f in os.scandir(bis_dir) if
                                 f.is_dir() and not f.name.startswith('.')]
        log.info('{}的子目录列表为: {}'.format(bis_dir, bis_sql_db_group_list))
        for bis_sql_db_group in bis_sql_db_group_list:
            if 'archive' in bis_sql_db_group:
                # 只复制有效的分支sql

                bis_branch_dir_list = [f.path for f in os.scandir(bis_sql_db_group) if
                                       f.is_dir() and not f.name.startswith('.')]
                log.info('bis_branch_dir_list{}'.format(bis_branch_dir_list))
                for bis_branch_dir in bis_branch_dir_list:

                    bis_db_group_name_list = [f.path for f in os.scandir(bis_branch_dir) if
                                              f.is_dir() and not f.name.startswith('.')]
                    log.info('bis_db_group_name_list{}'.format(bis_db_group_name_list))
                    for bis_db_group_name in bis_db_group_name_list:
                        # 只copy与应用相同的数据库组
                        if bis_db_group_name.split('/')[-1] in db_group_name_list:

                            if available_branches_dict is None or bis_db_group_name.split('/')[
                                -1] not in available_branches_dict:
                                cmd = 'cp -r {} {}'.format(bis_db_group_name + '/*', flyway_cache_path)
                                log.info("复制归档迭代的sql: " + cmd)
                                exec_local_cmd(cmd)

                            else:
                                biz_iter_br = bis_db_group_name.split('/')[-2]
                                log.info("biz_iter_br: {}".format(biz_iter_br))
                                if biz_iter_br in available_branches_dict[bis_db_group_name.split('/')[-1]]:
                                    cmd = 'cp -r {} {}'.format(bis_db_group_name + '/*', flyway_cache_path)
                                    log.info("复制有效的归档迭代的sql: " + cmd)
                                    exec_local_cmd(cmd)
                                else:
                                    log.info("开始复制{}分支的sequence".format(biz_iter_br))
                                    # 分拣sequence
                                    bis_db_name_dir_list = [f.path for f in os.scandir(bis_db_group_name) if
                                                            f.is_dir() and not f.name.startswith('.')]
                                    for db_name_dir in bis_db_name_dir_list:
                                        log.info("biz_iter_br: {}, db_name_dir:{}".format(biz_iter_br, db_name_dir))
                                        # /data/test_data_init/it44/it1000/archive/20240703sunshuangqing/PAY/DML/seq
                                        db_name = db_name_dir.split('/')[-1]
                                        seq_dir = os.path.join(db_name_dir, 'DML/seq')
                                        if os.path.exists(seq_dir):
                                            fc_path = os.path.join(flyway_cache_path, db_name, 'DDL')
                                            log.info("biz_iter_br: {}, fc_path:{}".format(biz_iter_br, fc_path))
                                            if not os.path.exists(fc_path):
                                                cmd = 'mkdir -p {}'.format(fc_path)
                                                exec_local_cmd(cmd)
                                            cmd = 'cp -r {} {}'.format(seq_dir + '/*', fc_path)
                                            log.info("复制已归档迭代的seq: " + cmd)
                                            exec_local_cmd(cmd)

            # 只copy与应用相同的数据库组
            if bis_sql_db_group.split('/')[-1] in db_group_name_list:
                cmd = 'cp -r {} {}'.format(bis_sql_db_group + '/*', flyway_cache_path)
                log.info("复制本迭代的sql到本地" + cmd)
                exec_local_cmd(cmd)


def calculate_directory_md5(directory_path):
    """计算目录下所有文件的MD5值"""
    import hashlib
    import tempfile
    
    if not os.path.exists(directory_path):
        log.warn("目录不存在: {}".format(directory_path))
        return ""
    
    # 确保目录路径以/结尾，与远程脚本保持一致
    if not directory_path.endswith(os.sep):
        base_dir = directory_path + os.sep
    else:
        base_dir = directory_path
    
    file_md5_list = []
    
    # 遍历目录下所有文件
    for root, dirs, files in os.walk(directory_path):
        # 排除.git目录
        if '.git' in dirs:
            dirs.remove('.git')
        
        for file in sorted(files):  # 排序确保一致性
            if file.startswith('.git'):
                continue
            file_path = os.path.join(root, file)
            # 计算相对路径，与远程脚本逻辑保持一致
            relative_path = os.path.relpath(file_path, directory_path)
            # 在Windows上将反斜杠转换为正斜杠，保持与Linux一致
            relative_path = relative_path.replace(os.sep, '/')
            
            try:
                file_md5_value = file_md5(file_path)
                # 将相对路径和MD5值组合
                combined_str = "{}:{}".format(relative_path, file_md5_value)
                file_md5_list.append(combined_str)
            except Exception as e:
                log.error("计算文件MD5失败: {}, 错误: {}".format(file_path, e))
                continue
    
    # 对文件MD5列表排序确保一致性
    file_md5_list.sort()
    
    if not file_md5_list:
        log.info("目录 {} 为空或无有效文件".format(directory_path))
        return ""
    
    # 创建临时文件，模拟远程脚本的行为
    with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as tmp_file:
        for item in file_md5_list:
            tmp_file.write(item + '\n')
        tmp_file_path = tmp_file.name
    
    try:
        # 计算临时文件的MD5，与远程脚本保持一致
        result_md5 = file_md5(tmp_file_path)
        log.info("目录 {} 的MD5值: {}".format(directory_path, result_md5))
        return result_md5
    finally:
        # 清理临时文件
        try:
            os.unlink(tmp_file_path)
        except:
            pass


def get_remote_directory_md5(target_ip, directory_path, password=None):
    """获取远程目录的MD5值"""
    try:
        # 创建临时脚本来计算远程目录MD5
        script_content = '''#!/bin/bash
if [ ! -d "{directory_path}" ]; then
    echo ""
    exit 0
fi

# 确保目录路径以/结尾
base_dir="{directory_path}"
if [[ "$base_dir" != */ ]]; then
    base_dir="$base_dir/"
fi

# 创建临时文件存储文件列表和MD5
tmp_file="/tmp/md5_calc_$$.tmp"

# 查找所有文件，排除.git相关文件，并按路径排序
find "$base_dir" -type f ! -name '*.git' ! -path '*/.git/*' | sort | while read file; do
    if [ -f "$file" ]; then
        # 计算相对路径 - 使用sed替代bash字符串替换
        relative_path=$(echo "$file" | sed "s|^$base_dir||")
        # 计算文件MD5
        file_md5=$(md5sum "$file" | cut -d' ' -f1)
        echo "$relative_path:$file_md5" >> "$tmp_file"
    fi
done

# 如果临时文件存在，计算整体MD5
if [ -f "$tmp_file" ]; then
    md5sum "$tmp_file" | cut -d' ' -f1
    rm -f "$tmp_file"
else
    echo ""
fi
'''.format(directory_path=directory_path)
        
        # 在远程服务器上执行脚本
        temp_script_path = "/tmp/calc_md5_{}.sh".format(int(time.time() * 1000000))
        
        # 创建脚本文件
        create_script_cmd = "cat > {} << 'EOF'\n{}\nEOF".format(temp_script_path, script_content)
        exec_remote_cmd(target_ip, create_script_cmd, password)
        
        # 执行脚本
        exec_script_cmd = "chmod +x {} && {}".format(temp_script_path, temp_script_path)
        result = exec_remote_cmd_back_stdout(target_ip, exec_script_cmd, password)
        
        # 清理临时脚本
        cleanup_cmd = "rm -f {}".format(temp_script_path)
        exec_remote_cmd(target_ip, cleanup_cmd, password)
        
        remote_md5 = result.strip() if result else ""
        log.info("远程目录 {}@{} 的MD5值: {}".format(directory_path, target_ip, remote_md5))
        return remote_md5
        
    except Exception as e:
        log.error("获取远程目录MD5失败: {}, 错误: {}".format(directory_path, e))
        return ""


def push_local_to_other_rsync_with_md5_check(target_ip, source_path, target_path, is_delete=None, password=None):
    """带MD5检查的rsync推送优化函数"""
    if not source_path.endswith("/"):
        source_path = source_path + '/'
    
    log.info("开始MD5优化推送: target_ip:{}, source_path:{}, target_path:{}".format(
        target_ip, source_path, target_path))
    
    # 1. 计算源目录MD5
    source_md5 = calculate_directory_md5(source_path.rstrip('/'))
    if not source_md5:
        log.error("无法计算源目录MD5，跳过推送")
        return
    
    # 2. 检查远程目录是否存在，不存在则创建
    chk_status, chk_msg = ssh_path_whether_exist(target_ip, target_path, False)
    if not chk_status and '目录' in chk_msg:
        log.info("远程目录不存在，创建目录: {}".format(target_path))
        ssh_path_mkdir(target_ip, target_path)
        chk_status, chk_msg = ssh_path_whether_exist(target_ip, target_path, False)
        if not chk_status and '目录' in chk_msg:
            raise ValueError(chk_msg)
    
    # 3. 计算远程目录MD5
    remote_md5 = get_remote_directory_md5(target_ip, target_path, password)
    
    # 4. 比较MD5值
    if source_md5 == remote_md5 and remote_md5 != "":
        log.info("MD5值相同，跳过推送。源MD5: {}, 远程MD5: {}".format(source_md5, remote_md5))
        return
    
    log.info("MD5值不同，开始推送。源MD5: {}, 远程MD5: {}".format(source_md5, remote_md5))
    
    # 5. 执行原有的推送逻辑
    push_local_to_other_rsync(target_ip, source_path, target_path, is_delete, password)
    
    # 6. 推送完成后再次验证MD5
    #  final_remote_md5 = get_remote_directory_md5(target_ip, target_path, password)
    # if source_md5 != final_remote_md5:
    #  log.error("推送后MD5验证失败！源MD5: {}, 远程MD5: {}".format(source_md5, final_remote_md5))
    #  raise CmdException("推送后MD5验证失败")
    
    #log.info("MD5优化推送完成，验证成功。最终MD5: {}".format(final_remote_md5))
    log.info("MD5优化推送完成，验证成功")


if __name__ == '__main__':
    # vm_stop('*******', '  ')
    # zeus_config_synchronos('spider', '2.18.0', 'tms15')
    # zeus_config_replace('D:\\GITProject\\app-ops\\test_app_resource\\zeus-service', 'pd-prod', '1.0.2')
    # push_local_to_other_rsync('0.0.0.0', 'D:\\GITProject\\app-ops\\test_app_resource\\fds-batch-center-new', 'D:\\',
    #                           True)
    # /usr/local/mysql/bin/mysqlbinlog --base64-output=decode-rows --start-datetime="2023-4-13 00:00:00" /data/mysql/log/bin.000170
    # log.info(exec_remote_cmd_by_username_password('**************', 'pwd && /usr/local/mysql/bin/mysqlbinlog --base64-output=decode-rows --start-datetime="2023-4-14 00:00:00" /data/mysql/log/bin.000170 > binlog.2023-4-14.log', 'mysql', '123456', size=1000))
    # check_test_set_run_result(169039454638787)
    access_token = archery_login('howbuyscm', ']9BGridB427_')
    s, l = sql_check(access_token, 'product', '#', 5)
    sql_commit(access_token, 'product', '000', '#', 5, 5)
