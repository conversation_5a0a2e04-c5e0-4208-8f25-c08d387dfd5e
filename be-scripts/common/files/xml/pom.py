# coding:utf-8
import os
import sys
import threading
from concurrent.futures import Thread<PERSON><PERSON><PERSON>xecutor, FIRST_COMPLETED, wait, as_completed

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
# sys.path.append("/data/test/be-scripts/be-scripts")
import common.files.xml.ElementTree as ET
# import xml.etree.ElementTree as ET
from common.files.xml import xml_cmd
from common.files import search
from settings import logger, POM_FILE_NAME_LIST
from dao.get.mysql import app_info
from ci_pipeline.ci_pipeline_models.iter_ser import UpgradeMiniVersionModuleInfo
from utils.compile.mvn.compiler import CompilerPomChecker

ET.register_namespace('', "http://maven.apache.org/POM/4.0.0")
ET.register_namespace('xsi', "http://www.w3.org/2001/XMLSchema-instance")


class PomModifier:
    special_pom_parent = "spring-boot-starter-parent"
    __multiple_version_module_upgrade_info = UpgradeMiniVersionModuleInfo

    def __init__(self, workspace, version=None):
        """

        :param workspace: str.工作空间
        :param version: str.要修改的版本
        """
        self.workspace = workspace
        self.version = version
        self.group_id = "com.howbuy"
        self.xmlns = "{http://maven.apache.org/POM/4.0.0}"
        # 不修改外部依赖版本的应用的列表
        self.not_modify_out_dep_app_list = []
        self.lockers = {}
        mul_ver_info = self.__multiple_version_module_upgrade_info()
        self._multiple_version_module_upgrade_dict = mul_ver_info.get_module_upgrade_info()
        self._multiple_version_property_upgrade_dict = {"com.howbuy." + key + ".version": values for key, values in
                                                        self._multiple_version_module_upgrade_dict.items()}

    def set_not_modify_out_dep_app_list(self, not_modify_out_dep_app_list):
        self.not_modify_out_dep_app_list = not_modify_out_dep_app_list

    def public_modify_pom(self, change_version=True, is_modify_out_dep=True, pom_check=True):
        """
        拉取分支后，修改pom文件版本
        :return:
        """
        artifact_list = self.public_find_all_artifacts_in_workspace(pom_check)
        artifact_kinship = self.find_kinship_of_artifact(artifact_list)
        jar_online_version_dict = app_info.get_online_jar_version()

        logger.info("当前需要处理的所有artifact列表 {}".format(artifact_list))
        with ThreadPoolExecutor(max_workers=1) as t:
            obj_list = []
            for pom_name in POM_FILE_NAME_LIST:
                for pom_path in search.file_search(self.workspace, file_name=pom_name, ignore=[".git"]):
                    obj_list.append(
                        t.submit(
                            self.do_process_single_by_pom_path, pom_path, artifact_list, artifact_kinship,
                            change_version,
                            is_modify_out_dep, jar_online_version_dict)
                    )

            for future in as_completed(obj_list):
                data = future.result()
                logger.info("do_process_single_by_pom_path 线程处理完  {}".format(data))

        logger.info("多线程处理 do_process_single_by_pom_path 全部结束")

    def do_pom_check(self, pom_path):
        comm = CompilerPomChecker(pom_path)
        result, msg = comm.pom_check_compile()
        return result, msg

    def do_process_single_by_pom_path(self, pom_path, artifact_list, artifact_kinship, change_version,
                                      is_modify_out_dep, jar_online_version_dict):
        with open(pom_path, "rt", encoding="utf-8") as f:
            logger.info("do_process_single_by_pom_path方法中解析文件{}".format(pom_path))
            tree = ET.parse(f)
        artifact_id = tree.find("./" + self.xmlns + "artifactId").text
        logger.info("开始处理pom文件：{}，artifact_id：{}".format(pom_path, artifact_id))
        # 改pom中的version以及<parent>标签里的version
        self.modify_version_of_app(tree, change_version)
        tree.write(pom_path, encoding='utf-8', xml_declaration=True, method='xml')

        dep_list, out_properties_dict = self.find_dep_variable_name_list(artifact_id, tree, artifact_list,
                                                                         artifact_kinship,
                                                                         is_modify_out_dep, jar_online_version_dict)

        father_father_pom_path, father_pom_path = self.generate_father_father_pom_path(
            change_version, artifact_list, artifact_kinship, tree, dep_list, artifact_id, pom_path)

        have_parent = self.do_process_father_tree(father_pom_path, dep_list, out_properties_dict, change_version,
                                                  is_modify_out_dep)

        have_parent = have_parent or self.do_process_father_tree(father_father_pom_path, dep_list, out_properties_dict,
                                                                 change_version,
                                                                 is_modify_out_dep)

        if not have_parent:
            self.do_process_tree(tree, pom_path, dep_list, out_properties_dict, change_version,
                                 is_modify_out_dep)

    def do_process_father_tree(self, father_pom_path, dep_list, out_properties_dict, change_version, is_modify_out_dep):
        if father_pom_path:
            key = "do_process_father_tree|{}".format(father_pom_path)
            if not self.lockers.__contains__(key):
                logger.info("do_process_father_tree 根据pom路径产生新的锁 {}".format(key))
                self.lockers[key] = threading.RLock()
            logger.info("do_process_father_tree 锁定处理pom路径 {}".format(key))
            self.lockers[key].acquire(timeout=15)
            logger.info("do_process_father_tree 锁定成功pom路径 {}".format(key))
            with open(father_pom_path, "rt", encoding="utf-8") as f:
                logger.info("do_process_father_tree方法中解析文件{}".format(father_pom_path))
                father_tree = ET.parse(f)
            have_parent = self.do_process_tree(father_tree, father_pom_path, dep_list, out_properties_dict,
                                               change_version, is_modify_out_dep)
            logger.info("do_process_father_tree 即将解除pom路径 {}".format(key))
            self.lockers[key].release()
            logger.info("do_process_father_tree 解除锁定pom路径 {}".format(key))
            return have_parent
        else:
            return False

    def do_process_tree(self, ref_tree, pom_path, dep_list, out_properties_dict, change_version, is_modify_out_dep):
        if ref_tree:
            # logger.info(dep_list)
            self.map_version_into_properties_on_list(ref_tree, dep_list, change_version)
            # 修改外部依赖版本
            if is_modify_out_dep:
                self.map_version_into_properties_on_list(ref_tree, out_properties_dict, change_version)
            # logger.info(pom_path)
            ref_tree.write(pom_path, encoding='utf-8', xml_declaration=True, method='xml')
            return True
        else:
            return False

    def generate_father_father_pom_path(self, change_version, artifact_list, artifact_kinship, tree, dep_list,
                                        artifact_id, pom_path):
        """

        @param change_version:
        @param artifact_list:
        @param artifact_kinship:
        @param tree:
        @param dep_list:
        @param artifact_id:
        @param pom_path:
        @return:
        """

        father_father_pom_path = None
        father_pom_path = None

        for father_artifact_id in artifact_kinship:
            if artifact_id in artifact_kinship[father_artifact_id]:
                # 因为pom排序 会用到子的properties 所以子pom里面有properties 时需要追加，重构 pom排序逻辑可以去掉写入子pom properties 20-09-02
                # logger.info(dep_list)
                self.map_version_into_properties_on_list(tree, dep_list, change_version, is_son=True)
                # 有条件删除 20-09-02
                tree.write(pom_path, encoding='utf-8', xml_declaration=True, method='xml')

                father_pom_path = os.path.join(self.workspace, artifact_list[father_artifact_id])

                # 有三层pom依赖的，需要把三层里面的properties都改了
                for father_father_artifact_id in artifact_kinship:
                    if father_artifact_id in artifact_kinship[father_father_artifact_id]:
                        father_father_pom_path = os.path.join(self.workspace,
                                                              artifact_list[father_father_artifact_id])

                        break

                break

        return father_father_pom_path, father_pom_path

    def public_swap_pom_content(self, source_module_name, target_module_name):
        """举例：cgi-ehowbuy的迭代pom的处理"""
        project_list = self.public_find_all_artifacts_in_workspace()
        logger.info("""开始进行pom覆盖""")
        source_pom_path = os.path.join(self.workspace, project_list[source_module_name])
        target_pom_path = os.path.join(self.workspace, project_list[target_module_name])
        logger.info("""源pom为{}""".format(source_pom_path))
        logger.info("""目标pom为{}""".format(target_pom_path))

        """记录目标的（比如：cgi-container）的artifactId和name"""
        with open(target_pom_path, "rt", encoding="utf-8") as f:
            logger.info("public_swap_pom_content方法中解析文件{}".format(target_pom_path))
            pom_tree = ET.parse(f)
            target_artifactId = pom_tree.find("./" + self.xmlns + "artifactId").text
            target_name = pom_tree.find("./" + self.xmlns + "name").text

        """将源pom内容覆盖目标pom.xml，但是保留目标pom的artifactId和name"""
        with open(source_pom_path, "rt", encoding="utf-8") as f:
            logger.info("public_swap_pom_content方法中解析文件{}".format(source_pom_path))
            tree = ET.parse(f)
            tree.find("./" + self.xmlns + "artifactId").text = target_artifactId
            tree.find("./" + self.xmlns + "name").text = target_name
            tree.write(target_pom_path, encoding='utf-8', xml_declaration=True, method='xml')

    def do_modify_version_in_property_node(self, change_version, ref_node):
        """

        @param change_version:
        @param ref_node: 修改后要继续使用
        @return:
        """

        if ref_node is not None:
            if change_version:
                ref_node.text = self.version
            else:
                ref_node.text = ref_node.text.replace("SNAPSHOT", "RELEASE")

    def modify_version_of_app(self, tree, change_version):
        """
        修改pom文件版本
        :param tree: obj.解析后的 pom树
        :param change_version: bool. 是否要更改版本
        :return:
        """
        node = tree.find("./" + self.xmlns + "version")
        self.do_modify_version_in_property_node(change_version, node)

        node_parent = tree.find('./{http://maven.apache.org/POM/4.0.0}parent')
        # print node_parent
        modify_tag = 1
        if node_parent is not None:
            for child_parent in node_parent:
                # spring-boot 的parent 不做修改
                try:
                    if "artifactId" in child_parent.tag:
                        if str(child_parent.text) == self.special_pom_parent:
                            modify_tag = 0
                    if modify_tag and 'version' in child_parent.tag:
                        # 引用变量的版本号不做修改
                        # 理财通接入取消 变量的方式传参，字pom中不通过变量方式打包 2020-05-11
                        # if "${" in child_parent.text:
                        #     pass
                        # else:
                        self.do_modify_version_in_property_node(change_version, child_parent)
                except Exception as e:
                    logger.warning("pom 解析parent 警告 {}".format(e))

    def public_find_all_artifacts_in_workspace(self, pom_check=False):
        """
        找到工作空间下所有artifact
        @param pom_check: 如果是True会先用clean命令检测pom本身
        @return: list. artifact_id 列表
        """
        artifact_list = {}
        for pom_name in POM_FILE_NAME_LIST:
            for pom_path in search.file_search(self.workspace, file_name=pom_name, ignore=[".git"]):
                # 解析pom之前先check pom格式 20220729 by fwm
                if pom_check:
                    result, msg = self.do_pom_check(pom_path)
                    if result == 'failure':
                        raise IOError(msg)
                with open(pom_path, "r", encoding="utf-8") as f:
                    logger.info("public_find_all_artifacts_in_workspace方法中解析文件{}".format(pom_path))
                    tree = ET.parse(f)
                    artifact_list[tree.find("./" + self.xmlns + "artifactId").text] = \
                        pom_path.replace(self.workspace + os.sep, "")
        return artifact_list

    def find_artifact_type(self):
        """
        找到工作空间下所有artifact
        :return: dict. artifact_type
        """
        artifact_type = {}
        for pom_name in POM_FILE_NAME_LIST:
            for pom_path in search.file_search(self.workspace, file_name=pom_name, ignore=[".git"]):
                with open(pom_path, "r", encoding="utf-8") as f:
                    logger.info("find_artifact_type方法中解析文件{}".format(pom_path))
                    tree = ET.parse(f)
                # logger.info("查找{}文件type".format(pom_path))
                packaging = tree.find("./" + self.xmlns + "packaging")
                if packaging is not None:
                    pom_type = packaging.text
                else:
                    pom_type = "jar"
                artifact_type[tree.find("./" + self.xmlns + "artifactId").text] = pom_type
        return artifact_type

    def find_all_artifact_include_dependencies(self, aritfact_list):
        """
        找到工作空间下所有artifact
        :return: dict. artifact_type
        """
        artifact_dependency_dict = {}
        for pom_name in POM_FILE_NAME_LIST:
            for pom_path in search.file_search(self.workspace, file_name=pom_name, ignore=[".git"]):
                with open(pom_path, "r", encoding="utf-8") as f:
                    logger.info("find_all_artifact_include_dependencies中解析文件{}".format(pom_path))
                    tree = ET.parse(f)
                # logger.info("查找{}文件type".format(pom_path))
                if tree.find("./" + self.xmlns + "artifactId").text in aritfact_list:
                    artifact_dependency_dict[tree.find("./" + self.xmlns + "artifactId").text] = \
                        self.do_find_dependencies_in_pom(tree, aritfact_list)
        return artifact_dependency_dict

    def do_find_dependencies_in_pom(self, tree, aritfact_list):
        dependency_list = []
        for node in tree.findall(".//" + self.xmlns + "dependency"):
            if node.find("./" + self.xmlns + "artifactId").text in aritfact_list:
                dependency_list.append(node.find("./" + self.xmlns + "artifactId").text)
        return dependency_list

    def sort_artifact(self, artifact_dependency, i=0):
        no_dependency_list = []
        for artifact in artifact_dependency:
            if artifact_dependency[artifact] == []:
                no_dependency_list.append(artifact)
        for artifact in no_dependency_list:
            artifact_dependency.pop(artifact)

        for artifact in artifact_dependency:
            artifact_dependency[artifact] = list(set(artifact_dependency[artifact]) - set(no_dependency_list))
        i = i + 1
        if artifact_dependency == {}:
            return {i: no_dependency_list}
        else:
            dep_dict = self.sort_artifact(artifact_dependency, i=i)
            dep_dict.update({i: no_dependency_list})
            return dep_dict

    def package_batch(self):

        all_project = self.public_find_all_artifacts_in_workspace()
        batch_list = []
        artifact_type = self.find_artifact_type()
        batch_list.append({i: os.path.join(self.workspace, all_project[i]) for i in artifact_type if
                           artifact_type[i] == "pom"})
        artifact_dependency = self.find_all_artifact_include_dependencies(
            [i for i in artifact_type if artifact_type[i] != "pom"])
        # logger.info(artifact_dependency)
        sort_dict = self.sort_artifact(artifact_dependency)
        for i in range(len(sort_dict)):
            batch_list.append({j: os.path.join(self.workspace, all_project[j]) for j in sort_dict[i + 1]})
        return batch_list

    def find_kinship_of_artifact(self, project_list):
        """
        生成artifact_id 的父子关系
        :return: dict{list}.
        """
        art_relationship = {}
        for pom_name in POM_FILE_NAME_LIST:
            logger.info("解析父子关系pom_name{}".format(pom_name))
            logger.info(
                "解析父子pom路径{}".format(search.file_search(self.workspace, file_name=pom_name, ignore=[".git"])))
            for pom_path in search.file_search(self.workspace, file_name=pom_name, ignore=[".git"]):
                logger.info("解析父子关系pom_path{}".format(pom_path))
                with open(pom_path, "r", encoding="utf-8") as f:
                    logger.info("find_kinship_of_artifact方法中解析文件{}".format(pom_path))
                    tree = ET.parse(f)
                    node_parent = tree.find("./" + self.xmlns + "parent")
                    if node_parent is not None:
                        father_artifact_id = node_parent.find("./" + self.xmlns + "artifactId").text
                        if father_artifact_id in project_list:
                            if father_artifact_id in art_relationship:
                                art_relationship[father_artifact_id].append(
                                    tree.find("./" + self.xmlns + "artifactId").text)
                            else:
                                art_relationship[father_artifact_id] = [
                                    tree.find("./" + self.xmlns + "artifactId").text]
        logger.info("父子关系{}".format(art_relationship))
        return art_relationship

    def __change_ref_and_get_variable_name_of_version_node(self, ref_node, artifact_id, need_change=True):
        """
        修改dependency的引用为变量
        @param ref_node: obj.一个dependency node节点
        @param artifact_id: str.artifactId
        @param need_change:
        @return:
        """

        if ref_node.find("./" + self.xmlns + "version").text == "${com.howbuy." + artifact_id + ".version}":
            old_version = "${com.howbuy." + artifact_id + ".version}"
        else:
            old_version = ref_node.find("./" + self.xmlns + "version").text
            if need_change:
                ref_node.find("./" + self.xmlns + "version").text = "${com.howbuy." + artifact_id + ".version}"

        return "com.howbuy." + artifact_id + ".version", old_version

    def __generate_real_variable_name(self, ref_node, artifact_id, artifact_kinship):
        # """
        # 比如 当前node是mring-itest-serivce，找到了父是mring-itest，那么版本号变量就叫做 ${howbuy.com.mring-itest.version}
        # 比如 当前node是mring-itest-service里依赖的order-center-client，发现没在当前仓库下，那么版本号变量就叫做${howbuy.com.order-center-client.version}
        #
        # @param ref_node:
        # @param artifact_id:
        # @param artifact_kinship:
        # @return:
        # """
        #
        # # ****在当前仓库的情况下****
        # independent_flag = 1
        #
        # # ********如果本身就【是父级】，就以自己的名字为主********
        # if artifact_id in artifact_kinship:
        #     independent_flag = 0
        #     variable_name, old_version = self.__change_ref_and_get_variable_name_of_version_node(ref_node, artifact_id)
        # # ********如果本身【不是父级】，找一找所有的父级的子级里有没有自己，找到了就跟着父级取名********
        # else:
        #     for father_artifact_id in artifact_kinship:
        #         # 如果是子项目引用父项目的版本号
        #         if artifact_id in artifact_kinship[father_artifact_id]:
        #             independent_flag = 0
        #             variable_name, old_version = self.__change_ref_and_get_variable_name_of_version_node(ref_node,
        #                                                                                                  father_artifact_id)
        #             break
        #
        # # ****不在当前仓库的情况下****
        # if independent_flag:
        #     variable_name, old_version = self.__change_ref_and_get_variable_name_of_version_node(ref_node, artifact_id)

        """生成标准化的版本变量名
                新规则：统一使用com.howbuy.{artifact_id}.version格式
                全局统一的依赖包版本properties管理模式，全部以依赖包的artifactId作为基准名

                @param ref_node: XML节点引用
                @param artifact_id: 依赖包的artifactId
                @param artifact_kinship: 依赖关系字典（保持兼容性但不再使用）
                @return: 标准化的变量名字符串
                """

        # 直接使用artifact_id生成标准变量名，移除复杂的父子关系判断逻辑
        # 统一规则：全部以依赖包的artifactId作为基准名
        variable_name, old_version = self.__change_ref_and_get_variable_name_of_version_node(ref_node, artifact_id)

        return variable_name

    def change_and_get_variable_name_of_dep_other_repos(self, ref_node, artifact_id, out_properties_dict, need_change,
                                                        jar_online_version_dict):

        # 去掉改老git打包方式打出的依赖包的版本号的逻辑 20240205 by fwm
        # old_git_jar_list = app_info.get_old_git_jar()

        # if artifact_id in old_git_jar_list:
        #     # 例如out_variable = "com.howbuy.fds-batch-facade.version"
        #     out_variable, old_version = self.change_and_get_variable_name_of_version_node(ref_node, artifact_id,
        #                                                                                   need_change)
        #     out_properties_dict[out_variable] = "RELEASE"

        # jar_online_version_dict = app_info.get_online_jar_version()

        if artifact_id in jar_online_version_dict:

            # todo 新的方案 放弃支持多版本，选择了不升级，  by 帅 20220324
            if artifact_id in self._multiple_version_module_upgrade_dict:
                pass
            else:
                out_variable, old_version = self.__change_ref_and_get_variable_name_of_version_node(ref_node,
                                                                                                    artifact_id,
                                                                                                    need_change)
                out_properties_dict[out_variable] = jar_online_version_dict[artifact_id]
            #  依赖中间件需要 需要判断 版本首位 by帅 2022.03.15
            # todo 新的方案 放弃支持多版本，选择了不升级，先注释掉 升级代码 by 帅 20220324
            # if artifact_id in self._multiple_version_module_upgrade_dict:
            #     logger.info("支持多版本中间件的pom中的版本 {}".format(old_version))
            #     for version in self._multiple_version_module_upgrade_dict[artifact_id]:
            #         logger.debug(version.split(".")[0])
            #         logger.debug(old_version.split(".")[0])
            #         if version.split(".")[0] == old_version.split(".")[0]:
            #             out_properties_dict[out_variable] = version+"-RELEASE"
            #             logger.debug(out_properties_dict[out_variable])

        return out_properties_dict

    def find_dep_variable_name_list(self, app_name, tree_of_a_pom, artifact_list, artifact_kinship,
                                    is_modify_out_dep=True, jar_online_version_dict=None):
        """

        @param app_name:
        @param tree_of_a_pom:obj.解析后的 pom树
        @param artifact_list:
        @param artifact_kinship:list[dict..].aritfact 的父子关系
        @return:list. 返回需要修改的 properties
        """

        variable_name_in_pom_properties_list = []
        variable_name_of_dep_other_repos_in_pom_properties_dict = {}
        with ThreadPoolExecutor(max_workers=15) as t:
            logger.info("开始多线程识别要修改properties的变量名列表")
            obj_list = []
            for node in tree_of_a_pom.findall(".//" + self.xmlns + "dependency"):
                obj_list.append(
                    t.submit(
                        self.__find_dep_variable_name_list_one_node, app_name, node, artifact_list, artifact_kinship,
                        is_modify_out_dep, variable_name_in_pom_properties_list,
                        variable_name_of_dep_other_repos_in_pom_properties_dict, jar_online_version_dict)
                )

            for future in as_completed(obj_list):
                future.result()

        logger.info("识别出要修改properties变量名列表-内部：{}".format(variable_name_in_pom_properties_list))
        logger.info(
            "识别出要修改properties变量名列表-外部：{}".format(variable_name_of_dep_other_repos_in_pom_properties_dict))

        return variable_name_in_pom_properties_list, variable_name_of_dep_other_repos_in_pom_properties_dict

    def __find_dep_variable_name_list_one_node(self, app_name, node, artifact_list, artifact_kinship,
                                               is_modify_out_dep, ref_variable_name_in_pom_properties_list,
                                               ref_variable_name_of_dep_other_repos_in_pom_properties_dict,
                                               jar_online_version_dict):

        # 不是内部的group_id 不参与修改版本号 20220104 by 帅
        if self.group_id not in node.find("./" + self.xmlns + "groupId").text:
            return

        # 如果没有找到节点里有version则不需要提取成变量
        if node.find("./" + self.xmlns + "version") is None:
            return
        dependency_artifact_id = node.find("./" + self.xmlns + "artifactId").text
        # ****如果依赖的artifact_id【在仓库列表里】****
        if dependency_artifact_id in artifact_list:
            variable_name = self.__generate_real_variable_name(node, dependency_artifact_id, artifact_kinship)
            if variable_name not in ref_variable_name_in_pom_properties_list:
                ref_variable_name_in_pom_properties_list.append(variable_name)
                logger.info(
                    "{} 的依赖 {} 的版本变量确定为 {}".format(app_name, dependency_artifact_id, variable_name))
        # ****如果依赖的artifact_id【不在】仓库列表里】****
        else:
            # ********找到已经存在应用，不修改它们pom文件对外部的依赖。目前列表仅在新开分支时才会传入 by帅 20210611********
            if app_name in self.not_modify_out_dep_app_list:
                return
            # ********处理外部依赖********
            variable_name_of_dep_other_repos_in_pom_properties_dict = self.change_and_get_variable_name_of_dep_other_repos(
                node, dependency_artifact_id, ref_variable_name_of_dep_other_repos_in_pom_properties_dict,
                is_modify_out_dep, jar_online_version_dict)

    def map_version_into_properties_on_list(self, tree, properties_list, change_version, is_son=False):
        """
        根据传入的列表修改properties
        @param tree: obj
        @param properties_list: list. 需要修改的properties标签列表 dict. key依赖包的名称 value依赖包的版本号
        @param change_version:
        @param is_son:
        @return:
        """

        artifact_id = tree.find("./" + self.xmlns + "artifactId").text
        is_dep_dict = isinstance(properties_list, dict)
        logger.info("正在处理修改properties的是：{}，类型是：{}，总共有：{}个".format(artifact_id,
                                                                                 "外部依赖的包" if is_dep_dict else "内部依赖的包",
                                                                                 len(properties_list)))
        node_properties = tree.find(".//" + self.xmlns + "properties")

        # 增加properties
        child_node_list = []
        # 没有直接返回
        if len(properties_list) == 0:
            return True
        if node_properties is not None:
            for dep in properties_list:
                # 是否新增
                is_add = 1
                for child_node_p in node_properties:
                    if str(child_node_p.tag) == self.xmlns + dep:
                        if is_dep_dict:
                            #  依赖中间件需要 需要判断 版本首位 by帅 2022.03.15
                            # com.howbuy.fds-batch-common-new.version 先转换为 key 为property的字典
                            # todo 新的方案 放弃支持多版本，选择了不升级，先注释掉 升级代码 by 帅 20220324
                            # logger.debug(self._multiple_version_property_upgrade_dict)
                            # logger.debug(dep)
                            # if dep in self._multiple_version_property_upgrade_dict:
                            #     for multi_version in self._multiple_version_property_upgrade_dict[dep]:
                            #         logger.debug(multi_version)
                            #         logger.debug(child_node_p.text)
                            #         if child_node_p.text.split(".")[0] == multi_version.split(".")[0]:
                            #             child_node_p.text = multi_version+"-RELEASE"
                            # else:
                            #     child_node_p.text = properties_list[dep]
                            # todo 新的方案 放弃支持多版本，选择了不升级，先注释掉 升级代码 by 帅 20220324
                            child_node_p.text = properties_list[dep]

                            logger.info("正在处理 {} 的【外部】依赖项properties标签 【{}】 为：{}".format(artifact_id,
                                                                                                     child_node_p.tag,
                                                                                                     child_node_p.text))
                        else:
                            self.do_modify_version_in_property_node(change_version, child_node_p)
                            logger.info(
                                "正在处理 {} 的【内部】依赖项properties标签 【{}】 为：{}".format(artifact_id,
                                                                                             child_node_p.tag,
                                                                                             child_node_p.text))
                        is_add = 0
                        # 去掉break，防止<properties>中有重复的标签，保证所有的标签都遍历一遍 20220729 by fwm
                        # break

                if is_add:
                    logger.info("没有找到 {} 的依赖项 {} 存在properties标签".format(artifact_id, self.xmlns + dep))
                    elem1 = ET.Element(dep)
                    if is_dep_dict:
                        elem1.text = properties_list[dep]
                    else:
                        elem1.text = self.version
                    logger.info(elem1.text)
                    elem1.tail = "\n"
                    child_node_list.append(elem1)
            # logger.info(child_node_list)
            node_properties.extend(child_node_list)
        else:
            # 子pom缺少 不追加
            if is_son:
                return True
            logger.info("pom文件中没有properties标签，先追加properties 标签")
            root = tree.getroot()
            elem2 = ET.Element(self.xmlns + "properties")
            elem2.text = "\n"
            elem2.tail = "\n"
            # 将properties 标签放到 version 后面
            import copy
            new_root = copy.deepcopy(root)
            new_root.clear()
            for row in root:
                new_root.append(row)
                if row.tag == self.xmlns + "version":
                    new_root.append(elem2)
            # 如果找不到version标签 ，放到最后 20210506
            if elem2 not in new_root:
                new_root.append(elem2)
            root.clear()
            root.extend(new_root)
            self.map_version_into_properties_on_list(tree, properties_list, change_version)


if __name__ == "__main__":
    # test = PomModifier('D:\\SVNProject\\Projects\\jenkins\\workspace\\ftx-online', "cccccccc-RELEASE")
    pom_modifier = PomModifier('D:\\fds-batch-new', "zzzzzzz-RELEASE")
    print(pom_modifier.version)
    new_artifact_dict = pom_modifier.public_find_all_artifacts_in_workspace()
    pom_modifier.public_modify_pom()

    # 开始修改pom文件版本
    # 根据前端选择来控制是否修改外部依赖 2021127 by fwm

    # repolist = [
    #     {'repos_path': 'scm_test_project/test-jar', 'module_name': 'test-all-war'},
    #     {'repos_path': 'tms-public/howbuy-cgi', 'module_name': 'cgi-ehowbuy-container'},
    #     {'repos_path': 'tms-public/howbuy-cgi', 'module_name': 'cgi-simu-container'}]
    # for repos in repolist:
    #     print(repos)
