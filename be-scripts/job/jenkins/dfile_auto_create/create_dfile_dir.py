import sys
import os
import json
import traceback

import requests

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

from enum import Enum
from settings import logger, INTERFACE_URL, D<PERSON>LE
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_local_cmd_by_sshpass_tomcat, \
    ssh_path_whether_exist
from ci_pipeline.pipeline_record.pipeline_record import PipelineStatus
from dao.connect.mysql import DBConnectionManager
from webdav3.client import Client


class DfileTypeEnum(Enum):
    NFS = "nfs"
    WEBDAV = "webdav"


class DfileDirCreator:
    def __init__(self, workspace, job_name, job_build_id, app_name_str, suite_code_str):
        self.workspace = workspace
        self.job_name = job_name
        self.job_build_id = job_build_id
        self.app_name_list = app_name_str.split(",") if app_name_str else []
        self.suite_code_list = suite_code_str.split(",") if suite_code_str else []
        self.pipeline_node_dict = {
            #"get_dir_info": self.get_dir_info,
            "get_dir_info_new": self.get_dir_info_new,
           # "create_webdav_dir": self.create_webdav_dir,
            "create_webdav_dir_new": self.create_webdav_dir_new,
            #"create_nginx_dir": self.create_nginx_dir,
            "create_result": self.create_result
        }

    def get_dir_info(self):
        """
        :param step_param:
        :return:
        """
        try:
            app_list = self.app_name_list if self.app_name_list else self.__get_app_list()
            logger.info("app_list==={}".format(app_list))
            # app_list = ["zeus-service", "adviser-batch-center-remote", "howbuy-fpc-reptile"]
            suite_list = self.suite_code_list if self.suite_code_list else self.__get_suite_list()
            # suite_list = ['it01', 'it02', 'it03']
            logger.info("suite_list==={}".format(suite_list))
            request_address = INTERFACE_URL.get("zeus") + DFILE.get("zeus_api")
            param = {"appNameList": app_list}
            logger.info("=========开始请求宙斯接口=========")
            res = requests.post(url=request_address, data=json.dumps(param),
                                headers={'Content-Type': 'application/json'})
            logger.info("=========结束请求宙斯接口=========")

            logger.info(res.text)
            res_json = res.json()
            nfs_path_list = []
            webdav_path_list = []
            if res_json.get("code") == "200":
                res_data = res_json.get("data")
                for item in res_data:
                    for dfile_info in item.get("dfileList"):
                        dfile_info_path = dfile_info.get("path")
                        if not dfile_info_path:
                            logger.warning("异常数据：{}".format(item))
                            continue
                        if dfile_info.get("type").lower() == DfileTypeEnum.NFS.value:
                            nfs_path_list.append(dfile_info_path)
                        elif dfile_info.get("type").lower() == DfileTypeEnum.WEBDAV.value:
                            webdav_path_list.append(dfile_info_path)
            nfs_absolute_path = []
            webdav_absolute_path = {}
            webdav_absolute_path[1] = []
            for suite_code in suite_list:
                for nfs_path in nfs_path_list:
                    if not nfs_path:
                        continue
                    if nfs_path.endswith('/'):
                        nfs_path = nfs_path[:-1]
                    if nfs_path.startswith('/'):
                        nfs_path = nfs_path[1:]
                    absolute_path = os.path.join('/data/webdav', suite_code, 'files', nfs_path)
                    if absolute_path not in nfs_absolute_path:
                        nfs_absolute_path.append(absolute_path)
                webdav_absolute_path[1].append('/' + suite_code)
                for webdav_path in webdav_path_list:
                    if not webdav_path:
                        continue
                    if webdav_path.endswith('/'):
                        webdav_path = webdav_path[:-1]
                    if webdav_path.startswith('/'):
                        webdav_path = webdav_path[1:]
                    webdav_sub_path_list = self.__get_webdav_sub_path(webdav_path)
                    for webdav_sub_path in webdav_sub_path_list:
                        webdav_sub_absolute_path = os.path.join('/', suite_code, webdav_sub_path)
                        level = len(webdav_sub_absolute_path.split('/')) - 1
                        if level not in webdav_absolute_path:
                            webdav_absolute_path[level] = [webdav_sub_absolute_path]
                        else:
                            if webdav_sub_absolute_path not in webdav_absolute_path[level]:
                                webdav_absolute_path[level].append(webdav_sub_absolute_path)
            logger.info("nfs_absolute_path==={}".format(nfs_absolute_path))
            logger.info("webdav_absolute_path==={}".format(webdav_absolute_path))
            param_dict = {
                "nfs_absolute_path": nfs_absolute_path,
                "webdav_absolute_path": webdav_absolute_path
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + ".json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info("数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + ".json")))

            return PipelineStatus.success, "获取目录信息成功"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def get_dir_info_new(self):
        """
        新的获取目录信息方法，处理新的返回报文格式
        :return:
        """
        try:
            logger.info("请求参数-app_list".format(self.app_name_list))
            app_list = self.app_name_list if self.app_name_list else self.__get_app_list()
            logger.info("app_list==={}".format(app_list))
            # app_list = ["zeus-service", "adviser-batch-center-remote", "howbuy-fpc-reptile"]
            suite_list = self.suite_code_list if self.suite_code_list else self.__get_suite_list()
            # suite_list = ['it01', 'it02', 'it03']
            logger.info("suite_list==={}".format(suite_list))
            request_address = INTERFACE_URL.get("zeus") + DFILE.get("zeus_api")
            param = {"appNameList": app_list}
            logger.info("=========开始请求宙斯接口=========")
            res = requests.post(url=request_address, data=json.dumps(param),
                                headers={'Content-Type': 'application/json'})
            logger.info("=========结束请求宙斯接口=========")

            logger.info(res.text)
            res_json = res.json()
            
            # 处理新的返回报文格式
            webdav_body = []
            if res_json.get("code") == "200":
                res_data = res_json.get("data")
                if res_data:
                    webdav_body = res_data
            
            # 构建缓存数据结构
            param_dict = {
                "webdav_body": webdav_body
            }
            
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + ".json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info("数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + ".json")))

            return PipelineStatus.success, "获取目录信息成功"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_nginx_dir(self):
        """
        清缓存
        :param step_param:
        :return:
        """
        try:
            param_data = self.parse_data()
            nfs_result = []
            nfs_absolute_path_list = param_data.get("nfs_absolute_path")
            for nfs_absolute_path in nfs_absolute_path_list:
                cmd = "mkdir -p {}".format(nfs_absolute_path)
                logger.info("创建目录命令：{}".format(cmd))
                try:
                    exec_local_cmd_by_sshpass_tomcat(DFILE.get("nfs_ip"), cmd)
                except Exception as e:
                    logger.info("创建目录失败！{}".format(str(e)))
                check_result, chk_msg = ssh_path_whether_exist(DFILE.get("nfs_ip"), nfs_absolute_path, is_file=False)
                if not check_result:
                    nfs_result.append(nfs_absolute_path)

            param_dict = {
                "nfs_result": nfs_result
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + "-nfs_result.json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + "-nfs_result.json")))

            return PipelineStatus.success, "创建NFS目录结束"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_webdav_dir(self):
        """
        删除文件
        :param step_param:
        :return:
        """
        try:
            param_data = self.parse_data()
            webdav_absolute_path = param_data.get("webdav_absolute_path")
            sorted_dict = {k: v for k, v in sorted(webdav_absolute_path.items(), key=lambda item: item[0])}

            options = {
                'webdav_hostname': DFILE.get("webdav_host"),
                'webdav_login': DFILE.get("webdav_login"),
                'webdav_password': DFILE.get("webdav_password")
            }
            client = Client(options)
            webdav_result = []
            for level, dir_info in sorted_dict.items():
                for dir_path in dir_info:
                    try:
                        client.mkdir(dir_path)
                    except Exception as e:
                        logger.info("目录{}创建失败！{}".format(dir_path, str(e)))
                    if client.check(dir_path):
                        logger.info("目录{}创建成功！".format(dir_path))
                    else:
                        webdav_result.append(dir_path)
            param_dict = {
                "webdav_result": webdav_result
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + "-webdav_result.json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + "-webdav_result.json")))

            return PipelineStatus.success, "创建webdav目录结束"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_webdav_dir_new(self):
        """
        新的创建webdav目录方法，处理新的数据格式
        :return:
        """
        try:
            param_data = self.parse_data()
            webdav_body = param_data.get("webdav_body", [])
            
            webdav_result = []
            processed_paths = set()  # 用于去重，避免重复处理相同路径
            logger.info("开始创建webdav目录：{}".format(webdav_body))
            # 循环处理webdav_body中的对象
            for webdav_item in webdav_body:
                if webdav_item.get("type") == "webdav" or webdav_item.get("type") == "WEBDAV":
                    # 提取serverHost作为webdav_hostname
                    server_host = webdav_item.get("serverHost", "").strip()
                    user_paths = webdav_item.get("userPath", [])
                    
                    # 配置webdav客户端
                    options = {
                        'webdav_hostname': server_host,
                        'webdav_login': DFILE.get("webdav_login"),
                        'webdav_password': DFILE.get("webdav_password")
                    }
                    client = Client(options)
                    
                    # 添加server_host级别的连接失败计数器
                    server_connection_failed_count = 0
                    skip_current_server = False

                    # 循环创建userPath中的目录
                    for dir_path in user_paths:
                        # 如果当前server_host已经连接失败2次，跳过该组的所有路径
                        if skip_current_server:
                            logger.warning("服务器{}连接失败次数过多，跳过路径{}".format(server_host, dir_path))
                            if dir_path not in webdav_result:
                                webdav_result.append(dir_path)
                            continue
                        
                        # 清理路径，移除多余的斜杠和规范化路径
                        dir_path = dir_path.strip()
                        # 移除多余的斜杠
                        while '//' in dir_path:
                            dir_path = dir_path.replace('//', '/')
                        # 确保路径以/开头
                        if not dir_path.startswith('/'):
                            dir_path = '/' + dir_path
                        # 移除末尾的斜杠（除非是根目录）
                        if dir_path.endswith('/') and len(dir_path) > 1:
                            dir_path = dir_path[:-1]
                        
                        # 检查是否已经处理过这个路径
                        if dir_path in processed_paths:
                            logger.info("路径{}已经处理过，跳过".format(dir_path))
                            continue
                        processed_paths.add(dir_path)
                        
                        logger.info("处理目录路径：{}".format(dir_path))
                        # 按照'/'分隔路径，逐层创建目录
                        path_parts = [part for part in dir_path.split('/') if part]
                        current_path = ''
                        logger.info("path_parts目录：{}".format(path_parts))
                        path_creation_failed = False  # 标记当前路径是否因连接问题失败
                        
                        for part in path_parts:
                            current_path += '/' + part
                            logger.info("part目录：{}".format(part))
                            # 检查当前层级目录是否存在
                            success = False
                            for retry in range(3):  # 最多重试3次
                                try:
                                    if not client.check(current_path):
                                        try:
                                            client.mkdir(current_path)
                                            logger.info("目录{}创建成功！".format(current_path))
                                            success = True
                                            break
                                        except Exception as e:
                                            if "No connection" in str(e):
                                                server_connection_failed_count += 1
                                                logger.warning("目录{}创建失败，连接异常(第{}次)：{}".format(current_path, server_connection_failed_count, str(e)))
                                                if server_connection_failed_count >= 2:
                                                    logger.warning("服务器{}连接失败次数达到2次，跳过该服务器组的所有路径处理".format(server_host))
                                                    skip_current_server = True
                                                    path_creation_failed = True
                                                    break
                                                if retry < 2:
                                                    client = Client(options)  # 重新创建客户端
                                                    continue
                                            else:
                                                logger.warning("目录{}创建失败！{}".format(current_path, str(e)))
                                                # 不要立即break，继续尝试其他重试
                                                if retry == 2:  # 最后一次重试失败
                                                    break
                                    else:
                                        logger.info("目录{}已存在，跳过创建".format(current_path))
                                        success = True
                                        break
                                except Exception as check_e:
                                    if "No connection" in str(check_e):
                                        server_connection_failed_count += 1
                                        logger.warning("检查目录{}时发生异常(第{}次)：{}".format(current_path, server_connection_failed_count, str(check_e)))
                                        if server_connection_failed_count >= 2:
                                            logger.warning("服务器{}连接失败次数达到2次，跳过该服务器组的所有路径处理".format(server_host))
                                            skip_current_server = True
                                            path_creation_failed = True
                                            break
                                        if retry < 2:
                                            client = Client(options)  # 重新创建客户端
                                            continue
                                    else:
                                        logger.warning("检查目录{}时发生异常：{}，尝试创建".format(current_path, str(check_e)))
                                        try:
                                            client.mkdir(current_path)
                                            logger.info("目录{}创建成功！".format(current_path))
                                            success = True
                                            break
                                        except Exception as e:
                                            if "No connection" in str(e):
                                                server_connection_failed_count += 1
                                                logger.warning("目录{}创建失败，连接异常(第{}次)：{}".format(current_path, server_connection_failed_count, str(e)))
                                                if server_connection_failed_count >= 2:
                                                    logger.warning("服务器{}连接失败次数达到2次，跳过该服务器组的所有路径处理".format(server_host))
                                                    skip_current_server = True
                                                    path_creation_failed = True
                                                    break
                                            else:
                                                logger.warning("目录{}创建失败！{}".format(current_path, str(e)))
                                            if retry == 2:  # 最后一次重试失败
                                                break
                            
                            # 如果因为连接问题需要跳过当前服务器，则退出路径创建循环
                            if skip_current_server:
                                break
                            
                            # 如果中间层级创建失败，记录但继续尝试（某些WebDAV服务器可能允许跳过中间目录）
                            if not success:
                                logger.warning("中间目录{}创建失败，但继续尝试创建完整路径".format(current_path))
                                # 不要break，继续尝试创建完整路径
                        
                        # 如果因为连接问题跳过了路径创建，直接将路径添加到失败列表
                        if path_creation_failed:
                            if dir_path not in webdav_result:
                                webdav_result.append(dir_path)
                            continue
                        
                        # 最终检查完整路径是否创建成功
                        final_check_success = False
                        for retry in range(3):  # 最多重试3次
                            try:
                                if not client.check(dir_path):
                                    if dir_path not in webdav_result:  # 避免重复添加
                                        webdav_result.append(dir_path)
                                        logger.warning("目录{}创建失败，已添加到失败列表".format(dir_path))
                                else:
                                    logger.info("目录{}最终检查成功".format(dir_path))
                                final_check_success = True
                                break
                            except Exception as check_e:
                                if "No connection" in str(check_e):
                                    server_connection_failed_count += 1
                                    logger.warning("最终检查目录{}时发生异常(第{}次)：{}".format(dir_path, server_connection_failed_count, str(check_e)))
                                    if server_connection_failed_count >= 2:
                                        logger.warning("服务器{}连接失败次数达到2次，跳过该服务器组的所有路径处理".format(server_host))
                                        skip_current_server = True
                                        break
                                    if retry < 2:
                                        client = Client(options)  # 重新创建客户端
                                        continue
                                else:
                                    logger.warning("最终检查目录{}时发生异常：{}，将其标记为失败".format(dir_path, str(check_e)))
                                    if dir_path not in webdav_result:  # 避免重复添加
                                        webdav_result.append(dir_path)
                                    break
                        
                        if not final_check_success and dir_path not in webdav_result:
                            webdav_result.append(dir_path)
                            logger.warning("目录{}最终检查失败，已添加到失败列表".format(dir_path))
            
            # 写入缓存结果
            param_dict = {
                "webdav_result": webdav_result
            }
            logger.info("待写入缓存的数据：{}".format(param_dict))
            with open(os.path.join(self.workspace, self.job_name + "-webdav_result.json"), "w", encoding='utf-8') as f:
                f.write(json.dumps(param_dict))
                logger.info(
                    "数据写入缓存文件: {}".format(os.path.join(self.workspace, self.job_name + "-webdav_result.json")))
            logger.info("创建webdav目录结束")
            return PipelineStatus.success, "创建webdav目录结束"
        except Exception as e:
            logger.info(str(e))
            sys.exit(1)
            return PipelineStatus.failure, str(e)

    def create_result(self):
        param_data = self.parse_data("webdav_result")
        webdav_result = param_data.get("webdav_result")
        if webdav_result:
            # 去重失败路径
            unique_failed_paths = list(set(webdav_result))
            logger.warning("以下{}个目录创建失败：".format(len(unique_failed_paths)))
            for failed_path in unique_failed_paths:
                logger.warning("失败路径: {}".format(failed_path))
            
            # 根据失败路径数量决定是否退出
            total_failed = len(unique_failed_paths)
            if total_failed > 10:  # 如果失败路径太多，可能是系统性问题
                logger.error("失败路径过多({}个)，可能存在系统性问题，请检查WebDAV服务器连接和权限".format(total_failed))
                sys.exit(1)
                return PipelineStatus.failure, "创建失败路径过多，请检查WebDAV服务器！"
            else:
                logger.warning("部分目录创建失败({}个)，但继续执行".format(total_failed))
                return PipelineStatus.success, "目录创建完成，但有{}个路径失败".format(total_failed)
        else:
            logger.info("所有目录创建成功")
            return PipelineStatus.success, "创建目录结束"

    def __get_app_list(self):
        sql = """SELECT DISTINCT t.module_name FROM env_mgt_node_bind t
                INNER JOIN app_mgt_app_module m ON t.module_name = m.module_name
                INNER JOIN app_mgt_app_info i ON m.app_id = i.id
                INNER JOIN app_mgt_app_build b ON b.app_id = i.id
                WHERE t.deploy_type = 2 AND i.third_party_middleware = 0
                      AND b.package_type IN ('jar', 'war', 'tar')
            """
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        app_list = []
        for row in db.cur.fetchall():
            app_list.append(row.get("module_name"))

        return app_list

    def __get_suite_list(self):
        sql = """
                SELECT s.suite_code FROM env_mgt_suite s 
                INNER JOIN env_mgt_suite_use_bind b ON s.suite_code= b.suite_code 
                WHERE s.suite_is_active = 1 AND s.support_docker = 1 AND b.need_dfile_dir = 1;
              """
        with DBConnectionManager() as db:
            db.cur.execute(sql)
        suite_code_list = []
        for row in db.cur.fetchall():
            suite_code_list.append(row.get("suite_code"))

        return suite_code_list

    def __get_webdav_sub_path(self, webdav_path):
        segments = []
        elements = webdav_path.split("/")
        current_path = ""
        for element in elements:
            if not element or element.strip() == "/":
                continue
            current_path += element
            segments.append(current_path)
            current_path += "/"
        return segments

    def parse_data(self, filename=None):
        """
        从缓存文件解析数据
        :return:
        """
        if filename:
            with open(os.path.join(self.workspace, self.job_name + "-{}.json".format(filename)), "r", encoding='utf-8') as f:
                param_dict = json.loads(f.read())
                logger.info("从缓存文件读数据: {}".format(
                    os.path.join(self.workspace, self.job_name + "-{}.json".format(filename))))
            return param_dict
        with open(os.path.join(self.workspace, self.job_name + ".json"), "r", encoding='utf-8') as f:
            param_dict = json.loads(f.read())
            logger.info("从缓存文件读数据: {}".format(os.path.join(self.workspace, self.job_name + ".json")))
        return param_dict

    def run_step(self, node_name):

        exec_status, exec_msg = self.pipeline_node_dict[node_name]()
        return exec_status, exec_msg

    def run(self, node_name):
        """
        运行入口程序
        :param node_name:
        :param step:
        :return:
        """
        self.run_step(node_name)


if __name__ == "__main__":
    logger.info("调用 {}".format(sys.argv[1:]))
    business_name = sys.argv[1]
    job_name = sys.argv[2]
    workspace = sys.argv[3]
    job_build_id = sys.argv[4]
    app_name_str = sys.argv[5] if len(sys.argv) > 5 else ""
    suite_code_str = sys.argv[6] if len(sys.argv) > 6 else ""

    tep = DfileDirCreator(workspace, job_name, job_build_id, app_name_str, suite_code_str)
    tep.run(business_name)
