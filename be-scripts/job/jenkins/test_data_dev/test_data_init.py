import concurrent
import os
import sys
import traceback

PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)
from concurrent.futures import ThreadPoolExecutor, as_completed
from db_mgt.db_dump_mgt import DbDumpMgt
from test_publish_aio.test_suite_init_impl_db import exec_dml, exec_mysql_dml, clean_dir
import datetime
import logging
from dao.connect.mysql_sqlalchemy import DBConnectionManagerForSqlalchemy
from jenkins_mgt.models import JenkinsInfo
from job.jenkins.test_data_dev.models import JenkinsMgtTestDataDevJobInfo, BizBaseDbBind, BizBaseInfo
from job.jenkins.test_data_dev.cdc_common.cdc_constants import DbCdcEnum
from job.jenkins.test_data_dev.param.oracle_cdc_param import DbCdcParam
from job.jenkins.test_data_dev.test_diff_sql_ser import get_current_scn, get_db_tables, add_supplemental_log_to_table, \
    get_all_table
from job.jenkins.test_data_dev.service.oracle_biz_data_split_service import oracle_truncate_data, mysql_truncate_data

from utils.test_env.test_env_lib import step_desc
from settings import logger as log, TEST_DATA_INIT, PRODUCT_STORE_SQL, GITLAB_LIB_SSH, logger, EXCEPT_SWITCH
from dao.get.mysql import db_mgt_bind_view
from db_mgt.db_dump_ser import get_dump_archive_pipeline_id
from test_publish_aio.test_publish_aio_exec.test_publish_aio_util import exec_flyway_command, \
    exec_remote_cmd_by_username_password, checkout_repo, sort_sql_common, sort_sql_common_second_step, exec_db_import_remote_cmd_by_username_password
from test_publish_aio.test_publish_aio_models.test_publish_ser import db_info_reset, get_biz_bind_app_list
from utils.form.models import DbMgtDumpFile

from common.common_tool.common_tool import exec_local_cmd
from common.mysql.models import DbMgtBisTestSql, DbMgtBisTestSqlCommitHis
from common.oracle.oracle import OracleHandler
from common.mysql.mysql import MysqlHandler
from common.mysql.mysql_binlog import MysqlUtils

from job.jenkins.test_data_dev.util.cdc_file_util import get_bis_iter_diff_sql_root_path
from job.pipeline_log.pipeline_log_decorator_mgt import TestDataInitPiplineLog
from job.jenkins.test_data_dev.test_data_init_ser import handler_db_info_list


class TestDataDev:
    def __init__(self, job_name, workspace, biz_iter_id, suite_code, business_id, build_id):
        self.job_name = job_name
        self.workspace = workspace
        self.biz_iter_id = biz_iter_id
        self.suite_code = suite_code
        self.business_id = business_id
        self.job_build_id = build_id

    def set_db_info_list(self, db_info_list):
        self.db_info_list = db_info_list

    def set_biz_base_db_br(self, biz_base_db_br):
        self.biz_base_db_br = biz_base_db_br

    def set_biz_category(self, biz_category):
        self.biz_category = biz_category

    def set_biz_original_basic_db_code(self, biz_original_basic_db_code):
        self.biz_original_basic_db_code = biz_original_basic_db_code

    def set_select_db_biz_category(self, select_db_biz_category):
        self.select_db_biz_category = select_db_biz_category

    def _get_db_info_list(self, business_id, suite_code):

        log.info("business_id=={}".format(business_id))
        with DBConnectionManagerForSqlalchemy() as db:
            jmtd = db.session.query(JenkinsMgtTestDataDevJobInfo).filter(
                JenkinsMgtTestDataDevJobInfo.id == business_id).one()
        app_dict_list = jmtd.job_param.get("app_list")

        app_list = []
        for app_dict in app_dict_list:
            app_list.append(app_dict.get("module_name"))
        db_info_result = db_mgt_bind_view.get(module_name_list=app_list, suite_code=suite_code)
        db_info_list = db_info_reset(db_info_result)

        for db_info in db_info_list:
            for app_dict in app_dict_list:
                if db_info.get("module_name") == app_dict.get("module_name") and app_dict.get("branch_name"):
                    db_info.update({"branch_name": app_dict.get("branch_name")})
        return db_info_list

    @step_desc("准备sql制品")
    @TestDataInitPiplineLog(step_name="pull_sql_repo")
    def pull_sql_repo(self):

        # if self.biz_category == 2 and self.select_db_biz_category == 2:
        #     logger.info("衍生业务选了衍生业务的dump，跳过准备sql制品")
        #     return 'success'

        data_suite_path = os.path.join(TEST_DATA_INIT.get('root_path'), self.suite_code)
        if not os.path.isdir(data_suite_path):
            cmd = 'mkdir -p {}'.format(data_suite_path)
            log.info("创建环境根目录： {}".format(cmd))
            os.system(cmd)
        else:
            if len(data_suite_path.split('/')) > 2:
                cmd = 'rm -rf {}'.format(os.path.join(data_suite_path, '*'))
                log.info("清理缓存目录：{}".format(cmd))
                os.system(cmd)
        # validate_db_cdc_info(bis_pipeline_id)
        # 拉feature sql
        db_group_name_list = []
        log.info("db_info_list: {}".format(self.db_info_list))

        # 选择数据库组的分支信息，没有在途的选最新归档，在途的随机选一个在途分支
        new_db_info_list = handler_db_info_list(self.db_info_list)
        log.info("new_db_info_list: {}".format(new_db_info_list))

        for db_info in new_db_info_list:
            db_group_name = db_info.get("db_group_name")
            # 一个数据库组的仓库只拉一次
            if db_group_name not in db_group_name_list:
                db_group_name_list.append(db_group_name)
                db_group_repo_local_path = os.path.join(data_suite_path, db_group_name)
                sql_repo_url = os.path.join(PRODUCT_STORE_SQL.get('url'), db_group_name + '.git')
                # if self.newest_flag == "true":
                #     checkout_repo(data_suite_path, db_group_repo_local_path, sql_repo_url, 'master')
                # else:
                if not checkout_repo(data_suite_path, db_group_repo_local_path, sql_repo_url,
                                     db_info.get("branch_name")):
                    checkout_repo(data_suite_path, db_group_repo_local_path, sql_repo_url, 'master')
                else:
                    # 如果拉取了分支，需要做一次master merge动作
                    db_group_repo_master_archive_local_path = os.path.join(data_suite_path, 'master', db_group_name,
                                                                           'archive')
                    db_group_repo_master_local_path = os.path.join(data_suite_path, 'master', db_group_name)
                    db_group_repo_archive_local_path = os.path.join(db_group_repo_local_path,
                                                                    'archive')
                    # 删除分支中的archive目录
                    clean_dir([db_group_repo_archive_local_path])

                    checkout_repo(data_suite_path, db_group_repo_master_local_path, sql_repo_url, 'master')
                    if os.path.exists(db_group_repo_master_archive_local_path):
                        # 将master分支的archive目录移动到当前分支
                        cmd = 'mv {} {}'.format(db_group_repo_master_archive_local_path, db_group_repo_local_path)
                        exec_local_cmd(cmd)

        return 'success'

    @step_desc("分拣sql")
    @TestDataInitPiplineLog(step_name="sort_sql")
    def sort_sql(self, sort_sql_step=None):
        flyway_cache_path = os.path.join(TEST_DATA_INIT.get('root_path'), self.suite_code,
                                         TEST_DATA_INIT.get('flyway_cache_path'))
        db_cache_path = os.path.join(TEST_DATA_INIT.get('root_path'), self.suite_code)
        if not os.path.isdir(flyway_cache_path):
            cmd = 'mkdir -p {}'.format(flyway_cache_path)
            exec_local_cmd(cmd)
        else:
            if len(flyway_cache_path.split('/')) > 2:
                log.info("清空flyway缓存目录：{}".format(flyway_cache_path))
                cmd = 'rm -rf {}'.format(os.path.join(flyway_cache_path, '*'))
                exec_local_cmd(cmd)

        db_group_name_list = []
        db_name_list = []
        db_name_dictionary = {}
        for db_info in self.db_info_list:
            db_name_list.append(db_info.get('db_name'))
            if db_info.get('db_group_name') not in db_group_name_list:
                db_group_name_list.append(db_info.get('db_group_name'))

        # 初始化时 dump 时间之后的 归档版本是 可用的归档版本        by shuai 20240422
        biz_code = self.biz_iter_id.split('_')[0]
        biz_bind_app_list = get_biz_bind_app_list(biz_code)
        # copy feature sql
        # 查询两个业务归档版本之间，应用的所有归档版本
        dump_pipeline_id_list = get_dump_archive_pipeline_id(self.biz_iter_id)

        available_dev_br = DbDumpMgt.get_new_after_dump_file_dev_iter_2(dump_pipeline_id_list, self.biz_iter_id,
                                                                        biz_bind_app_list)
        for db_group_name in db_group_name_list:
            feature_sql_path = os.path.join(db_cache_path, db_group_name)
            feature_sql_db_list = [f.path for f in os.scandir(feature_sql_path) if
                                   f.is_dir() and not f.name.startswith('.')]
            log.info('{}的子目录列表为: {}'.format(feature_sql_path, feature_sql_db_list))
            for feature_sql_db in feature_sql_db_list:
                if 'archive' in feature_sql_db:
                    # 多分支目录copy
                    archive_dir = os.path.join(feature_sql_path, 'archive')
                    if sort_sql_step and sort_sql_step == 'second_step':
                        log.info("二阶段分拣SQL:{}".format('不执行DML'))
                        sort_sql_common_second_step(archive_dir, flyway_cache_path, available_dev_br)
                    else:
                        log.info("一阶段分拣SQL:{}".format('执行符合条件的DML'))
                        sort_sql_common(archive_dir, flyway_cache_path, available_dev_br)
                else:
                    # copy 库
                    if sort_sql_step and sort_sql_step == 'second_step':
                        # 二阶段分拣：只拷贝DDL，过滤DML
                        log.info("二阶段分拣SQL - 非archive目录:{}，过滤DML目录".format(feature_sql_db))
                        
                        # 获取数据库目录下的子目录
                        if os.path.exists(feature_sql_db):
                            db_sub_dirs = [f.path for f in os.scandir(feature_sql_db) if f.is_dir()]
                            
                            for db_sub_dir in db_sub_dirs:
                                sub_dir_name = os.path.basename(db_sub_dir)
                                
                                # 只拷贝DDL目录，跳过DML目录
                                if sub_dir_name.upper() == 'DDL':
                                    target_path = os.path.join(flyway_cache_path, os.path.basename(feature_sql_db))
                                    if not os.path.exists(target_path):
                                        cmd = 'mkdir -p {}'.format(target_path)
                                        exec_local_cmd(cmd)
                                    
                                    cmd = 'cp -r {} {}'.format(db_sub_dir, target_path)
                                    log.info("======DDL======拷贝非archive目录的DDL：{}".format(cmd))
                                    exec_local_cmd(cmd)
                                elif sub_dir_name.upper() == 'DML':
                                    log.info("======排除DML======跳过非archive目录的DML：{}".format(db_sub_dir))
                                else:
                                    # 其他目录正常拷贝
                                    target_path = os.path.join(flyway_cache_path, os.path.basename(feature_sql_db))
                                    if not os.path.exists(target_path):
                                        cmd = 'mkdir -p {}'.format(target_path)
                                        exec_local_cmd(cmd)
                                    
                                    cmd = 'cp -r {} {}'.format(db_sub_dir, target_path)
                                    exec_local_cmd(cmd)
                    else:
                        # 一阶段分拣或其他情况：直接整体拷贝
                        cmd = 'cp -r {} {}'.format(feature_sql_db, flyway_cache_path)
                        exec_local_cmd(cmd)

        return 'success'

    @step_desc("数据库恢复")
    @TestDataInitPiplineLog(step_name="restore_dump")
    def restore_dump(self):
        oracle_db_info_list = []
        mysql_db_info_list = []
        exist_db_list = set()
        for db_info in self.db_info_list:
            if db_info.get("suite_db_name") not in exist_db_list:
                exist_db_list.add(db_info.get("suite_db_name"))
                if db_info.get("db_srv_type") == 'oracle':
                    oracle_db_info_list.append(db_info)
                elif db_info.get("db_srv_type") == 'mysql':
                    mysql_db_info_list.append(db_info)
        log.info("exist_db_list: {}".format(exist_db_list))
        with ThreadPoolExecutor(max_workers=20) as oracle_thread_pool:
            futures = []
            for db_info in oracle_db_info_list:
                future = oracle_thread_pool.submit(self.restore_oracle, self.biz_iter_id, db_info, self.suite_code)
                futures.append(future)
            oracle_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)
            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    log.error(repr(future.exception()))
                    raise Exception("oracle restore error")

        with ThreadPoolExecutor(max_workers=20) as mysql_thread_pool:
            futures = []
            for db_info in mysql_db_info_list:
                future = mysql_thread_pool.submit(self.restore_mysql, self.biz_iter_id, db_info, self.suite_code)
                futures.append(future)
            mysql_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)
            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    log.error(repr(future.exception()))
                    raise Exception("mysql restore error")
        return "success"

    def restore_mysql(self, biz_iter_id, db_info, suite_code):
        mh = MysqlHandler(db_info.get("module_name"), suite_code, [db_info], biz_iter_id)
        mh._check_dump_exist('mysql')
        mh._db_drop()
        mh._db_create()
        mh._db_restore()

    def restore_oracle(self, biz_iter_id, db_info, suite_code):
        oh = OracleHandler(db_info.get("module_name"), suite_code, [db_info], biz_iter_id)
        oh._check_dump_exist('oracle')

        # 尝试不做断连操作
        # 不做断连直接覆盖操作有问题，已经存在的表不能删除，到账后面的create table失败
        # oh._db_lock()
        # oh._kill_session()
        oh._db_unlock()
        oh._drop_ddl()
        # oh._db_drop()

        # oh._user_create()
        oh._db_restore()
        # oh._db_unlock()
        oh._db_set_user_and_password()
        oh._db_update_synonym()

    @step_desc("数据删除")
    @TestDataInitPiplineLog(step_name="truncate_data")
    def truncate_data(self):
        oracle_db_info_list = []
        mysql_db_info_list = []
        exist_db_list = set()
        for db_info in self.db_info_list:
            if db_info.get("suite_db_name") not in exist_db_list:
                exist_db_list.add(db_info.get("suite_db_name"))
                if db_info.get("db_srv_type") == 'oracle':
                    oracle_db_info_list.append(db_info)
                elif db_info.get("db_srv_type") == 'mysql':
                    mysql_db_info_list.append(db_info)
        oracle_export_fail = False
        log.info("需要删除数据的oracle_db_info_list: {}".format(oracle_db_info_list))
        with ThreadPoolExecutor(max_workers=20) as oracle_thread_pool:
            futures = []
            for db_info in oracle_db_info_list:
                future = oracle_thread_pool.submit(oracle_truncate_data, db_info)
                futures.append(future)
            oracle_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)
            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    log.error(repr(future.exception()))
                    oracle_export_fail = True
        mysql_export_fail = False
        log.info("需要删除数据mysql_db_info_list: {}".format(mysql_db_info_list))
        with ThreadPoolExecutor(max_workers=20) as mysql_thread_pool:
            futures = []
            for db_info in mysql_db_info_list:
                future = mysql_thread_pool.submit(mysql_truncate_data, db_info)
                futures.append(future)
            mysql_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)

            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    traceback.print_exc()
                    log.error(repr(future.exception()))
                    mysql_export_fail = True
        if mysql_export_fail or oracle_export_fail:
            raise Exception("数据删除失败")
        return "success"

    @step_desc("数据导出")
    @TestDataInitPiplineLog(step_name="test_data_export")
    def test_data_export(self):
        oracle_db_info_list = []
        mysql_db_info_list = []
        exist_db_list = set()
        for db_info in self.db_info_list:
            if db_info.get("suite_db_name") not in exist_db_list:
                exist_db_list.add(db_info.get("suite_db_name"))
                if db_info.get("db_srv_type") == 'oracle':
                    oracle_db_info_list.append(db_info)
                elif db_info.get("db_srv_type") == 'mysql':
                    mysql_db_info_list.append(db_info)
        oracle_export_fail = False
        log.info("需要导出的oracle_db_info_list: {}".format(oracle_db_info_list))
        with ThreadPoolExecutor(max_workers=20) as oracle_thread_pool:
            futures = []
            for db_info in oracle_db_info_list:
                future = oracle_thread_pool.submit(self.oracle_data_export, db_info)
                futures.append(future)
            oracle_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)
            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    log.error(repr(future.exception()))
                    oracle_export_fail = True
        mysql_export_fail = False
        log.info("需要导出的mysql_db_info_list: {}".format(mysql_db_info_list))
        with ThreadPoolExecutor(max_workers=20) as mysql_thread_pool:
            futures = []
            for db_info in mysql_db_info_list:
                future = mysql_thread_pool.submit(self.mysql_data_export, db_info)
                futures.append(future)
            mysql_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)

            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    log.error(repr(future.exception()))
                    mysql_export_fail = True
        if mysql_export_fail or oracle_export_fail:
            raise Exception("数据导出失败")
        return "success"

    def oracle_data_export(self, db_info):
        # 先删除同名文件，再导出数据
        db_srv_hosts = db_info.get("db_srv_hosts")
        db_srv_username = db_info.get("db_srv_username")
        suite_db_name = db_info.get("suite_db_name")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        data_dump_dir = db_info.get("data_dump_dir")
        logger.info("======开始执行oracle数据提取，数据库名：{}======".format(suite_db_name))
        dump_file_name = suite_db_name + '.dmp'
        self.delete_dump_data_file(data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, dump_file_name)
        oracle_dump_cmd = '''source {db_srv_bash_profile};expdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile={dump_file_name} content=data_only schemas={suite_db_name} exclude=table:\\\"= \\\'flyway_schema_history\\\'\\\"
                                         '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                                    dump_file_name=dump_file_name,
                                                    suite_db_name=suite_db_name)
        start_time = datetime.datetime.now()
        logger.info("库：{}-->dump命令:{}".format(suite_db_name, oracle_dump_cmd))
        res = exec_remote_cmd_by_username_password(db_srv_hosts, oracle_dump_cmd, db_srv_username, db_srv_password)
        logger.info("库:{},DB数据提取结果：{}".format(suite_db_name, res))
        end_time = datetime.datetime.now()
        logger.info("======数据抽取结束，数据库名：{}======，  恢复操作耗时：{}s".format(suite_db_name, round(
            (end_time - start_time).total_seconds(), 2)))
        self.oracle_sequence_export(db_info)

    def oracle_sequence_export(self, db_info):
        # 先删除同名文件，再导出sequence和数据
        db_srv_hosts = db_info.get("db_srv_hosts")
        db_srv_username = db_info.get("db_srv_username")
        suite_db_name = db_info.get("suite_db_name")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        data_dump_dir = db_info.get("data_dump_dir")
        logger.info("======开始执行oracle-SEQUENCE提取，数据库名：{}======".format(suite_db_name))
        sequence_file_name = suite_db_name + '_SEQUENCE.dmp'
        self.delete_dump_data_file(data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, sequence_file_name)
        oracle_dump_sequence_cmd = '''source {db_srv_bash_profile};expdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile={sequence_dump_name} schemas={suite_db_name} content= ALL include=SEQUENCE
                                         '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                                    sequence_dump_name=sequence_file_name,
                                                    suite_db_name=suite_db_name)
        start_time = datetime.datetime.now()
        logger.info("库：{}-->dump命令:{}".format(suite_db_name, oracle_dump_sequence_cmd))
        res = exec_remote_cmd_by_username_password(db_srv_hosts, oracle_dump_sequence_cmd, db_srv_username,
                                                   db_srv_password)
        logger.info("库:{},DB-SEQUENCE提取结果：{}".format(suite_db_name, res))
        end_time = datetime.datetime.now()
        logger.info("======SEQUENCE抽取结束，数据库名：{}======，  恢复操作耗时：{}s".format(suite_db_name, round(
            (end_time - start_time).total_seconds(), 2)))

    def mysql_data_export(self, db_info):
        # 先删除同名文件，再导出数据
        suite_db_name = db_info.get("suite_db_name")
        db_srv_hosts = db_info.get("db_srv_hosts")
        db_srv_username = db_info.get("db_srv_username")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        db_srv_socket_path = db_info.get("db_srv_socket_path")
        data_dump_dir = db_info.get("data_dump_dir")
        logger.info("======开始执行mysql数据提取，数据库名：{}======".format(db_info.get("db_name")))
        dump_file_name = suite_db_name + '.sql'
        self.delete_dump_data_file(data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, dump_file_name)
        mysql_dump_cmd = '''source {db_srv_bash_profile};mysqldump --set-gtid-purged=off --default-character-set=utf8mb4 --skip-lock-tables --single-transaction   --hex-blob --socket={db_srv_socket_path} -u{username} -p'{password}' {suite_db_name} --no-create-info --complete-insert --skip-triggers --ignore-table={suite_db_name}.flyway_schema_history > {dump_file_path}/{dump_file_name}
                                   '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                              db_srv_socket_path=db_srv_socket_path,
                                              username=db_info.get("username"),
                                              password=db_info.get("password"),
                                              suite_db_name=suite_db_name,
                                              dump_file_name=dump_file_name,
                                              dump_file_path=data_dump_dir)
        start_time = datetime.datetime.now()
        logger.info("数据库名：{}-->dump命令:{}".format(suite_db_name, mysql_dump_cmd))
        res = exec_remote_cmd_by_username_password(db_srv_hosts, mysql_dump_cmd, db_srv_username, db_srv_password)
        logger.info("数据库名{},数据抽取dump结果：{}".format(suite_db_name, res))
        end_time = datetime.datetime.now()
        logger.info("======数据抽取结束，数据库名：{}======，  恢复操作耗时：{}s".format(suite_db_name, round(
            (end_time - start_time).total_seconds(), 2)))

    def delete_dump_data_file(self, data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, dump_file_name):
        delete_file_cmd = '''rm -rf {dump_file_path}/{dump_file_name}'''.format(
            dump_file_path=data_dump_dir, dump_file_name=dump_file_name)
        res = exec_remote_cmd_by_username_password(db_srv_hosts, delete_file_cmd, db_srv_username, db_srv_password)
        logger.info("删除文件结果：{}".format(res))

    @step_desc("数据导入")
    @TestDataInitPiplineLog(step_name="test_data_import")
    def test_data_import(self):
        oracle_db_info_list = []
        mysql_db_info_list = []
        exist_db_list = set()
        for db_info in self.db_info_list:
            if db_info.get("suite_db_name") not in exist_db_list:
                exist_db_list.add(db_info.get("suite_db_name"))
                if db_info.get("db_srv_type") == 'oracle':
                    oracle_db_info_list.append(db_info)
                elif db_info.get("db_srv_type") == 'mysql':
                    mysql_db_info_list.append(db_info)
        oracle_export_fail = False
        log.info("需要导入的oracle_db_info_list: {}".format(oracle_db_info_list))
        with ThreadPoolExecutor(max_workers=20) as oracle_thread_pool:
            futures = []
            futures_info = []
            for db_info in oracle_db_info_list:
                future = oracle_thread_pool.submit(self.oracle_data_import, db_info)
                futures.append(future)
                futures_info.append({"suite_db_name": db_info.get("suite_db_name"), "future": future})
            oracle_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)
            for item in futures_info:
                future = item.get("future")
                log.info("{}的数据导入结果是：{}".format(item.get("suite_db_name"),
                                                        future.exception() if not future.exception() else "success"))
                if future.exception() is not None:
                    log.info("--------------以下是库：{}的导入错误详细信息, 开始".format(item.get("suite_db_name")))
                    log.error(repr(future.exception()))
                    log.info("--------------以上是库：{}的导入错误详细信息，结束".format(item.get("suite_db_name")))
                    oracle_export_fail = True
        mysql_export_fail = False
        log.info("需要导入的mysql_db_info_list: {}".format(mysql_db_info_list))
        with ThreadPoolExecutor(max_workers=20) as mysql_thread_pool:
            futures = []
            futures_info = []
            for db_info in mysql_db_info_list:
                future = mysql_thread_pool.submit(self.mysql_data_import, db_info)
                futures.append(future)
                futures_info.append({"suite_db_name": db_info.get("suite_db_name"), "future": future})
            mysql_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)

            for item in futures_info:
                future = item.get("future")
                log.info("{}的数据导入结果是：{}".format(item.get("suite_db_name"),
                                                        future.exception() if not future.exception() else "success"))
                if future.exception() is not None:
                    log.info("--------------以下是库：{}的导入错误详细信息, 开始".format(item.get("suite_db_name")))
                    log.error(repr(future.exception()))
                    log.info("--------------以上是库：{}的导入错误详细信息，结束".format(item.get("suite_db_name")))
                    mysql_export_fail = True
        if mysql_export_fail or oracle_export_fail:
            switch = EXCEPT_SWITCH['test_data_import']
            logger.info("数据导入异常开关{}".format(switch))
            if 'open' == switch:
                raise Exception("数据导入失败")
        return "success"

    def oracle_sequence_import(self, db_info):
        logger.info("======开始执行oracle-sequence灌入，数据库名：{}======".format(db_info.get("db_name")))
        db_srv_hosts = db_info.get("db_srv_hosts")
        db_srv_username = db_info.get("db_srv_username")
        suite_db_name = db_info.get("suite_db_name")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        data_dump_dir = db_info.get("data_dump_dir")
        logger.info("======开始执行oracle-sequence灌入，数据库名：{}======".format(suite_db_name))
        sequence_file_name = suite_db_name + "_SEQUENCE.dmp"
        self.check_dump_file_exist(data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username,
                                   sequence_file_name)
        sequence_dump_cmd = '''source {db_srv_bash_profile};impdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile={dump_file_name} content=ALL remap_schema={suite_db_name}:{suite_db_name}
                                                         '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                                                    dump_file_name=sequence_file_name,
                                                                    suite_db_name=suite_db_name)
        logger.info("库：{}-->sequence灌入命令:{}".format(suite_db_name, sequence_dump_cmd))
        start_time = datetime.datetime.now()
        res = exec_db_import_remote_cmd_by_username_password(db_srv_hosts, sequence_dump_cmd, db_srv_username,
                                                             db_srv_password)
        logger.info("库:{},sequence灌入结果：{}".format(suite_db_name, res))
        end_time = datetime.datetime.now()
        logger.info("======sequence灌入结束，数据库名：{}======，  恢复操作耗时：{}s".format(suite_db_name, round(
            (end_time - start_time).total_seconds(), 2)))

    def oracle_data_import(self, db_info):
        logger.info("======开始执行oracle数据灌入，数据库名：{}======".format(db_info.get("db_name")))
        db_srv_hosts = db_info.get("db_srv_hosts")
        db_srv_username = db_info.get("db_srv_username")
        suite_db_name = db_info.get("suite_db_name")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        data_dump_dir = db_info.get("data_dump_dir")
        logger.info("======开始执行oracle数据灌入，数据库名：{}======".format(suite_db_name))
        dump_file_name = suite_db_name + ".dmp"
        self.check_dump_file_exist(data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, dump_file_name)
        oracle_dump_cmd = '''source {db_srv_bash_profile};impdp \\\'/ as sysdba\\\' directory=DATA_PUMP_DIR dumpfile={dump_file_name} content=data_only remap_schema={suite_db_name}:{suite_db_name}
                                                 '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                                            dump_file_name=dump_file_name,
                                                            suite_db_name=suite_db_name)
        start_time = datetime.datetime.now()
        logger.info("库：{}-->dump灌入命令:{}".format(suite_db_name, oracle_dump_cmd))
        res = exec_db_import_remote_cmd_by_username_password(db_srv_hosts, oracle_dump_cmd, db_srv_username,
                                                             db_srv_password)
        logger.info("库:{},DB数据灌入结果：{}".format(suite_db_name, res))
        end_time = datetime.datetime.now()
        logger.info("======数据灌入结束，数据库名：{}======，  恢复操作耗时：{}s".format(suite_db_name, round(
            (end_time - start_time).total_seconds(), 2)))
        self.oracle_sequence_import(db_info)

    def mysql_data_import(self, db_info):
        # 先判断文件是否存在，再导入数据
        suite_db_name = db_info.get("suite_db_name")
        db_srv_hosts = db_info.get("db_srv_hosts")
        db_srv_username = db_info.get("db_srv_username")
        db_srv_password = db_info.get("db_srv_password")
        db_srv_bash_profile = db_info.get("db_srv_bash_profile")
        db_srv_socket_path = db_info.get("db_srv_socket_path")
        data_dump_dir = db_info.get("data_dump_dir")
        logger.info("======开始执行mysql数据灌入，数据库名：{}======".format(db_info.get("db_name")))
        dump_file_name = suite_db_name + ".sql"
        self.check_dump_file_exist(data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, dump_file_name)
        mysql_dump_cmd = '''source {db_srv_bash_profile};mysql --socket={db_srv_socket_path} -u{username} -p'{password}' {suite_db_name} < {dump_file_path}/{suite_db_name}.sql
                                           '''.format(db_srv_bash_profile=db_srv_bash_profile,
                                                      db_srv_socket_path=db_srv_socket_path,
                                                      username=db_info.get("username"),
                                                      password=db_info.get("password"),
                                                      suite_db_name=suite_db_name,
                                                      dump_file_path=data_dump_dir)
        logger.info("数据库名：{}-->dump灌入命令:{}".format(suite_db_name, mysql_dump_cmd))
        res = exec_db_import_remote_cmd_by_username_password(db_srv_hosts, mysql_dump_cmd, db_srv_username,
                                                             db_srv_password)
        logger.info("数据库名{},数据灌入dump结果：{}".format(suite_db_name, res))

    def check_dump_file_exist(self, data_dump_dir, db_srv_hosts, db_srv_password, db_srv_username, dump_file_name):
        logger.info("======开始检查数据文件是否存在======")
        check_file_cmd = '''cd {dump_file_path};find . -name {dump_file_name}'''.format(
            dump_file_path=data_dump_dir, dump_file_name=dump_file_name)
        logger.info("检查文件命令：{}".format(check_file_cmd))
        res = exec_remote_cmd_by_username_password(db_srv_hosts, check_file_cmd, db_srv_username, db_srv_password)
        logger.info("检查文件结果：{}".format(res))
        if dump_file_name not in res:
            logger.error("{}文件不存在，数据导入失败，检查数据导出是否存在异常".format(dump_file_name))
            raise Exception('数据灌入失败：{}文件不存在，检查数据导出是否存在异常'.format(dump_file_name))

    @step_desc("执行sql")
    @TestDataInitPiplineLog(step_name="execute_sql")
    def execute_sql(self):
        # if self.biz_category == 2 and self.select_db_biz_category == 2:
        #     logger.info("衍生业务选了衍生业务的dump，跳过执行sql步骤")
        #     return 'success'
        suite_db_names = []
        new_db_info_list = []
        # 去掉库的应用属性
        for db_info in self.db_info_list:
            if db_info.get("suite_db_name") in suite_db_names:
                continue
            suite_db_names.append(db_info.get("suite_db_name"))
            db_info.pop("module_name")
            # update_data_base_config(self.opt_type, db_info)
            new_db_info_list.append(db_info)
        with ThreadPoolExecutor(max_workers=20) as mysql_thread_pool:
            futures = []
            for new_db_info in new_db_info_list:
                log.info("开始执行SQL: {}".format(new_db_info.get("db_name")))
                future = mysql_thread_pool.submit(self.restore_db, new_db_info)
                futures.append(future)
            mysql_thread_pool.shutdown(wait=True)
            concurrent.futures.wait(futures)
            for future in futures:
                log.info("future执行结果：{}".format(future.exception()))
                if future.exception() is not None:
                    log.error(repr(future.exception()))
                    raise Exception("执行sql error")
        return 'success'

    def restore_db(self, new_db_info):
        flyway_db_info = {}
        flyway_db_info['db_user'] = new_db_info.get("username")
        flyway_db_info['db_passwd'] = new_db_info.get("password")
        flyway_db_info['conn_url'] = new_db_info.get("conn_url")
        db_dir = os.path.join(TEST_DATA_INIT.get('root_path'), suite_code,
                              TEST_DATA_INIT.get('flyway_cache_path'), new_db_info.get("db_name"))
        try:
            ora_dml_root_dir = os.path.join(db_dir, 'DML')
            mv_seq_to_ddl = os.path.join(ora_dml_root_dir, 'seq')
            db_dir = os.path.join(db_dir, 'DDL')
            if os.path.exists(mv_seq_to_ddl):
                log.info("mv_seq_to_ddl: {}".format(mv_seq_to_ddl))
                cmd = 'mv {}/** {}/'.format(mv_seq_to_ddl, db_dir)
                exec_local_cmd(cmd)
                cmd = 'rm -rf {}'.format(mv_seq_to_ddl)
                exec_local_cmd(cmd)

            exec_flyway_command(flyway_db_info, 'repair', db_dir)
            exec_flyway_command(flyway_db_info, 'migrate', db_dir)
            if new_db_info.get("db_srv_type") == 'oracle':
                exec_dml(ora_dml_root_dir, new_db_info)
            if new_db_info.get("db_srv_type") == 'mysql':
                exec_mysql_dml(ora_dml_root_dir, new_db_info)

            # 执行dump操作时，cdc不执行
            # if self.opt_type == 1:
            #     curr_time = self.__get_remote_datetime(new_db_info.get("db_srv_hosts"),
            #                                            new_db_info.get("db_srv_username"),
            #                                            new_db_info.get("db_srv_password"))
            #     db_name = new_db_info.get("db_name")
            #     cdc_position = None
            #     binlog_name = None
            #     log_pos = None
            #     cdc_cache = dict()
            #     logging.info("记录数据库{}的开始恢复时间为{}".format(db_name, curr_time))
            #     curr_time = datetime.datetime.now()
            #     # 从缓存取cdc_position
            #     logging.info("self.biz_category=={}".format(self.biz_category))
            #     if new_db_info.get("db_srv_type") == DbCdcEnum.ORACLE.value:
            #         his_db_name = new_db_info.get("username")
            #         if self.biz_category == 1:
            #             cdc_position = self.__get_cdc_position(new_db_info, cdc_cache, db_name)
            #     else:
            #         his_db_name = new_db_info.get("suite_db_name")
            #         if self.biz_category == 1:
            #             binlog_name, log_pos = self.__get_binlog_info(new_db_info, cdc_cache, db_name)
            #     db_restore_his, created = DbMgtDbRestoreHis.get_or_create(db_name=his_db_name,
            #                                                               suite_code=suite_code,
            #                                                               db_info_id=new_db_info.get("db_info_id"),
            #                                                               restore_datetime=curr_time,
            #                                                               defaults={'opt_pipeline_id': bis_pipeline_id})
            #
            #     if self.biz_category == 1:
            #         cdc_info, created = DbMgtCdcInfo.get_or_create(suite_code=suite_code,
            #                                                        db_info_id=new_db_info.get("db_info_id"),
            #                                                        defaults={
            #                                                            'restore_his_id': db_restore_his.id,
            #                                                            'cdc_position': cdc_position,
            #                                                            'binlog_name': binlog_name,
            #                                                            'log_pos': log_pos,
            #                                                            'cdc_flag': 1,
            #                                                            'create_time': curr_time})
            #         log.info("created:{},cdc_info:{}".format(created, cdc_info))
            #         if not created:
            #             DbMgtCdcInfo.update(restore_his_id=db_restore_his.id,
            #                                 cdc_position=cdc_position,
            #                                 binlog_name=binlog_name,
            #                                 log_pos=log_pos,
            #                                 cdc_flag=1,
            #                                 update_time=curr_time).where(
            #                 DbMgtCdcInfo.suite_code == suite_code,
            #                 DbMgtCdcInfo.db_info_id == new_db_info.get("db_info_id")).execute()
            #         logging.info("更新数据库{}的恢复时间为{}".format(db_name, curr_time))
        except Exception as e:
            log.error('sql执行失败,数据库：{}'.format(new_db_info.get("db_name")))
            traceback.print_exc()
            logging.error(str(e))
            raise Exception(e)

    @step_desc("库清理")
    @TestDataInitPiplineLog(step_name="db_clean")
    def db_clean(self):
        """利用flyway的clean实现库的清理，解决DDL灌入失败的问题。zt@2024-09-18"""
        suite_db_names = []
        new_db_info_list = []
        mysql_db_info_list = []
        oracle_db_info_list = []
        # 去掉库的应用属性
        # for db_info in self.db_info_list:
        #     if db_info.get("suite_db_name") in suite_db_names:
        #         continue
        #     suite_db_names.append(db_info.get("suite_db_name"))
        #     db_info.pop("module_name")
        #     new_db_info_list.append(db_info)
        #
        #     if db_info.get("db_srv_type") == 'mysql':
        #         mysql_db_info_list.append(db_info)
        #     elif db_info.get("db_srv_type") == 'oracle':
        #         oracle_db_info_list.append(db_info)
        #     else:
        #         logging.warning(">>>> 数据错误，不支持的数据库类型：{}".format(db_info.get("db_srv_type")))
        # 优化保持和库恢复一样的去重逻辑。zt@2024-09-24
        exist_db_list = set()
        for db_info in self.db_info_list:
            if db_info.get("suite_db_name") not in exist_db_list:
                exist_db_list.add(db_info.get("suite_db_name"))
                if db_info.get("db_srv_type") == 'oracle':
                    # update_data_base_config(self.opt_type, db_info)
                    oracle_db_info_list.append(db_info)
                elif db_info.get("db_srv_type") == 'mysql':
                    # update_data_base_config(self.opt_type, db_info)
                    mysql_db_info_list.append(db_info)
        log.info("exist_db_list: {}".format(exist_db_list))

        # MySQL直接使用Flyway清理：
        if mysql_db_info_list:
            with ThreadPoolExecutor(max_workers=20) as mysql_thread_pool:
                futures = []
                for mysql_db_info in mysql_db_info_list:
                    log.info("MySQL开始执行「库清理」: {}".format(mysql_db_info.get("db_name")))
                    future = mysql_thread_pool.submit(self.__clean_db_with_flyway, mysql_db_info)
                    futures.append(future)
                mysql_thread_pool.shutdown(wait=True)
                concurrent.futures.wait(futures)
                for future in futures:
                    log.info("MySQL线程池执行「库清理」结果：{}".format(future.exception()))
                    if future.exception() is not None:
                        log.error(repr(future.exception()))
                        raise Exception("MySQL执行「库清理」error")

        # Oracle库需要使用空dump还原：
        if oracle_db_info_list:
            with ThreadPoolExecutor(max_workers=20) as oracle_thread_pool:
                futures = []
                for oracle_db_info in oracle_db_info_list:
                    log.info("Oracle开始执行「库清理」: {}".format(oracle_db_info.get("db_name")))
                    future = oracle_thread_pool.submit(self.__clean_db_for_oracle, oracle_db_info)
                    futures.append(future)
                oracle_thread_pool.shutdown(wait=True)
                concurrent.futures.wait(futures)
                for future in futures:
                    log.info("Oracle线程池执行「库清理」结果：{}".format(future.exception()))
                    if future.exception() is not None:
                        log.error(repr(future.exception()))
                        raise Exception("Oracle执行「库清理」error")

        return 'success'

    def __clean_db_with_flyway(self, new_db_info):
        """flyway执行clean操作。zt@2024-09-18"""
        flyway_db_info = {}
        flyway_db_info['db_user'] = new_db_info.get("username")
        flyway_db_info['db_passwd'] = new_db_info.get("password")
        flyway_db_info['conn_url'] = new_db_info.get("conn_url")
        db_dir = os.path.join(TEST_DATA_INIT.get('root_path'), suite_code,
                              TEST_DATA_INIT.get('flyway_cache_path'), new_db_info.get("db_name"))
        try:
            exec_flyway_command(flyway_db_info, 'clean', db_dir)
        except Exception as e:
            log.error('flyway执行MySQL库clean操作失败，数据库：{}'.format(new_db_info.get("db_name")))
            traceback.print_exc()
            logging.error(str(e))
            raise Exception(e)

    def __clean_db_for_oracle(self, new_db_info):
        """oracle需要使用空dump来清空。zt@2024-09-20"""
        flyway_db_info = {}
        flyway_db_info['db_user'] = new_db_info.get("username")
        flyway_db_info['db_passwd'] = new_db_info.get("password")
        flyway_db_info['conn_url'] = new_db_info.get("conn_url")
        db_dir = os.path.join(TEST_DATA_INIT.get('root_path'), suite_code,
                              TEST_DATA_INIT.get('flyway_cache_path'), new_db_info.get("db_name"))
        try:
            oh = OracleHandler(new_db_info.get("module_name"), suite_code, [new_db_info], None)
            oh._check_dump_exist('oracle')
            # oh._db_lock()
            # oh._kill_session()
            oh._db_unlock()
            oh._drop_ddl()
            # oh._db_drop()
            # 数据开发线清空库，所以不需要恢复。zt@2024-09-20
            # oh._user_create()
            oh._db_restore()
            # oh._db_unlock()
            oh._db_set_user_and_password()
            oh._db_update_synonym()
        except Exception as e:
            log.error('空dump执行Oracle库clean操作失败，数据库：{}'.format(new_db_info.get("db_name")))
            traceback.print_exc()
            logging.error(str(e))
            raise Exception(e)

    def __get_remote_datetime(self, node_ip, username, password):
        cmd = "date '+%F %T'"
        curr_time = exec_remote_cmd_by_username_password(node_ip, cmd, username, password)
        return curr_time

    def __get_cdc_position(self, new_db_info, cdc_cache, db_name):
        logging.info(new_db_info)
        cdc_position = get_current_scn(
            DbCdcParam(host=new_db_info.get("db_srv_hosts"), port=new_db_info.get("db_srv_port"),
                       user=DbCdcEnum.ORACLE_CDC_USER.value, password=DbCdcEnum.ORACLE_CDC_PWD.value,
                       service_name=new_db_info.get("db_srv_name")))
        logging.info("host:{},port:{},db:{},cdc_position:{}".format(new_db_info.get("db_srv_hosts"),
                                                                    new_db_info.get("db_srv_port"),
                                                                    db_name, cdc_position))
        all_tables = []
        all_tables = get_all_table(DbCdcParam(host=new_db_info.get("db_srv_hosts"),
                                              port=new_db_info.get("db_srv_port"),
                                              user=DbCdcEnum.ORACLE_CDC_USER.value,
                                              password=DbCdcEnum.ORACLE_CDC_PWD.value,
                                              service_name=new_db_info.get("db_srv_name")
                                              ), new_db_info.get("username"))
        # 给所有表添加补充日志
        for table in get_db_tables(str(db_name)):
            full_table_name = new_db_info.get("username") + "." + table
            all_tables.append(full_table_name)
        # 按每30个分一组
        group_size = 30
        groups = [all_tables[i:i + group_size] for i in range(0, len(all_tables), group_size)]
        # 创建线程池
        with ThreadPoolExecutor() as executor:
            # 提交每组数据给线程池处理
            future_to_group = {
                executor.submit(add_supplemental_log_to_table, group, DbCdcParam(host=new_db_info.get("db_srv_hosts"),
                                                                                 port=new_db_info.get("db_srv_port"),
                                                                                 user=DbCdcEnum.ORACLE_CDC_USER.value,
                                                                                 password=DbCdcEnum.ORACLE_CDC_PWD.value,
                                                                                 service_name=new_db_info.get(
                                                                                     "db_srv_name")
                                                                                 )): group for group in groups}

            # 获取线程处理的结果
            for future in as_completed(future_to_group):
                group = future_to_group[future]
                try:
                    result = future.result()
                    logging.info("group:{} error_tables:{}".format(group, result))
                except Exception as e:
                    print(f"Exception occurred while processing group: {group}")
                    print(str(e))
        return cdc_position

    def __get_binlog_info(self, db_info, cdc_cache, db_name):
        obj = cdc_cache.get(
            db_info.get("db_srv_hosts") + ":" + str(db_info.get("db_srv_port")))
        logging.info("1-->__get_binlog_info=={}".format(db_info))

        binlog_name = obj.get("binlog_name") if obj else None
        log_pos = obj.get("log_pos") if obj else None
        if not binlog_name or not log_pos:
            db_info = {k: v for k, v in db_info.items() if
                       k in ["db_name", "db_group_name", "db_srv_type", "db_srv_hosts", "db_srv_port",
                             "db_srv_username", "db_srv_password", "db_srv_name", "username", "password"]}
            db_info.update({"db_srv_username": db_info.get("db_name")})
            db_info.update({"db_info_username": db_info.get("username")})
            db_info.update({"db_info_password": db_info.get("password")})
            db_info.pop("username")
            db_info.pop("password")
            logging.info("2-->__get_binlog_info=={}".format(db_info))
            new_db_info = type("TestingDbInfoBo", (object,), db_info)
            mu = MysqlUtils(new_db_info)
            binlog_name, log_pos = mu._get_current_log_bin_file()
            # cdc_position放入缓存
            cdc_cache[
                (db_info.get("db_srv_hosts") + ":" + str(db_info.get("db_srv_port")))] = {
                "binlog_name": binlog_name,
                "log_pos": log_pos}

            logging.info("host:{},port:{},db:{},binlog_name:{},log_pos:{}".format(db_info.get("db_srv_hosts"),
                                                                                  db_info.get("db_srv_port"),
                                                                                  db_name, binlog_name, log_pos))
        return binlog_name, log_pos

    def call(self, params):
        log.info("参数总数 {}".format(len(params)))

        acceptor = {
            "pull_sql_repo": self.pull_sql_repo,
            "sort_sql": self.sort_sql,
            "restore_dump": self.restore_dump,
            "execute_sql": self.execute_sql,
            "test_data_export": self.test_data_export,
            "test_data_import": self.test_data_import,
            "db_clean": self.db_clean,
            "truncate_data": self.truncate_data,
        }

        # 特殊处理sort_sql方法，传递sort_sql_step参数
        if params[0] == "sort_sql":
            sort_sql_step = params[8] if len(params) > 8 else None
            self.sort_sql(sort_sql_step)
        else:
            acceptor[params[0]]()


if __name__ == "__main__":
    log.info("调用 {}".format(sys.argv[1:]))
    log.info('sys.argv[6] = {}, 类型为： {}'.format(sys.argv[6], type(sys.argv[6])))
    params = sys.argv[1:]
    step_name = params[1]
    biz_code = params[3]
    biz_branch_name = params[4]
    suite_code = params[5]
    business_id = params[6]
    biz_iter_id = biz_code + '_' + biz_branch_name

    testDataDev = TestDataDev(params[1], params[2], biz_iter_id, suite_code, business_id, params[7])
    db_info_list = testDataDev._get_db_info_list(business_id, suite_code)

    # 归档的时候，排除业务master分支没有初始化dump的逻辑库，为了跳过中间步骤，最后一步直接复制dev dump信息到master 20250324 by fwm
    if step_name == 'archive_make_dump':
        master_biz_iter_id = biz_code + '_master'
        logic_id_set = {
            item.db_logic_id
            for item in DbMgtDumpFile.select(DbMgtDumpFile.db_logic_id).where(
                DbMgtDumpFile.biz_test_iter_id == master_biz_iter_id
            )
        }
        # 使用列表推导式过滤（避免遍历时修改列表导致的逻辑错误）
        db_info_list[:] = [
            db_info
            for db_info in db_info_list
            if db_info.get("db_logic_id") in logic_id_set
        ]

    testDataDev.set_db_info_list(db_info_list)

    testDataDev.call(params)
