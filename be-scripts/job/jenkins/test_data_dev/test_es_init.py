# ES初始化功能实现模块
# 参考test_data_init.py的模式，提取ES相关功能
# 第3版 直接调用test_suite_init_impl.py中的Handler，传入ES相关参数

import json
import os
import sys
import datetime
import traceback

# 添加项目路径
PROJECT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.append(PROJECT_DIR)

# 调试信息


from settings import logger as log
from dao.connect.mysql import DBConnectionManager
from test_publish_aio.test_suite_init_utils import get_time_str
from test_publish_aio.test_suite_init_constants import StepEnum, TypeEnum
from test_publish_aio.test_suite_init_db import get_db_param_by_business_id
from test_publish_aio.test_suite_init_impl import StepHandlerFactory


class EsInitHandler:
    """ES初始化处理类"""
    
    def __init__(self, job_name, workspace, biz_code, biz_branch_name, suite_code, business_id, build_id):
        self.job_name = job_name
        self.workspace = workspace
        self.biz_code = biz_code
        self.biz_branch_name = biz_branch_name
        self.suite_code = suite_code
        self.business_id = business_id
        self.build_id = build_id
        self.biz_iter_id = biz_code + '_' + biz_branch_name
        
        log.info(f"初始化ES处理器: job_name={job_name}, biz_iter_id={self.biz_iter_id}, business_id={business_id}")

    def _get_param_dict(self):
        """获取参数字典，从数据库查询业务参数"""
        try:
            # 从数据库获取业务参数
            db_row = get_db_param_by_business_id(self.business_id)
            log.info(f"从数据库获取的参数: {db_row}")
            
            # 构建参数字典
            param_dict = {
                'build_id': self.build_id,
                'job_workspace': self.workspace,
                'job_duration': 0,
                'job_result': 'RUNNING',
                'type_enum': TypeEnum.MULTI_PUSH,  # ES操作需要MULTI_PUSH类型
                'job_business_id': self.business_id,
                'suite_code': self.suite_code,
                'operator': 'system',
                'opt_time': datetime.datetime.now(),
                'sys_datetime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            }
            
            # 从数据库参数中提取需要的字段
            if db_row:
                # 数据库返回的是字典格式，包含HTTP_PARAM_LIST中的字段
                param_dict.update({
                    'app_dict_list': json.loads(db_row.get('APP_DICT_LIST', '[]')) if db_row.get('APP_DICT_LIST') else [],
                    'br_name': db_row.get('BR_NAME', ''),
                    'db_env': db_row.get('DB_ENV', ''),
                    'is_clear_cache': db_row.get('IS_CLEAR_CACHE', False),
                    'test_set': db_row.get('TEST_SET', ''),
                    'opt_type': db_row.get('OPT_TYPE', ''),
                    'jacoco_type': db_row.get('JACOCO_TYPE', ''),
                    'pinpoint_type': db_row.get('PINPOINT_TYPE', ''),
                    'mock_type': db_row.get('MOCK_TYPE', ''),
                    'arex_type': db_row.get('AREX_TYPE', ''),
                    'agent_info': db_row.get('AGENT_INFO', ''),
                    'bis_pipeline_id': db_row.get('BIS_PIPELINE_ID', ''),
                    'biz_db_init_flag': db_row.get('BIZ_DB_INIT_FLAG', ''),
                    'param1': db_row.get('PARAM1', ''),
                    'param2': db_row.get('PARAM2', ''),
                    'param3': db_row.get('PARAM3', ''),
                    'job_log_id': db_row.get('job_log_id', ''),
                    'job_bo_key': db_row.get('job_bo_key', ''),
                })
            
            log.info(f"构建的参数字典: {json.dumps(param_dict, indent=2, default=str)}")
            return param_dict
            
        except Exception as e:
            log.error(f"获取参数字典失败: {e}")
            log.error(traceback.format_exc())
            # 返回基础参数字典
            return {
                'build_id': self.build_id,
                'job_workspace': self.workspace,
                'job_duration': 0,
                'job_result': 'RUNNING',
                'type_enum': TypeEnum.MULTI_PUSH,
                'job_business_id': self.business_id,
                'suite_code': self.suite_code,
                'operator': 'system',
                'opt_time': datetime.datetime.now(),
                'sys_datetime': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'app_dict_list': [],
                'br_name': '',
                'db_env': '',
                'is_clear_cache': False,
                'test_set': '',
                'opt_type': '',
                'jacoco_type': '',
                'pinpoint_type': '',
                'mock_type': '',
                'arex_type': '',
                'agent_info': '',
                'bis_pipeline_id': '',
                'biz_db_init_flag': '',
                'param1': '',
                'param2': '',
                'param3': '',
                'job_log_id': '',
                'job_bo_key': '',
            }

    def _execute_es_handler(self, step_enum):
        """执行ES处理器"""
        try:
            # 获取参数字典
            param_dict = self._get_param_dict()
            
            # 创建处理器实例
            handler = StepHandlerFactory.get_instance(step_enum, param_dict)
            
            # 执行处理器
            handler.run()
            
            log.info(f"ES处理器执行成功: {step_enum.cn_name}")
            return True
            
        except Exception as e:
            log.error(f"ES处理器执行失败: {step_enum.cn_name}, 错误: {e}")
            raise

    def es_health_check(self):
        """ES健康检查"""
        log.info(f"========== 开始ES健康检查 [业务: {self.biz_iter_id}] ==========")
        try:
            result = self._execute_es_handler(StepEnum.ES_HEALTH_CHECK)
            log.info(f"========== ES健康检查完成 [业务: {self.biz_iter_id}] ==========")
            return result
        except Exception as e:
            log.error(f"ES健康检查失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def es_backup_verify(self):
        """ES备份验证"""
        log.info(f"========== 开始ES备份验证 [业务: {self.biz_iter_id}] ==========")
        try:
            result = self._execute_es_handler(StepEnum.ES_BACKUP_VERIFY)
            log.info(f"========== ES备份验证完成 [业务: {self.biz_iter_id}] ==========")
            return result
        except Exception as e:
            log.error(f"ES备份验证失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def es_data_clear(self):
        """ES数据清空"""
        log.info(f"========== 开始ES数据清空 [业务: {self.biz_iter_id}] ==========")
        try:
            result = self._execute_es_handler(StepEnum.ES_DATA_CLEAR)
            log.info(f"========== ES数据清空完成 [业务: {self.biz_iter_id}] ==========")
            return result
        except Exception as e:
            log.error(f"ES数据清空失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def es_backup_restore(self):
        """ES备份恢复"""
        log.info(f"========== 开始ES备份恢复 [业务: {self.biz_iter_id}] ==========")
        try:
            result = self._execute_es_handler(StepEnum.ES_BACKUP_RESTORE)
            log.info(f"========== ES备份恢复完成 [业务: {self.biz_iter_id}] ==========")
            return result
        except Exception as e:
            log.error(f"ES备份恢复失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def es_script_exec(self):
        """ES脚本执行"""
        log.info(f"========== 开始ES脚本执行 [业务: {self.biz_iter_id}] ==========")
        try:
            result = self._execute_es_handler(StepEnum.ES_SCRIPT_EXEC)
            log.info(f"========== ES脚本执行完成 [业务: {self.biz_iter_id}] ==========")
            return result
        except Exception as e:
            log.error(f"ES脚本执行失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def record_build_id(self, status):
        """记录构建ID和状态"""
        log.info(f"========== 记录构建状态: {status} [业务: {self.biz_iter_id}] ==========")
        try:
            # 获取参数字典并更新状态
            param_dict = self._get_param_dict()
            param_dict['job_result'] = status
            
            # 创建RECORD处理器
            handler = StepHandlerFactory.get_instance(StepEnum.RECORD, param_dict)
            handler.run()
            
            log.info(f"========== 构建状态记录完成: {status} [业务: {self.biz_iter_id}] ==========")
            return True
        except Exception as e:
            log.error(f"记录构建状态失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def record_execute_result(self, result):
        """记录执行结果"""
        log.info(f"========== 记录执行结果: {result} [业务: {self.biz_iter_id}] ==========")
        try:
            # 获取参数字典并更新结果
            param_dict = self._get_param_dict()
            param_dict['job_result'] = result
            
            # 创建RECORD处理器
            handler = StepHandlerFactory.get_instance(StepEnum.RECORD, param_dict)
            handler.run()
            
            # 新增：直接操作jenkins_mgt_test_es_init_job表的逻辑
            log.info('record_execute_result - 更新jenkins_mgt_test_es_init_job表状态')
            with DBConnectionManager() as db:
                sql = '''
                update jenkins_mgt_test_es_init_job set status = '{}' 
                where id = {}
                '''.format(result, self.business_id)
                log.info(sql)
                db.cur.execute(sql)
                db.connection.commit()
            
            log.info(f"========== 执行结果记录完成: {result} [业务: {self.biz_iter_id}] ==========")
            return True
        except Exception as e:
            log.error(f"记录执行结果失败 [业务: {self.biz_iter_id}]: {e}")
            raise

    def call(self, params):
        """主调用方法，参考test_data_init.py的call方法"""
        action = params[0]
        
        # 定义可用的操作映射
        acceptor = {
            'es_health_check': self.es_health_check,
            'es_backup_verify': self.es_backup_verify,
            'es_data_clear': self.es_data_clear,
            'es_backup_restore': self.es_backup_restore,
            'es_script_exec': self.es_script_exec,
        }
        
        if action in acceptor:
            log.info(f"执行ES操作: {action}")
            return acceptor[action]()
        else:
            log.error(f"未知的ES操作: {action}")
            raise ValueError(f"不支持的操作: {action}")


# 主函数入口，支持命令行调用
if __name__ == '__main__':
    cur_time = datetime.datetime.now()
    log.info(">>>>ES初始化功能模块启动 {}".format(get_time_str(cur_time)))

    param_list = sys.argv
    log.info("调用 {}".format(param_list[1:]))
    
    # 解析参数，参考test_data_init.py的方式
    params = param_list[1:]
    
    if len(params) < 1:
        log.error("参数不足，至少需要操作类型参数")
        sys.exit(-1)
    
    action = params[0]           # 操作类型
    
    # 根据操作类型检查参数数量
    if action in ["record_build_id", "record_execute_result"]:
        if len(param_list) < 7:  # 脚本名 + 6个参数
            log.error(f"{action}操作参数不足，需要至少6个参数")
            sys.exit(-1)
        
        # record操作的参数格式: action job_name business_id biz_code build_id status
        job_name = params[1]         # 任务名称
        business_id = params[2]      # 业务ID
        biz_code = params[3]         # 业务代码
        build_id = params[4]         # 构建ID
        status = params[5] if len(params) > 5 else 'running'  # 状态
        
        log.info(f'business_id = {business_id}, 类型为： {type(business_id)}')
        
        try:
            # 对于record操作，使用简化的参数创建处理器
            es_handler = EsInitHandler(job_name, '', biz_code, '', 
                                      '', business_id, build_id)
            
            if action == "record_build_id":
                es_handler.record_build_id(status)
            elif action == "record_execute_result":
                es_handler.record_execute_result(status)  # 这里status实际是result
                
        except Exception as e:
            log.error(f"ES初始化功能执行失败: {e}")
            log.error(traceback.format_exc())
            sys.exit(-1)
            
    else:
        # 标准ES操作的参数格式
        if len(param_list) < 8:  # 标准ES操作需要8个参数
            log.error("标准ES操作参数不足，需要至少8个参数")
            sys.exit(-1)
            
        job_name = params[1]         # 任务名称
        workspace = params[2]        # 工作空间
        biz_code = params[3]         # 业务代码
        biz_branch_name = params[4]  # 业务分支名称
        suite_code = params[5]       # 套件代码
        business_id = params[6]      # 业务ID
        build_id = params[7] if len(params) > 7 else ''  # 构建ID
        
        log.info(f'business_id = {business_id}, 类型为： {type(business_id)}')
        
        try:
            # 创建ES初始化处理器
            es_handler = EsInitHandler(job_name, workspace, biz_code, biz_branch_name, 
                                      suite_code, business_id, build_id)
            
            # 调用标准ES操作
            es_handler.call(params)
            
        except Exception as e:
            log.error(f"ES初始化功能执行失败: {e}")
            log.error(traceback.format_exc())
            sys.exit(-1)
    
    log.info("ES初始化功能执行完成")
    sys.exit(0)