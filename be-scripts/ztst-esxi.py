#!/usr/bin/env python3
# --------------------------------------
# File   : ztst-esxi.py
# Author : zt
# Email  : <EMAIL>
# History:
#    2025-01-XX    Zt    First release.
# --------------------------------------

# 从 Prometheus 获取 ESXi 指标数据并生成 CSV 文件
# 包含 CPU、内存、硬盘、IO 等数据
# 版本
#     2025-01-XX    zt    First release.

import os
import sys
import json
import requests
import pandas as pd
import logging
from datetime import datetime, timedelta
from urllib.parse import urlencode
import time

# Prometheus 配置信息
PROMETHEUS_CONFIG = {
    "BASE_URL": "http://prometheus.intelnal.howbuy.com/api/v1",
    "TIMEOUT": 30,
    "RETRY_COUNT": 3,
    "RETRY_DELAY": 5
}

# ESXi 监控指标配置 - 更全面的指标查询
ESXI_METRICS = {
    # 主机发现查询 - 用于获取所有ESXi主机列表
    "discovery": {
        "all_hosts": 'up{job=~".*esxi.*|.*vmware.*"}',
        "vmware_hosts": 'vmware_host_power_state',
        "esxi_exporters": 'up{job=~".*vmware.*"}',
        "host_info": 'vmware_host_info'
    },
    # CPU 相关指标
    "cpu": {
        "cpu_usage_percent": 'vmware_host_cpu_usage',
        "cpu_usage_average": 'vmware_host_cpu_usage_average',
        "cpu_cores": 'vmware_host_cpu_cores',
        "cpu_mhz": 'vmware_host_cpu_mhz',
        "cpu_ready_summation": 'vmware_vm_cpu_ready_summation_rate'
    },
    # 内存相关指标
    "memory": {
        "memory_usage_percent": 'vmware_host_memory_usage',
        "memory_usage_average": 'vmware_host_memory_usage_average',
        "memory_total_mb": 'vmware_host_memory_max',
        "memory_used_mb": 'vmware_host_memory_usage_average',
        "memory_available_mb": 'vmware_host_memory_available'
    },
    # 硬盘相关指标
    "disk": {
        "disk_usage_percent": 'vmware_datastore_disk_used / vmware_datastore_disk_capacity * 100',
        "disk_capacity_gb": 'vmware_datastore_disk_capacity / 1024 / 1024 / 1024',
        "disk_used_gb": 'vmware_datastore_disk_used / 1024 / 1024 / 1024',
        "disk_free_gb": 'vmware_datastore_disk_free / 1024 / 1024 / 1024'
    },
    # IO 相关指标
    "io": {
        "disk_read_rate": 'vmware_host_disk_read_rate',
        "disk_write_rate": 'vmware_host_disk_write_rate',
        "network_receive_rate": 'vmware_host_network_receive_rate',
        "network_transmit_rate": 'vmware_host_network_transmit_rate'
    },
    # 系统相关指标
    "system": {
        "uptime_seconds": 'vmware_host_uptime_seconds',
        "power_state": 'vmware_host_power_state',
        "vm_count": 'vmware_host_vm_count',
        "connection_state": 'vmware_host_connection_state'
    }
}

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s]: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
log = logging.getLogger(__name__)

class PrometheusClient:
    """Prometheus 客户端类"""
    
    def __init__(self, base_url, timeout=30):
        self.base_url = base_url
        self.timeout = timeout
        self.session = requests.Session()
        
    def query(self, query, time_param=None):
        """执行 Prometheus 查询"""
        url = f"{self.base_url}/query"
        params = {"query": query}
        
        if time_param:
            params["time"] = time_param
            
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            log.error(f"Prometheus 查询失败: {e}")
            return None
    
    def query_range(self, query, start, end, step="60s"):
        """执行 Prometheus 范围查询"""
        url = f"{self.base_url}/query_range"
        params = {
            "query": query,
            "start": start,
            "end": end,
            "step": step
        }
        
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            log.error(f"Prometheus 范围查询失败: {e}")
            return None
    
    def get_label_values(self, label_name):
        """获取标签的所有值"""
        url = f"{self.base_url}/label/{label_name}/values"
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            result = response.json()
            if result.get("status") == "success":
                return result.get("data", [])
            return []
        except requests.exceptions.RequestException as e:
            log.error(f"获取标签值失败: {e}")
            return []
    
    def get_series(self, match):
        """获取时间序列"""
        url = f"{self.base_url}/series"
        params = {"match[]": match}
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            result = response.json()
            if result.get("status") == "success":
                return result.get("data", [])
            return []
        except requests.exceptions.RequestException as e:
            log.error(f"获取时间序列失败: {e}")
            return []

class ESXiMetricsCollector:
    """ESXi 指标收集器"""
    
    def __init__(self, prometheus_client):
        self.prometheus_client = prometheus_client
        self.discovered_hosts = set()
        
    def discover_hosts(self):
        """发现所有 ESXi 主机"""
        log.info("开始发现 ESXi 主机...")
        
        hosts = set()
        
        # 方法1: 通过发现查询获取主机
        for query_name, query in ESXI_METRICS["discovery"].items():
            log.info(f"执行发现查询: {query_name}")
            result = self.prometheus_client.query(query)
            
            if result and result.get("status") == "success":
                for item in result["data"]["result"]:
                    labels = item.get("metric", {})
                    
                    # 尝试多种标签来识别主机
                    host_candidates = [
                        labels.get("instance"),
                        labels.get("esxi_host"),
                        labels.get("host"),
                        labels.get("server"),
                        labels.get("vmware_host"),
                        labels.get("target")
                    ]
                    
                    for host in host_candidates:
                        if host and host not in ["", "unknown"]:
                            hosts.add(host)
                            log.info(f"发现主机: {host} (通过 {query_name})")
        
        # 方法2: 通过标签值获取主机
        for label in ["instance", "esxi_host", "host", "server", "vmware_host"]:
            label_values = self.prometheus_client.get_label_values(label)
            for value in label_values:
                if value and "esxi" in value.lower() or "vmware" in value.lower() or ":" in value:
                    hosts.add(value)
                    log.info(f"发现主机: {value} (通过标签 {label})")
        
        # 方法3: 通过时间序列获取主机
        series_queries = [
            'vmware_host_power_state',
            'vmware_host_cpu_usage',
            'vmware_host_memory_usage',
            'up{job=~".*vmware.*"}'
        ]
        
        for query in series_queries:
            series = self.prometheus_client.get_series(query)
            for serie in series:
                host_candidates = [
                    serie.get("instance"),
                    serie.get("esxi_host"),
                    serie.get("host"),
                    serie.get("server"),
                    serie.get("vmware_host")
                ]
                
                for host in host_candidates:
                    if host and host not in ["", "unknown"]:
                        hosts.add(host)
                        log.info(f"发现主机: {host} (通过时间序列)")
        
        self.discovered_hosts = hosts
        log.info(f"总共发现 {len(hosts)} 台主机: {list(hosts)}")
        return hosts
    
    def collect_host_specific_metrics(self, host):
        """收集特定主机的指标"""
        log.info(f"收集主机 {host} 的指标...")
        
        host_data = {
            "host": host,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "datacenter": "",
            "cluster": "",
        }
        
        # 为每个主机构建特定的查询
        for category, metrics in ESXI_METRICS.items():
            if category == "discovery":  # 跳过发现查询
                continue
                
            for metric_name, base_query in metrics.items():
                # 构建针对特定主机的查询
                host_queries = [
                    f'{base_query}{{instance="{host}"}}',
                    f'{base_query}{{esxi_host="{host}"}}',
                    f'{base_query}{{host="{host}"}}',
                    f'{base_query}{{server="{host}"}}',
                    f'{base_query}{{vmware_host="{host}"}}',
                    base_query  # 最后尝试原始查询
                ]
                
                value = None
                for query in host_queries:
                    try:
                        result = self.prometheus_client.query(query)
                        if result and result.get("status") == "success":
                            data = result["data"]["result"]
                            
                            # 查找匹配的主机数据
                            for item in data:
                                labels = item.get("metric", {})
                                item_host = (labels.get("instance") or 
                                           labels.get("esxi_host") or 
                                           labels.get("host") or 
                                           labels.get("server") or 
                                           labels.get("vmware_host"))
                                
                                if item_host == host or not item_host:
                                    if item.get("value"):
                                        value = float(item["value"][1])
                                        
                                        # 提取额外的标签信息
                                        if not host_data["datacenter"]:
                                            host_data["datacenter"] = labels.get("datacenter", "")
                                        if not host_data["cluster"]:
                                            host_data["cluster"] = labels.get("cluster", "")
                                        
                                        break
                            
                            if value is not None:
                                break
                                
                    except Exception as e:
                        log.debug(f"查询失败 {query}: {e}")
                        continue
                
                # 记录指标值
                field_name = f"{category}_{metric_name}"
                host_data[field_name] = value if value is not None else 0
                
                if value is not None:
                    log.debug(f"主机 {host} 指标 {field_name}: {value}")
                else:
                    log.debug(f"主机 {host} 指标 {field_name}: 无数据")
        
        return host_data
    
    def collect_all_hosts_metrics(self):
        """收集所有主机的指标"""
        # 首先发现所有主机
        hosts = self.discover_hosts()
        
        if not hosts:
            log.warning("未发现任何 ESXi 主机")
            return []
        
        all_metrics = []
        
        # 为每个主机收集指标
        for host in hosts:
            try:
                host_metrics = self.collect_host_specific_metrics(host)
                all_metrics.append(host_metrics)
                log.info(f"成功收集主机 {host} 的指标")
            except Exception as e:
                log.error(f"收集主机 {host} 指标失败: {e}")
                # 即使失败也添加基本信息
                all_metrics.append({
                    "host": host,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "datacenter": "",
                    "cluster": "",
                    "error": str(e)
                })
        
        return all_metrics
    
    def collect_range_metrics(self, hours=1):
        """收集指定时间范围的指标数据"""
        log.info(f"开始收集最近 {hours} 小时的 ESXi 指标数据...")
        
        # 首先发现所有主机
        hosts = self.discover_hosts()
        
        if not hosts:
            log.warning("未发现任何 ESXi 主机")
            return []
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        # 转换为 Unix 时间戳
        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())
        
        all_metrics = []
        
        # 为每个主机收集历史数据
        for host in hosts:
            log.info(f"收集主机 {host} 的历史数据...")
            
            # 构建基本的范围查询
            base_queries = [
                f'vmware_host_cpu_usage{{instance="{host}"}}',
                f'vmware_host_memory_usage{{instance="{host}"}}',
                f'vmware_host_power_state{{instance="{host}"}}'
            ]
            
            for query in base_queries:
                try:
                    result = self.prometheus_client.query_range(
                        query, start_timestamp, end_timestamp, "300s"
                    )
                    
                    if result and result.get("status") == "success":
                        for item in result["data"]["result"]:
                            values = item.get("values", [])
                            labels = item.get("metric", {})
                            
                            for timestamp, value in values:
                                metric_data = {
                                    "host": host,
                                    "timestamp": datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S"),
                                    "datacenter": labels.get("datacenter", ""),
                                    "cluster": labels.get("cluster", ""),
                                    "metric_name": query.split("{")[0],
                                    "value": float(value) if value != "NaN" else 0
                                }
                                all_metrics.append(metric_data)
                                
                except Exception as e:
                    log.error(f"收集主机 {host} 历史数据失败: {e}")
        
        return all_metrics

def generate_csv_report(metrics_data, output_file="esxi.csv"):
    """生成 CSV 报告"""
    if not metrics_data:
        log.warning("没有收集到指标数据，无法生成 CSV 文件")
        return False
    
    try:
        # 创建 DataFrame
        df = pd.DataFrame(metrics_data)
        
        # 排序
        if "host" in df.columns and "timestamp" in df.columns:
            df = df.sort_values(["host", "timestamp"])
        
        # 保存到 CSV
        df.to_csv(output_file, index=False, encoding="utf-8")
        log.info(f"CSV 报告已生成: {output_file}")
        log.info(f"共收集到 {len(df)} 条记录")
        
        if "host" in df.columns:
            unique_hosts = df['host'].nunique()
            log.info(f"涉及 {unique_hosts} 台主机: {list(df['host'].unique())}")
        
        return True
        
    except Exception as e:
        log.error(f"生成 CSV 文件失败: {e}")
        return False

def print_summary(metrics_data):
    """打印数据摘要"""
    if not metrics_data:
        log.info("没有收集到数据")
        return
    
    df = pd.DataFrame(metrics_data)
    
    print("\n" + "="*60)
    print("ESXi 指标数据摘要")
    print("="*60)
    print(f"总记录数: {len(df)}")
    
    if 'host' in df.columns:
        unique_hosts = df['host'].unique()
        print(f"主机数量: {len(unique_hosts)}")
        print(f"主机列表:")
        for i, host in enumerate(sorted(unique_hosts), 1):
            host_data = df[df['host'] == host]
            print(f"  {i:2d}. {host} ({len(host_data)} 条记录)")
    
    if 'timestamp' in df.columns:
        print(f"时间范围: {df['timestamp'].min()} ~ {df['timestamp'].max()}")
    
    # 显示指标统计
    numeric_columns = df.select_dtypes(include=['float64', 'int64']).columns
    if len(numeric_columns) > 0:
        print(f"\n数值指标数量: {len(numeric_columns)}")
        
        # 显示有数据的指标
        non_zero_metrics = []
        for col in numeric_columns:
            if col in df.columns and df[col].sum() > 0:
                non_zero_metrics.append(col)
        
        if non_zero_metrics:
            print(f"有效指标: {len(non_zero_metrics)}")
            for metric in non_zero_metrics[:10]:  # 只显示前10个
                avg_val = df[metric].mean()
                print(f"  - {metric}: 平均值 {avg_val:.2f}")
    
    print("="*60)

def main():
    """主函数"""
    log.info("ESXi 指标收集器启动")
    
    try:
        # 创建 Prometheus 客户端
        prometheus_client = PrometheusClient(
            PROMETHEUS_CONFIG["BASE_URL"],
            PROMETHEUS_CONFIG["TIMEOUT"]
        )
        
        # 创建指标收集器
        collector = ESXiMetricsCollector(prometheus_client)
        
        # 收集所有主机的即时指标数据
        log.info("收集所有主机的即时指标数据...")
        instant_metrics = collector.collect_all_hosts_metrics()
        
        if instant_metrics:
            # 生成即时数据 CSV
            generate_csv_report(instant_metrics, "esxi_instant.csv")
            print_summary(instant_metrics)
        
        # 收集最近1小时的历史数据
        log.info("收集历史指标数据...")
        range_metrics = collector.collect_range_metrics(hours=1)
        
        if range_metrics:
            # 生成历史数据 CSV
            generate_csv_report(range_metrics, "esxi_history.csv")
            
        # 合并数据生成主报告
        all_metrics = instant_metrics + range_metrics if instant_metrics and range_metrics else (instant_metrics or range_metrics)
        
        if all_metrics:
            generate_csv_report(all_metrics, "esxi.csv")
            log.info("ESXi 指标收集完成")
        else:
            log.error("未能收集到任何指标数据")
            
    except Exception as e:
        log.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()