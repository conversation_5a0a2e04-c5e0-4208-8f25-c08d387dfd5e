# ES 初始化与备份管理设计文档

本文档详细描述了 ES (Elasticsearch) 初始化和备份管理相关接口的设计。

## 1. ES 备份列表分页查询接口

### 1.1. 功能描述

提供一个列表分页查询接口，用于根据指定条件筛选 `es_mgt_dump_info` 表中的 ES 备份信息。接口名包含 "List"。

### 1.2. 接口设计

- **HTTP 方法**: `GET`
- **URL**: `/spider/es_mgt/backups/list/` (暂定, 根据项目路由规范)
- **所属模块**: `es_mgt`

### 1.3. 请求参数

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| `es_dump_name` | string | 否 | ES 备份名称 |
| `remark` | string | 否 | 备注信息 |
| `source_es_module_name` | string | 否 | 源 ES 模块名称 |
| `page` | integer | 否 | 页码 (默认为 1) |
| `page_size` | integer | 否 | 每页数量 (默认为 10) |

### 1.4. 返回数据示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "count": 1,
    "results": [
      {
        "id": 1,
        "es_dump_name": "backup_20231027",
        "remark": "这是一个备份",
        "source_es_module_name": "module_a",
        "create_time": "2023-10-27T10:00:00Z"
      }
    ]
  }
}
```

## 2. ES 备份详情列表分页查询接口

### 2.1. 功能描述

根据备份名称 (`es_dump_name`) 查询 `jenkins_mgt_test_es_init_job` 表中的备份初始化作业详情。结果按创建时间倒序分页展示。接口名包含 "List"。

### 2.2. 接口设计

- **HTTP 方法**: `GET`
- **URL**: `/spider/es_mgt/backups/details/list/` (暂定, 根据项目路由规范)
- **所属模块**: `es_mgt`

### 2.3. 请求参数

| 参数名 | 类型 | 是否必填 | 描述 |
| --- | --- | --- | --- |
| `es_dump_name` | string | 是 | ES 备份名称 |
| `page` | integer | 否 | 页码 (默认为 1) |
| `page_size` | integer | 否 | 每页数量 (默认为 10) |

### 2.4. 返回数据示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "count": 1,
    "results": [
      {
        "id": 1,
        "es_dump_name": "backup_20231027",
        "job_type": "test_es_init",
        "status": "completed",
        "job_name": "Test_es_init",
        "job_build_id": "123",
        "create_time": "2023-10-27T10:05:00Z",
        "update_time": "2023-10-27T10:10:00Z"
      }
    ]
  }
}
```

## 3. 验证/更新 ES 备份状态接口

### 3.1. 功能描述

用于验证或更新 ES 备份的状态。这可能是一个触发异步任务（如 Jenkins Job）并更新状态的回调或主动操作。

### 3.2. 接口设计

- **HTTP 方法**: `POST`
- **URL**: `/spider/es_mgt/backups/status/`
- **所属模块**: `es_mgt`

### 3.3. 请求体 (Body)

```json
{
  "es_dump_name": "backup_20231027",
  "status": "verifying"
}
```

### 3.4. 返回数据示例

```json
{
  "code": 0,
  "message": "状态更新成功",
  "data": {
    "es_dump_name": "backup_20231027",
    "current_status": "verifying"
  }
}
```